{"name": "qst-merchant-admin-2.0", "version": "2.5.6", "author": "zxwk1998", "participants": [], "homepage": "https://vuejs-core.cn", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "serve:node20": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "build:node20": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build", "serve:mac": "export NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "build:mac": "export NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build", "lint": "vue-cli-service lint --fix", "lint:prettier": "prettier {src,mock,library}/**/*.{html,vue,css,sass,scss,js,ts,md} --write", "clear": "rimraf node_modules&&npm install  --registry=--registry=https://registry.npmmirror.com", "push": "start ./push.sh"}, "repository": {"type": "git", "url": "git+https://github.com/zxwk1998/vue-admin-better.git"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,vue}": ["vue-cli-service lint", "git add"]}, "dependencies": {"@tinymce/tinymce-vue": "^2.1.0", "axios": "^1.7.9", "caniuse-lite": "^1.0.30001700", "clipboard": "^2.0.11", "core-js": "^3.40.0", "dayjs": "^1.11.13", "echarts": "5.6.0", "element-ui": "^2.15.14", "jsencrypt": "^3.3.2", "layouts": "file:layouts", "lodash": "^4.17.21", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "qqmap": "^1.0.1", "qs": "^6.14.0", "screenfull": "^5.2.0", "tinymce": "^5.0.11", "v-viewer": "^1.6.4", "vab-icon": "file:vab-icon", "viewerjs": "^1.11.7", "vue": "~2.7.14", "vue-draggable-plus": "^0.6.0", "vue-echarts": "6.7.3", "vue-jsonp": "^2.1.0", "vue-pdf": "^4.3.0", "vue-router": "^3.6.5", "vue-template-compiler": "~2.7.14", "vue-visibility-change": "^1.2.1", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.5.19", "@vue/cli-service": "^4.5.19", "body-parser": "^1.20.3", "chalk": "^4.1.2", "chokidar": "^4.0.3", "default-passive-events": "^2.0.0", "html2canvas": "^1.4.1", "lint-staged": "^15.4.3", "moment": "^2.30.1", "prettier": "^2.8.8", "qrcode": "^1.5.4", "sass": "~1.32.13", "sass-loader": "^10.1.1", "stylelint": "^14.16.1", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recess-order": "^3.1.0", "svg-sprite-loader": "^6.0.11", "v-viewer": "^1.6.4", "vue-visibility-change": "^1.2.1", "webpack": "4.46.0", "webpackbar": "^7.0.0"}, "keywords": ["vue", "admin", "dashboard", "element-ui", "vue-admin", "element-admin", "boilerplate", "admin-template", "management-system"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}}