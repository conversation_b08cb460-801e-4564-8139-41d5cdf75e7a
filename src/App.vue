<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-02-18 14:31:04
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-03-12 15:13:42
 * @FilePath: /qst-merchant-admin-2.0/src/App.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div id="vue-admin-better">
    <router-view />
  </div>
</template>

<script>
export default {
  name: 'App',
  mounted() {},
  created() {
    if (localStorage.getItem('accessToken')) {
      console.log('ReachToken')
      this.$ReachToken.init()
    }
  },
}
</script>
