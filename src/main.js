/*
 * @Author: shangfei <EMAIL>
 * @Date: 2025-02-18 14:31:04
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-24 09:23:41
 * @FilePath: \qst-merchant-admin-2.0\src\main.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue'
import App from './App'
import store from './store'
import router from './router'
import './plugins'
import '@/layouts/export'
import '@/components/export'
import { VueJsonp } from 'vue-jsonp'
import Viewer from 'v-viewer'
import 'viewerjs/dist/viewer.css'
import visibility from 'vue-visibility-change'
Vue.use(visibility)
Vue.use(Viewer)
/**
 * @description 生产环境默认都使用mock，如果正式用于生产环境时，记得去掉
 */
// if (process.env.NODE_ENV === 'production') {
//   const { mockXHR } = require('@/utils/static')
//   mockXHR()
// }

Vue.use(VueJsonp)
Vue.config.productionTip = false
// visibility.change((evt, hidden) => {
//   console.log('global callback: ' + hidden)
// })
new Vue({
  el: '#vue-admin-better',
  router,
  store,
  render: (h) => h(App),
})
