/*
 * @Author: liqian liqian@123
 * @Date: 2025-05-19 09:34:09
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-24 11:52:58
 * @FilePath: \qst-merchant-admin-2.0\src\api\shop\order.js
 * @Description:
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
 */
import request from '@/utils/request'
// 获取商品参数配置
export function getOrderListApi(data) {
  return request({
    url: '/merchant/order/list',
    method: 'post',
    data,
  })
}

//获取订单筛选配置
export function getSearchConfig(data) {
  return request({
    url: '/merchant/order/getSearchConfig',
    method: 'post',
    data,
  })
}

//订单详情
export function getOrderDetail(data) {
  return request({
    url: '/merchant/order/detail',
    method: 'post',
    data,
  })
}

//取消订单
export function getOrderCancel(data) {
  return request({
    url: '/merchant/order/cancel',
    method: 'post',
    data,
  })
}

//添加订单备注
export function getOrderRemarks(data) {
  return request({
    url: '/merchant/order/addRemark',
    method: 'post',
    data,
  })
}

//订单改价详细信息（订单维度）
export function changePriceInfoByOrders(data) {
  return request({
    url: '/merchant/order/changePriceInfoByOrders',
    method: 'post',
    data,
  })
}

//订单改价（订单维度）
export function changePriceByOrders(data) {
  return request({
    url: '/merchant/order/changePriceByOrders',
    method: 'post',
    data,
  })
}

//订单改价详细信息（商品维度）
export function changePriceInfoByGoods(data) {
  return request({
    url: '/merchant/order/changePriceInfoByGoods',
    method: 'post',
    data,
  })
}

//订单改价（商品维度）
export function changePriceByGoods(data) {
  return request({
    url: '/merchant/order/changePriceByGoods',
    method: 'post',
    data,
  })
}

//获取改价选项
export function getChangePriceDimension(data) {
  return request({
    url: '/merchant/order/getChangePriceDimension',
    method: 'post',
    data,
  })
}

// 单订单发货
export function getShipments(data) {
  return request({
    url: '/merchant/order/shipments',
    method: 'post',
    data,
  })
}
//单订单发货获取可用商品列表
export function getPackageGoods(data) {
  return request({
    url: '/merchant/order/getPackageGoods',
    method: 'post',
    data,
  })
}

//单号查询
export function expressNumberRecognition(data) {
  return request({
    url: '/merchant/order/expressNumberRecognition',
    method: 'post',
    data,
  })
}

//在线拉取快递单号
export function createWaybillNo(data) {
  return request({
    url: '/merchant/carrier/createWaybillNo',
    method: 'post',
    data,
  })
}

// 承运商列表
export function getCarrierList(data) {
  return request({
    url: '/merchant/carrier/getSelectList',
    method: 'post',
    data,
  })
}

// 商户模板选择列表
export function templateListSelect(data) {
  return request({
    url: '/merchant/print/templateListSelect',
    method: 'post',
    data,
  })
}

// 打单发货时-添加批量获取面单号任务
export function batchWaybillNo(data) {
  return request({
    url: '/merchant/print/batchWaybillNo',
    method: 'post',
    data,
  })
}

// 异步导出订单列表（根据搜索条件导出）
export function exportByConditions(data) {
  return request({
    url: '/merchant/order/exportByConditions',
    method: 'post',
    data,
  })
}

// 异步导出订单列表（根据订单号导出）
export function exportBySubOrderNo(data) {
  return request({
    url: '/merchant/order/exportBySubOrderNo',
    method: 'post',
    data,
  })
}
// 订单配置详情
export function settingDetail(data) {
  return request({
    url: '/merchant/order/settingDetail',
    method: 'post',
    data,
  })
}

// 发货地址
export function shipmentAddressList(data) {
  return request({
    url: '/merchant/shop/shipmentAddressList',
    method: 'post',
    data,
  })
}

// 承运商列表
export function carrierList(data) {
  return request({
    url: '/merchant/carrier/list',
    method: 'post',
    data,
  })
}

// 上传发货模版后数据验证（成功后投递消息队列）
export function uploadExcelVerify(data) {
  return request({
    url: '/merchant/order/uploadExcelVerify',
    method: 'post',
    data,
  })
}

// 打印-批量出库单打印
export function batchOutboundOrderPrintDetail(data) {
  return request({
    url: '/merchant/print/batchOutboundOrderPrintDetail',
    method: 'post',
    data,
  })
}

// 获取脱敏数据字段
export function getOrderHideFiled(data) {
  return request({
    url: '/merchant/order/getOrderHideFiled',
    method: 'post',
    data,
  })
}

// 打单发货时-单个电子面单详情
export function waybillPrintDetail(data) {
  return request({
    url: '/merchant/print/waybillPrintDetail',
    method: 'post',
    data,
  })
}

// 商飞
// 打印设置 - 商户模板详情
export function settingDetailApi(data) {
  return request({
    url: '/merchant/order/settingDetail',
    method: 'post',
    data,
  })
}

// 打印设置 - 商户模板保存
export function settingSaveApi(data) {
  return request({
    url: '/merchant/order/settingSave',
    method: 'post',
    data,
  })
}

// 打印设置 - 商户模板选择列表
export function templateListSelectApi(data) {
  return request({
    url: '/merchant/print/templateListSelect',
    method: 'post',
    data,
  })
}

// 批量操作结果查询 - 筛选项
export function filterTaskListApi(data) {
  return request({
    url: '/merchant/task/filterTaskList',
    method: 'post',
    data,
  })
}

// 批量操作结果查询 - 列表
export function taskListApi(data) {
  return request({
    url: '/merchant/task/taskList',
    method: 'post',
    data,
  })
}

// 批量操作结果查询 - 导出错误信息
export function errExportApi(data) {
  return request({
    url: '/merchant/task/errExport',
    method: 'post',
    data,
  })
}

// 批量操作结果查询 - 错误详情列表
export function errDetailApi(data) {
  return request({
    url: '/merchant/task/errDetail',
    method: 'post',
    data,
  })
}

// 备货完成
export function prepareApi(data) {
  return request({
    url: '/merchant/order/prepare',
    method: 'post',
    data,
  })
}

// 运费差额计算
export function calculateFreightDifferenceApi(data) {
  return request({
    url: 'merchant/refund/calculateFreightDifference',
    method: 'post',
    data,
  })
}

// 打印设置下拉选项
export function setNodeList(data) {
  return request({
    url: '/merchant/order/nodeList',
    method: 'post',
    data,
  })
}

// 售后订单列表
export function refundList(data) {
  return request({
    url: '/merchant/refund/list',
    method: 'post',
    data,
  })
}

// 售后订单详情
export function refundDetails(data) {
  return request({
    url: '/merchant/refund/details',
    method: 'post',
    data,
  })
}
// 点击核销订单
export function verifyOrder(data) {
  return request({
    url: '/merchant/order/verifyOrder',
    method: 'post',
    data,
  })
}

// 售后订单详情记录
export function refundRecords(data) {
  return request({
    url: '/merchant/refund/records',
    method: 'post',
    data,
  })
}

// 根据店铺id获取自提下拉
export function pickupAddressListBySelect(data) {
  return request({
    url: '/merchant/address/pickupAddressListBySelect',
    method: 'post',
    data,
  })
}
// 售后订单搜索条件下拉框
export function refundGetSearchConfig(data) {
  return request({
    url: 'merchant/refund/getSearchConfig',
    method: 'post',
    data,
  })
}

// 通过弹窗数据
export function refundCalculateRefundAndFreight(data) {
  return request({
    url: 'merchant/refund/calculateRefundAndFreight',
    method: 'post',
    data,
  })
}

// 售后退货退款地址查询
export function refundShipmentAddressList(data) {
  return request({
    url: '/merchant/shop/shipmentAddressList',
    method: 'post',
    data,
  })
}

// 售后退货退款转为仅退款
export function refundChangeReturnRefundToRefundOnly(data) {
  return request({
    url: '/merchant/refund/changeReturnRefundToRefundOnly',
    method: 'post',
    data,
  })
}

// 通过售后申请
export function refundApproveAfterSale(data) {
  return request({
    url: '/merchant/refund/approveAfterSale',
    method: 'post',
    data,
  })
}

// 拒绝售后申请
export function refundRejectAfterSale(data, config = {}) {
  return request({
    url: '/merchant/refund/rejectAfterSale',
    method: 'post',
    data,
    ...config,
  })
}

// 售后寄回确认收货
export function refundConfirmReturnGoodsAndReceive(data) {
  return request({
    url: '/merchant/refund/confirmReturnGoodsAndReceive',
    method: 'post',
    data,
  })
}

// 售后批量通过接口
export function refundBatchApproveAfterSale(data) {
  return request({
    url: '/merchant/refund/batchApproveAfterSale',
    method: 'post',
    data,
  })
}

// 售后查询退货物流信息
export function refundLogistics(data) {
  return request({
    url: '/merchant/refund/logistics',
    method: 'post',
    data,
  })
}

// 预约管理列表
export function orderReservationsApi(data) {
  return request({
    url: '/merchant/order/orderReservations',
    method: 'post',
    data,
  })
}

// 预约管理列表详情
export function orderReservationDetailApi(data) {
  return request({
    url: '/merchant/order/orderReservationDetail',
    method: 'post',
    data,
  })
}

// 预约管理添加备注
export function orderReservationAddRemarkApi(data) {
  return request({
    url: '/merchant/order/orderReservationAddRemark',
    method: 'post',
    data,
  })
}

// 预约管理核销
export function orderReservationVerifyApi(data) {
  return request({
    url: '/merchant/order/orderReservationVerify',
    method: 'post',
    data,
  })
}

// 预约管理删除
export function orderReservationDeleteApi(data) {
  return request({
    url: '/merchant/order/orderReservationDelete',
    method: 'post',
    data,
  })
}
