
import request from '@/utils/request'

// 分组列表查询
export function goodsGroupListApi(data = {}) {
  return request({
    url: '/merchant/goodsGroup/list',
    method: 'post',
    data,
  })
}

// 新建分组
export function goodsGroupCreateApi(data = {}) {
  return request({
    url: '/merchant/goodsGroup/create',
    method: 'post',
    data,
  })
}

// 修改分组 
export function goodsGroupUpdateApi(data = {}) {
  return request({
    url: '/merchant/goodsGroup/update',
    method: 'post',
    data,
  })
}

// 删除分组
export function goodsGroupDeleteApi(data = {}) {
  return request({
    url: '/merchant/goodsGroup/delete',
    method: 'post',
    data,
  })
}

// 未关联商品列表
export function goodsGroupNotRelationGoodsApi(data = {}) {
  return request({
    url: '/merchant/goodsGroup/notRelationGoods',
    method: 'post',
    data,
  })
}

// 关联商品
export function goodsGroupRelationGoodsApi(data = {}) {
  return request({
    url: '/merchant/goodsGroup/relationGoods',
    method: 'post',
    data,
  })
}

// 添加商品到分组
export function goodsGroupAddGoodsApi(data = {}) {
  return request({
    url: '/merchant/goodsGroup/addRelation',
    method: 'post',
    data,
  })
}