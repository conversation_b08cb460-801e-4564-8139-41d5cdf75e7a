/*
 * @Author: shangfei <EMAIL>
 * @Date: 2025-04-08 15:32:05
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-13 10:35:17
 * @FilePath: /qst-merchant-admin-2.0/src/api/goodsCate.js
 * @Description: 分类列表
 */

import request from '@/utils/request'

// 分类列表
export function shopCategoryListApi(data = {}) {
  return request({
    url: '/merchant/shopCategory/list',
    method: 'post',
    data,
  })
}

// 分类列表-获取分类上级分类
export function getListBySelectApi(data = {}) {
  return request({
    url: '/merchant/shopCategory/listBySelect',
    method: 'post',
    data,
  })
}

// 分类列表-新增分类
export function createShopCategoryApi(data = {}) {
  return request({
    url: '/merchant/shopCategory/create',
    method: 'post',
    data,
  })
}

// 分类列表-修改分类
export function updateShopCategoryApi(data = {}) {
  return request({
    url: '/merchant/shopCategory/update',
    method: 'post',
    data,
  })
}

// 分类列表-修改显示状态
export function updateStatusShopCategoryApi(data = {}) {
  return request({
    url: '/merchant/shopCategory/updateStatus',
    method: 'post',
    data,
  })
}

// 修改icon
export function updateIconShopCategoryApi(data = {}) {
  return request({
    url: '/merchant/shopCategory/updateIcon',
    method: 'post',
    data,
  })
}

// 删除分类
export function deleteShopCategoryApi(data = {}) {
  return request({
    url: '/merchant/shopCategory/delete',
    method: 'post',
    data,
  })
}

// 分类排序
export function sortShopCategoryApi(data = {}) {
  return request({
    url: '/merchant/shopCategory/sort',
    method: 'post',
    data,
  })
}

// 系统图标列表
export function systemIconShopCategoryApi(data = {}) {
  return request({
    url: '/merchant/shopCategory/systemIcon',
    method: 'post',
    data,
  })
}