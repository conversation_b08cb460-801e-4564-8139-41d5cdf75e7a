/*
 * @Author: liqian liqian@123
 * @Date: 2025-04-02 09:21:52
 * @LastEditors: liqian liqian@123
 * @LastEditTime: 2025-04-16 09:56:57
 * @FilePath: \qst-merchant-admin-2.0\src\api\freightTemplate.js
 * @Description:
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
 */
import request from '@/utils/request'

// 运费模版列表
export function getdeliveryList(data) {
  return request({
    url: '/merchant/shop/deliveryList',
    method: 'post',
    data,
  })
}

// 新建运费模版
export function createDeliveryList(data) {
  return request({
    url: '/merchant/shop/createDelivery',
    method: 'post',
    data,
  })
}
// 运费模版详情
export function getDeliveryDetail(data) {
  return request({
    url: '/merchant/shop/deliveryDetail',
    method: 'post',
    data,
  })
}
// 删除运费模版列表
export function deleteDeliveryList(data) {
  return request({
    url: '/merchant/shop/deleteDelivery',
    method: 'post',
    data,
  })
}

// 修改运费模版列表
export function updateDeliveryList(data) {
  return request({
    url: '/merchant/shop/updateDelivery',
    method: 'post',
    data,
  })
}

// 切换模版状态
export function changeDeliveryStatus(data) {
  return request({
    url: '/merchant/shop/deliveryToggleStatus',
    method: 'post',
    data,
  })
}

//获取省市区
export function getAreaList(data) {
  return request({
    url: '/common/getProvinceCityDistrict',
    method: 'post',
    data,
  })
}

//获取计费方式
export function getdeliveryMethod(data) {
  return request({
    url: 'merchant/shop/deliveryMethodSelect',
    method: 'post',
    data,
  })
}
