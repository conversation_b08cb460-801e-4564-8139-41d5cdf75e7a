import request from '@/utils/request'

// 图片分类列表
export function categorylistApi(data = {}) {
  return request({
    url: '/merchant/gallery/listCategory',
    method: 'post',
    data,
  })
}

// 创建图片分类
export function createCategoryApi(data) {
  return request({
    url: '/merchant/gallery/createCategory',
    method: 'post',
    data,
  })
}

// 编辑图片分类
export function editCategoryApi(data) {
  return request({
    url: '/merchant/gallery/editCategory',
    method: 'post',
    data,
  })
}
// 删除图片分类
export function deletetCategoryApi(data) {
  return request({
    url: '/merchant/gallery/deleteCategory',
    method: 'post',
    data,
  })
}

// 网络图片转本地图片
export function getImageUrlApi(data) {
  return request({
    url: '/merchant/gallery/getImageUrl',
    method: 'post',
    data,
  })
}

// 图库列表
export function getImageListApi(data) {
  return request({
    url: '/merchant/gallery/list',
    method: 'post',
    data,
  })
}

// 图库创建
export function getcreateImageApi(data) {
  return request({
    url: '/merchant/gallery/create',
    method: 'post',
    data,
  })
}

// 图库编辑
export function getbatchEditImageApi(data) {
  return request({
    url: '/merchant/gallery/batchEdit',
    method: 'post',
    data,
  })
}

// 图库删除
export function getdeleteImageApi(data) {
  return request({
    url: '/merchant/gallery/delete',
    method: 'post',
    data,
  })
}

// 图库标签列表
export function getLabelListApi(data) {
  return request({
    url: '/merchant/gallery/getLabelList',
    method: 'post',
    data,
  })
}
