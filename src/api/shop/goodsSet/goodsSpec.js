import request from '@/utils/request'

// 规格列表
export function getGoodsSpecList(data) {
    return request({
      url: '/merchant/goodsSpec/list',
      method: 'post',
      data,
    })
  }

// 新建规格列表
export function createGoodsSpecList(data) {
    return request({
      url: '/merchant/goodsSpec/create',
      method: 'post',
      data,
    })
  }

// 修改规格列表
export function updateGoodsSpecList(data) {
    return request({
      url: '/merchant/goodsSpec/update',
      method: 'post',
      data,
    })
  }

// 详情规格列表
export function detailsGoodsSpecList(data) {
    return request({
      url: '/merchant/goodsSpec/details',
      method: 'post',
      data,
    })
  }

  // 删除规格列表
export function deleteGoodsSpecList(data) {
    return request({
      url: '/merchant/goodsSpec/delete',
      method: 'post',
      data,
    })
  }