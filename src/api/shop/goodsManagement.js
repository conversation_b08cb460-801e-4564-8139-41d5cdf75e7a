/*
 * @Author: shangfei <EMAIL>
 * @Date: 2025-04-19 11:16:24
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-28 11:32:18
 * @FilePath: /qst-merchant-admin-2.0/src/api/shop/goodsManagement.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'
import { status } from 'nprogress'
// 获取商品参数配置
export function getGoodsConfigApi() {
  return request({
    url: '/merchant/goods/getGoodsConfig',
    method: 'post'
  })
}

// 商品列表
export function getGoodsListApi(data) {
  return request({
    url: '/merchant/goods/list',
    method: 'post',
    data
  })
}

// 商品统计
export function getGoodsCensusApi(data) {
  return request({
    url: '/merchant/goods/census',
    method: 'post',
    data
  })
}



// 创建商品 第一步 保存基本信息
export function goodsSaveFirstApi(data) {
  return request({
    url: '/merchant/goods/saveFirst',
    method: 'post',
    data
  })
}

// 创建商品 第二步 保存商品（规格库存）
export function goodsSaveSecondApi(data) {
  return request({
    url: '/merchant/goods/saveSecond',
    method: 'post',
    data
  })
}

// 创建商品 第三步  保存商品（图文详情）
export function goodsSaveThirdApi(data) {
  return request({
    url: '/merchant/goods/saveThird',
    method: 'post',
    data
  })
}

// 创建商品 第四步  保存物流
export function goodsSaveFourthApi(data) {
  return request({
    url: '/merchant/goods/saveFourth',
    method: 'post',
    data
  })
}

// 创建商品 第五步  保存门店
export function goodsSaveFiveApi(data) {
  return request({
    url: '/merchant/goods/saveFive',
    method: 'post',
    data
  })
}

// 商品详情
export function goodsDetailsApi(data) {
  return request({
    url: '/merchant/goods/details',
    method: 'post',
    data
  })
}

// spu商品上下架架
export function goodsShowApi(data) {
  return request({
    url: '/merchant/goods/isShow',
    method: 'post',
    data
  })
}


// sku 显示隐藏
export function skuShowApi(data) {
  return request({
    url: '/merchant/goods/isShowSku',
    method: 'post',
    data
  })
}

// 商品sku设置默认规格
export function skuDefaultApi(data) {
  return request({
    url: '/merchant/goods/isDefaultSku',
    method: 'post',
    data
  })
}

// 商品sku修改库存
export function skuStockApi(data) {
  return request({
    url: '/merchant/goods/updateStockSku',
    method: 'post',
    data
  })
}

// sku删除商品
export function deleteGoodsSkuApi(data) {
  return request({
    url: '/merchant/goods/deleteSku',
    method: 'post',
    data
  })
}

// sku恢复被删除商品
export function restoreSkuApi(data) {
  return request({
    url: '/merchant/goods/restoreSku',
    method: 'post',
    data
  })
}



// 商品删除spu
export function deleteGoodsApi(data) {
  return request({
    url: '/merchant/goods/delete',
    method: 'post',
    data
  })
}

// 商品恢复被删除spu
export function restoreGoodsApi(data) {
  return request({
    url: '/merchant/goods/restore',
    method: 'post',
    data
  })
}





// 验证店铺图库权限
export function checkGalleryPermissionApi(data) {
  return request({
    url: '/merchant/goods/checkGalleryPermission',
    method: 'post',
    data
  })
}


// spu 修改回收站商品编码
export function updateDeleteGoodsCodeApi(data) {
  return request({
    url: '/merchant/goods/updateDeleteGoodsCode',
    method: 'post',
    data
  })
}

// 修改回收站商品sku条形码
export function updateDeleteGoodsSkuCodeApi(data) {
  return request({
    url: '/merchant/goods/updateDeleteBarcode',
    method: 'post',
    data
  })
}

// 分组列表（不分页-下拉选项用）
export function goodsGroupListApi(data) {
  return request({
    url: '/merchant/goodsGroup/listBySelect',
    method: 'post',
    data
  })
}


// 分类列表-下拉选项用
export function goodsCategoryListApi(data) {
  return request({
    url: '/merchant/shopCategory/list',
    method: 'post',
    data: {
      ...data,
      status: 1
    }
  })
}


// 商品管理页面 - 分类列表-下拉选项用
export function goodsCategorylistBySelectApi(data) {
  return request({
    url: '/merchant/shopCategory/listBySelect',
    method: 'post',
    data: {
      ...data,
      status: 1
    }
  })
}


// 门店商品
export function goodsPickupShopList(data) {
  return request({
    url: '/merchant/shop/goodsPickupShopList',
    method: 'post',
    data
  })
}

// 获取卡券预约日期
export function getCardReservationApi(data) {
  return request({
    url: '/merchant/goods/getCardReservation',
    method: 'post',
    data
  })
}



// 保存商品（卡券有效期及预约设置）
export function saveCardSettingApi(data) {
  return request({
    url: '/merchant/goods/saveCardSetting',
    method: 'post',
    data
  })
}

// 保存卡券预约日期

export function saveCardReservationApi(data) {
  return request({
    url: '/merchant/goods/saveCardReservation',
    method: 'post',
    data
  })
}

// 保存商品（卡片设置）
export function saveCardBackgroundApi(data) {
  return request({
    url: '/merchant/goods/saveCardBackground',
    method: 'post',
    data
  })
}


// 审核商品
export function reviewGoodsApi(data) {
  return request({
    url: '/merchant/goods/review',
    method: 'post',
    data
  })
}


// 回收站删除
export function deleteHideApi(data) {
  return request({
    url: '/merchant/goods/deleteHide',
    method: 'post',
    data
  })
}
