/*
 * @Author: liqian <EMAIL>
 * @Email: <EMAIL>
 * @Date: 2025-06-18 14:21:44
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-06-25 15:57:00
 * @FilePath: \qst-merchant-admin-2.0\src\api\shop\orderVerification.js
 * @Description:
 */
import request from '@/utils/request'

// 获取当前店铺自提点
export function getOrderListApi(data) {
  return request({
    url: '/merchant/order/getBindPickupPoint',
    method: 'post',
    data,
  })
}

// 切换店铺自提点列表
export function switchPickupPointList(data) {
  return request({
    url: '/merchant/order/switchPickupPointList',
    method: 'post',
    data,
  })
}

// 点击绑定发送验证码
export function sendVerificationCode(data) {
  return request({
    url: '/merchant/order/sendVerificationCode',
    method: 'post',
    data,
  })
}

// 点击绑定自提点
export function verifyCodeBinding(data) {
  return request({
    url: '/merchant/order/verifyCodeBinding',
    method: 'post',
    data,
  })
}

// 核销列表
export function verifyRecord(data) {
  return request({
    url: '/merchant/order/verifyRecord',
    method: 'post',
    data,
  })
}
// 批量核销列表
export function batchVerifyRecord(data) {
  return request({
    url: '/merchant/order/batchVerifyRecord',
    method: 'post',
    data,
  })
}

// 点击批量核销
export function batchVerify(data) {
  return request({
    url: '/merchant/order/batchVerify',
    method: 'post',
    data,
  })
}

// 导出核销列表
export function exportVerifyRecord(data) {
  return request({
    url: '/merchant/order/exportVerifyRecord',
    method: 'post',
    data,
  })
}
