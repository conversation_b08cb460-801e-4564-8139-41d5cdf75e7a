

import request from '@/utils/request'

// 打印模版列表
export function printTemplateList(data = {}) {
  return request({
    url: 'merchant/print/templateList',
    method: 'post',
    data
  })
}

// 打印模版筛选项
export function filterTemplateListApi(data = {}) {
  return request({
    url: 'merchant/print/filterTemplateList',
    method: 'post',
    data
  })
}

// 创建打印模版
export function createTemplate(data = {}) {
  return request({
    url: 'merchant/print/createTemplate',
    method: 'post',
    data
  })
}

// 修改打印模版
export function updateTemplate(data = {}) {
  return request({
    url: 'merchant/print/updateTemplate',
    method: 'post',
    data
  })
}

// 系统模板选择列表
export function systemTemplateSelectApi(data = {}) {
  return request({
    url: 'merchant/print/systemTemplateSelect',
    method: 'post',
    data
  })
}

// 删除打印模版
export function deleteTemplate(data = {}) {
  return request({
    url: 'merchant/print/deleteTemplate',
    method: 'post',
    data
  })
}



// 设计-出库单获取系统可配置字段
export function getTemplateFields(data = {}) {
  return request({
    url: 'merchant/print/getTemplateFields',
    method: 'post',
    data
  })
}

// 设计-出库单获取系统详情
export function getTemplateDetail(data = {}) {
  return request({
    url: 'merchant/print/templateDetail',
    method: 'post',
    data
  })
}

// 设计-出库单保存
export function configureTemplateFields(data = {}) {
  return request({
    url: 'merchant/print/configureTemplateFields',
    method: 'post',
    data
  })
}


// 承运商列表
export function carrierListApi(data = {}) {
  return request({
    url: 'merchant/carrier/list',
    method: 'post',
    data
  })
}

// 商户模板选择列表
export function merchantTemplateSelectApi(data = {}) {
  return request({
    url: 'merchant/print/templateListSelect',
    method: 'post',
    data
  })
}

// 创建承运商
export function createCarrier(data = {}) {
  return request({
    url: 'merchant/carrier/save',
    method: 'post',
    data
  })
}

// 承运商详情
export function carrierDetailApi(data = {}) {
  return request({
    url: 'merchant/carrier/details',
    method: 'post',
    data
  })
}

// 修改承运商
export function updateCarrier(data = {}) {
  return request({
    url: 'merchant/carrier/update',
    method: 'post',
    data
  })
}

// 删除承运商
export function deleteCarrier(data = {}) {
  return request({
    url: 'merchant/carrier/delete',
    method: 'post',
    data
  })
}

// 承运商筛选项
export function filterCarrierListApi(data = {}) {
  return request({
    url: 'merchant/carrier/filterList',
    method: 'post',
    data
  })
}

// 承运商详情
export function carrierDetail(data = {}) {
  return request({
    url: 'merchant/carrier/detail',
    method: 'post',
    data
  })
}

// 删除承运商
export function deleteCarrierApi(data = {}) {
  return request({
    url: 'merchant/carrier/delete',
    method: 'post',
    data
  })
}


// 承运商设置是否禁用
export function isForbidden(data = {}) {
  return request({
    url: 'merchant/carrier/isForbidden',
    method: 'post',
    data
  })
}

// 打印模版 启用-停用
export function templateStatusApi(data = {}) {
  return request({
    url: 'merchant/print/templateStatus',
    method: 'post',
    data
  })
}