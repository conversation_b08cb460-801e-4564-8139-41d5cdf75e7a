import request from '@/utils/request'

// 获取库存预警设置
export function getStockWarning(data = {}) {
  return request({
    url: 'merchant/goods/warningDetail',
    method: 'post',
    data
  })
}

// 更新库存预警设置
export function updateStockWarning(data = {}) {
  return request({
    url: '/merchant/goods/createWarning',
    method: 'post',
    data
  })
}

/**
 * 获取商品分类列表
 * @returns {Promise} 返回商品分类列表数据
 */
export function getClassificationList(data = {}) {
  return request({
    url: '/merchant/shopCategory/listBySelect',
    method: 'post',
    data
  })
}

/**
 * 获取商品分组列表
 * @returns {Promise} 返回商品分组列表数据
 */
export function getGroupList(data = {}) {
  return request({
    url: '/merchant/goodsGroup/listBySelect',
    method: 'post',
    data
  })
}