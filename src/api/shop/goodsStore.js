
import request from '@/utils/request'
// 商品管理页面 - 分类列表-下拉选项用
export function goodsCategorylistBySelectApi(data) {
  return request({
    url: '/merchant/shopCategory/listBySelect ',
    method: 'post',
    data: {
      ...data,
      status: 1
    }
  })
}

// 分组列表（不分页-下拉选项用）
export function goodsGroupListApi(data) {
  return request({
    url: '/merchant/goodsGroup/listBySelect',
    method: 'post',
    data
  })
}



// 获取商品参数配置
export function getGoodsConfigApi() {
  return request({
    url: '/merchant/shopGoods/getGoodsConfig',
    method: 'post'
  })
}


// 商品列表
export function getGoodsListApi(data) {
  return request({
    url: '/merchant/shopGoods/list',
    method: 'post',
    data
  })
}

// 商品统计
export function getGoodsCensusApi(data) {
  return request({
    url: '/merchant/shopGoods/census',
    method: 'post',
    data
  })
}


// spu商品上下架架
export function goodsShowApi(data) {
  return request({
    url: '/merchant/shopGoods/isShow',
    method: 'post',
    data
  })
}

// sku删除商品
export function deleteGoodsSkuApi(data) {
  return request({
    url: '/merchant/shopGoods/deleteSku',
    method: 'post',
    data
  })
}


// sku恢复被删除商品
export function restoreSkuApi(data) {
  return request({
    url: '/merchant/shopGoods/restoreSku',
    method: 'post',
    data
  })
}


// sku 显示隐藏
export function skuShowApi(data) {
  return request({
    url: '/merchant/shopGoods/isShowSku',
    method: 'post',
    data
  })
}



// 商品删除spu
export function deleteGoodsApi(data) {
  return request({
    url: '/merchant/shopGoods/delete',
    method: 'post',
    data
  })
}

// 商品恢复被删除spu
export function restoreGoodsApi(data) {
  return request({
    url: '/merchant/shopGoods/restore',
    method: 'post',
    data
  })
}



// 商品sku修改库存
export function skuStoreStockApi(data) {
  return request({
    url: '/merchant/shopGoods/updateStockSku',
    method: 'post',
    data
  })
}


// 获取当前店铺是否有按钮权限
export function getCurrentCreateGoodsShopApi() {
  return request({
    url: '/merchant/shop/getCurrentCreateGoodsShop',
    method: 'post'
  })
}


// 提交审核
export function submitReviewApi(data = {}) {
  return request({
    url: '/merchant/shopGoods/submitReview',
    method: 'post',
    data
  })
}

// 商品详情
export function goodsStoreDetailsApi(data) {
  return request({
    url: '/merchant/shopGoods/details',
    method: 'post',
    data
  })
}

// 回收站删除
export function deleteHideApi(data) {
  return request({
    url: '/merchant/shopGoods/deleteHide',
    method: 'post',
    data
  })
}
