import request from '@/utils/request'

// 获取评价列表
export function getEvaluatedApi(data) {
  return request({
    url: '/merchant/orderGoods/evaluated',
    method: 'post',
    data,
  })
}

// 获取商品名称
export function goodsListBySelect(data) {
  return request({
    url: '/merchant/goods/goodsListBySelect',
    method: 'post',
    data,
  })
}

// 删除评价
export function delEvaluatedApi(data) {
  return request({
    url: '/merchant/orderGoods/delEvaluated',
    method: 'post',
    data,
  })
}
//置顶/取消置顶-
export function setToggleTop(data) {
  return request({
    url: '/merchant/orderGoods/toggleTop',
    method: 'post',
    data,
  })
}

//回复评价-
export function setReplyApi(data) {
  return request({
    url: '/merchant/orderGoods/reply',
    method: 'post',
    data,
  })
}

//通过/批量通过
export function setApproveApi(data) {
  return request({
    url: '/merchant/orderGoods/approve',
    method: 'post',
    data,
  })
}
//驳回/批量驳回
export function setdisapproveApi(data) {
  return request({
    url: '/merchant/orderGoods/disapprove',
    method: 'post',
    data,
  })
}
