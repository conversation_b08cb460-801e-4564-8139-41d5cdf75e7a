import request from '@/utils/request'



/**
 * 搜索条件
 * @param data
 * @returns {*}
 */
export function filterNotices(data = {}) {
  return request({
    url: '/merchant/notice/filterNotices',
    method: 'post',
    data,
  })
}

/**
 * 获取系统通知列表
 * @param {Object} data - 查询参数
 * @param {number} data.page - 页码
 * @param {number} data.limit - 每页数量
 * @param {string} data.title - 通知标题
 * @param {string} data.type - 通知类型
 * @param {string} data.level - 重要等级
 * @param {string} data.is_read - 是否阅读
 * @param {string} data.start - 通知开始时间
 * @param {string} data.end - 通知结束时间
 * @returns {Promise} 返回系统通知列表数据
 */
export function noticeList(data = {}) {
  return request({
    url: '/merchant/notice/noticeList',
    method: 'post',
    data,
  })
}

/**
 * 获取通知详情
 * @param {Object} data - 查询参数
 * @param {number} data.notice_id - 通知唯一标识
 * @returns {Promise} 返回通知详情数据
 */
export function noticeDetail(data = {}) {
  return request({
    url: '/merchant/notice/noticeDetail',
    method: 'post',
    data,
  })
}

/**
 * 标记通知为已读
 * @param {Object} data - 参数
 * @param {number} data.notice_id - 通知唯一标识
 * @returns {Promise} 返回操作结果
 */
export function markNoticeRead(data = {}) {
  return request({
    url: '/merchant/notice/markNoticeRead',
    method: 'post',
    data,
  })
}

/**
 * 获取通知统计数量
 * @param {Object} data - 参数（空对象）
 * @returns {Promise} 返回统计数据
 */
export function countNotices(data = {}) {
  return request({
    url: '/merchant/notice/countNotices',
    method: 'post',
    data,
  })
}

/**
 * 保存待办项目配置
 * @param {Object} data - 参数
 * @param {Array} data.setting - 配置列表
 * @param {number} data.setting[].todo_id - 待办事项ID
 * @param {string} data.setting[].is_audio - 语音是否开启：Y 是 N否
 * @param {string} data.setting[].is_backend - 商家后台提醒：Y 是 N否
 * @param {string} data.setting[].is_wechat - 公众号提醒：Y 是 N否
 * @param {string} data.setting[].is_sms - 短信提醒：Y 是 N否
 * @returns {Promise} 返回操作结果
 */
export function todoSetting(data = {}) {
  return request({
    url: '/merchant/notice/todoSetting',
    method: 'post',
    data,
  })
}

/**
 * 可配置的待办项目列表
 */
export function todoList(data = {}) {
  return request({
    url: '/merchant/notice/todoList',
    method: 'post',
    data,
  })
}


/**
 * 待办和消息通知统计
 * @param {Object} data - 参数（空对象）
 * @returns {Promise} 返回统计数据
 * @returns {string} data.remind_count - 待办数量+未读消息（铃铛数据）
 * @returns {string} data.unread_count - 未读消息数量
 * @returns {number} data.todo_count - 待办数量
 */
export function remind(data = {}) {
  return request({
    url: '/merchant/notice/remind',
    method: 'post',
    data,
  })
}
/**
 * 清空待办
 * @param {Object} data - 参数（空对象）
 * @returns {Promise} 返回操作结果
 */
export function clearTodo(data = {}) {
  return request({
    url: '/merchant/notice/clearTodo',
    method: 'post',
    data,
  })
}
/**
 * 待办消息列表
 * @param {Object} data - 参数（空对象）
 * @returns {Promise} 返回待办消息列表
 * @returns {Array} data - 待办消息数组
 * @returns {number} data[].system_todo_id - 待办项目类型：1订单待发货、2售后待审核、3售后审核即将超时、4售后待商家收货、5商品库存预警、6商品评价、7商家入驻申请、8卡券超期下架
 * @returns {number} data[].total_num - 数量
 * @returns {string} data[].msg - 描述信息
 */
export function listTodoType(data = {}) {
  return request({
    url: '/merchant/notice/listTodoType',
    method: 'post',
    data,
  })
}

/**
 * 获取最新喇叭通知
 * @returns {Promise} 返回最新通知数据
 * @returns {number} data.notice_id - 通知唯一标识
 * @returns {string} data.title - 通知标题
 * @returns {string} data.content - 通知内容
 * @returns {string} data.created_at - 通知时间
 */
export function getLatestNotice(data = {}) {
  return request({
    url: '/merchant/notice/getLatestNotice',
    method: 'post',
    data,
  })
}

/**
 * 获取最新待办通知
 * @returns {Promise} 返回最新待办数据
 * @returns {string} data.title - 待办事项
 * @returns {number} data.system_todo_id - 待办项目类型
 * @returns {string} data.sub_order_no - 子订单编号
 * @returns {string} data.refund_order_no - 售后单号
 * @returns {string} data.store_uid - 入驻商家唯一标识
 * @returns {number} data.goods_id - 商品id
 * @returns {number} data.shop_goods_id - 店铺商品id
 * @returns {number} data.comment_id - 评论ID
 * @returns {string} data.message - 待办事项信息
 * @returns {string} data.audio - 语音信息
 * @returns {string} data.created_at - 时间
 */
export function getLatestTodo() {
  return request({
    url: '/merchant/notice/getLatestTodo',
    method: 'post',
    data: {}
  })
}
