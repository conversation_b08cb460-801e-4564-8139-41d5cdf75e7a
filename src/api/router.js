/*
 * @Author: shangfei <EMAIL>
 * @Date: 2025-02-18 14:31:04
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-03-21 14:42:25
 * @FilePath: /qst-merchant-admin-2.0/src/api/router.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

export function getRouterList(data = {}) {
  return request({
    url: '/roles/index',
    method: 'post',
    data,
  })
  return new Promise((resolve, reject) => {
    resolve({
      code: 200, msg: 'success', data: [

        {
          path: '/',
          name: 'Home',
          component: 'Layout',
          redirect: '/index',
          meta: { title: '首页', icon: 'el-icon-house' },
          children: [
            {
              path: 'index',
              name: 'Index',
              component: '@/views/index/index',
              meta: {
                title: '首页',
                icon: 'el-icon-house',
                affix: false,
                permissionsBtn: ['admin', 'editor'],
              },
            },
          ],
        },
        {
          path: '',
          component: '',
          redirect: '/enterpriseMerchant/merchantManagement',
          name: 'Merchant',
          meta: { title: '企业管理' },
          children: [
            {
              path: '/enterpriseMerchant',
              component: 'Layout',
              redirect: '/merchantManagement',
              name: 'enterpriseMerchant',
              affix: true,
              meta: {
                title: '入驻商家管理',
                icon: 'el-icon-collection',
              },
              children: [
                {
                  path: 'merchantManagement',
                  name: 'MerchantManagement',
                  affix: true,
                  component: '@/views/enterpriseManagement/merchantManagement/index',
                  meta: { title: '入驻商家管理' },
                },
                {
                  path: 'merchantDetails',
                  name: 'merchantDetails',
                  hidden: true,
                  component: '@/views/enterpriseManagement/merchantManagement/merchantDetails',
                  meta: { title: '商家详情' },
                },
              ]
            },
            {
              path: '/enterpriseStaff',
              component: 'Layout',
              redirect: 'noRedirect',
              name: 'enterpriseStaff',
              affix: true,
              meta: {
                title: '员工账号管理',
                icon: 'el-icon-collection',
              },
              children: [
                {
                  path: 'staffManagement',
                  name: 'StaffManagement',
                  component: '@/views/enterpriseManagement/staffManagement/index',
                  meta: { title: '员工账号管理' },
                },
              ]
            },
            {
              path: '/informationManagement',
              component: 'Layout',
              redirect: 'noRedirect',
              name: 'informationManagement',
              affix: true,
              meta: {
                title: '企业信息管理',
                icon: 'el-icon-collection',
              },
              children: [
                {
                  path: 'informationManagement',
                  name: 'InformationManagement',
                  component: '@/views/enterpriseManagement/informationManagement/index',
                  meta: { title: '企业信息管理' },
                }
              ]
            },

            {
              path: '/roleManagement',
              component: 'Layout',
              redirect: 'noRedirect',
              name: 'roleManagement',
              meta: { title: '角色管理', icon: 'el-icon-key' },
              children: [
                {
                  path: 'role',
                  name: 'Role',
                  component: '@/views/enterpriseManagement/role/index',
                  meta: { title: '角色管理', icon: 'el-icon-key' },
                },
                {
                  path: 'roleDetail',
                  name: 'RoleDetail',
                  hidden: true,
                  component: '@/views/enterpriseManagement/role/roleDetail',
                  meta: { title: '角色详情' },
                },
              ]
            },
          ]
        },
        {
          path: '',
          component: '',
          redirect: '/miniProgramManagement/index',
          name: 'MiniProgram',
          meta: { title: '小程序管理' },
          children: [

            {
              path: '/miniProgramManagement',
              component: 'Layout',
              redirect: 'noRedirect',
              name: 'miniProgramManagement',
              meta: { title: '小程序管理', icon: 'el-icon-key' },
              children: [
                {
                  path: 'index',
                  name: 'minManagement',
                  component: '@/views/miniProgramManagement/index',
                  meta: { title: '小程序管理', icon: 'el-icon-key' },
                },
              ]
            },
          ]
        },
        {
          path: '',
          component: '',
          redirect: '/shop/message/system',
          name: 'Message',
          meta: { title: '消息管理' },
          children: [
            {
              path: '/shop',
              component: 'Layout',
              redirect: 'noRedirect',
              name: 'Shop',
              meta: { title: '消息管理', icon: 'el-icon-message' },
              children: [
                {
                  path: 'message/system',
                  name: 'SystemNotification',
                  component: '@/views/shop/message/system',
                  meta: { title: '系统通知' },
                },
                {
                  path: 'message/toBeDone',
                  name: 'ToBeDone',
                  component: '@/views/shop/message/toBeDone',
                  meta: { title: '待办配置' },
                }
              ]
            }
          ]
        },
        {
          path: '/personal',
          component: 'Layout',
          redirect: 'noRedirect',
          name: 'Personal',
          hidden: true,
          children: [
            {
              path: 'personalCenter',
              name: 'personalCenter',
              component: '@/views/personalCenter/index',
              meta: { title: '个人中心', icon: 'el-icon-shopping-cart-full' },
            }
          ]
        }
      ]
    })
  })

}
