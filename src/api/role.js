/*
 * @Author: shangfei <EMAIL>
 * @Date: 2025-03-18 16:59:58
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-03-22 09:43:59
 * @FilePath: /qst-merchant-admin-2.0/src/api/permissionManagement.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import request from '@/utils/request'

// 角色列表
export function rolesListApi(data = {}) {
  return request({
    url: '/roles/list',
    method: 'post',
    data,
  })
}

// 新建角色下拉列表
export function rolesSelectListByCreateApi(data = {}) {
  return request({
    url: '/roles/selectListByCreate',
    method: 'post',
    data,
  })
}

// 获取主页菜单列表
export function rolesIndexApi(data = {}) {
  return request({
    url: '/roles/index',
    method: 'post',
    data,
  })
}

// 新建角色返回全部菜单列表
export function rolesCreateGetRouleApi(data = {}) {
  return request({
    url: '/roles/createGetRoule',
    method: 'post',
    data,
  })
}

// 新建角色
export function rolesCreateApi(data = {}) {
  return request({
    url: '/roles/create',
    method: 'post',
    data,
  })
}


// 启用、停用角色
export function rolesDeactivateApi(data = {}) {
  return request({
    url: '/roles/deactivate',
    method: 'post',
    data,
  })
}

// 角色列表-下拉列表
export function rolesSelectListApi(data = {}) {
  return request({
    url: '/roles/selectList',
    method: 'post',
    data,
  })
}

// 删除角色
export function rolesDeleteApi(data = {}) {
  return request({
    url: '/roles/delete',
    method: 'post',
    data,
  })
}

// 角色详情
export function rolesDetailApi(data = {}) {
  return request({
    url: '/roles/detail',
    method: 'post',
    data,
  })
}

// 修改角色
export function rolesUpdateApi(data = {}) {
  return request({
    url: '/roles/update',
    method: 'post',
    data,
  })
}
