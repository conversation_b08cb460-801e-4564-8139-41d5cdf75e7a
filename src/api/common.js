/*
 * @Author: shang<PERSON>i <EMAIL>
 * @Date: 2025-03-21 14:20:39
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-16 08:57:05
 * @FilePath: \qst-merchant-admin-2.0\src\api\common.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'
import config from '../../config.js'

// ocr 识别
export function ocrUpdata(data = {}) {
  return request({
    url: '/common/testOcrData',
    method: 'post',
    data,
  })
}

// 生成小程序二维码
export function qrcodeApi(data = {}) {
  return request({
    url: '/enterShop/getUnlimitedQrcode',
    method: 'post',
    data: {
      ...data,
      envVersion: config.envVersion,
      check_path: config.check_path,
      // develop	开发版
      // trial	体验版
      // release 正式版
    },
  })
}

// 获取分类列表
export function getCateListApi(data = {}) {
  return request({
    url: '/common/getCateList',
    method: 'post',
    data,
  })
}

// 获取短信验证码接口
export function sendCodeApi(data = {}) {
  return request({
    url: '/common/sendCode',
    method: 'post',
    data,
  })
}

// 店铺管理-店铺列表
export function shopListApi(data = {}) {
  return request({
    url: '/merchant/shop/tabShopList',
    method: 'post',
    data,
  })
}

// 店铺权限
export function checkAccountShop(data = {}) {
  return request({
    url: '/merchant/shop/checkAccountShop',
    method: 'post',
    data,
  })
}

// 物流公司列表
export function expressCompanyListApi(data = {}) {
  return request({
    url: 'common/expressCompany',
    method: 'post',
    data,
  })
}

// 根据物流公司id获取需要设置的参数
export function expressCompanyOptionApi(data = {}) {
  return request({
    url: 'common/expressCompanyOption',
    method: 'post',
    data,
  })
}

// 上传文件
export function uploadFile(data) {
  return request({
    url: '/common/uploadFile',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}


// 获取协议内容
export function agreementApi(data = {}) {
  return request({
    url: '/common/agreement',
    method: 'post',
    data,
  })
}