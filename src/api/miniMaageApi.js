/*
 * @Author: liq<PERSON> liqian@123
 * @Date: 2025-04-16 09:27:54
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-06-17 15:33:43
 * @FilePath: \qst-merchant-admin-2.0\src\api\miniMaageApi.js
 * @Description:
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
 */
import request from '@/utils/request'
/**
 * 创建商户程序
 *
 * @param data 创建商户程序所需的参数
 * @returns 返回创建商户程序的结果
 */
export function create(data) {
  // 发起请求
  return request({
    // 请求的URL地址
    url: '/merchant/program/create',
    // 请求的方法
    method: 'post',
    // 请求的数据
    data,
  })
}

/**
 * 小程序快捷编辑
 *
 * @param data 创建商户程序所需的参数
 * @returns 返回创建商户程序的结果
 */
export function editProgramApi(data) {
  // 发起请求
  return request({
    // 请求的URL地址
    url: '/merchant/program/edit',
    // 请求的方法
    method: 'post',
    // 请求的数据
    data,
  })
}

/**
 * 调用OCR接口处理数据
 *
 * @param data 需要处理的数据
 * @returns 调用请求返回的结果
 */
export function ocrProcess(data) {
  return request({
    url: '/merchant/program/ocr',
    method: 'post',
    data,
  })
}
/**
 * 保存草稿
 *
 * @param {Object} data - 要保存的数据
 * @returns {Promise} - 返回一个Promise对象，该对象在解析后返回一个包含响应数据的对象
 */
export function saveDraft(data) {
  return request({
    url: '/merchant/program/drafts',
    method: 'post',
    data,
  })
}
/**
 * 获取节目详情
 *
 * @param data 请求数据
 * @returns 返回请求结果
 */
export function getProgramDetail(data) {
  return request({
    url: '/merchant/program/detail',
    method: 'post',
    data,
  })
}

/**
 * 获取小程序类目
 *
 * @param data 请求数据
 * @returns 返回请求结果
 */
export function getWechatCategoryApi(data) {
  return request({
    url: '/merchant/program/getWechatCategory',
    method: 'post',
    data,
  })
}

// 商户号管理列表
export function managePaymentChannels(data) {
  return request({
    url: '/merchant/onboarding/managePaymentChannels',
    method: 'post',
    data,
  })
}
// 通联开通企业类型选择
export function allinPayCompanyTypeSelect(data) {
  return request({
    url: '/merchant/onboarding/allinPayCompanyTypeSelect',
    method: 'post',
    data,
  })
}
// 通联开通提交审核（含保存草稿）
export function createAllinPayCompany(data) {
  return request({
    url: '/merchant/onboarding/createAllinPayCompany',
    method: 'post',
    data,
  })
}

// 通联开通查看详情（含草稿详情）
export function allinPayCompanyDetail(data) {
  return request({
    url: '/merchant/onboarding/allinPayCompanyDetail',
    method: 'post',
    data,
  })
}

// 通联商户号注册模板文件地址
export function templateFile(data) {
  return request({
    url: '/merchant/onboarding/templateFile',
    method: 'post',
    data,
  })
}

// 检查是否满足开通支付渠道的条件
export function checkOpenPaymentChannel(data) {
  return request({
    url: '/merchant/onboarding/checkOpenPaymentChannel',
    method: 'post',
    data,
  })
}

// 获取小程序服务内容展示
export function getWechatIcpServiceContentTypes(data) {
  return request({
    url: '/merchant/program/getWechatIcpServiceContentTypes',
    method: 'post',
    data,
  })
}

// 获取微信地址
export function getRegionTree(data) {
  return request({
    url: '/merchant/program/getRegionTree',
    method: 'post',
    data,
  })
}



// 重新发起小程序快捷注册认证
export function againFastRegisterWeapp(data) {
  return request({
    url: '/merchant/program/againFastRegisterWeapp',
    method: 'post',
    data,
  })
}

// 预授权回调上报
export function componentPreAuthorizationApi(data) {
  return request({
    url: '/merchant/program/componentPreAuthorization',
    method: 'post',
    data,
  })
}



// 获取授权码
export function getPreAuthorizationUrlApi(data) {
  return request({
    url: '/merchant/program/getPreAuthorizationUrl',
    method: 'post',
    data,
  })
}

// 获取小程序基本信息
export function getMerchantWxDetailApi(data) {
  return request({
    url: '/merchant/program/getMerchantWxDetail',
    method: 'post',
    data,
  })
}


// 获取小程序基本信息
export function createIcpVerifyTaskApi(data) {
  return request({
    url: '/merchant/program/createIcpVerifyTask',
    method: 'post',
    data,
  })
}