import request from '@/utils/request'
// 获取绑定银行卡列表
export function getBankCardList(data) {
  return request({
    url: '/merchant/allinpayAuth/getBankCardList',
    method: 'post',
    data,
  })
}

// 申请绑定个人或法人银行卡并获取验证码
export function applyBindPersonalBankCard(data) {
  return request({
    url: '/merchant/allinpayAuth/applyBindPersonalBankCard',
    method: 'post',
    data,
  })
}

// 确认绑定银行卡
export function bindBankCard(data) {
  return request({
    url: '/merchant/allinpayAuth/bindBankCard',
    method: 'post',
    data,
  })
}

// 绑定对公户
export function bindCompanyBankCard(data) {
  return request({
    url: '/merchant/allinpayAuth/bindCompanyBankCard',
    method: 'post',
    data,
  })
}

// 解绑银行卡
export function unbindBankCard(data) {
  return request({
    url: '/merchant/allinpayAuth/unbindBankCard',
    method: 'post',
    data,
  })
}

// 设置银行卡默认状态
export function defaultBankCard(data) {
  return request({
    url: '/merchant/allinpayAuth/defaultBankCard',
    method: 'post',
    data,
  })
}

// 获取真实银行卡卡号
export function getRealBankCardNo(data) {
  return request({
    url: '/merchant/allinpayAuth/getRealBankCardNo',
    method: 'post',
    data,
  })
}

// 获取真实银行预留手机号
export function getRealBankPhone(data) {
  return request({
    url: '/merchant/allinpayAuth/getRealBankPhone',
    method: 'post',
    data,
  })
}
