/*
 * @Author: liqian liqian@123
 * @Date: 2025-04-21 17:11:47
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-03 09:32:09
 * @FilePath: \qst-merchant-admin-2.0\src\api\finance\fund.js
 * @Description:
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
 */
import request from '@/utils/request'
// 根据公司主体uid，获取当前主体的通联认证情况、进度、状态等
export function getCompanyAuthInfo(data) {
  return request({
    url: '/merchant/allinpayAuth/getCompanyAuthInfo',
    method: 'post',
    data,
  })
}
// 通联企业信息补充
export function getimproveCompanyInfo(data) {
  return request({
    url: '/merchant/allinpayAuth/improveCompanyInfo',
    method: 'post',
    data,
  })
}
// 根据keyword查询公户（公司开户银行）列表
export function getCompanyBankList(data) {
  return request({
    url: '/merchant/allinpayAuth/getCompanyBankList',
    method: 'post',
    data,
  })
}
// 根据keyword查询开户行支行列表
export function getBankBranchList(data) {
  return request({
    url: '/merchant/allinpayAuth/getBankBranchList',
    method: 'post',
    data,
  })
}

// 发送验证码（通联绑定）
export function sendVerificationCode(data) {
  return request({
    url: '/merchant/allinpayAuth/sendVerificationCode',
    method: 'post',
    data,
  })
}

// 企业认证绑定手机号
export function bindPhoneCompany(data) {
  return request({
    url: '/merchant/allinpayAuth/bindPhoneCompany',
    method: 'post',
    data,
  })
}

// 个人认证绑定手机号
export function bindPhonePersonal(data) {
  return request({
    url: '/merchant/allinpayAuth/bindPhonePersonal',
    method: 'post',
    data,
  })
}

//根据公司统一信用代码查询企业认证记录
export function getCompanyMember(data) {
  return request({
    url: '/merchant/allinpayAuth/getCompanyMember',
    method: 'post',
    data,
  })
}

//根据身份证号查询个人认证记录
export function getPersonalMember(data) {
  return request({
    url: '/merchant/allinpayAuth/getPersonalMember',
    method: 'post',
    data,
  })
}

//签订商户服务协议
export function signContract(data) {
  return request({
    url: '/merchant/allinpayAuth/signContract',
    method: 'post',
    data,
  })
}

//签订免密支付协议
export function signBalanceProtocol(data) {
  return request({
    url: '/merchant/allinpayAuth/signBalanceProtocol',
    method: 'post',
    data,
  })
}

// 绑定通联会员到公司实体
export function bindAllinpayMemberToCompany(data) {
  return request({
    url: '/merchant/allinpayAuth/bindAllinpayMemberToCompany',
    method: 'post',
    data,
  })
}

// 根据法人信息进行通联个人认证
export function improvePersonalInfoy(data) {
  return request({
    url: '/merchant/allinpayAuth/improvePersonalInfo',
    method: 'post',
    data,
  })
}

// 资金管理
export function fundInfo(data) {
  return request({
    url: '/merchant/account/fundInfo',
    method: 'post',
    data,
  })
}

// 解绑手机号
export function unBindPhone(data) {
  return request({
    url: '/merchant/allinpayAuth/unBindPhone',
    method: 'post',
    data,
  })
}

// 获取完整的身份证号
export function getCompleteIdentityNo(data) {
  return request({
    url: '/merchant/allinpayAuth/getCompleteIdentityNo',
    method: 'post',
    data,
  })
}
