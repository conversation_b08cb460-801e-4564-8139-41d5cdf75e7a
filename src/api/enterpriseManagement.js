/*
 * @Author: shangfei <EMAIL>
 * @Date: 2025-03-12 19:38:46
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-04-07 11:28:51
 * @FilePath: /qst-merchant-admin-2.0/src/api/enterpriseManagement.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

// 入驻商家列表
export function enterShopListApi(data = {}) {
  return request({
    url: '/merchant/enterShop/list',
    method: 'post',
    data,
  })
}

// 入驻商家-下拉选项数据接口
export function enterShopSelectListApi(data = {}) {
  return request({
    url: '/merchant/enterShop/selectList',
    method: 'post',
    data,
  })
}

// 商户新建第一步
export function enterShopCreateFirstApi(data = {}) {
  return request({
    url: '/merchant/enterShop/createFirst',
    method: 'post',
    data,
  })
}


// 商户新建第二步
export function enterShopCreateSecondApi(data = {}) {
  return request({
    url: '/merchant/enterShop/createSecond',
    method: 'post',
    data,
    isNofilter: true
  })
}

// 入驻商户详情
export function enterShopDetailApi(data = {}) {
  return request({
    url: '/merchant/enterShop/detail',
    method: 'post',
    data,
  })
}

// 入驻商户管理-点击查看手机号
export function showPhoneApi(data = {}) {
  return request({
    url: '/merchant/enterShop/showPhone',
    method: 'post',
    data,
  })
}


// 账号管理 - 获取员工账号列表
export function accountListApi(data = {}) {
  return request({
    url: '/merchant/account/list',
    method: 'post',
    data,
  })
}

// 获取账号下商户及小店列表
export function getMerchantListByAdminIdApi(data = {}) {
  return request({
    url: '/merchant/account/getMerchantListByAdminId',
    method: 'post',
    data,
  })
}

// 账号管理 - 获取账号商家下员工角色列表
export function getRoleListByAdminIdApi(data = {}) {
  return request({
    url: '/merchant/account/getRoleListByAdminId',
    method: 'post',
    data,
  })
}

// 账号管理 - 启用/停用员工账号
export function enableAccountApi(data = {}) {
  return request({
    url: '/merchant/account/enable',
    method: 'post',
    data,
  })
}

// 账号管理 - 根据所属商家获取角色列表
export function getRoleListByMerchantIdApi(data = {}) {
  return request({
    url: '/merchant/account/getRoleListByMerchantId',
    method: 'post',
    data,
  })
}

// 账号管理 - 根据所属商家及当前账号获取店铺列表
export function getShopListByMchAdminIdApi(data = {}) {
  return request({
    url: '/merchant/account/getShopListByMchAdminId',
    method: 'post',
    data,
  })
}

// 账号管理 - 分配店铺时获取店铺列表
export function getShopListByIntersectionApi(data = {}) {
  return request({
    url: '/merchant/account/getShopListByIntersection',
    method: 'post',
    data,
  })
}

// 账号管理 - 保存分配店铺
export function assignShopsApi(data = {}) {
  return request({
    url: '/merchant/account/assignShops',
    method: 'post',
    data,
  })
}

// 商户删除
export function deleteApi(data = {}) {
  return request({
    url: '/merchant/account/delete',
    method: 'post',
    data,
  })
}

// 账号管理 - 验证商家+手机号是否存在账号
export function checkExistUserApi(data = {}) {
  return request({
    url: '/merchant/account/checkExistUser',
    method: 'post',
    data,
  })
}
// 账号管理 - 验证登录名是否存在
export function checkUserNameApi(data = {}) {
  return request({
    url: '/merchant/account/checkUserName',
    method: 'post',
    data,
  })
}


// 账号管理 - 新增账号
export function addAccountApi(data = {}) {
  return request({
    url: '/merchant/account/add',
    method: 'post',
    data,
  })
}


// 账号管理 - 员工账号分配角色
export function assignPermissionsApi(data = {}) {
  return request({
    url: '/merchant/account/assignPermissions',
    method: 'post',
    data,
  })
}

// 企业信息管理 - 获取当前企业详情
export function getCompanyInfoApi(data = {}) {
  return request({
    url: '/merchant/account/getCompanyInfo',
    method: 'post',
    data,
  })
}



// 企业信息管理 - 修改当前企业详情
export function saveCompanyInfoApi(data = {}) {
  return request({
    url: '/merchant/account/saveCompanyInfo',
    method: 'post',
    data,
  })
}

// 审核入驻商户（通过、拒绝操作）
export function checkEnterShopApi(data = {}) {
  return request({
    url: '/merchant/enterShop/check',
    method: 'post',
    data,
  })
}




// 关闭入驻商户
export function closeShopApi(data = {}) {
  return request({
    url: '/merchant/enterShop/close',
    method: 'post',
    data,
  })
}

// 重新提交入驻商户
export function resubmitShopApi(data = {}) {
  return request({
    url: '/merchant/enterShop/resubmit',
    method: 'post',
    data,
  })
}


// 入驻检测
export function checkBeforeSubmitApi(data = {}) {
  return request({
    url: '/enterShop/checkBeforeSubmit',
    method: 'post',
    data,
  })
}
