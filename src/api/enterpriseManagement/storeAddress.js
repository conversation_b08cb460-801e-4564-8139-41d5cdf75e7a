import request from '@/utils/request'

// 地址列表
export function getaddressList(data) {
  return request({
    url: '/merchant/address/list',
    method: 'post',
    data,
  })
}
//切换状态
export function changeupdateStatus(data) {
  return request({
    url: '/merchant/address/forbidden',
    method: 'post',
    data,
  })
}
//删除地址
export function deleteAddressList(data) {
  return request({
    url: '/merchant/address/delete',
    method: 'post',
    data,
  })
}
//保存地址
export function saveAddressList(data) {
  return request({
    url: '/merchant/address/save',
    method: 'post',
    data,
  })
}
// 地址列表下拉选择可用店铺列表
export function getselectShopList(data) {
  return request({
    url: '/merchant/address/selectShopList',
    method: 'post',
    data,
  })
}

// 地址列表下拉选项
export function getselectList(data) {
  return request({
    url: '/merchant/address/select',
    method: 'post',
    data,
  })
}
