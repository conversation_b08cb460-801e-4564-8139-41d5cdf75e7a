import request from '@/utils/request'



// 店铺列表
export function getshopList(data) {
  return request({
    url: '/merchant/shop/list',
    method: 'post',
    data,
  })
}

// 店铺模版状态
export function enterShopSelectListApi(data) {
  return request({
    url: '/merchant/shop/indexSelect',
    method: 'post',
    data,
  })
}

// 编辑类型
export function enterShopEditApi(data) {
  return request({
    url: '/merchant/enterShop/edit',
    method: 'post',
    data,
  })
}


// 新建店铺确认
export function createShopValidate(data) {
  return request({
    url: '/merchant/shop/createShopValidate',
    method: 'post',
    data,
  })
}

// 新建店铺
export function createshopList(data) {
  return request({
    url: '/merchant/shop/create',
    method: 'post',
    data,
  })
}

// 修改店铺
export function updateshopList(data) {
  return request({
    url: '/merchant/shop/update',
    method: 'post',
    data,
  })
}

// 删除店铺
export function deleteshopList(data) {
  return request({
    url: '/merchant/shop/delete',
    method: 'post',
    data,
  })
}

// 详情店铺
export function detailshopList(data) {
  return request({
    url: '/merchant/shop/detail',
    method: 'post',
    data,
  })
}
// 修改店铺模版状态
export function changeupdateStatus(data) {
  return request({
    url: '/merchant/shop/updateStatus',
    method: 'post',
    data,
  })
}

// 同步自提点列表
export function syncPickupAddress(data) {
  return request({
    url: '/merchant/shop/syncPickupAddress',
    method: 'post',
    data,
  })
}


// 自提点列表
export function getPickupAddressList(data) {
  return request({
    url: '/merchant/shop/pickupAddressList',
    method: 'post',
    data,
  })
}

// 新建自提点列表
export function getcreatePickupAddress(data) {
  return request({
    url: '/merchant/shop/createPickupAddress',
    method: 'post',
    data,
  })
}

// 修改自提点列表
export function editPickupAddress(data) {
  return request({
    url: '/merchant/shop/editPickupAddress',
    method: 'post',
    data,
  })
}

// 删除自提点列表
export function deletePickupAddress(data) {
  return request({
    url: '/merchant/shop/deletePickupAddress',
    method: 'post',
    data,
  })
}
// 修改默认自提点状态
export function updateDefaultPickupAddress(data) {
  return request({
    url: '/merchant/shop/updateDefaultPickupAddress',
    method: 'post',
    data,
  })
}


// 修改店铺配置
export function updateShopConfig(data) {
  return request({
    url: '/merchant/shop/updateShopConfig',
    method: 'post',
    data,
  })
}



// 自提点列表接口
export function pickupAddressListApi(data) {
  return request({
    url: '/merchant/shop/pickupAddressList',
    method: 'post',
    data,
  })
}


