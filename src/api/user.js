/*
 * @Author: shangfei <EMAIL>
 * @Date: 2025-02-18 14:31:04
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-03-29 16:23:09
 * @FilePath: /qst-merchant-admin-2.0/src/api/user.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'
import { encryptedData } from '@/utils/encrypt'
import { loginRSA, tokenName } from '@/config'

// 账号密码登录
export function loginByPassword(data) {
  console.log(data)
  return request({
    url: '/merchant/loginByPassword',
    method: 'post',
    data,
  })
}

// 验证码
export function captcha(data) {
  return request({
    url: '/merchant/captcha',
    method: 'post',
    data,
  })
}

// 登录
export function loginBySmsAffirm(data) {
  return request({
    url: '/merchant/loginBySmsAffirm',
    method: 'post',
    data,
  })
}

// 点击弹窗选择入驻商家所属租户后登录接口
export function selectLoginApi(data) {
  return request({
    url: '/merchant/selectLogin',
    method: 'post',
    data,
  })
}
// 发送验证码
export function sendCodeApi(data = {}) {
  return request({
    url: '/merchant/sendCodeByLogin',
    method: 'post',
    data,
  })
}

// 短信登录
export function loginBySms(data) {
  return request({
    url: '/merchant/loginBySms',
    method: 'post',
    data,
  })
}



// 获取商户列表
export function merchantListApi(data) {
  return request({
    url: '/user/merchantList',
    method: 'post',
    data,
  })
}



// 刷新token
export function refreshTokenApi(data) {
  return request({
    url: '/merchant/refreshToken',
    method: 'post',
    data,
  })
}




// 获取商户信息
export function merchantInfoApi(data) {
  return request({
    url: '/user/merchantInfo',
    method: 'post',
    data: {
      currentMchUid: localStorage.getItem('currentMchUid')
    },
  })
}


// 验证密码
export function passwordVerifyApi(data) {
  return request({
    url: '/merchant/passwordVerify',
    method: 'post',
    data
  })
}
// 修改密码
export function setPasswordApi(data) {
  return request({
    url: '/merchant/setPassword',
    method: 'post',
    data,
  })
}

// 修改手机号验证码
export function sendCodeByEditPhonePwdApi(data = {}) {
  return request({
    url: '/merchant/sendCodeByEditPhonePwd',
    method: 'post',
    data,
  })
}

// 校验手机号
export function phoneVerifyApi(data) {
  return request({
    url: '/merchant/phoneVerify',
    method: 'post',
    data,
  })
}
// 修改手机号
export function setPhoneApi(data) {
  return request({
    url: '/merchant/setPhone',
    method: 'post',
    data,
  })
}


// 商户切换是否显示
export function switchIsShowApi(data) {
  return request({
    url: '/user/switchIsShow',
    method: 'post',
    data,
  })
}


// 商户取消
export function cancelApi(data) {
  return request({
    url: '/user/cancel',
    method: 'post',
    data,
  })
}

// 个人中心-修改昵称
export function setRealNameApi(data) {
  return request({
    url: '/merchant/setRealName',
    method: 'post',
    data,
  })
}


export async function login(data) {
  if (loginRSA) {
    data = await encryptedData(data)
  }
  return request({
    url: '/merchant/loginBySms',
    method: 'post',
    data,
  })
}



export function getUserInfo(accessToken) {
  return new Promise((resolve, reject) => {
    resolve(
      {
        roles: ['admin'],
        name: 'admin',
        username: 'admin',
        avatar: '',
        introduction: '',
        email: '',
        permissions: ['admin']
      },
    )
  })

  return request({
    url: '/userInfo',
    method: 'post',
    data: {
      [tokenName]: accessToken,
    },
  })
}
// 退出方法
export function logout() {
  return request({
    url: '/merchant/logout',
    method: 'post',
  })
}

export function register() {
  return request({
    url: '/register',
    method: 'post',
  })
}

