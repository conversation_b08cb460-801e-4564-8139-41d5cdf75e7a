/*
 * @Author: shangfei <EMAIL>
 * @Date: 2025-03-10 13:59:41
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-24 15:19:41
 * @FilePath: /qst-merchant-admin-2.0/src/utils/uploadImage.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'
import { Message } from 'element-ui'

//这里是文件转base64
let getBase64 = (file) => {
  return new Promise(function (resolve, reject) {
    const reader = new FileReader()
    let imgResult = ''
    reader.readAsDataURL(file)
    reader.onload = function () {
      imgResult = reader.result
    }
    reader.onerror = function (error) {
      reject(error)
    }
    reader.onloadend = function () {
      resolve(imgResult)
    }
  })
}
let beforeAvatarUpload = (file) => {
  // console.log(file)
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    Message.error('上传图片大小不能超过 2MB!')
  }
  return isLt2M
}

export let upLoadImg = (file) => {
  return new Promise((resolve, reject) => {
    if (beforeAvatarUpload(file)) {
      getBase64(file).then((res) => {
        let data = new FormData()
        data.append('name', file.name)
        data.append('extension', file.type.split('/')[2] || file.type.split('/')[1])
        data.append('image', res)
        // for (let [a, b] of data.entries()) {
        //   console.log(a, ":", b);
        // }
        resolve(
          request({
            url: '/common/upload',
            method: 'post',
            data: data,
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          })
        )
      })
    } else {
      reject(file)
    }
  })
}

let getVideoDuration = (file) => {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')
    video.preload = 'metadata'
    video.onloadedmetadata = function () {
      window.URL.revokeObjectURL(video.src) // 释放内存
      const duration = video.duration // 获取时长（秒）
      console.log('视频时长:', duration)
      // 将duration随文件一起提交到服务器
      resolve(duration)
    }
    video.src = URL.createObjectURL(file)
  })
}

// 上传视频
export let upLoadVideo = (file) => {
  console.log(file)
  return new Promise(async (resolve, reject) => {
    const isLt2M = file.size / 1024 / 1024 < 50
    if (!isLt2M) {
      Message.error('上传视频大小不能超过 50MB!')
      reject(false)
      return
    }

    let duration = await getVideoDuration(file)
    console.log(duration)
    if (duration > 60) {
      Message.error('上传视频时长不能超过 60秒!')
      reject(false)
      return
    }

    let data = new FormData()
    // data.append('name', file.name)
    data.append('video', file)
    resolve(
      request({
        url: '/merchant/goods/uploadVideo',
        method: 'post',
        data: data,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
    )
  })
}
