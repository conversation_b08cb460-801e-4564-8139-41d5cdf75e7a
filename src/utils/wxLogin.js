/*
 * @Author: shangfei <EMAIL>
 * @Date: 2025-02-28 09:51:39
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-02-28 09:51:46
 * @FilePath: /new_qst_manage_web/src/utils/wxLogin.js
 * @Description: pc 微信登陆  
 * https://developers.weixin.qq.com/doc/oplatform/Website_App/WeChat_Login/Wechat_Login.html
 */
!function (e, t) { e.WxLogin = function (e) { var r = "default"; !0 === e.self_redirect ? r = "true" : !1 === e.self_redirect && (r = "false"); var i = t.createElement("iframe"), n = "https://open.weixin.qq.com/connect/qrconnect?appid=" + e.appid + "&scope=" + e.scope + "&redirect_uri=" + e.redirect_uri + "&state=" + e.state + "&login_type=jssdk&self_redirect=" + r + "&styletype=" + (e.styletype || "") + "&sizetype=" + (e.sizetype || "") + "&bgcolor=" + (e.bgcolor || "") + "&rst=" + (e.rst || ""); n += e.style ? "&style=" + e.style : "", n += e.href ? "&href=" + e.href : "", n += "en" === e.lang ? "&lang=en" : "", n += 1 === e.stylelite ? "&stylelite=1" : "", n += 0 === e.fast_login ? "&fast_login=0" : "", i.src = n, i.frameBorder = "0", i.allowTransparency = "true", i.scrolling = "no", i.width = "300px", i.height = "400px"; var l = t.getElementById(e.id); l.innerHTML = "", l.appendChild(i) } }(window, document);