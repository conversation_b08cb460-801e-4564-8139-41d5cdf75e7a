/*
 * @Author: shangfei <EMAIL>
 * @Date: 2025-02-27 16:19:28
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-03-03 17:34:04
 * @FilePath: /qst-merchant-admin-2.0/src/utils/directive.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue';
import store from '@/store'
Vue.directive('permButton', {
  inserted(el, binding, vnode) {
    const permButton = binding.value;
    const permissionsBtn = store.getters["user/permissionsBtn"]
    const hasPermission = permissionsBtn.includes(permButton);
    if (!hasPermission) {
      el.parentNode && el.parentNode.removeChild(el);
    }
  }
});


Vue.directive('numeric', {
  bind(el) {
    // 处理键盘输入
    el.addEventListener('keydown', (e) => {
      const allowedKeys = ['Backspace', 'Delete', 'Tab', 'ArrowLeft', 'ArrowRight', 'Enter'];
      // 允许功能键和数字
      if (allowedKeys.includes(e.key) || /^\d||.$/.test(e.key)) return;
      // 允许 Ctrl/Cmd + A/C/V/X 等组合键
      if (e.ctrlKey || e.metaKey) return;
      e.preventDefault();
    });

    // 处理粘贴内容
    el.addEventListener('paste', (e) => {
      e.preventDefault();
      const text = (e.clipboardData || window.clipboardData).getData('text');
      const numbers = text.replace(/\D/g, '');
      document.execCommand('insertText', false, numbers);
    });
  }
});