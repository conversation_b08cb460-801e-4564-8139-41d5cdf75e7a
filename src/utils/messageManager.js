/**
 * 全局消息管理器
 * 用于管理系统消息和待办事项的状态
 */

import Vue from 'vue'

class MessageManager {
  constructor() {
    // 创建一个 Vue 实例作为事件总线
    this.eventBus = new Vue()
    
    // 消息数据
    this.messages = [
      {
        id: 1,
        title: '系统维护通知',
        content: '系统将于今晚22:00-24:00进行维护升级，期间可能影响正常使用，请提前做好准备',
        time: '2025-01-15 10:30',
        status: 'unread',
        importance: 'high'
      },
      {
        id: 2,
        title: '促销活动提醒',
        content: '新年促销活动即将开始，请及时更新商品价格和库存信息，活动时间为1月20日-2月20日',
        time: '2025-01-14 09:15',
        status: 'unread',
        importance: 'medium'
      },
      {
        id: 3,
        title: '订单支付异常通知',
        content: '检测到部分订单支付状态异常，请及时处理以免影响用户体验和订单流程',
        time: '2025-01-13 16:30',
        status: 'unread',
        importance: 'medium'
      },
      {
        id: 4,
        title: '新功能上线通知',
        content: '商品管理模块新增批量导入功能，支持Excel格式文件上传，提升操作效率',
        time: '2025-01-13 14:20',
        status: 'unread',
        importance: 'medium'
      },
      {
        id: 5,
        title: '库存预警提醒',
        content: '多个商品库存不足，请及时补货以免影响正常销售，建议设置自动补货提醒',
        time: '2025-01-12 16:45',
        status: 'unread',
        importance: 'high'
      },
      {
        id: 6,
        title: '用户反馈处理通知',
        content: '收到新的用户反馈和建议，请及时查看并回复，提升用户满意度',
        time: '2025-01-12 11:20',
        status: 'unread',
        importance: 'medium'
      },
      {
        id: 7,
        title: '数据备份完成通知',
        content: '系统数据备份已完成，备份文件已保存至安全存储位置，请定期检查备份状态',
        time: '2025-01-11 23:30',
        status: 'read',
        importance: 'low'
      },
      {
        id: 8,
        title: '安全登录提醒',
        content: '检测到异常登录行为，建议立即修改密码并启用双重验证以保障账户安全',
        time: '2025-01-11 15:45',
        status: 'unread',
        importance: 'high'
      }
    ]
    
    // 待办事项数据
    this.todos = [
      {
        id: 1,
        title: '您有3个待发货的订单',
        time: '2025-01-15 09:00'
      },
      {
        id: 2,
        title: '您有3717条评论待回复',
        time: '2025-01-14 15:20'
      },
      {
        id: 3,
        title: '您有1个售后订单待处理',
        time: '2025-01-13 16:30'
      }
    ]
  }

  // 获取所有消息
  getAllMessages() {
    return this.messages
  }

  // 获取未读消息
  getUnreadMessages() {
    return this.messages.filter(msg => msg.status === 'unread')
  }

  // 获取所有待办事项
  getAllTodos() {
    return this.todos
  }

  // 标记消息为已读
  markMessageAsRead(messageId) {
    const message = this.messages.find(msg => msg.id === messageId)
    if (message) {
      message.status = 'read'
      this.eventBus.$emit('message-updated', this.messages)
      this.eventBus.$emit('unread-messages-updated', this.getUnreadMessages())
    }
  }

  // 标记所有消息为已读
  markAllMessagesAsRead() {
    this.messages.forEach(msg => {
      if (msg.status === 'unread') {
        msg.status = 'read'
      }
    })
    this.eventBus.$emit('message-updated', this.messages)
    this.eventBus.$emit('unread-messages-updated', this.getUnreadMessages())
  }

  // 清空所有待办事项
  clearAllTodos() {
    this.todos = []
    this.eventBus.$emit('todos-updated', this.todos)
  }

  // 添加新消息
  addMessage(message) {
    const newMessage = {
      id: Date.now(),
      status: 'unread',
      time: new Date().toLocaleString(),
      importance: 'medium',
      ...message
    }
    this.messages.unshift(newMessage)
    this.eventBus.$emit('message-updated', this.messages)
    this.eventBus.$emit('unread-messages-updated', this.getUnreadMessages())
    this.eventBus.$emit('new-message-added', newMessage)
  }

  // 添加新待办事项
  addTodo(todo) {
    const newTodo = {
      id: Date.now(),
      time: new Date().toLocaleString(),
      ...todo
    }
    this.todos.unshift(newTodo)
    this.eventBus.$emit('todos-updated', this.todos)
    this.eventBus.$emit('new-todo-added', newTodo)
  }

  // 获取未读消息数量
  getUnreadCount() {
    return this.getUnreadMessages().length
  }

  // 获取待办事项数量
  getTodoCount() {
    return this.todos.length
  }

  // 获取总的未读数量
  getTotalUnreadCount() {
    return this.getUnreadCount() + this.getTodoCount()
  }

  // 监听事件
  on(event, callback) {
    this.eventBus.$on(event, callback)
  }

  // 移除事件监听
  off(event, callback) {
    this.eventBus.$off(event, callback)
  }

  // 触发事件
  emit(event, data) {
    this.eventBus.$emit(event, data)
  }
}

// 创建单例实例
const messageManager = new MessageManager()

export default messageManager
