/*
 * @Author: shangfei <EMAIL>
 * @Date: 2025-02-28 10:20:03
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-02-28 10:27:54
 * @FilePath: /new_qst_manage_web/src/utils/regexKey.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

// 手机号正则表达式
export let PhoneReg = /^1[3-9]\d{9}$/;
// 身份证号正则表达式
export let IdcardNoReg = /^\d{6}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X|x)$/
// 邮箱正则表达式
export let EmailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
// 密码正则表达式
export let PasswordReg = /^(?![\d]+$)(?![a-zA-Z]+$)(?![^\da-zA-Z]+$).{6,16}$/;
// 用户名正则表达式
export let UsernameReg = /^[a-zA-Z0-9_-]{4,16}$/;
// 银行卡号正则表达式
export let BankCardNoReg = /^\d{16,20}$/;
// 验证码正则表达式
export let VerificationCodeReg = /^\d{6}$/;
// 数字正则表达式
export let NumberReg = /^-?\d+$/;
// 正整数正则表达式
export let PositiveIntegerReg = /^\d+$/;
// 非负数正则表达式
export let NonNegativeNumberReg = /^\d+(\.\d+)?$/;
// 浮点数正则表达式
export let FloatNumberReg = /^-?\d+(\.\d+)?$/;
// 金额正则表达式
export let MoneyReg = /^-?\d+(\.\d{1,2})?$/;