/*
 * @Author: shangfei <EMAIL>
 * @Date: 2025-03-04 09:30:26
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-03-12 09:50:59
 * @FilePath: /qst-merchant-admin-2.0/src/utils/validations.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// form的手机号校验规则
const validatePhone = (rule, value, callback) => {
  if ('' == value) {
    callback(new Error('手机号不能为空'))
  } else if (!/^1\d{10}$/.test(value)) {
    callback(new Error('请填写正确的手机号'))
  } else {
    callback()
  }
}
export const phoneRule = [
  {
    required: true,
    trigger: 'blur',
    validator: validatePhone,
  }
]

// form的用户名校验规则
export const userNameRule = [
  {
    required: true,
    trigger: 'blur',
    message: '用户名不能为空'
  }
]

// form的密码校验规则
export const passwordRule = [
  {
    required: true,
    trigger: 'blur',
    message: '密码不能为空'
  }
]


// 图形验证码
const imageCode = (rule, value, callback) => {
  if (value.length == 0) {
    callback(new Error('请输入图片验证码'))
  } else if (value.length < 4) {
    callback(new Error('图片验证码不能少于4位'))
  } else {
    callback()
  }
}
export const imageCodeRule = [
  {
    required: true,
    validator: imageCode,
  }
]

// 短信验证码
const phoneCode = (rule, value, callback) => {
  if (value.length == 0) {
    callback(new Error('请输入验证码'))
  } else if (value.length < 6) {
    callback(new Error('验证码不能少于6位'))
  } else {
    callback()
  }
}
export const phoneCodeRule = [
  {
    required: true,
    validator: phoneCode,
  }
]