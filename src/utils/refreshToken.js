/*
 * @Author: shangfei <EMAIL>
 * @Date: 2025-03-11 14:37:06
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-03-21 10:52:08
 * @FilePath: /qst-merchant-admin-2.0/src/utils/refreshToken.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import store from '@/store'

export default class ReachToken {
  time = null
  constructor(token) {
  }
  init() {
    if (this.time) clearTimeout(this.time)
    this.time = setTimeout(() => {
      store.dispatch('user/refreshTokenApi').then(res => {
        if (res.code == 200) {
          this.init()
        } else {
          this.time = null
        }
      })
    }, 3600000)
  }
  clearTimeout() {
    clearTimeout(this.time)
    this.time = null
  }
}