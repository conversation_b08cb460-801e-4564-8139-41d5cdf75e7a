/*
 * @Author: shangfei <EMAIL>
 * @Date: 2025-02-18 14:31:04
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-04-24 15:03:07
 * @FilePath: /qst-merchant-admin-2.0/src/utils/request.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue'
import axios from 'axios'
import {
  contentType,
  debounce,
  invalidCode,
  loginInterception,
  noPermissionCode,
  requestTimeout,
  successCode,
  tokenName,
} from '@/config'

import config from '../../config.js'
import store from '@/store'
import qs from 'qs'
import router from '@/router'
import { isArray } from '@/utils/validate'

let baseURL = config.baseURL
let loadingInstance

/**
 * 
 * @description 处理code异常
 * @param {*} code
 * @param {*} msg
 */
const handleCode = (code, msg, url) => {
  // 对于通知相关接口，如果是"未找到"类型的错误，不显示错误提示
  const notificationApis = [
    '/merchant/notice/getLatestNotice',
    '/merchant/notice/getLatestTodo'
  ]

  const isNotificationApi = notificationApis.some(api => url && url.includes(api))
  const isNotFoundError = msg && (
    msg.includes('未找到') ||
    msg.includes('不存在') ||
    msg.includes('无数据') ||
    msg.includes('没有数据')
  )

  switch (code) {
    case 42004:
    case 41001: // 未登录 登录失效
      store.dispatch('user/resetAccessToken').catch(() => { })
      location.reload()
      break
    case invalidCode:
      Vue.prototype.$baseMessage(msg || `后端接口${code}异常`, 'error')
      store.dispatch('user/resetAccessToken').catch(() => { })
      if (loginInterception) {
        location.reload()
      }
      break
    case noPermissionCode:
      router.push({ path: '/401' }).catch(() => { })
      break
    default:
      // 如果是通知相关接口的"未找到"错误，不显示提示
      if (!(isNotificationApi && isNotFoundError)) {
        Vue.prototype.$baseMessage(msg || `后端接口${code}异常`, 'error')
      }
      break
  }
}

const instance = axios.create({
  baseURL,
  timeout: requestTimeout,
  headers: {
    'Content-Type': contentType,
  },
})

instance.interceptors.request.use(
  (config) => {
    if (config.url.includes('/merchant/refreshToken')) {
      let userInfo = JSON.parse(localStorage.getItem('userInfo'))
      config.headers.Authorization = 'Bearer ' + userInfo.refreshToken
    } else if (store.getters['user/accessToken']) {
      config.headers.Authorization = 'Bearer ' + store.getters['user/accessToken']
    }
    if (config.data && !config.data.currentMchUid) {
      config.data.currentMchUid = localStorage.getItem('currentMchUid') || ''
    }

    if (config.url.includes('/common/upload') || config.url.includes('/merchant/goods/uploadVideo')) {
      config.headers['Content-Type'] = 'multipart/form-data'
    } else {
      //这里会过滤所有为空、0、false的key，如果不需要请穿isNofilter参数
      if (config.data && !config.isNofilter) config.data = Vue.prototype.$baseLodash.pickBy(config.data, Vue.prototype.$baseLodash.identity)
      if (config.data && config.headers['Content-Type'] === 'application/x-www-form-urlencoded;charset=UTF-8')
        config.data = qs.stringify(config.data)
      if (debounce.some((item) => config.url.includes(item))) loadingInstance = Vue.prototype.$baseLoading()
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

instance.interceptors.response.use(
  (response) => {
    if (loadingInstance) loadingInstance.close()

    const { data, config } = response
    const { code, msg } = data
    // 操作正常Code数组
    const codeVerificationArray = isArray(successCode) ? [...successCode] : [...[successCode]]
    // 是否操作正常
    if (codeVerificationArray.includes(code)) {
      return data
    } else {
      handleCode(code, msg, config.url)
      // return Promise.reject(
      //   `vue-admin-beautiful请求异常拦截:${JSON.stringify({
      //     url: config.url,
      //     code,
      //     msg,
      //   })}` || 'Error'
      // )
      return data
    }
  },
  (error) => {
    if (loadingInstance) loadingInstance.close()
    const { response, message } = error
    if (error.response && error.response.data) {
      const { status, data } = response
      handleCode(status, data.msg || message, error.config?.url)
      return Promise.reject(error)
    } else {
      let { message } = error
      if (message === 'Network Error') {
        message = '后端接口连接异常'
      }
      if (message.includes('timeout')) {
        message = '后端接口请求超时'
      }
      if (message.includes('Request failed with status code')) {
        const code = message.substr(message.length - 3)
        message = `后端接口${code}异常`
      }
      Vue.prototype.$baseMessage(message || `后端接口未知异常`, 'error')
      return Promise.reject(error)
    }
  }
)

export default instance
