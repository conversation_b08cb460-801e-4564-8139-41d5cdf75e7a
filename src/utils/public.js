/*
 * @Author: shangfei <EMAIL>
 * @Date: 2025-03-01 09:18:16
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-03-07 16:12:27
 * @FilePath: /new_qst_manage_web/src/utils/public.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

/**
 * @description: 倒计时类
 * @return {*}
 */
export class timeOut {
  timeout = 0;
  time = 0;
  interval = null;
  constructor(num, time = 1000) {
    if (typeof num !== 'number') {
      return new Error('Please enter a number');
    }
    if (typeof time !== 'number') {
      return new Error('Please enter a number');
    }
    this.timeout = num;
    this.time = time;
  }
  start(fn) {
    if (this.interval) {
      clearInterval(this.interval);
    }
    this.interval = setInterval(() => {
      this.timeout--;
      fn(this.convertSeconds(this.timeout))
      if (this.timeout <= 0) {
        clearInterval(this.interval);
      } else {
        console.log(this.timeout);
      }
    }, this.time)
  }

  end() {
    if (this.interval) {
      clearInterval(this.interval);
      return true;
    }
  }

  convertSeconds(seconds) {
    if (seconds <= 0) return {
      days: '00',
      hours: '00',
      minutes: '00',
      seconds: '00',
      totalSeconds: 0,
    };
    const days = Math.floor(seconds / 86400);    // 1天=86400秒
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor(((seconds % 86400) % 3600) / 60);
    const remainingSeconds = ((seconds % 86400) % 3600) % 60;
    return {
      days: days > 10 ? days : '0' + days,
      hours: hours > 10 ? hours : '0' + hours,
      minutes: minutes > 10 ? minutes : '0' + minutes,
      seconds: remainingSeconds > 10 ? remainingSeconds : '0' + remainingSeconds,
      totalSeconds: seconds,
    };
  }
}