// 累加器函数
function accumulator(initialValue = 0) {
  let sum = initialValue;
  
  return function(value = 0) {
    sum += value;
    return sum;
  };
}

// 使用示例
const acc = accumulator();
console.log(acc(1));  // 输出: 1
console.log(acc(2));  // 输出: 3
console.log(acc(3));  // 输出: 6

// 使用初始值
const accWithInitial = accumulator(10);
console.log(accWithInitial(1));  // 输出: 11
console.log(accWithInitial(2));  // 输出: 13
console.log(accWithInitial(3));  // 输出: 16

export default accumulator;