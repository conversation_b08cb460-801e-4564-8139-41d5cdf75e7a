/*
 * @Author: shangfei <EMAIL>
 * @Date: 2025-03-05 13:57:15
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-03-05 16:06:39
 * @FilePath: /qst-merchant-admin-2.0/src/components/export.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import Vue from 'vue'

const requireComponents = require.context('./Public', true, /\.vue$/)

requireComponents.keys().forEach((fileName) => {
  const componentConfig = requireComponents(fileName)

  const componentName = componentConfig.default.name

  Vue.component(componentName, componentConfig.default || componentConfig)
})