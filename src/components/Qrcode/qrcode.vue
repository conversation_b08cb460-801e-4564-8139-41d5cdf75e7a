<template>
  <div>
    <canvas :id="'canvas' + ids" class="yzq">二维码</canvas>
  </div>
</template>  

<script>
import QRCode from 'qrcode'
export default {
  name: 'Qrcode',
  props: {
    url: {
      type: String,
      default: '',
    },
    ids: {},
  },
  mounted() {
    this.createQRCode()
  },
  methods: {
    createQRCode() {
      let canvas = document.getElementById('canvas' + this.ids)
      QRCode.toCanvas(canvas, this.url, { margin: 1, width: 110, height: 110 }, (error) => {
        if (error) console.error(error)
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.yzq {
  margin: 20px;
}
</style>