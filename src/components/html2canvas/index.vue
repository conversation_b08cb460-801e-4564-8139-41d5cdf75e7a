<template>
  <div ref="html2canvas">
    <slot></slot>
  </div>
</template>

<script>
import html2canvas from 'html2canvas'
import request from '@/utils/request'

export default {
  name: 'html2canvas',
  methods: {
    capture() {
      return new Promise((resolve, reject) => {
        html2canvas(this.$refs.html2canvas)
          .then((canvas) => {
            // 将 canvas 元素添加到页面或进行其他处理
            document.body.appendChild(canvas)
            // 如果想将 canvas 保存为图片，可以这样做：
            const img = canvas.toDataURL('image/png')
            console.log(img) // 这里你可以将 img 用于下载或者显示等操作
            console.log(canvas)

            let data = new FormData()
            data.append('name', new Date().getTime())
            data.append('image', img)
            document.body.removeChild(canvas)

            // for (let [a, b] of data.entries()) {
            //   console.log(a, ":", b);
            // }
            resolve(
              request({
                url: '/common/upload',
                method: 'post',
                data: data,
                headers: {
                  'Content-Type': 'multipart/form-data',
                },
              })
            )
          })
          .catch((err) => {
            reject(err)
            console.log(err)
          })
      })
    },
  },
}
</script>