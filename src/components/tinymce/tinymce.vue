<template>
  <div class="tinymce-box">
    <editor id="editor" v-model="myValue" :init="init" :disabled="disabled" @click="onClick"></editor>
  </div>
</template>

<script>
import { upLoadImg } from '@/utils/uploadImage.js'
// 文档 http://tinymce.ax-z.cn/
// 引入组件
import tinymce from 'tinymce/tinymce' // tinymce默认hidden，不引入不显示
import Editor from '@tinymce/tinymce-vue'

// 引入富文本编辑器主题的js和css
import 'tinymce/icons/default/icons'
import 'tinymce/skins/content/default/content.css'
import 'tinymce/themes/silver/theme.min.js'
import 'tinymce/icons/default/icons' // 解决了icons.js 报错Unexpected token '<'

// 编辑器插件plugins
// 更多插件参考：https://www.tiny.cloud/docs/plugins/
import 'tinymce/plugins/image' // 插入上传图片插件
import 'tinymce/plugins/media' // 插入视频插件
import 'tinymce/plugins/table' // 插入表格插件
import 'tinymce/plugins/lists' // 列表插件
import 'tinymce/plugins/wordcount' // 字数统计插件
import 'tinymce/plugins/link'
import 'tinymce/plugins/code'
import 'tinymce/plugins/preview'
import 'tinymce/plugins/fullscreen'
import 'tinymce/plugins/help'
import 'tinymce/plugins/paste'
import '/public/tinymce/plugins/axupimgs/plugin.js'

export default {
  components: {
    Editor,
  },
  name: 'Tinymce',
  props: {
    // 默认的富文本内容
    value: {
      type: String,
      default: '',
    },
    // 基本路径，默认为空根目录，如果你的项目发布后的地址为目录形式，
    // 即abc.com/tinymce，baseUrl需要配置成tinymce，不然发布后资源会找不到
    baseUrl: {
      type: String,
      default: window.location.origin ? window.location.origin : '',
    },
    // 禁用
    disabled: {
      type: Boolean,
      default: false,
    },
    plugins: {
      type: [String, Array],
      default: 'link lists image code table wordcount media preview fullscreen help paste axupimgs',
    },
    toolbar: {
      type: [String, Array],
      default:
        'bold italic underline strikethrough | fontsizeselect | formatselect | forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent blockquote | undo redo | link unlink code lists table image media | removeformat | fullscreen preview | paste | axupimgs',
    },
  },
  data() {
    let _this = this
    return {
      isUpdata: false,
      init: {
        language_url: `${this.baseUrl}/tinymce/langs/zh-Hans.js`,
        language: 'zh-Hans',
        skin_url: `${this.baseUrl}/tinymce/skins/ui/oxide`,
        // skin_url: 'tinymce/skins/ui/oxide-dark', // 暗色系
        convert_urls: false,
        height: 800,
        // content_css（为编辑区指定css文件）,加上就不显示字数统计了
        // content_css: `${this.baseUrl}tinymce/skins/content/default/content.css`,
        // （指定需加载的插件）
        plugins: this.plugins,
        toolbar: this.toolbar, // （自定义工具栏）
        toolbar_mode: 'wrap',

        statusbar: true, // 底部的状态栏
        // menubar: 'file edit insert view format table tools help', // （1级菜单）最上方的菜单
        menubar: '',
        branding: false, // （隐藏右下角技术支持）水印“Powered by TinyMCE”
        // 此处为图片上传处理函数，这个直接用了base64的图片形式上传图片，
        // 如需ajax上传可参考https://www.tiny.cloud/docs/configure/file-image-upload/#images_upload_handler
        paste_data_images: true,

        image_resize: false,
        resize: false,
        image_dimensions: false,
        image_advtab: false,
        content_style:
          'img { width: 100%; height: auto; max-width: 100% !important; object-fit: cover; } .mce-content-body div.mce-resizehandle{ display: none;}p{margin:0}',

        images_upload_handler: (blobInfo, success, failure) => {
          // const img = 'data:image/jpeg;base64,' + blobInfo.base64()
          this.updataImage(blobInfo.blob())
            .then((res) => {
              success(res)
            })
            .catch((err) => {
              console.log(err)
              // failure(err)
            })
        },

        urlconverter_callback: function (url, node, on_save, name) {
          console.log('图片链接', url, node, on_save, name)
          // if( _this.isUpdata){return}
          // _this.isUpdata = true
          // _this.blobUpdataImage(url).then(res=>{
          //   return res ;
          // })
          return url
        },
        images_upload_base_path(res) {
          console.log(res, '=====')
        },
      },
      myValue: this.value,
    }
  },
  mounted() {
    console.log(window)
    tinymce.init({
      image_resize: false,
      resize: false,
      image_dimensions: false,
      image_advtab: false,
    })
  },
  methods: {
    updataImage(file) {
      return new Promise((resolve) => {
        upLoadImg(file).then((res) => {
          console.log(res, '图片上传成功')
          if (res.code == 200) {
            resolve(res.data.url)
          }
        })
      })
    },
    blobUpdataImage(res) {
      let _this = this
      let blob = new Blob([res], { type: 'text/plain;charset=utf-8' })
      console.log('Blob对象', blob)
      let file = new File([blob], 'test.png', { type: blob.type })
      console.log('File对象', file)
      return new Promise((resolve) => {
        upLoadImg(file)
          .then((res) => {
            if (res.code == 200) {
              resolve(res.data.url)
            }
            _this.isUpdata = false
          })
          .catch((err) => {
            _this.isUpdata = false
          })
      })
    },
    // 添加相关的事件，可用的事件参照文档=> https://github.com/tinymce/tinymce-vue => All available events
    // 需要什么事件可以自己增加
    onClick(e) {
      this.$emit('onClick', e, tinymce)
    },
    // 可以添加一些自己的自定义事件，如清空内容
    clear() {
      this.myValue = ''
    },
  },
  watch: {
    value(newValue) {
      this.myValue = newValue
    },
    myValue(newValue) {
      this.$emit('input', newValue)
    },
  },
}
</script>



