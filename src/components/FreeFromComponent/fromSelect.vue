<!--
 * @Author: liqian liqian@123
 * @Date: 2025-04-30 09:56:31
 * @LastEditors: liqian liqian@123
 * @LastEditTime: 2025-04-30 09:56:45
 * @FilePath: \qst-merchant-admin-2.0\src\components\FreeFromComponent\fromSelect.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-select v-bind="$attrs" v-on="$listeners" v-model="modelValue">
    <el-option v-for="(option, index) in options" :key="index" :label="option.label" :value="option.value"></el-option>
  </el-select>
</template>

<script>
  export default {
    props: {
      value: {
        required: true,
      },
      options: {
        type: Array,
        default: () => [],
      },
    },
    computed: {
      modelValue: {
        get() {
          return this.value
        },
        set(val) {
          this.$emit('input', val)
        },
      },
    },
  }
</script>
