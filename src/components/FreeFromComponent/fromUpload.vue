<!--
 * @Author: liqian liqian@123
 * @Date: 2025-04-29 15:58:24
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-18 10:55:21
 * @FilePath: \qst-merchant-admin-2.0\src\components\FreeFromComponent\fromUpload.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <div class="flex avatar-flex">
      <div class="flex" v-if="$attrs.prop1 && $attrs.prop1 != ''">
        <PictureComponent
          v-if="modelValue[$attrs.prop]"
          :imgParams="modelValue[$attrs.prop]"
          imgWidth="100px"
          imgHeight="102px"
          @deleteImg="deleteImg($attrs.prop)"
        ></PictureComponent>
        <el-upload
          v-else
          :disabled="$attrs.disabled"
          class="avatar-uploader"
          action="fakeaction"
          :show-file-list="false"
          multiple
          accept=".jpg, .png, .jpeg, .bmp"
          :http-request="(e) => upLoadCardImg(e, $attrs.prop)"
        >
          <!-- <img v-if="modelValue[$attrs.prop]" :src="modelValue[$attrs.prop]" class="avatar" /> -->
          <i v-if="!modelValue[$attrs.prop]" class="el-icon-plus avatar-uploader-icon"></i>
          <div v-if="!modelValue[$attrs.prop]" class="el-upload__text">
            {{ $attrs.prop1 && $attrs.prop1 != '' ? '上传人像面' : '上传' }}
          </div>
        </el-upload>
        <PictureComponent
          v-if="modelValue[$attrs.prop1]"
          :imgParams="modelValue[$attrs.prop1]"
          imgWidth="100px"
          imgHeight="102px"
          @deleteImg="deleteImg($attrs.prop1)"
        ></PictureComponent>
        <el-upload
          :disabled="$attrs.disabled"
          v-else
          v-bind="$attrs"
          v-on="$listeners"
          class="avatar-uploader"
          action="fakeaction"
          :show-file-list="false"
          multiple
          accept=".jpg, .png, .jpeg, .bmp"
          :http-request="(e) => upLoadCardImg(e, $attrs.prop1)"
        >
          <!-- <img v-if="modelValue[$attrs.prop1]" :src="modelValue[$attrs.prop1]" class="avatar" /> -->
          <i v-if="!modelValue[$attrs.prop1]" class="el-icon-plus avatar-uploader-icon"></i>
          <div v-if="!modelValue[$attrs.prop1]" class="el-upload__text">上传国徽面</div>
        </el-upload>
      </div>
      <div v-else>
        <PictureComponent
          v-if="modelValue[$attrs.prop]"
          :imgParams="modelValue[$attrs.prop]"
          :uploadType="modelValue[$attrs.prop] | getFileType"
          imgWidth="100px"
          imgHeight="102px"
          @deleteImg="deleteImg($attrs.prop)"
        ></PictureComponent>
        <el-upload
          v-else
          :disabled="$attrs.disabled"
          class="avatar-uploader"
          action="fakeaction"
          :show-file-list="false"
          multiple
          :accept="$attrs.type1 == 'file' ? '.pdf, .jpg, .png, .jpeg, ' : '.jpg, .png, .jpeg'"
          :http-request="(e) => upLoadFileImg(e, $attrs.prop)"
        >
          <!-- <img v-if="modelValue[$attrs.prop]" :src="modelValue[$attrs.prop]" class="avatar" /> -->
          <i v-if="!modelValue[$attrs.prop]" class="el-icon-plus avatar-uploader-icon"></i>
          <div v-if="!modelValue[$attrs.prop]" class="el-upload__text">{{ '上传' }}</div>
        </el-upload>
      </div>
      <el-button type="text" v-if="$attrs.lookCard" @click="lookTemplate($attrs)">
        查看示例
      </el-button>
    </div>
    <div class="el-upload__tip">
      <el-button type="text" v-if="$attrs.downLoad" @click="downLoadTemplate($attrs)">
        <i class="el-icon-download el-icon--right"></i>
        下载模板
      </el-button>
      <div v-html="$attrs.tip"></div>
    </div>
    <el-dialog :close-on-click-modal="false" :visible.sync="dialogVisible" title="查看示例">
      <div class="id-card-model" v-if="dialogImageUrl.length > 0">
        <div v-for="(item, index) in dialogImageUrl">
          <el-image class="cardimg" :src="item" :preview-src-list="dialogImageUrl" alt></el-image>
          <div v-if="itemProps == 'legal_card_front'">
            {{ index == 0 ? '身份证人像面' : '身份证国徽面' }}
          </div>
        </div>
      </div>

      <el-empty v-else description="暂无示例"></el-empty>
    </el-dialog>
  </div>
</template>

<script>
  import { ocrProcess } from '@/api/miniMaageApi'
  import { uploadFile } from '@/api/common.js'

  export default {
    props: {
      value: {
        type: Object,
        default: () => {},
      },
      options: {
        type: Array,
        default: () => [],
      },
    },
    computed: {
      modelValue: {
        get() {
          console.log(this.value)
          return this.value
        },
        set(val) {
          this.$emit('input', val)
        },
      },
    },
    filters: {
      getFileType(data) {
        let type = data.split('.').length > 0 ? data.split('.')[data.split('.').length - 1] : ''
        return type
      },
    },
    data() {
      return {
        dialogImageUrl: [],
        itemProps: '',
        dialogVisible: false,
      }
    },
    methods: {
      // 上传文件
      upLoadFileImg(file, key) {
        // 上传
        let data = new FormData()
        data.append('file', file.file)
        uploadFile(data).then((res) => {
          this.modelValue[key] = res.data.url
          console.log(res.data)
        })
      },

      upLoadImg(file, key) {
        // this.convertToBase64(file.file, key)
        // 上传
        // return
        this.$upLoadImg(file.file).then((res) => {
          // 上传完执行的操作
          this.modelValue[key] = res.data.url
          //   switch (
          //     key // 根据上传的图片类型执行不同的操作
          //   ) {
          //     // 营业执照
          //     case 'business_license_pic':
          //       this.getOcrProcess('business', res.data.url, '0')
          //       break
          //     case 'legal_id_card_front_pic':
          //       this.getOcrProcess('idCardFront', res.data.url)
          //       break
          //     case 'legal_id_card_back_pic':
          //       this.getOcrProcess('idCardBack', res.data.url)
          //       break
          //   }
        })
      },
      //上传ocr识别的身份
      upLoadCardImg(file, key) {
        this.convertToBase64(file.file, key)
      },
      /**
       * 将文件转换为Base64编码的字符串
       *
       * @param file 文件对象，通常是通过文件输入获取的
       * @param key 用于标识或分类文件的唯一键
       */
      convertToBase64(file, key) {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => {
          const base64String = reader.result
          this.displayImage(base64String, key)
        }
      },
      displayImage(base64String, key) {
        // 显示图片的示例方法
        switch (
          key // 根据上传的图片类型执行不同的操作
        ) {
          // 营业执照
          case 'license':
            this.getOcrProcess(key, base64String, '0')
            break
          case 'legal_card_front':
            this.getOcrProcess(key, base64String, '1')
            break
          case 'legal_card_back':
            this.getOcrProcess(key, base64String, '1')
            break
        }
      },
      /**
       * 获取OCR处理结果
       *
       * @param {string} type OCR处理类型
       * @param {File} img 待处理的图片文件
       * @returns {Promise<any>} 返回OCR处理结果
       */
      async getOcrProcess(type, img, i) {
        let res = await ocrProcess({ type: i, image: img })
        this.modelValue[type] = res.data.image.url
        switch (type) {
          // 营业执照
          case 'license':
            // let params = res.data.ocr_business.content.result
            // this.formLabelAlign.company_name = params.name
            // this.formLabelAlign.unique_social_credit_code = params.registration_number
            break
          case 'legal_card_front':
            let data = res.data.ocr_id_card
            // this.formLabelAlign.legal_name = data.name
            // this.formLabelAlign.legal_id_card_no = data.idNumber
            if (!data.name || !data.idNumber) {
              this.modelValue[type] = ''
              this.$message.error('请上传身份证人像面')
              return
            }
            break
          case 'legal_card_back':
            let data1 = res.data.ocr_id_card
            if (!data1.validPeriod) {
              this.modelValue[type] = ''
              this.$message.error('请上传身份证国徽面')
              return
            }
            break
        }
      },
      /**
       * 处理文件上传超过限制的处理函数
       *
       * @param files 当前选择的文件列表
       * @param fileList 组件内部维护的文件列表
       */
      handleExceed(files, fileList) {
        console.log(fileList[0].raw)
        this.fileList = [files[0]] // 只保留最新的文件
        this.upLoadImg({ file: fileList[0].raw }, 'principal_authorization')
      },
      //查看示例
      lookTemplate(item) {
        console.log(item)
        this.itemProps = item.prop
        this.dialogImageUrl = item?.lookCardList
        this.dialogVisible = true
      },
      // 删除图片
      deleteImg(key) {
        this.$set(this.modelValue, key, '')
      },
      downLoadTemplate(item) {
        window.open(item.lookTemplate?.url)
      },
    },
  }
</script>
<style lang="scss">
  .avatar-flex {
    align-items: flex-end;
  }
  .avatar-uploader {
    margin-right: 24px;
    width: 100px;
    height: 100px;
    border: 1px dashed #dcdfe6;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    text-align: center;
    &:hover {
      border-color: #409eff;
    }

    .el-upload__text {
      font-weight: 400;
      font-size: 12px;
      color: #606266;
      line-height: 17px;
    }
  }
  .avatar-uploader-icon {
    font-size: 15px;
    color: #8c939d;
    line-height: 20px;
    text-align: center;
    margin: 32px auto 0px;
  }

  .avatar {
    width: 100px;
    height: 100px;
    display: block;
  }
  .avatar_img {
    display: block;
    width: 360px;
    height: 180px;
  }
  // / 身份证示例图片样式
  .id-card-model {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    text-align: center;
    .cardimg {
      width: 200px;
      height: 112px;
    }
    div {
      font-weight: 400;
      font-size: 14px;
      color: #8c8c8c;
      line-height: 14px;
      margin-top: 15px;
    }
  }
</style>
