<template>
  <div>
    <el-dialog
      :visible.sync="isSelectCompany"
      width="480px"
      append-to-body
      destroy-on-close
      :show-close="false"
      :before-close="closeFn"
      :close-on-click-modal="false"
    >
      <div slot="header" class="dialog-header"></div>
      <div style="min-height: 200px" v-loading="loading">
        <div v-if="isSelectMerchant">
          <el-form ref="form" :model="form">
            <el-form-item label="选择商家主体: ">
              <el-select
                style="width: 300px"
                v-model="form.mchAdminUid"
                placeholder="请选择"
                @change="uidChange"
              >
                <el-option
                  v-for="item in merchantList"
                  :key="item.mchAdminUid"
                  :label="item.merchantName"
                  :value="item.mchAdminUid"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <el-divider class="divider"></el-divider>
        </div>

        <div v-if="companyList.length > 1">
          <div class="title">请选择要登录的企业</div>
          <div
            class="company-scroll"
            :class="companyList.length <= 3 ? 'company-scroll-height' : ''"
          >
            <div class="company-item flex-b" v-for="(item, index) in companyList" :key="index">
              <div class="flex">
                <el-image class="logo" :src="item.logo"></el-image>
                <div class="line1 company-name">{{ item.merchantName }}</div>
              </div>

              <el-button @click="selectCompanyFn(item)" type="primary" plain>选择</el-button>
            </div>
          </div>
        </div>

        <el-image
          v-else-if="merchantList.length > 1"
          class="select_image"
          :src="require('@/assets/login_images/image_join.png')"
          @click="selectCompanyFn(companyList[0])"
        ></el-image>
      </div>

      <base-dialog-footer :confirmText="'关闭'" @confirm="cancelDialog" :isCancel="false"></base-dialog-footer>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { merchantListApi, loginBySmsAffirm, selectLoginApi } from '@/api/user'
import store from '@/store'
export default {
  name: 'SelectComponent',
  props: {},
  computed: {
    ...mapGetters({
      isMerchantList: 'user/isOpenMerchant',
    }),
  },
  watch: {
    $route: {
      handler(route) {
        this.redirect = (route.query && route.query.redirect) || '/'
      },
      immediate: true,
    },
    isMerchantList(val) {
      if (val) {
        console.log(val)
        this.loading = true
        // 账号密码登录且有多个 租户/入驻商家，选择租户
        if (val == 1) {
          this.getmerchantListApi()
        } else {
          // 手机号登录且有多个 企业先选择企业，后选择租户/入驻商家
          this.$nextTick(() => {
            let merchantList = localStorage.getItem('merchantList')
            merchantList = JSON.parse(merchantList)

            // 默认选择第一个商户登录
            if (merchantList.length > 0) {
              this.form.mchAdminUid = merchantList[0].mchAdminUid
              this.merchantList = merchantList
              this.getmerchantListApi(merchantList[0].mchAdminUid)
            }
            this.isSelectMerchant = this.selectKey == 2 && this.merchantList.length > 1
          })
        }
        this.isSelectCompany = true
        this.selectKey = val
      }
    },
  },
  data() {
    return {
      // 登录弹窗
      isSelectCompany: false,
      // 表单数据
      form: {
        mchAdminUid: '',
      },
      // 公司列表
      companyList: [],
      // 1 账号密码登录 2 手机号登录
      selectKey: 0,
      // 重定向页面
      redirect: '',
      // 企业列表
      merchantList: [],
      // 是否是多商户登录选择公司主体页面  手机号登录且有多商户时才可以使用该功能
      isSelectMerchant: false,
      // 加在
      loading: false,
    }
  },
  methods: {
    // 关闭弹窗
    cancelDialog() {
      store.dispatch('user/setIsMerchant', false)
      this.isSelectCompany = false
    },
    // 获取商户列表信息
    getmerchantListApi(mchAdminUid) {
      mchAdminUid = mchAdminUid || JSON.parse(localStorage.getItem('loginBySms')).mchAdminUid
      merchantListApi({
        mchAdminUid,
      }).then((res) => {
        if (res.code === 200) {
          this.form.mchAdminUid = mchAdminUid
          this.loading = false
          this.companyList = res.data || []
        }
      })
    },

    // 选择公司主体变化时触发事件
    uidChange() {
      this.getmerchantListApi(this.form.mchAdminUid)
    },

    // 选择对应租户
    selectCompanyFn(item) {
      selectLoginApi({
        mchStoreUid: item.mchStoreUid,
        mchAdminUid: this.form.mchAdminUid,
      }).then((res) => {
        if (res.code === 200) {
          this.loginSuccess(res)
          this.isSelectCompany = false
        }
      })
    },
    // 登录成功
    loginSuccess(res) {
      localStorage.setItem('currentMchUid', res.data.mchStoreUid)
      localStorage.setItem('mchAdminUid', res.data.mchAdminUid)
      this.$ReachToken.init()
      store.dispatch('user/getUserInfo')
      store.dispatch('user/setIsMerchant', false)
      store.dispatch('user/login', res.data)
      store.dispatch('user/setPermissions', [])
      const routerPath = this.redirect === '/404' || this.redirect === '/401' ? '/' : this.redirect
      this.$router.push(routerPath)
      this.companyList = []
      console.log('登录成功')
    },

    // 关闭弹窗事件
    closeFn(done) {
      console.log('关闭')
      this.isSelectCompany = false
      store.dispatch('user/setIsMerchant', false)
      done()
    },
  },
}
</script>

<style lang="scss" scoped>
.divider {
  margin-top: 30px;
  background: #e8e8e8;
}
.title {
  font-size: $base-font-size-bigger;
  text-align: center;
  margin: 0 0 20px 0;
  font-weight: 600;
}

.company-scroll.company-scroll-height {
  height: auto;
}
.company-scroll {
  height: 300px;
  overflow: auto;

  .company-item {
    height: 80px;
    background: #f5f7fa;
    padding: 0 20px;
    border-radius: 8px;
    & + .company-item {
      margin-top: 20px;
    }

    .logo {
      width: 50px;
      height: 50px;
      margin-right: 10px;
    }
    .company-name {
      max-width: 200px;
      font-size: 16px;
    }
  }
}
.select_image {
  width: 420px;
  cursor: pointer;
}
::v-deep .el-dialog__header {
  padding: 0 20px !important;
}
::v-deep .el-dialog__body {
  border-radius: 16px;
  border: 0 !important;
}

::v-deep .el-form-item--small .el-form-item__label {
  line-height: 40px;
}

::v-deep .el-button--primary.is-plain {
  font-weight: bold;
  border: 0;
  background: #dfedff;
  &:hover {
    background: $base-color-default;
  }
}
</style>
