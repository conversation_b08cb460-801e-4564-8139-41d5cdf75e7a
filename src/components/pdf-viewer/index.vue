<!--
 * @Author: liqian <EMAIL>
 * @Email: <EMAIL>
 * @Date: 2025-07-16 11:47:06
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-16 16:37:02
 * @FilePath: \qst-merchant-admin-2.0\src\components\pdf-viewer\index.vue
 * @Description: 
-->
<template>
  <el-dialog
    title="预览"
    :visible.sync="visible"
    width="100%"
    top="0"
    @closed="close"
    append-to-body
  >
    <iframe :src="dialogSrc" frameborder="0"></iframe>

    <!-- <pdf-viewer
      :src="dialogSrc"
      :startPage="startPage"
      :style="{ height: height, width: '100%' }"
    ></pdf-viewer> -->
  </el-dialog>
</template>

<script>
  import pdf from 'vue-pdf'
  import pdfViewer from './viewer'
  export default {
    components: {
      pdf,
      pdfViewer,
    },
    props: {
      srcList: {
        type: String,
        default: '',
      },
      startPage: {
        type: Number,
        default: 1,
      },
    },
    data() {
      return {
        dialogSrc: '',
        visible: false,
        height: '100%',
      }
    },
    methods: {
      open(src) {
        this.dialogSrc = this.srcList
        this.visible = true
      },
      close() {
        this.visible = false
      },
      maxScreen(a) {
        if (a) {
          this.height = '100%'
        } else {
          this.height = window.innerHeight - 65 + 'px'
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .pdf {
    cursor: pointer;
  }
  ::v-deep {
    .el-dialog {
      margin: 0;
      // margin-top: 10px;
      height: 100%;
      .el-dialog__body {
        height: calc(100% - 115px);
      }
      iframe {
        height: 100%;
        width: 100%;
      }
    }
  }
</style>
