<!--
 * @Description: 系统通知详情弹窗组件
 * @Author: AI Assistant
 * @Date: 2025-07-25
-->
<template>
  <!-- 通知详情弹窗 -->
  <el-dialog
    title="通知详情"
    :visible.sync="dialogVisible"
    width="740px"
    style="margin-top: 15vh"
    :before-close="handleClose"
    custom-class="notification-dialog"
    :modal-append-to-body="false"
    :append-to-body="true"
    :z-index="3000"
    @closed="handleClosed"
  >
    <div class="notification-detail" v-if="notificationData">
      <!-- 通知标题 -->
      <div class="detail-title">
        {{ notificationData.title }}
      </div>

      <!-- 通知标签 -->
      <div class="detail-tags">
        <el-tag :type="getTypeTagType(notificationData.type)" size="small">
          {{ notificationData.type_text }}
        </el-tag>
        <el-tag :type="getLevelTagType(notificationData.level)" size="small">
          {{ notificationData.level_text }}
        </el-tag>
        <el-tag :class="getStatusTagClass(notificationData.is_read)" size="small">
          {{ notificationData.is_read_text }}
        </el-tag>
      </div>

      <!-- 通知内容 -->
      <div class="detail-content">
        <div class="content-divider"></div>
        <h4>通知内容</h4>
        <div class="content-text" v-html="notificationData.content">
        </div>
      </div>

      <!-- 通知时间 -->
      <div class="detail-time">
        通知时间：{{ notificationData.created_at }}
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button class="confirm-btn" @click="handleClose">我已知晓</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'SystemDetails',
  props: {
    // 控制弹窗显示隐藏
    visible: {
      type: Boolean,
      default: false
    },
    // 通知数据
    notificationData: {
      type: Object,
      default: () => null
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    // 关闭弹窗
    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },

    // 弹窗关闭后的回调
    handleClosed() {
      this.$emit('closed')
    },



    // 获取类型标签类型（用于弹窗）
    getTypeTagType(typeText) {
      // 根据接口返回的文本设置标签类型
      if (typeText === 1) {
        return 'primary'
      } else if (typeText === 2) {
        return 'warning'
      } else if (typeText === 3) {
        return 'success'
      } else {
        return ''
      }
    },



    // 获取重要等级标签类型（用于弹窗）
    getLevelTagType(level) {
      // 根据汉字设置标签类型
      if (level === 1) {
        return 'danger'
      } else if (level === 2) {
        return 'warning'
      } else {
        // 普通或其他
        return 'info'
      }
    },

    // 判断是否未读
    isUnread(isRead) {
      // 兼容多种数据格式：'N', 0, false, '0'
      return isRead === 'N' || isRead === 0 || isRead === false || isRead === '0'
    },

    // 获取状态标签样式类
    getStatusTagClass(isReadText) {
      // 根据接口返回的文本设置样式类
      return isReadText === 'N' ? 'status-unread' : 'status-read'
    },


  }
}
</script>

<style lang="scss" scoped>
// 通知详情弹窗样式
::v-deep .notification-dialog {
  border-radius: 16px !important;
  overflow: hidden !important;
  z-index: 3000 !important; // 提高层级，确保在最上层

  .el-dialog {
    border-radius: 16px !important;
    overflow: hidden !important;
  }

  .el-dialog__wrapper {
    z-index: 3000 !important; // 确保包装器也有足够高的层级
  }
}

// 全局样式，确保弹窗遮罩层级正确
::v-deep .v-modal {
  z-index: 2999 !important; // 遮罩层级稍低于弹窗
}

// 确保弹窗包装器在最上层
::v-deep .el-dialog__wrapper {
  z-index: 3000 !important;
}

// 针对可能的遮挡元素，强制提升弹窗层级
.notification-dialog {
  position: relative !important;
  z-index: 9999 !important;
}

// 如果是 Popover 遮挡，降低其层级
::v-deep .el-popover {
  z-index: 2000 !important;
}

::v-deep .el-popper {
  z-index: 2000 !important;
}

::v-deep .notification-dialog .el-dialog__header {
    padding: 20px 24px 16px;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

::v-deep .notification-dialog .el-dialog__body {
  padding: 0 24px 0 24px;
  border-top: 0px solid #dcdfe6;
}

::v-deep .notification-dialog .el-dialog__footer {
  padding: 16px 24px 24px;
  text-align: center;
  border-top: 0px solid #EBEEF5;

  .confirm-btn {
    background-color: #0071FE !important;
    border-color: #0071FE !important;
    color: white !important;
    padding: 10px 24px;
    font-size: 14px;
    border-radius: 6px;

    &:hover {
      background-color: #005bb5 !important;
      border-color: #005bb5 !important;
    }
  }
}

.notification-detail {
  .detail-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 12px;
    line-height: 1.5;
  }

  .detail-tags {
    margin-bottom: 20px;

    .el-tag {
      margin-right: 8px;
    }
  }

  .detail-content {
    margin-bottom: 20px;

    .content-divider {
      height: 1px;
      background-color: #EBEEF5;
      margin: 20px 0 16px 0;
    }

    h4 {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 12px;
      text-align: left;
    }

    .content-text {
      font-size: 14px;
      color: #606266;
      line-height: 1.6;
      background-color: #FFFFFF;
      text-align: left;
    }
  }

  .detail-time {
    font-size: 14px;
    color: #909399;
    text-align: left;
  }
}

// 自定义状态标签样式
::v-deep .el-tag {
  &.status-unread {
    color: #0071FE !important;
    background-color: #ECF5FF !important;
    border: 1px solid #D3E6FF !important;
  }

  &.status-read {
    color: #A8ABB2 !important;
    background-color: #F5F7FA !important;
    border: 1px solid #E9E9EB !important;
  }
}
</style>
