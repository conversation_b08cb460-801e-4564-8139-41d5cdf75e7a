<template>
  <span>
    <el-button type="text" style="padding: 0;" @click="openAgreement">{{title}}</el-button>

    <el-drawer :title="title" :visible.sync="isOpen" direction="rtl" size="50%">
      <div style="color: #333;padding: 20px;" v-html="agreement"></div>
    </el-drawer>
  </span>
</template>

<script>
import { agreementApi } from '@/api/common.js'
export default {
  name: 'agreement',
  props: {
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      agreement: '',
      isOpen: false,
    }
  },
  methods: {
    // 打开抽屉组件，显示协议内容
    openAgreement() {
      if (this.agreement) {
        this.isOpen = true
        return
      }

      agreementApi({
        cate_title: this.title,
      }).then((res) => {
        if (res.code == 200) {
          this.agreement = res.data.content
          this.isOpen = true
        }
      })
    },
  },
  mounted() {},
}
</script>