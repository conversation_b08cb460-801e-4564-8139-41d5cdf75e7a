<template>
  <div>
    <iframe
      ref="iframe"
      id="printView'"
      style="width:0;height: 0;"
      :src="prevViewUrl"
      frameborder="0"
    ></iframe>
  </div>
</template>


<script>
import store from '@/store'
import config from '../../../config.js'
export default {
  name: 'PrintView',
  data() {
    return {
      // 打印模版
      prevViewUrl: config.path + 'print.html',
    }
  },
  mounted() {
    window.addEventListener('message', this.receiveMessage, false)
  },
  /**
   * 在组件销毁前执行，移除全局监听事件
   */
  beforeDestroy() {
    window.removeEventListener('message', this.receiveMessage, false)
  },
  methods: {
    /**
     * 接收消息的方法
     *
     * @param res 服务器返回的消息对象
     */
    receiveMessage(res) {
      if (res.code == 200) {
        this.$message.success(res.msg)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 打开打印页面，并传递参数
    open(param, printType) {
      return new Promise((resolve, reject) => {
        this.sendMessage(param, printType)
        resolve()
      })
    },
    // 向子页面发送消息
    sendMessage(params, printType) {
      this.$refs.iframe.contentWindow.postMessage(
        {
          baseURL: config.baseURL,
          token: store.getters['user/accessToken'],
          params,
          printType,
        },
        '*'
      )
    },
  },
}
</script>