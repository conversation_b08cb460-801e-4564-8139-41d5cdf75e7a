<template>
  <div v-loading="isLoading" :style="'width:' + width + ';height:' + height">
    <iframe
      ref="iframe"
      id="printView'"
      :style="'width:' + width + ';height:' + height"
      :src="prevViewUrl"
      frameborder="0"
    ></iframe>
  </div>
</template>

<script>
import config from '../../../config.js'
export default {
  name: 'PrintView',
  props: {
    isOneListener: {
      type: Boolean,
      default: true,
    },
    priveView: {},
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    type: {
      type: String,
      default: 'prevView',
    },
    // 打印类型  电子面单1 小票2 出库单3
    printType: {
      type: Number,
      default: 1,
    },
  },
  watch: {
    priveView: {
      handler(val) {
        this.isLoading = true
        if (this.type == 'prevView') {
          setTimeout(() => {
            this.$nextTick(() => {
              this.$refs.iframe.contentWindow.postMessage(
                {
                  data: JSON.stringify(val),
                  printType: this.printType,
                  isOneListener: this.isOneListener,
                },
                '*'
              )
            })
            this.isLoading = false
          }, 500)
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.$nextTick(() => {
      setTimeout(() => {
        this.$refs.iframe.contentWindow.postMessage(
          {
            data: JSON.stringify(this.priveView),
            printType: this.printType,
            isOneListener: this.isOneListener,
          },
          '*'
        )
      }, 1500)
    })
  },
  data() {
    return {
      prevViewUrl: config.path + 'prevView.html',
      isLoading: false,
    }
  },
  methods: {},
}
</script>