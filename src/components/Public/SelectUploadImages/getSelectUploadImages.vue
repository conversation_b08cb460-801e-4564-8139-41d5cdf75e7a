<!--
 * @Author: liq<PERSON> liqian@123
 * @Date: 2025-04-01 15:21:59
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-07 17:09:48
 * @FilePath: \qst-merchant-admin-2.0\src\components\Public\SelectUploadImages\getSelectUploadImages.vue
 * @Description:  商场管理-图片管理
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-card>
    <div class="picture-management">
      <div class="categorytop flex">
        <el-col class="treecol" :span="3">
          <div class="tree-header">
            <span class="title">图片分类</span>
            <span @click="addCategory"><i class="el-icon-plus"></i></span>
          </div>
        </el-col>

        <!-- 搜索框 -->
        <el-row :span="20" class="search-bar">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索图片名称或标签"
            clearable
            prefix-icon="el-icon-search"
          />
          <el-button type="primary" class="btn" block @click="searchPictures">查询</el-button>
        </el-row>
      </div>
      <el-row class="picture-management-row">
        <!-- 左侧分类树 -->
        <el-col :span="3" class="category-tree">
          <div class="tree-content">
            <el-tree
              ref="treeSelect"
              :data="categoryDataAll"
              :props="treeProps"
              :current-node-key="currentTd"
              :expand-on-click-node="false"
              node-key="id"
              default-expand-all
              highlight-current
              @node-click="handleCategoryClick"
            >
              <span class="custom-tree-node" slot-scope="{ node, data }">
                <span
                  class="allImages"
                  :title="data.name"
                  :class="{ active: !clickFlag && data.id == '' }"
                >
                  {{ data.name }}
                </span>
                <div @click.stop v-if="data.id != ''">
                  <el-dropdown trigger="click" @command="handleCommand">
                    <span class="el-dropdown-link">
                      <span><i class="el-icon-more"></i></span>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item
                        icon="el-icon-plus"
                        v-if="data.level != 2"
                        :command="beforeHandleCommand(data, 'Next')"
                      >
                        新增下级
                      </el-dropdown-item>
                      <el-dropdown-item
                        icon="el-icon-edit"
                        :command="beforeHandleCommand(data, 'Edit')"
                      >
                        修改
                      </el-dropdown-item>
                      <el-dropdown-item
                        icon="el-icon-delete"
                        :command="beforeHandleCommand(data, 'Delete')"
                      >
                        删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </span>
            </el-tree>
          </div>
        </el-col>

        <!-- 右侧内容 -->
        <el-col :span="21" class="picture-content">
          <!-- 操作按钮 -->
          <div class="action-buttons">
            <el-button icon="el-icon-plus" type="primary" @click="uploadPicture">
              上传图片
            </el-button>
            <el-button type="primary" :disabled="!pictureListChecked.length" @click="batchEdit">
              批量编辑
            </el-button>
          </div>
          <!-- 图片列表 -->
          <el-row :gutter="20">
            <div class="picture-list flex" ref="picture-list" v-if="pictureList.length > 0">
              <el-col
                class="picture_con"
                style="display: flex"
                :xs="12"
                :sm="12"
                :md="18"
                :lg="24"
                :xl="24"
              >
                <div
                  class="pictureMain"
                  v-for="picture in pictureList"
                  :label="picture"
                  :key="picture.id"
                >
                  <el-card class="main_card">
                    <el-checkbox-group
                      v-model="pictureListChecked"
                      @change="changeCheck"
                      :min="0"
                      :max="limit"
                    >
                      <el-Checkbox
                        v-model="picture.checked"
                        :label="picture"
                        class="picture-checkbox picture-main"
                      >
                        <el-image
                          :fit="fit"
                          :ref="(el) => setImageRef(picture.image_url, el)"
                          class="picture-preview"
                          :src="picture.image_url"
                          :preview-src-list="[picture.image_url]"
                        ></el-image>
                      </el-Checkbox>
                    </el-checkbox-group>
                    <div class="picture-card">
                      <div class="picture-info">
                        <div class="picture-title" :title="picture.name">
                          {{ picture.name || '默认名称' }}
                        </div>
                        <div class="picture-tag tagtype" :title="picture.category_name">
                          {{ picture.category_name || '暂无分类' }}
                        </div>

                        <div class="picture-tags flex">
                          <el-tag
                            class="tagtype1"
                            :class="{
                              taglen2: picture.label.length == 2,
                              taglen3: picture.label.length == 3,
                              taglenmore: picture.label.length > 3,
                            }"
                            :title="tag"
                            v-if="index < 3"
                            v-for="(tag, index) in picture.label"
                            :key="index"
                          >
                            <span v-if="index < 3">{{ tag }}</span>
                          </el-tag>

                          <el-popover
                            popper-class="popoverStyle"
                            placement="top"
                            width="250"
                            trigger="hover"
                          >
                            <template>
                              <el-tag
                                class="tagtype1"
                                :title="tag"
                                v-for="(tag, index) in picture.label"
                                :key="tag"
                              >
                                <span>{{ tag }}</span>
                              </el-tag>
                            </template>
                            <template slot="reference" class="aa">
                              <span class="tagtype2" v-show="picture.label.length > 3">
                                +{{ picture.label.length - 3 }}
                              </span>
                              <!-- <el-tag class="tagtype2" type="info" v-if="picture.label.length > 3">+{{ picture.label.length - 3 }}</el-tag> -->
                            </template>
                          </el-popover>
                        </div>

                        <div class="picture-time">上传时间：{{ picture.updated_at }}</div>
                      </div>
                      <div class="picture-actions">
                        <i class="el-icon-view" @click="viewPicture(picture)"></i>
                        <span class="addline">|</span>
                        <i class="el-icon-edit" @click="editPicture(picture)"></i>
                        <span class="addline">|</span>
                        <i class="el-icon-delete" @click="deletePicture(picture)"></i>
                      </div>
                    </div>
                  </el-card>
                </div>
              </el-col>
            </div>
            <div v-else>
              <el-empty description="暂无上传图片"></el-empty>
            </div>
          </el-row>

          <!-- 分页 -->
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page"
            :page-sizes="[12, 24, 36, 48, 60]"
            :page-size="size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
          <!-- 添加按钮插槽 -->
          <slot></slot>
        </el-col>
      </el-row>

      <!-- 图片分组弹窗 -->
      <imagesDialog
        :pictureTitle="pictureTitle"
        :selcategoryData="selcategoryData"
        :dialoagName="dialoagName"
        :basicRloeBtn="basicRloeBtn"
        :categoryData="categoryData"
        :editNodeName="editNodeName"
        :currentTreeLi="currentTreeLi"
        :currentEditPic="currentEditPic"
        :pictureListChecked="pictureListChecked"
        @confirmBtn="uploadConfirm"
        ref="showimagesDialog"
      ></imagesDialog>
    </div>
  </el-card>
</template>

<script>
  /**
   * getSelectUploadImages 图片上传选择
   * @property {Arrary} selectPicList   清空选中的图片数据
   * @event {Function} getSelectPicList 得到选中的图片数据
   * slot 插槽  可以添加按钮
   **/
  // import Lcheckbox from '../l-checkbox.vue'

  import imagesDialog from './uploadImagesDialog/imagesDialog.vue'
  import { getmaparrList } from '@/utils/index'
  import {
    categorylistApi,
    getImageListApi,
    getdeleteImageApi,
    deletetCategoryApi,
  } from '@/api/shop/goodsSet/pictureManagement'
  export default {
    name: 'getSelectUploadImages',
    components: {
      imagesDialog,
      // Lcheckbox,
    },
    props: {
      selectPicList: {
        // 选中的图片列表
        type: Array,
        default: () => [],
      },
      // 选中的图片限制
      limit: {
        type: Number,
        default: 100,
      },
    },
    data() {
      return {
        previewImgSrc: '',
        dialoagName: '新增分类',
        pictureTitle: '父级分类',
        basicRloeBtn: '', //'ADD','Next', 'Edit', 'Upload', 'EditPic', 'EditPicMore'
        editNodeName: '',
        currentEditPic: {}, //编辑当前的图片
        selcategoryData: [],
        currentTreeLi: {}, //点击的当前树节点
        categoryDataAll: [{ name: '全部图片', id: '', children: [] }],
        categoryData: [],
        treeProps: {
          label: 'name',
          value: 'id',
          children: 'children',
        },
        searchForm: {
          keyword: '',
        },
        pictureList: [],
        pictureListChecked: [],
        clickFlag: false,
        imageRefMap: new Map(), // 存储文件 UID 和组件实例的映射
        currentTd: '',
        total: 0,
        size: 12,
        page: 1,
        fit: 'contain', // 'fill', 'contain', 'cover', 'none', 'scale-down',
      }
    },
    watch: {
      selectPicList: {
        handler(newVal) {
          console.log(newVal.length)
          if (newVal.length == 0) {
            this.pictureList.forEach((item) => {
              item.checked = false
            })
            console.log(this.pictureList)
            this.pictureListChecked = []
          }
        },
        deep: true,
      },
    },
    created() {
      // 获取图片分类
      this.getPictureCategoryFGroup()
    },
    methods: {
      getPictureCategoryFGroup() {
        categorylistApi().then((res) => {
          if (res.code == 200) {
            this.categoryDataAll = [{ name: '全部图片', id: '', children: [] }]
            this.categoryData = res.data
            this.categoryDataAll = this.categoryDataAll.concat(this.categoryData)
            this.getPictureCategory()
          }
        })
      },
      // 获取图片数据
      getPictureCategory() {
        this.clickFlag = this.currentTreeLi?.id ? true : false
        this.currentTd = this.currentTreeLi?.id ? Number(this.currentTreeLi?.id) : ''
        this.$nextTick(() => {
          this.$refs['treeSelect'].setCurrentKey(this.currentTd)
        })
        getImageListApi({
          page: this.page,
          size: this.size,
          category_id: this.currentTreeLi?.id,
          name: this.searchForm.keyword,
        }).then((res) => {
          if (res.code == 200) {
            this.pictureList = res.data.list
            this.total = res.data.pagination.totalCount
          }
        })
      },
      //上传确认的回调
      uploadConfirm() {
        this.getPictureCategoryFGroup()
        console.log('确认')
      },
      handleAdd(row) {
        this.basicRloeBtn = 'Next'
        this.dialoagName = '新增分类'
        this.pictureTitle = '父级分类'
        this.getParentsId(row)
        this.currentTreeLi = row
        this.$refs.showimagesDialog.openimagesDialog()
        console.log('新增', row)
      },
      handleEdit(row) {
        this.basicRloeBtn = 'Edit'
        this.dialoagName = '编辑分类'
        this.pictureTitle = '父级分类'
        this.getParentsId(row)
        this.editNodeName = row.name
        this.currentTreeLi = row
        this.$refs.showimagesDialog.openimagesDialog()
        console.log('编辑', row)
      },
      handledelete(row) {
        this.$confirm(`确定要删除"${row.name}"分类吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            deletetCategoryApi({ id: row.id }).then((res) => {
              if (res.code == 200) {
                this.$message.success('删除成功')
                this.getPictureCategoryFGroup()
              }
            })
          })
          .catch((action) => {
            // this.$message({
            //   type: 'info',
            //   message: action === 'cancel' ? '取消删除' : '停留在当前页面',
            // })
          })
        console.log('删除', row)
      },

      beforeHandleCommand(row, command) {
        return {
          row: row,
          command: command,
        }
      },
      handleCommand(command) {
        switch (command.command) {
          case 'Next': //新增下一级
            this.handleAdd(command.row)
            break
          case 'Edit': //修改
            this.handleEdit(command.row)
            break
          case 'Delete': //删除
            this.handledelete(command.row)
            break
        }
      },
      addCategory() {
        this.basicRloeBtn = 'ADD'
        this.dialoagName = '新增分类'
        this.selcategoryData = []
        this.$refs.showimagesDialog.openimagesDialog()
      },
      // 得到父节点的id
      getParentsId(row) {
        console.log(11)
        row.category_id = row.category_id ? Number(row.category_id) : row.id
        let idPath = this.findAllParentNodes(this.categoryData, row.category_id)
        if (idPath.length == 0) {
          this.selcategoryData = row.category_id ? [row.category_id] : []
        } else {
          let parentsId = idPath.map((node) => node.id) //得到父节点的id
          this.selcategoryData = [...parentsId, row.category_id]
        }
      },
      /**
       * 根据子节点ID查找其所有父节点数据
       * @param {Object} roots 树结构的根节点
       * @param {string|number} targetId 目标子节点ID
       * @param {string} [childrenKey='children'] 子节点字段名
       * @returns {Array} 父节点路径数组（从根到直接父节点）
       */
      findAllParentNodes(roots, targetId) {
        for (const root of roots) {
          const path = []
          if (this.findNodePath(root, targetId, path)) {
            return path.slice(0, -1) // 排除目标节点自身
          }
        }
        return []
      },

      findNodePath(node, targetId, path) {
        path.push(node)
        if (node.id == targetId) {
          return true
        }
        for (const child of node?.children || []) {
          if (this.findNodePath(child, targetId, path)) {
            return true
          }
        }
        path.pop()
        return false
      },
      handleCategoryClick(node) {
        this.page = 1
        this.currentTreeLi = node
        this.clickFlag = node.id ? true : false
        this.getPictureCategory()
        this.$nextTick(() => {
          if (this.$refs['picture-list']) {
            this.$refs['picture-list'].scrollTop = 0
          }
        })
      },
      //搜索图片
      searchPictures() {
        this.getPictureCategory()
      },
      uploadPicture() {
        this.basicRloeBtn = 'Upload'
        this.dialoagName = '上传图片'
        this.pictureTitle = '图片分类'
        this.getParentsId(this.currentTreeLi)
        this.$refs.showimagesDialog.openimagesDialog()
        console.log('上传图片')
      },
      //得到选择的图片
      changeCheck() {
        // this.pictureListChecked = getmaparrList(this.pictureList, 'checked', true)
        this.$emit('getSelectPicList', this.pictureListChecked)
        console.log('选择图片', this.pictureListChecked)
      },
      batchEdit() {
        this.basicRloeBtn = 'EditPicMore'
        this.dialoagName = '批量编辑图片'
        this.pictureTitle = '图片分类'
        this.getParentsId(this.currentTreeLi)
        this.$refs.showimagesDialog.openimagesDialog()
        console.log('批量编辑', this.pictureListChecked)
      },
      batchDelete() {
        let checkPicList = this.pictureListChecked.map((item) => item.resource_uid).join()
        this.deleteUploadPic(checkPicList)
        console.log('批量删除', this.pictureListChecked)
      },
      //删除图片
      deleteUploadPic(data) {
        this.$confirm('是否删除选择的图片?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            getdeleteImageApi({
              image_uid: data,
            }).then((res) => {
              if (res.code == 200) {
                this.$message.success('删除成功')
                this.getPictureCategory()
              }
            })
          })
          .catch((action) => {
            // this.$message({
            //   type: 'info',
            //   message: action === 'cancel' ? '取消删除' : '停留在当前页面',
            // })
          })
      },
      setImageRef(uid, el) {
        if (el) {
          this.imageRefMap.set(uid, el)
        } else {
          this.imageRefMap.delete(uid)
        }
      },
      // 预览处理
      viewPicture(file, flag) {
        // 通过DOM操作触发el-image的预览
        const imageComponent = this.imageRefMap.get(file.image_url)
        this.$nextTick(() => {
          if (imageComponent) {
            imageComponent.clickHandler()
          }
        })
      },
      editPicture(picture) {
        this.basicRloeBtn = 'EditPic'
        this.dialoagName = '编辑图片'
        this.pictureTitle = '图片分类'
        this.getParentsId(picture)
        this.currentEditPic = picture
        this.$refs.showimagesDialog.openimagesDialog()
        console.log('编辑图片', picture)
      },
      deletePicture(picture) {
        let checkPicList = picture.resource_uid
        this.deleteUploadPic(checkPicList)
        console.log('删除图片', picture)
      },
      // 分页
      handleSizeChange(val) {
        this.size = val
        this.page = 1
        this.getPictureCategory()
      },
      load() {
        this.page++
        this.getPictureCategory()
      },
      // 分页
      handleCurrentChange(val) {
        this.page = val
        this.getPictureCategory()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .uploadImagesDiv {
    color: #fff;
    background: #0071fe;
    border-radius: 0px 4px 4px 0px;
  }

  .picture-management {
    .el-cascader {
      width: 100%;
    }
    .categorytop {
      border-bottom: 1px solid #e6e6e6;
      padding-bottom: 20px;
      .treecol {
        min-width: 200px;
      }
      .tree-header {
        min-width: 200px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
          font-size: 14px;
          font-weight: 600;
          color: #303133;
        }
      }
      .search-bar {
        display: flex;
        margin-left: 20px;
        .el-input {
          width: 100%;
        }
        .btn {
          margin-left: 10px;
        }
      }
    }
    .picture-management-row {
      display: flex;
      margin-top: 15px;
      .category-tree {
        min-width: 200px;
        .tree-content {
          padding-top: 10px;

          .allnode {
            border-radius: 6px;
            background-color: #e6f4ff;
            color: rgb(24, 144, 255);
            cursor: pointer;
            margin-left: 23px;
          }

          .el-tree--highlight-current {
            .el-tree-node.is-current {
              > .el-tree-node__content {
                color: #1890ff;
              }
            }
          }

          .el-tree {
            padding-right: 5px;

            .el-tree-node__content {
              .custom-tree-node {
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-size: 14px;
                padding-right: 8px;

                .allImages {
                  display: inline-block;
                  white-space: nowrap;
                  width: 100px;
                  overflow: hidden;
                  text-overflow: ellipsis;

                  &.active {
                    color: rgb(24, 144, 255);
                  }
                }
              }
            }
          }
        }
      }

      .picture-content {
        margin-left: 20px;

        .action-buttons {
          margin-bottom: 20px;
          display: flex;
          gap: 10px;
        }

        .picture-list {
          // height: 690px;
          overflow: auto;
          .picture_con {
            display: flex;
            flex-wrap: wrap;
          }
          .pictureMain {
            width: 200px;
            padding-left: 10px;
          }

          .el-checkbox__input {
            position: absolute;
            top: 10px;
            left: 19px;
          }

          .el-checkbox {
            margin-right: 0;
          }
          .main_card {
            border-radius: 10px;
            ::v-deep {
              .el-card__body {
                padding: 0;
              }
              .el-checkbox {
                margin-right: 0;
              }

              .el-checkbox__input {
                position: absolute;
                top: 10px;
                left: 10px;
              }
              .el-checkbox__label {
                padding-left: 0;
              }
            }

            .picture-preview {
              width: 200px;
              height: 200px;
              pointer-events: none;
            }
            .picture-card {
              position: relative;
              border-radius: 10px;
              .el-card__body {
                padding: 0 !important;
              }

              .picture-info {
                width: 180px;
                padding: 5px 10px 10px;
                border-bottom: 1px solid #f2f3f6;

                .picture-title {
                  font-weight: bold;
                  font-size: 14px;
                  width: 185px;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  cursor: pointer;
                }

                .picture-tag {
                  color: #999999;
                  font-size: 12px;
                  display: inline-block;
                  max-width: 150px;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }

                .picture-tags {
                  margin-bottom: 5px;
                  height: 24px;

                  .tagtype1 {
                    color: #0071fe;
                    max-width: 180px;
                    min-width: 42px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    &.taglen2 {
                      max-width: 95px;
                    }
                    &.taglen3 {
                      max-width: 70px;
                    }
                    &.taglenmore {
                      max-width: 60px;
                    }
                  }
                  .tagtype2 {
                    margin-left: 5px;
                    color: #909399;
                  }
                }

                .picture-time {
                  width: 150px;
                  color: #909399;
                  font-size: 14px;
                  white-space: pre-wrap;
                }
              }

              .picture-actions {
                display: flex;
                justify-content: space-around;
                align-items: center;
                padding: 10px;
                font-size: 14px;
                cursor: pointer;
                color: #89919a;
                .addline {
                  color: #e3e7ed;
                }
              }
            }
          }
        }
      }
    }
  }

  .el-upload--picture-card {
    width: 100px;
    height: 100px;
    line-height: 100px;

    i {
      font-size: 20px;
    }
  }

  .el-upload-list--picture-card .el-upload-list__item {
    width: 100px;
    height: 100px;
  }
  .popoverStyle {
    span {
      margin-top: 10px;
    }
    .tagtype1 {
      color: #0071fe;
      max-width: 180px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  html body .el-tag + .el-tag {
    margin-left: 5px;
  }
</style>
