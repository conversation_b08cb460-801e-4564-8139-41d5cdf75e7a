<!--
 * @Author: liqian liqian@123
 * @Date: 2025-04-01 15:43:11
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-06-24 12:13:13
 * @FilePath: \qst-merchant-admin-2.0\src\components\Public\SelectUploadImages\uploadImagesDialog\imagesDialog.vue
 * @Description: 图片分组选择弹窗 上传 编辑
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <!-- 图片分组弹窗 -->
    <el-dialog
      :title="dialoagName"
      width="700px"
      top="15vh"
      :visible.sync="pictureGroupDialog"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      append-to-body
    >
      <el-form :model="basicInfo" :rules="basicInfoRule" ref="basicInfo" :label-position="labelPosition">
        <el-form-item :label="pictureTitle" v-if="['Next'].includes(basicRloeBtn)" style="width: 100%">
          <el-cascader
            v-model="basicInfo.category_id"
            :options="categoryData"
            :props="{ checkStrictly: true, label: 'name', value: 'id' }"
            clearable
            style="width: 100%"
          ></el-cascader>
          <div v-if="['Next'].includes(basicRloeBtn)">不选择则创建为顶级分类</div>
        </el-form-item>
        <el-form-item
          :label="pictureTitle"
          prop="category_id"
          v-if="['Upload', 'EditPic', 'EditPicMore'].includes(basicRloeBtn)"
          style="width: 100%"
        >
          <el-cascader
            v-model="basicInfo.category_id"
            :options="categoryData"
            :props="{ checkStrictly: true, label: 'name', value: 'id' }"
            clearable
            style="width: 100%"
          ></el-cascader>
          <!-- <div v-if="['Next'].includes(basicRloeBtn)">不选择则创建为顶级分类</div> -->
        </el-form-item>
        <el-form-item label="分类名称" v-if="['ADD', 'Next', 'Edit'].includes(basicRloeBtn)" prop="name">
          <el-input type="text" v-model="basicInfo.name" placeholder="请输入分类名称"></el-input>
        </el-form-item>
        <el-form-item label="图片名称" v-if="['EditPic'].includes(basicRloeBtn)" prop="name">
          <el-input v-model="basicInfo.name" placeholder="请输入图片名称" clearable />
        </el-form-item>
        <el-form-item label="图片标签" v-if="['Upload', 'EditPic', 'EditPicMore'].includes(basicRloeBtn)" prop="label_name">
          <el-input v-model="basicInfo.label_name" placeholder="请输入图片标签，多个标签用逗号分隔" clearable />
          <div>
            <el-button type="text" v-for="item in showPictureList" :key="item" @click="showAddPicture(item)">{{ item }}</el-button>
            <!-- <el-button type="text" @click="showPicture('商品主题')">商品主题</el-button> -->
          </div>
        </el-form-item>
        <el-form-item label="本地上传" v-if="['Upload'].includes(basicRloeBtn)" prop="fileList">
          <!-- :file-list="basicInfo.fileList" -->
          <el-upload
            v-if="pictureGroupDialog"
            class="upload-demo"
            action="fakeaction"
            list-type="picture-card"
            multiple
            accept=".jpg, .png, .jpeg,"
            :file-list="basicInfo.fileList"
            :http-request="(e) => upLoadImg(e, 'license')"
            :before-upload="handelUpload"
          >
            <i class="el-icon-plus"></i>
            <div class="el-upload__tip" slot="tip">建议图片大小不超过3MB，宽度不超过750px，支持jpg、jpeg、png格式</div>
            <!-- 自定义文件列表项 -->
            <template #file="{ file }" v-if="basicInfo.fileList.length">
              <div class="image-item-wrapper" @mouseenter="setActiveFile(file.url)" @mouseleave="activeFileUid = null">
                <el-image
                  class="upload-image"
                  :src="file.url"
                  :ref="(el) => setImageRef(file.url, el)"
                  fit="cover"
                  :preview-src-list="previewSrcList"
                >
                  <template #error>
                    <div class="image-error">加载失败</div>
                  </template>
                </el-image>

                <div class="action-buttons" v-show="activeFileUid === file.url">
                  <el-button class="btn-preview" type="text" icon="el-icon-zoom-in" @click.stop="handlePreview(file, 'upload')"></el-button>
                  <el-button
                    class="btn-delete"
                    type="text"
                    icon="el-icon-delete"
                    @click.stop="handleRemoveFile(basicInfo.fileList, file, 'upload')"
                  ></el-button>
                </div>
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="网络下载" v-if="['Upload'].includes(basicRloeBtn)" prop="netWorkUrl">
          <el-input v-model="basicInfo.netWorkUrl" clearable type="text" placeholder="请输入图片链接">
            <el-button @click="uploadImages" class="uploadImagesDiv" slot="append" icon="el-icon-download">拉起图片</el-button>
          </el-input>
          <div
            class="networkimage"
            v-for="file in netWorkList"
            :key="file.url"
            @mouseenter="setActiveFile(file.url)"
            @mouseleave="activeFileUid = null"
          >
            <!-- 图片显示 -->
            <el-image
              class="upload-networkimage"
              :ref="(el) => setImageRef(file.url, el)"
              :src="file.url"
              fit="cover"
              :preview-src-list="previewSrcList"
            >
              <template #error>
                <div class="image-error">加载失败</div>
              </template>
            </el-image>
            <!-- 操作按钮容器 -->
            <div class="action-buttons" v-show="activeFileUid === file.url">
              <!-- 预览按钮 -->
              <el-button
                class="btn-preview"
                type="text"
                icon="el-icon-zoom-in"
                size="big"
                @click.stop="handlePreview(file, 'netWork')"
              ></el-button>
              <!-- 删除按钮 -->
              <el-button
                class="btn-delete"
                type="text"
                icon="el-icon-delete"
                size="big"
                @click.stop="handleRemoveFile(netWorkList, file, 'netWork')"
              ></el-button>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <base-dialog-footer @cancel="pictureGroupDialog = false" @confirm="confirm"></base-dialog-footer>
    </el-dialog>
  </div>
</template>

<script>
  import {
    getcreateImageApi,
    createCategoryApi,
    editCategoryApi,
    getImageUrlApi,
    getbatchEditImageApi,
    getLabelListApi,
  } from '@/api/shop/goodsSet/pictureManagement'

  export default {
    name: 'uploadImages',
    props: {
      basicRloeBtn: {
        // 权限按钮//'ADD', 'Edit', 'Upload', 'EditPic', 'EditPicMore'
        type: String,
        default: 'ADD',
      },
      // 选择的图片列表
      selcategoryData: {
        type: Array,
        default: () => [],
      },
      //点击的树节点
      currentTreeLi: {
        type: Object,
        default: () => {},
      },
      //批量编辑的图片
      pictureListChecked: {
        type: Array,
        default: () => [],
      },
      pictureTitle: {
        type: String,
        default: '父级分类',
      },
      dialoagName: {
        type: String,
        default: '新增分类',
      },
      categoryData: {
        type: Array,
        default: () => [],
      },
      editNodeName: {
        type: String,
        default: '',
      },
      currentEditPic: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        netWorkList: [], //网络图片
        imageRefMap: new Map(), // 存储文件 UID 和组件实例的映射
        activeFileUid: null, // 当前激活的文件UID
        previewSrcList: [], //预览图片数据
        basicInfo: {
          name: '', // 分类名称,图片名称
          category_id: '', // 分类地址
          label_name: '', // 图片标签
          address3: '', // 本地上传图片地址
          fileList: [], //上传的图片
          netWorkUrl: '', // 网络下载图片地址
        },
        labelPosition: 'top',
        pictureGroupDialog: false,
        basicInfoRule: {
          category_id: [{ required: true, message: '请选择图片分类', trigger: 'change' }],
          name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
          selcategoryData: [{ required: true, message: '请输入图片分类 ', trigger: 'blur' }],
          fileList: [{ required: false, message: '请上传图片 ', trigger: 'blur' }],
        },
        showPictureList: [],
      }
    },
    watch: {
      currentEditPic: {
        handler(newVal) {
          this.currentEditPic = newVal
          this.basicInfo.label_name = newVal.label.join()
        },
        deep: true,
      },
      pictureListChecked: {
        handler(newVal) {
          if (newVal.length != 0) {
            this.basicInfo.label_name = ''
          }
        },
        deep: true,
      },
      selcategoryData: {
        handler(newVal) {
          if (newVal.length != 0) {
            this.basicInfo.category_id = newVal
          } else {
            this.basicInfo.category_id = ''
          }
        },
        deep: true,
      },
    },
    created() {},
    methods: {
      openimagesDialog() {
        this.basicInfo.fileList = []
        this.showPicture()
        this.$nextTick(() => {
          this.pictureGroupDialog = true
          if (this.basicRloeBtn == 'Edit') {
            this.basicInfo.name = this.editNodeName
          }
          if (this.basicRloeBtn == 'EditPic') {
            this.basicInfo.name = this.currentEditPic.name
          }
          if (['ADD', 'Next', 'Upload'].includes(this.basicRloeBtn)) {
            this.basicInfo.label_name = ''
            this.basicInfo.name = ''
            this.basicInfo.netWorkUrl = ''
            this.basicInfo.fileList = []
            this.netWorkList = []
          }
        })
      },
      confirm() {
        this.$refs.basicInfo.validate((valid) => {
          if (valid) {
            if (this.basicRloeBtn == 'Upload') {
              this.uploadCategory()
            }
            if (this.basicRloeBtn == 'ADD') {
              let data = {
                pid: '',
                name: this.basicInfo.name,
                sort: 1,
              }
              this.createCategory(data)
            }
            if (this.basicRloeBtn == 'EditPic') {
              this.editPicMoreCategory()
            }
            if (this.basicRloeBtn == 'EditPicMore') {
              this.editPicMoreCategory()
            }
            if (this.basicRloeBtn == 'Edit') {
              this.editCategory()
            }

            if (this.basicRloeBtn == 'Next') {
              let data = {
                pid: this.basicInfo.category_id[this.basicInfo.category_id.length - 1],
                name: this.basicInfo.name,
                sort: 1,
              }
              this.createCategory(data)
            }
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
      cancel() {
        this.basicInfo.fileList = []
        this.pictureGroupDialog = false
        this.$emit('confirmBtn')
      },
      // 新增图片分类
      createCategory(data) {
        createCategoryApi(data).then((res) => {
          if (res.code == 200) {
            this.$message.success('新增分类成功')
            this.cancel()
          }
        })
      },
      // 编辑图片分类
      editCategory() {
        editCategoryApi({
          pid: this.currentTreeLi.pid,
          name: this.basicInfo.name,
          id: this.currentTreeLi.id,
          sort: 1,
        }).then((res) => {
          if (res.code == 200) {
            this.$message.success('编辑分类成功')
            this.cancel()
          }
        })
      },
      // 编辑图片 批量编辑图片
      editPicMoreCategory() {
        let editPicImages = ''
        if (this.basicRloeBtn == 'EditPic') {
          editPicImages = [this.currentEditPic.resource_uid]
        }
        if (this.basicRloeBtn == 'EditPicMore') {
          editPicImages = this.pictureListChecked.map((item) => item.resource_uid)
        }
        let params = {
          name: this.basicInfo.name,
          category_id: this.basicInfo.category_id[this.basicInfo.category_id.length - 1],
          label_name:
            this.basicInfo.label_name != ''
              ? this.basicInfo.label_name
                  .split(/[，,]+/) // 直接按中英文逗号分割
                  .map((s) => s.trim())
                  .filter((s) => s)
              : [],
          images: editPicImages,
        }
        getbatchEditImageApi(params).then((res) => {
          if (res.code == 200) {
            this.$message.success('编辑图片成功')
            this.cancel()
          }
        })
      },

      // 上传图片
      uploadCategory() {
        let fileListUnique = this.basicInfo.fileList.length > 0 ? this.basicInfo.fileList.map((item) => item.unique_id) : []
        let netWorkListUnique = this.netWorkList.length > 0 ? this.netWorkList.map((item) => item.unique_id) : []
        let params = {
          category_id: this.basicInfo.category_id[this.basicInfo.category_id.length - 1],
          label_name:
            this.basicInfo.label_name != ''
              ? this.basicInfo.label_name
                  .split(/[，,]+/) // 直接按中英文逗号分割
                  .map((s) => s.trim())
                  .filter((s) => s)
              : '',
          images: fileListUnique.concat(netWorkListUnique),
        }
        getcreateImageApi(params).then((res) => {
          if (res.code == 200) {
            this.$message.success('上传图片成功')
            this.cancel()
          }
        })
      },
      //拉取图片
      uploadImages() {
        getImageUrlApi({ image_url: this.basicInfo.netWorkUrl }).then((res) => {
          if (res.code == 200) {
            this.$message.success('拉取图片成功')
            this.netWorkList.push({
              name: res.data.name,
              url: res.data.url,
              unique_id: res.data.unique_id,
            })
          }
        })
      },
      showPicture() {
        getLabelListApi().then((res) => {
          if (res.code == 200) {
            this.showPictureList = res.data
          }
          console.log(res)
        })
      },
      showAddPicture(tip) {
        if (this.basicInfo.label_name == '') {
          this.basicInfo.label_name += tip + ','
          this.basicInfo.label_name = this.basicInfo.label_name.substring(0, this.basicInfo.label_name.length - 1)
        } else {
          this.basicInfo.label_name += ',' + tip
        }
      },
      upLoadImg(file, key) {
        // 上传
        this.$upLoadImg(file.file).then((res) => {
          this.basicInfo.fileList.push({
            name: file.file.name,
            url: res.data.url,
            uid: file.file.uid,
            unique_id: res.data.unique_id,
          })
        })
      },

      //上传校验
      handelUpload(file) {
        if (file.size > 3 * 1024 * 1024) {
          this.$message.error('图片大小不能超过3MB！')
          return false
        }
        const index = file.name.lastIndexOf('.')
        const suffix = file.name.substring(index, file.name.length)
        console.log('suffix', suffix)
        if (!this.checkSuffix(suffix)) {
          this.$message.error('上传图片只能是jpg,png,jpeg格式!')
          return false
        }
      },
      checkSuffix(str) {
        var strRegex = /\.(jpg|png|jpeg)$/
        if (strRegex.test(str.toLowerCase())) {
          return true
        } else {
          return false
        }
      },
      // 设置当前激活文件
      setActiveFile(uid) {
        this.activeFileUid = uid
      },

      // 删除文件
      handleRemoveFile(picList, file, flag) {
        const index =
          flag == 'upload' ? picList.findIndex((f) => f.uid === file.uid) : picList.findIndex((f) => f.unique_id === file.unique_id)

        if (index !== -1) {
          flag == 'upload' ? this.basicInfo.fileList.splice(index, 1) : this.netWorkList.splice(index, 1)
        }
      },
      setImageRef(uid, el) {
        if (el) {
          this.imageRefMap.set(uid, el)
        } else {
          this.imageRefMap.delete(uid)
        }
      },
      // 预览处理
      handlePreview(file, flag) {
        this.previewSrcList = flag == 'upload' ? this.basicInfo.fileList.map((item) => item.url) : this.netWorkList.map((item) => item.url)
        // 通过DOM操作触发el-image的预览
        const imageComponent = this.imageRefMap.get(file.url)

        this.$nextTick(() => {
          if (imageComponent) {
            imageComponent.clickHandler()
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  html body {
    .el-dialog__body {
      padding: 10px 20px;
      .uploadImagesDiv {
        color: #fff;
        background: #0071fe;
        border-radius: 0px 4px 4px 0px;
      }
    }
  }
  /* 文件项容器 */
  .image-item-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    /* 操作按钮容器 */
    .action-buttons {
      display: flex;
      justify-content: center;
      position: absolute;
      width: 100%;
      height: 100%;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      gap: 12px;
      opacity: 0; /* 默认透明 */
      transition: opacity 0.3s ease;
      background: rgba(0, 0, 0, 0.5);
      padding: 8px 16px;
      border-radius: 4px;
      /* 按钮样式 */
      .el-button {
        color: white !important;
        font-size: 20px;
      }
    }
  }
  /* 鼠标悬停时显示按钮 */
  .image-item-wrapper:hover .action-buttons {
    opacity: 1;
  }

  /* 图片hover效果 */
  // .image-item-wrapper:hover .upload-image {
  //   transform: scale(1.05);
  //   transition: transform 0.3s ease;
  // }
  /* 隐藏默认删除按钮 */

  .el-upload-list__item .el-icon-close {
    display: none !important;
  }

  /* 图片显示样式 */
  .upload-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
  }
  .networkimage {
    position: relative;
    overflow: hidden;
    background-color: #fff;
    border: 1px solid #c0ccda;
    border-radius: 6px;
    box-sizing: border-box;
    width: 100px;
    height: 100px;
    margin: 10px 8px 8px 0;
    display: inline-block;
    .upload-networkimage {
      width: 100%;
      height: 100%;
    }
    /* 操作按钮容器 */
    .action-buttons {
      display: flex;
      justify-content: center;
      position: absolute;
      width: 100%;
      height: 100%;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      gap: 12px;
      opacity: 0; /* 默认透明 */
      transition: opacity 0.3s ease;
      background: rgba(0, 0, 0, 0.5);
      padding: 8px 16px;
      border-radius: 4px;
      /* 按钮样式 */
      .el-button {
        color: white !important;
        font-size: 20px;
      }
    }
  }
  /* 鼠标悬停时显示按钮 */
  .networkimage:hover .action-buttons {
    opacity: 1;
  }
</style>
