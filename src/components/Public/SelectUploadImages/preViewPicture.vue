<template>
  <div class="image-item-wrapper" @mouseenter="setActiveFile(file.url)" @mouseleave="activeFileUid = null">
    <!-- 图片显示 -->
    <el-image class="upload-image" :src="file.url" :ref="(el) => setImageRef(file.url, el)" fit="cover" :preview-src-list="previewSrcList">
      <template #error>
        <div class="image-error">加载失败</div>
      </template>
    </el-image>

    <!-- 操作按钮容器（增加v-show条件） -->
    <div class="action-buttons" v-show="activeFileUid === file.url">
      <!-- 预览按钮 -->
      <el-button class="btn-preview" type="text" icon="el-icon-zoom-in" @click.stop="handlePreview(file)"></el-button>
      <!-- 删除按钮 -->
      <el-button class="btn-delete" type="text" icon="el-icon-delete" @click.stop="handleRemoveFile(preViewInfo, file)"></el-button>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'preViewPicture',
    props: {
      file: {
        // 当前的图片'
        type: Object,
        default: () => {},
      },
      preViewInfo: {
        type: Array,
        default: () => [],
      }, //预览图片数据
    },
    data() {
      return {
        activeFileUid: '',
        imageRefMap: new Map(), // 存储文件 UID 和组件实例的映射
        previewSrcList: [],
      }
    },
    methods: {
      // 设置当前激活文件
      setActiveFile(uid) {
        this.activeFileUid = uid
      },

      // 删除文件
      handleRemoveFile(picList, file) {
        const index = picList.findIndex((f) => f.uid === file.uid)
        if (index !== -1) {
          // flag == 'upload' ? this.basicInfo.fileList.splice(index, 1) : this.netWorkList.splice(index, 1)
          this.preViewInfo.splice(index, 1)
        }
      },
      setImageRef(uid, el) {
        if (el) {
          this.imageRefMap.set(uid, el)
        } else {
          this.imageRefMap.delete(uid)
        }
      },
      // 预览处理
      handlePreview(file) {
        this.previewSrcList = this.preViewInfo.map((item) => item.url)
        // this.previewSrcList = flag == 'upload' ? this.basicInfo.fileList.map((item) => item.url) : this.netWorkList.map((item) => item.url)
        // 通过DOM操作触发el-image的预览
        const imageComponent = this.imageRefMap.get(file.url)
        this.$nextTick(() => {
          if (imageComponent) {
            imageComponent.clickHandler()
          }
        })
      },
    },
  }
</script>
