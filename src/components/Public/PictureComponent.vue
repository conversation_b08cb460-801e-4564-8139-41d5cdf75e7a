<template>
  <div class>
    <div
      @mouseenter="mouseover(imgParams)"
      @mouseleave="viewImg = null"
      v-if="typeof imgParams === 'string'"
      class="ui_postion"
      :style="{ width: imgWidth, height: imgHeight }"
    >
      <img
        class="ui_img"
        :style="{ width: imgWidth, height: imgHeight }"
        :src="uploadType != 'pdf' ? imgParams : originPdfSrc"
        alt
      />

      <div class="ui_mask" v-if="viewImg == imgParams">
        <div class="ui_line" @click.stop="preview(imgParams)">预览</div>
        <div class="ui_kill"></div>
        <div @click="deleteImg(imgIndex)">删除</div>
      </div>
    </div>
    <div v-else class="ui_flex">
      <VueDraggable class="flex-draggable ui_flex" v-model="imgParams" group="items">
        <div
          v-for="(item, index) in imgParams"
          class="ui_postion ui_flex_item"
          :style="{ width: imgWidth, height: imgHeight }"
          :key="item"
          @mouseenter="mouseover(index)"
          @mouseleave="viewImg = ''"
        >
          <img class="ui_img" :src="item" alt />
          <div class="ui_mask" v-if="viewImg === index">
            <div class="ui_line" @click.stop="preview(item, index)">预览</div>
            <div class="ui_kill"></div>
            <div @click="deleteImg(index)">删除</div>
          </div>
        </div>
      </VueDraggable>
    </div>
    <div ref="viewer" v-viewer="viewerOptions">
      <img style="display: none" v-for="(src, index) in currentImages" :src="src" :key="index" />
    </div>
    <pdfViewer ref="pdfviewer" :srcList="fileUrlSrc"></pdfViewer>
  </div>
</template>

<script>
import { VueDraggable } from 'vue-draggable-plus'
import pdfViewer from '@/components/pdf-viewer/index'
import pdf from 'vue-pdf'
export default {
  name: 'PictureComponent',
  components: {
    VueDraggable,
    pdfViewer,
    pdf,
  },
  props: {
    imgIndex: {
      type: Number,
      default: 0,
    },
    imgParams: {
      type: [String, Array], //图片地址，可以是字符串或者数组
      default: '',
      required: true,
    },
    // 图片预览数组，可以是字符串或者数组
    imgViewer: {},
    imgWidth: {
      type: [String, Number],
      default: '100px',
    },
    imgHeight: {
      type: [String, Number],
      default: '100px',
    },
    //文件后缀类型
    uploadType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      previewUrl: '',
      srcList: [],
      viewerOptions: {
        toolbar: true, //是否显示工具栏。工具栏通常包含一些操作按钮，如缩放、旋转等。
        title: true, //是否显示图片的标题。标题通常显示在图片的上方或下方。
        navbar: true, // 是否显示导航栏。导航栏允许用户在不同图片之间切换。
        button: true, //是否显示按钮（如关闭按钮）
        loop: true, //是否启用循环播放。当启用时，用户可以无缝地在最后一张图片和第一张图片之间切换。
        movable: false, //是否允许拖动图片。
        zoomable: true, //是否允许缩放图片。
        rotatable: true, //是否允许旋转图片。
        scalable: true, //否允许用户通过手势（如捏合）来缩放图片。请注意，这与 zoomable 不同，zoomable 允许通过按钮或工具栏来缩放图片。
        transition: true, // 是否启用过渡动画。当在图片之间切换或进行其他操作时，过渡动画可以提供更平滑的用户体验。
        fullscreen: true, //是否允许进入全屏模式。在全屏模式下，用户可以更专注地查看图片。
        keyboard: true, //否允许使用键盘快捷键来控制图片查看器。例如，使用左右箭头键来切换图片
      },
      currentImages: [],
      currentArrary: [],
      isMask: false,
      viewImg: '',
      // imgParams: [],
      fileUrlSrc: '', //pdf预览地址
      originPdfSrc: require('../../assets/pdf_20250716.png'),
    }
  },
  created() {},
  mounted() {},
  methods: {
    arraysEqual(a, b) {
      return JSON.stringify(a) === JSON.stringify(b)
    },
    mouseover(i) {
      this.viewImg = i
    },
    mouseout() {
      // this.viewImg = null
    },
    deleteImg(index) {
      this.$emit('deleteImg', index)
      this.viewImg = null
    },
    getType(value) {
      if (value instanceof Number) {
        return 'number'
      } else if (value instanceof String) {
        return 'string'
      } else if (value instanceof Boolean) {
        return 'boolean'
      } else if (value instanceof Array) {
        return 'array'
      } else if (value instanceof Object) {
        return 'object'
      } else if (value instanceof Function) {
        return 'function'
      }
    },
    preview(url, i) {
      console.log(url)
      let urlType = url.split('.')[4]
      if (urlType == 'pdf') {
        this.fileUrlSrc = url
        this.$nextTick(() => {
          this.$refs.pdfviewer.open()
        })
        // window.open(this.fileUrlSrc)
        return
      }
      this.viewImg = null
      if (typeof this.imgParams === 'string') {
        this.$nextTick(() => {
          const viewer = this.$refs.viewer.$viewer
          if (viewer) {
            this.currentImages = [url]
            viewer.show()
          }
        })
      } else {
        this.$nextTick(() => {
          const viewer = this.$refs.viewer.$viewer
          if (viewer) {
            viewer.view(i)
          }
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.ui_img {
  flex-shrink: 0;
  object-fit: contain;
}
.ui_flex {
  display: flex;
}
.ui_postion {
  position: relative;
  flex-shrink: 0;
}
.ui_flex_item {
  margin-right: 10px;
}
.ui_mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 6px;
  font-size: 14px;
  color: #ffffff;
  line-height: 14px;
  cursor: pointer;
}
img {
  border-radius: 6px;
  width: 100%;
  height: 100%;
}
.ui_kill {
  margin: 0 8px;
}
.ui_line {
  position: relative;
}
.ui_line::after {
  content: '';
  position: absolute;
  top: 2px;
  right: -8px;
  width: 1px;
  height: 9px;
  background: #ffffff;
}
</style>
