<!-- 判断是否有权限 -->
 <template>
  <div>
    <div class="no-tip" v-if="!isShowPage">
      <div v-if="account_shop_status == 2">
        <div style="margin-bottom: 10px">{{ account_shop_status_tip[account_shop_status] }}</div>
        <el-button @click="$router.push('/storeManagement/index')" type="primary">马上创建</el-button>
      </div>
      <span v-else>{{ account_shop_status_tip[account_shop_status] }}</span>
    </div>
  </div>
</template>

 <script>
import { checkAccountShop } from '@/api/common.js'
export default {
  name: 'BaseAccountShop',
  data() {
    return {
      isShowPage: true,
      // 加载数据
      account_shop_status: '', //"account_shop_status": 1 //1、正常 2、未创建店铺 3、无店铺权限 4、店铺被禁用
      account_shop_status_tip: [
        '',
        '',
        '使用此功能需先开通店铺，马上创建？',
        '抱歉，您暂无店铺权限无法正常使用该功能，请联系管理员协助开通！',
        '抱歉，您暂无店铺权限无法正常使用该功能，请联系管理员协助开通！',
      ],
    }
  },
  mounted() {
    this.checkAccountShopFn()
  },
  methods: {
    checkAccountShopFn() {
      checkAccountShop().then((res) => {
        if (res.code == 200) {
          console.log('店铺权限', res.data)
          this.account_shop_status = res.data.account_shop_status
          let { goods_library_status, account_shop_status } = res.data
          this.isShowPage =
            goods_library_status == 1 || (goods_library_status == 2 && account_shop_status == 1)
          this.$emit('checkAccountShopFn', this.isShowPage)
        }
      })
    },
  },
}
</script>