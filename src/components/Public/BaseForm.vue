<!--
 * @Author: shang<PERSON>i <EMAIL>
 * @Date: 2025-03-04 16:40:25
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-19 11:14:30
 * @FilePath: \qst-merchant-admin-2.0\src\components\Public\BaseForm.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="form-model" :class="dialogClass">
    <el-form
      ref="form"
      :inline="true"
      :model="form"
      :label-width="labelWidth"
      :label-position="labelPosition"
    >
      <el-form-item :label="item.label" v-for="item in formArray" :key="item.key">
        <el-input
          class="input-with-search"
          v-if="item.type == 'input'"
          v-model="form[item.key]"
          :placeholder="item.placeholder"
          :suffix-icon="item.icon ? item.icon : ''"
        ></el-input>
        <!-- el-icon-search -->

        <el-select
          class="input-with-search"
          @change="(e) => changeStore(e, item)"
          v-if="item.type == 'select'"
          v-model="form[item.key]"
          :placeholder="item.placeholder"
        >
          <el-option
            v-for="it in item.options"
            :key="it[item.props?.value || 'value']"
            :label="it[item.props?.label || 'label']"
            :value="it[item.props?.value || 'value']"
          ></el-option>
        </el-select>

        <!-- 联动操作 -->
        <el-cascader
          v-if="item.type == 'cascader'"
          v-model="form[item.key]"
          :props="item.props"
          :options="item.options"
          :placeholder="item.placeholder"
        ></el-cascader>

        <el-date-picker
          v-if="item.type == 'time'"
          v-model="form[item.key]"
          :type="item.timeType || 'datetimerange'"
          :value-format="item.valueFormat || 'yyyy-MM-dd HH:mm:ss'"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>

        <StoreHeader
          :searchType="1"
          v-if="item.type == 'store'"
          @changeStore="(e) => changeStore(e, item)"
          ref="storeHeader"
        ></StoreHeader>
      </el-form-item>
      <slot></slot>

      <el-form-item
        :label="labelWidth == '0px' ? '' : '操作'"
        style="min-width: 130px"
        class="base-option"
      >
        <el-button @click="resetForm">重置</el-button>
        <el-button type="primary" @click="searchForm">搜索</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import StoreHeader from './StoreHeader.vue'
  export default {
    name: 'BaseForm',
    components: {
      StoreHeader,
    },
    props: {
      dialogClass: {
        type: String,
        default: '',
      },
      labelWidth: {
        type: String,
        default: '90px',
      },
      labelPosition: {
        type: String,
        default: 'right',
      },
      formArray: {
        type: Array,
        default() {
          return [
            /**
             * @description:
             * @param {
             *   type: input 输入框 select 下拉框 time 时间选择器
             *   key: 字段名
             *   placeholder: 占位符
             *   options: 下拉框选项 [{label, value}]
             *   timeKey: 时间选择器字段名数组 ['startTime', 'endTime']
             * }
             *
             * @return {*}
             */
            // {
            //   label: '用户名',
            //   type: 'input',
            //   key: 'username',
            //   placeholder: '请输入用户名',
            // },
            // {
            //   label: '所属行业',
            //   type: 'select',
            //   key: 'industry',
            //   placeholder: '全部',
            //   options: [
            //     { label: '全部', value: '' },
            //     { label: '餐饮', value: '1' },
            //     { label: '服装', value: '2' },
            //     { label: '百货', value: '3' },
            //   ],
            // },
            // {
            //   label: '申请时间',
            //   type: 'time',
            //   key: 'time',
            //   timeKey: ['startTime', 'endTime'],
            // },
          ]
        },
      },
      tableForm: {
        type: Object,
        default() {
          return {}
        },
      },
    },
    watch: {
      tableForm: {
        deep: true,
        immediate: true,
        handler(val) {
          console.log('加载数据', val)
          // 店铺id 特殊处理，如果父组件传了店铺id，则子组件也要有店铺id，反之亦然
          if (this.tableForm.search_shop_id == '' && this.form.search_shop_id) {
            this.tableForm.search_shop_id = this.form.search_shop_id
          }
          if (!this.searchInit) {
            this.searchInit = {}
            this.formArray.map((item) => {
              this.searchInit[item.key] = val[item.key] || ''
            })
          }

          this.form = Object.assign({}, this.tableForm)
        },
      },
    },
    data() {
      return {
        form: {},
        isResetFields: false,
        searchInit: null, // 初始化搜索对象，用于重置表单时清空数据
      }
    },

    methods: {
      // 重置表单
      resetForm() {
        this.form = JSON.parse(JSON.stringify(this.searchInit))
        this.isResetFields = true
        this.$refs.form.resetFields()
        if (this.$refs.storeHeader) {
          this.$refs.storeHeader[0].resetStore()
        }
      },
      // 搜索表单
      searchForm() {
        // 时间选择器特殊处理，将时间选择器的时间格式化成字符串
        this.formArray.map((item) => {
          if (item.type == 'time') {
            if (
              this.form[item.key] &&
              this.form[item.key].length == 2 &&
              this.form[item.key][0] &&
              this.form[item.key][1]
            ) {
              this.form[item.timeKey[0]] = this.form[item.key][0]
              this.form[item.timeKey[1]] = this.form[item.key][1]
            } else {
              this.form[item.timeKey[0]] = ''
              this.form[item.timeKey[1]] = ''
            }
          }
        })
        this.form.tableTime = new Date().getTime()
        this.$emit('searchForm', this.form, this.isResetFields)
        this.isResetFields = false
      },
      // 选择店铺回调
      changeStore(e, item) {
        console.log(11)
        this.form[item.key] = e
        console.log(this.form)
        if (item.callback) {
          item.callback(e)
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .form-model {
    border-bottom: 1px solid #e8e8e8;
  }
  .input-with-search {
    width: 100%;
  }

  ::v-deep {
    .el-input__inner {
      width: 220px;
    }
    .base-option .el-form-item__label {
      color: #fff;
      opacity: 0;
    }
    html body .el-dialog__body .el-form,
    html body .el-message-box__body .el-form {
      padding-right: 0px;
    }
  }
  // 关联商品表单样式调整
  .associationShopTem ::v-deep {
    .el-input__inner {
      width: 140px;
    }
  }

  ::v-deep {
    .el-input__inner {
      width: 220px;
    }
  }
</style>
