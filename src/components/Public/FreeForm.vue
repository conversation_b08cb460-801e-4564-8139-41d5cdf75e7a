<template>
  <!-- 表单组件核心代码 -->
  <el-form class="freeForm" :ref="formRef" :model="model" v-bind="$attrs" :rules="rules">
    <el-row :gutter="24">
      <!-- 显示hidden为false的表单项 -->
      <el-col
        v-for="(item, index) in formItemConfig"
        :key="index"
        :xs="item.span"
        :sm="item.span"
        :md="item.span"
        :lg="item.span"
        :xl="item.span"
        v-show="!item.hidden"
      >
        <div class="freeFormItem">
          <!-- 处理标题 -->
          <p v-if="item.title" class="cgtitle">{{ item.title }}</p>

          <el-form-item
            v-else
            :label="item.label"
            :rules="item.rules"
            :prop="item.prop"
            style="width: 100%"
          >
            <!-- 动态渲染组件 -->
            <component
              :disabled="item.disabled"
              v-if="item.type != 'upload'"
              v-model="model[item.prop]"
              v-bind="item"
              :is="isComponentName(item)"
              :placeholder="placeholder(item)"
              :style="{ width: item.width }"
              @input="changeValue(item, $event)"
              @click="handleClick(item, $event)"
              @change="handleChange(item, $event)"
            />
            <!-- 动态渲染上传组件 -->
            <component
              v-if="item.type == 'upload'"
              v-model="model"
              v-bind="item"
              :is="isComponentName(item)"
              :placeholder="placeholder(item)"
              :style="{ width: item.width }"
              @input="changeValue(item, $event)"
              @click="handleClick(item, $event)"
              @change="handleChange(item, $event)"
            />
          </el-form-item>
        </div>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
  /**
   * @desc 表单组件
   * @param {Object} formRef - el-form 的 ref 名称
   * @param {Object} model - 表单数据模型
   * @param {Object} formItemConfig - el-form-item 配置项
   * @param {Object} rules - el-form-item 验证规则
   */
  export default {
    name: 'FreeForm',
    props: {
      // 表单引用名称
      formRef: {
        type: String,
        default: 'formRef',
      },
      // 表单数据模型
      model: {
        type: Object,
        default: () => ({}),
      },
      // 表单项配置
      formItemConfig: {
        type: Array,
        default: () => [],
      },
      // 表单验证规则
      rules: {
        type: Object,
        default: () => ({}),
      },
      modelCode: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        that: this,
      }
    },
    computed: {
      /**
       * 根据组件类型获取需要渲染的组件名称
       */
      isComponentName() {
        return (item) => {
          if (item.component === 'el-select') {
            return 'SelectForm'
          } else if (item.component === 'radio') {
            return 'RadioGroupForm'
          } else if (item.component === 'checkbox') {
            return 'CheckboxGroupForm'
          } else {
            return item.component || 'el-input'
          }
        }
      },
      /**
       * 根据表单项配置获取占位符
       */
      placeholder() {
        return (item) => {
          if (item.placeholder) return item.placeholder
          const arr = ['el-input', 'el-input-number']
          return !item.component || arr.includes(item.component)
            ? `请输入${item.label || ''}`
            : `请选择${item.label || ''}`
        }
      },
    },
    methods: {
      /**
       * 验证表单并执行回调函数
       * @param {Function} cb - 表单验证通过后的回调函数
       * @returns {boolean} - 表单验证结果
       */
      validate(cb) {
        this.$refs[this.formRef].validate((valid) => {
          cb(valid, this.model)

          if (valid) {
            // 如果表单验证通过，执行提交操作
          } else {
            // 如果表单验证失败，处理失败情况
            return false
          }
        })
      },
      /**
       * 处理表单项的点击事件
       * @param {Object} item - 当前点击的表单项配置
       */
      handleClick(item, e) {
        // 处理数据改变的逻辑
        item.onClick ? item.onClick(e) : () => {}
      },
      //change型式的回调
      handleChange(item, e) {
        item.onChange ? item.onChange(e, this.model) : () => {}
      },
      /**
       * 更新表单数据模型到父组件
       */
      changeValue(item, e) {
        this.$emit('input', e)
      },
    },
    filters: {
      getPropVal(item, that) {
        if (item?.type == 'upload') {
          return that.model
        } else {
          return that.model[item.prop]
        }
      },
    },
  }
</script>
