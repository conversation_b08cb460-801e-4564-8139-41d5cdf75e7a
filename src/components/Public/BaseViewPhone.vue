<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-03-14 13:56:58
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-24 09:24:46
 * @FilePath: /qst-merchant-admin-2.0/src/components/Public/BaseViewPhone.vue
 * @Description: 查看手机号
-->
<template>
  <div>
    <el-button @click="openPhone" type="text">{{phone}}</el-button>
    <el-dialog title="手机号" :visible.sync="dialogVisible" :close-on-click-modal="false">
      <div class="view-phone">{{modelPhone}}</div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'BaseViewPhone',
  props: {
    phone: {
      type: String,
      default: '',
    },
    funKey: {
      type: Object,
      default: () => {},
    },
    row: {
      type: Object,
      default: () => {},
    },
    fun: {
      type: Function,
    },
  },
  data() {
    return {
      dialogVisible: false,
      modelPhone: '',
    }
  },
  methods: {
    openPhone() {
      let params = {}
      let keys = Object.keys(this.funKey)
      keys.map((item) => {
        params[item] = this.row[this.funKey[item]]
      })
      console.log(params)
      this.fun(params).then((res) => {
        if (res.code === 200) {
          this.dialogVisible = true
          this.modelPhone = res.data.phone
        }
      })
    },
  },
  mounted() {},
}
</script>

<style lang="scss" scoped>
.view-phone {
  text-align: center;
}
</style>