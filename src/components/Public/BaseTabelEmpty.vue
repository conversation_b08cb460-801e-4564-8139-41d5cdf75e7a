<template>
  <div class="null">
    <img :src="image" alt="" />
    <span>{{ content }}</span>
  </div>
</template>
<script>
  export default {
    name: 'BaseTabelEmpty',
    props: {
      content: {
        type: String,
        default: '暂无内容',
      },
    },
    data() {
      return {
        image: require('../../assets/nulldata.png'),
      }
    },
  }
</script>
<style lang="scss">
  .null {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
</style>
