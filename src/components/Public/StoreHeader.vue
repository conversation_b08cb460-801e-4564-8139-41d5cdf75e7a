<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-04-01 14:17:25
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-26 11:21:41
 * @FilePath: \qst-merchant-admin-2.0\src\components\Public\StoreHeader.vue
 * @Description: 切换门店头部组件
-->
<template>
  <div v-if="searchType == 0">
    <div class="no-tip" v-if="account_shop_status != 1 && !loading">
      <div v-if="account_shop_status == 2">
        <div style="margin-bottom: 10px">{{ account_shop_status_tip[account_shop_status] }}</div>
        <el-button @click="$router.push('/storeManagement/index')" type="primary">马上创建</el-button>
      </div>
      <span v-else>{{ account_shop_status_tip[account_shop_status] }}</span>
    </div>

    <div class="more-store flex" v-if="storeType == 1">
      <div
        class="store-item"
        :class="key == item.shop_id ? 'key' : ''"
        @click="selectKey(item.shop_id)"
        v-for="(item, index) in storeList"
        :key="index"
      >{{ item.name }}</div>
    </div>

    <div class="once-store" v-if="storeType == 2">
      <el-form>
        <el-form-item label="切换门店">
          <el-select
            v-model="key"
            filterable
            remote
            reserve-keyword
            placeholder="请输入关键词"
            :remote-method="remoteMethod"
            :loading="loading"
            @change="selectKey($event)"
          >
            <template #default v-if="storeName == ''">
              <el-option
                v-for="item in storeList"
                :key="item.shop_id"
                :label="item.name"
                :value="item.shop_id"
              ></el-option>
            </template>
            <template #default v-else>
              <el-option
                v-for="item in storeFilterList"
                :key="item.shop_id"
                :label="item.name"
                :value="item.shop_id"
              ></el-option>
            </template>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
  </div>

  <!-- 用于商品详情 模块 1 用于搜索  2 用于商品详情创建 -->
  <div v-else>
    <el-select
      v-model="key"
      filterable
      remote
      reserve-keyword
      placeholder="请选择所属门店"
      :remote-method="remoteMethod"
      :loading="loading"
      @change="selectKey($event)"
      style="width: 100%"
      :disabled="disabled"
    >
      <template #default v-if="storeName == ''">
        <el-option
          v-for="item in storeList"
          :key="item.shop_id"
          :label="item.name"
          :value="item.shop_id"
        ></el-option>
      </template>
      <template #default v-else>
        <el-option
          v-for="item in storeFilterList"
          :key="item.shop_id"
          :label="item.name"
          :value="item.shop_id"
        ></el-option>
      </template>
    </el-select>
  </div>
</template>

<script>
import { shopListApi, checkAccountShop } from '@/api/common.js'
import { getCurrentCreateGoodsShopApi } from '@/api/shop/goodsStore.js'

export default {
  name: 'StoreHeader',
  props: {
    storeId: {
      type: String | Number,
      default: 0,
    },

    // 0 商品设置  1 用于搜索  2 用于商品详情创建
    searchType: {
      type: Number,
      default: 0,
    },

    // 是否禁用 仅用于商品详情创建
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    storeId: {
      immediate: true,
      handler(val) {
        if (val == 0) return
        this.key = val
      },
    },
  },
  data() {
    return {
      key: '',
      storeList: [],
      storeFilterList: [],
      options: [],
      value: [],
      list: [],
      loading: false,
      page: 1,

      // 门店类型 1 平铺  2 下拉选择
      storeType: 0,
      storeName: '',
      account_shop_status: '', //"account_shop_status": 1 //1、正常 2、未创建门店 3、无门店权限 4、门店被禁用
      account_shop_status_tip: [
        '',
        '',
        '使用此功能需先开通门店，马上创建？',
        '抱歉，您暂无门店权限无法正常使用该功能，请联系管理员协助开通！',
        '抱歉，您暂无门店权限无法正常使用该功能，请联系管理员协助开通！',
      ],
    }
  },
  async mounted() {
    this.loading = true
    await this.initStore()
    await this.checkAccountShopFn()
  },
  methods: {
    remoteMethod(e) {
      this.storeName = e
      this.initStore(e)
    },
    checkAccountShopFn() {
      checkAccountShop().then((res) => {
        if (res.code == 200) {
          console.log('门店权限', res.data)
          this.account_shop_status = res.data.account_shop_status
        }
      })
    },
    initStore() {
      let _this = this
      // 门店列表接口 2 商品详情创建模块 直接返回新门店列表接口  其他模块 门店列表接口
      let http = this.searchType == 2 ? getCurrentCreateGoodsShopApi : shopListApi
      http({
        page: this.page,
        limit: 100,
        name: this.storeName,
      })
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            // 如果是商品详情创建模块 直接返回门店列表
            if (this.searchType == 2) {
              res.data.list = res.data
            }

            if (this.storeName) {
              _this.storeFilterList = res.data.list
              return
            }
            _this.storeList = res.data.list
            _this.storeType = res.data.pagination && res.data.pagination.totalCount <= 10 ? 1 : 2

            // 1 用于搜索  2 用于商品详情创建  如果只有一个门店时 默认选中第一个门店
            console.log(
              _this.searchType && _this.key == '',
              _this.storeList.length == 1 && !this.storeId
            )
            if (_this.searchType && _this.key == '') {
              if (_this.storeList.length == 1 && !this.storeId) {
                console.log('只有一个门店时 默认选中第一个门店')
                _this.key = _this.storeList[0].shop_id
                _this.$emit('changeStore', _this.key)
              }
            } else if (_this.key == '') {
              // 顶部搜索模块 如果只有一个门店时 默认选中第一个门店
              if (_this.storeList.length > 0) {
                _this.key = _this.storeList[0].shop_id
                if (this.searchType != 1) {
                  _this.$emit('changeStore', _this.key)
                }
              } else {
                // 无门店时需要单独处理
                _this.$emit('changeStore', 0)
              }
            }

            // 如果不是搜索模块 默认选中第一个门店
            if (this.searchType == 1) {
              _this.storeList.unshift({
                name: '全部门店',
                shop_id: 0,
              })
            }
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    selectKey(key) {
      console.log(key)
      this.key = key
      this.$emit('changeStore', key)
    },
    // 重置门店选择
    resetStore() {
      // this.key = ''
      if (this.storeList.length == 2) {
        this.key = this.storeList[1].shop_id
        this.$emit('changeStore', this.key)
      } else {
        this.key = ''
      }
    },
  },
}
</script>

<style scoped lang="scss">
.once-store {
  padding: 20px 0 0 20px;
  border-bottom: 1chx solid #e8e8e8;
}
.more-store {
  display: flex;
  width: 100%;
  overflow-x: auto;
  background: #f9f9f9;
  .store-item {
    padding: 10px 24px;
    border: 10px;
    color: rgba(0, 0, 0, 0.65);
    font-weight: 400;
    font-size: 14px;
    font-style: normal;
    cursor: pointer;
    white-space: nowrap;
    &:hover {
      color: #0071fe;
    }
  }
  .store-item.key {
    color: #0071fe;
    background: #fff;
  }
}
</style>

<style lang="scss">
.store-model {
  .el-card__header {
    padding: 0 !important;
    border-bottom: 0;
  }
}
.no-tip {
  position: absolute;
  font-size: 25px;
  z-index: 200;
  background: #fff;
  width: 100%;
  height: 100%;
  text-align: center;
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;
}
</style>
