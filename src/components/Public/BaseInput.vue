<template>
  <el-input
    v-bind="$attrs"
    v-on="$listeners"
    :value="value"
    @input="handleInput"
    :type="isShowPassword ? 'text' : 'password'"
    ref="inputRef"
  >
    <i
      slot="suffix"
      class="iconfont paw_icon"
      :class="{
        'icon-zhengyan1': isShowPassword,
        'icon-biyan': !isShowPassword,
        'icon-top': showPwdTop
      }"
      @click="isShowPassword = !isShowPassword"
    ></i>
  </el-input>
</template>
 
<script>
export default {
  name: 'BaseInput',
  inheritAttrs: false,
  data() {
    return {
      isShowPassword: false, // 默认密码隐藏
    }
  },
  props: {
    type: {},
    value: {
      type: String,
      default: '',
    },
    showPwd: {
      type: Boolean,
      default: false,
    },
    showPwdTop: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    handleInput(value) {
      this.$emit('input', value) // 触发input事件更新父组件的值
    },
  },
}
</script>

<style lang="scss" scoped>
.paw_icon {
  cursor: pointer;
  margin-right: 10px;
}
.icon-top {
  position: relative;
  top: 2px;
}
</style>