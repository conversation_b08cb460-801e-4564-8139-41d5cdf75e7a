<!--
 * @Author: liqian <EMAIL>
 * @Email: <EMAIL>
 * @Date: 2025-06-26 09:16:19
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-08-04 10:08:33
 * @FilePath: \qst-merchant-admin-2.0\src\components\Public\BaseContentTip.vue
 * @Description: 
-->
<template>
  <div class>
    <div class="n_tips" :style="styleVar">
      <img class="tip_icon" :src="srcImg ? srcImg : imgPath" alt />
      <div v-if="typeof tip === 'string'">
        <div class="tip_name1">{{ tip }}</div>
      </div>
      <div v-else>
        <div class="tip_title">{{ tiptitle }}</div>
        <div class="tip_name flex" v-for="item in tip">
          <span class="dian"></span>
          {{ item }}
        </div>
      </div>
      <slot></slot>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'BaseContentTip',
    props: {
      tiptitle: {
        //多条的提示头文字
        type: String,
        default: '',
      },
      tip: {
        type: [String, Array], //提示内容  一条就'xxxxx',多条['111','11111']
        default: '',
      },
      tipColor: {
        //显示的颜色
        type: String,
        default: '#FFFBE6',
      },
      srcImg: {
        //显示的图片
        type: String,
        default: '',
      },
    },

    data() {
      return {
        imgPath: require('../../assets/warn_20250626.png'),
        '--Contentcolor': '#FFFBE6',
      }
    },
    computed: {
      styleVar() {
        return {
          '--Contentcolor': this.tipColor,
        }
      },
    },
    created() {},
    mounted() {},
    methods: {},
  }
</script>

<style lang="scss" scoped>
  .n_tips {
    display: flex;
    align-items: flex-start;
    padding: 18px 20px;
    background: var(--Contentcolor);
    border-radius: 8px;
    border: 1px solid var(--Contentcolor);
    .tip_icon {
      display: block;
      width: 20px;
      height: 20px;
      margin-right: 10px;
    }
    .tip_title {
      font-size: 16px;
      color: #222222;
      line-height: 16px;
      font-weight: 600;
    }
    .tip_name {
      font-family: PingFangSC, PingFang SC;
      font-size: 14px;
      color: #32363a;
      font-weight: 400;
      margin-top: 8px;
      .dian {
        display: inline-block;
        width: 4px;
        height: 4px;
        background: #0071fe;
        margin-right: 10px;
      }
    }
    .tip_name1 {
      font-family: PingFangSC, PingFang SC;
      font-size: 14px;
      font-weight: 400;
      color: #32363a;
    }
  }
</style>
