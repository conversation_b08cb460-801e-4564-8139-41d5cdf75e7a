<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-03-05 13:54:55
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-03-05 16:10:18
 * @FilePath: /qst-merchant-admin-2.0/src/components/Public/BaseDialog.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
  <!-- 统一的底部按钮 -->

<template #footer>
  <span class="dialog-footer">
    <el-button v-if="isCancel" @click="handleCancel">{{ cancelText || '取消' }}</el-button>
    <el-button
      type="primary"
      :loading="confirmLoading"
      @click="handleConfirm"
    >{{ confirmText || '保存' }}</el-button>
  </span>
</template>

<script>
export default {
  name: 'BaseDialogFooter',
  props: {
    // 按钮文案
    confirmText: String,
    cancelText: String,
    // 加载状态
    confirmLoading: Boolean,
    // 是否显示按钮
    isConfirm: {
      type: Boolean,
      default: true,
    },
    isCancel: {
      type: Boolean,
      default: true,
    },
  },

  methods: {
    // 确定按钮
    handleConfirm() {
      this.$emit('confirm')
    },
    // 取消按钮
    handleCancel() {
      this.$emit('cancel')
    },
  },
}
</script>

<style scoped lang="scss">
.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 14px; /* 按钮间距统一 */
  margin-top: 40px; /* 与内容部分间距 */
}
</style>