<template>
  <div class="public-table" v-loading="!isHeaderShow">
    <el-table
      :data="tableData"
      v-loading="loading"
      @selection-change="handleSelectionChange"
      :row-key="(row) => row[rowKey]"
      :key="dynamicsIndex"
      ref="table"
      class="base-table"
      :tree-props="treeProps"
      :row-class-name="iconHadler"
      :height="tableHeight"
    >
      <el-table-column reserve-selection v-if="isSelect" type="selection" width="55" :selectable="selectableFunction"></el-table-column>
      <el-table-column
        v-for="(item, index) in tableColumnFilter"
        :key="item.prop"
        :label="item.label"
        :prop="item.prop"
        :width="item.width"
        :min-width="100"
        :fixed="item.fixed"
        :sortable="item.isSortable"
        :type="item.isExpand ? 'expand' : ''"
        v-if="isHeaderShow"
      >
        <!-- 自定义头部插槽 -->
        <template slot-scope="scope" slot="header">
          <div v-if="index == tableColumnFilter.length - 1" class="flex">
            <span>{{ item.label }}</span>
            <i class="el-icon-s-tools icon-left" @click="isFilterHeader = true"></i>
          </div>
          <slot v-else-if="item.headType" :name="item.headType" :scope="scope"></slot>
          <span v-else>{{ item.label }}</span>
        </template>

        <!-- 内容插槽 -->
        <template slot-scope="scope">
          <!-- 自定义插槽 -->
          <slot v-if="item.type == 'customize' || item.isExpand" :name="item.prop" :scope="scope"></slot>
          <!-- 查看详细 手机号 插槽 -->
          <base-view-phone
            v-else-if="item.type == 'customizePhone'"
            :phone="scope.row[item.prop]"
            :fun="item.fun"
            :funKey="item.funKey"
            :row="scope.row"
          ></base-view-phone>

          <!-- 固定模版 -->
          <div v-else-if="item.type == 'template'">
            <!-- 状态转文案 -->
            <span class="defalut-size" v-if="item.stateType == 'select'">{{ item.stateObj[scope.row[item.prop]] }}</span>
            <!-- 后续 可继续增加模块 -->
          </div>
          <span v-else class="defalut-size">{{ scope.row[item.prop] }}</span>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="page"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </div>

    <!-- 过滤Header -->
    <el-dialog append-to-body :close-on-click-modal="false" :visible.sync="isFilterHeader" title="设置列显示" width="300px">
      <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
      <div class="mt10"></div>
      <el-checkbox-group v-model="checkboxHeader" @change="handleCheckedCitiesChange">
        <el-checkbox
          class="flex"
          v-for="item in tableColumn"
          :label="item.prop"
          :key="item.prop"
          :disabled="(checkboxHeader.length == 1 && item.prop == checkboxHeader[0]) || item.isExpand"
        >
          {{ item.isExpand ? '查看子集' : item.label }}
        </el-checkbox>
      </el-checkbox-group>
      <base-dialog-footer
        @confirm="handleFilterHeader"
        @cancel=";(isFilterHeader = false), (checkboxHeader = filterHeader)"
      ></base-dialog-footer>
    </el-dialog>
  </div>
</template>

<script>
  import BaseDialogFooter from './BaseDialogFooter.vue'
  
  export default {
    components: { BaseDialogFooter },
    name: 'SelectableBaseTable',
    props: {
      // 初始化是回调
      isInitCallback: {
        type: Boolean,
        default: false,
      },
      // 是否监听滚动
      isScrollX: {
        type: Boolean,
        default: true,
      },
      isSelect: {
        type: Boolean,
        default: false,
      },
      // 行选择函数，用于控制哪些行可以被选择
      selectable: {
        type: Function,
        default: null,
      },
      rowKey: {},
      tableForm: {
        type: Object,
        default: () => {},
      },
      tableRequest: {},
      tableColumn: {
        type: Array,
        default: () => [],
      },
      treeProps: {
        type: Object,
      },
      tableHeight: {
        type: [Number, String],
        default: null,
      },
    },
    computed: {
      tableColumnFilter() {
        return this.tableColumn.filter((item) => this.filterHeader.includes(item.prop))
      },
      selectableFunction() {
        return this.selectable || (() => true);
      },
    },
    watch: {
      'tableForm.tableTime': {
        deep: true,
        handler() {
          this.tableRequestFn(true)
        },
      },
      tableColumn: {
        immediate: true,
        handler() {
          // 选择Header默认选中状态
          this.checkboxHeader = this.tableColumn.map((item) => item.prop)

          // 过滤Header默认选中状态
          this.filterHeader = this.tableColumn.map((item) => item.prop)
        },
      },
    },

    data() {
      return {
        dynamicsIndex: 0,
        page: 1,
        tableData: [],
        loading: false,
        total: 0,
        size: 10,

        // 过滤Header
        isHeaderShow: true,
        checkboxHeader: [],
        filterHeader: [],
        isFilterHeader: false,
        checkAll: true,
        isIndeterminate: false,
        TableDataAll: [], //返回的全部数据
      }
    },
    methods: {
      // 展开行
      iconHadler({ row }) {
        if (this.treeProps) {
          if (!row[this.treeProps.hasChildren] || row[this.treeProps.hasChildren] === '0') {
            return 'icon-no'
          }
        }
      },

      // 过滤Header start
      handleFilterHeader() {
        if (this.checkboxHeader.length === 0) {
          return this.$message.error('请选择要显示的列')
        }
        this.isHeaderShow = false
        this.isFilterHeader = false
        this.filterHeader = this.checkboxHeader
        setTimeout(() => {
          this.dynamicsIndex++
          this.isHeaderShow = true
        }, 300)
      },
      handleCheckAllChange(val) {
        if (val) {
          this.checkboxHeader = this.tableColumn.map((item) => item.prop)
        } else {
          this.checkboxHeader = this.tableColumn.filter((item) => item.isExpand).length > 0 ? ['expand'] : []
        }
        this.isIndeterminate = false
      },
      handleCheckedCitiesChange(value) {
        let checkedCount = value.length
        this.checkAll = checkedCount === this.tableColumn.length
        this.isIndeterminate = checkedCount > 0 && checkedCount < this.tableColumn.length
      },
      // 过滤Header end

      // 分页
      handleSizeChange(val) {
        this.size = val
        this.tableRequestFn()
      },
      // 分页
      handleCurrentChange(val) {
        this.page = val
        this.tableRequestFn()
      },
      // 请求表格数据
      tableRequestFn(isReachPage) {
        // 外部请求时重置分页
        if (isReachPage) {
          this.page = 1
        }
        this.loading = true
        let params = {
          page: this.page,
          limit: this.size,
          ...this.tableForm,
        }
        if (params.tableTime) {
          params.tableTime = null
        }
        if (this.tableRequest) {
          this.tableRequest(params).then((res) => {
            this.loading = false
            if (res.code == 200) {
              // 是否存在子集数据，且有子集字段标识字段（hasChildren）和子集字段名称（children），则在表格渲染时添加该字段，用于控制展开/收起按钮的显示
              if (this.treeProps && this.treeProps.hasChildren) {
                res.data.list = res.data.list.map((item) => {
                  return {
                    ...item,
                    [this.treeProps.hasChildren]: !!(item[this.treeProps.children] && item[this.treeProps.children].length > 0),
                  }
                })
              }
              this.TableDataAll = res.data
              this.tableData = res.data.list

              if (res.data.pagination) {
                this.total = res.data.pagination.totalCount
              }

              // 表格初始化回调事件，用于表格渲染完毕后执行某些操作
              this.isInitCallback && this.$emit('initCallback')

              // 商品详情使用
              if (this.isScrollX) {
                //获取节点
                this.tableDistance = this.$refs.table.$el.querySelector('.el-table__body-wrapper')
                //写入监听事件
                this.tableDistance.addEventListener(
                  'scroll',
                  this.$baseLodash.throttle(() => {
                    this.$emit('scrollX', this.tableDistance.scrollLeft + 10 + 'px')
                  }, 10)
                )
              }
            }
          })
        } else {
          this.loading = false
        }
      },
      // 多选表格选择事件
      handleSelectionChange(val) {
        this.$emit('selectiKey', val)
      },
      // 清除选择
      clearSelection() {
        this.$refs.table.clearSelection()
      },
      // 自定义操作table 数据  抛出方法
      setTableData(callback) {
        callback(this)
      },
    },
    mounted() {
      this.tableRequestFn()
    },
  }
</script>

<style lang="scss" scoped>
  .public-table {
    margin-top: 20px;
  }
  .defalut-size {
    font-weight: 400;
    font-size: 14px;
    color: #6e6e7a;
  }
  .pagination {
    display: flex;
    justify-content: flex-end;
  }

  .icon-left {
    margin-left: 4px;
    cursor: pointer;
  }
  .mt10 {
    margin-top: 10px;
  }

  .base-table ::v-deep {
    // 表格头部样式
    .el-table th.el-table__cell {
      background: #f5f7fa;
      box-shadow: inset 0px -1px 0px 0px #ebeef5, inset 0px -1px 0px 0px #ebeef5;
    }
    th.el-table__cell {
      background-color: #f5f7fa;
    }
    th.el-table__cell .cell {
      color: #303133;
      font-weight: 600;
    }
    .el-pagination.is-background .btn-prev,
    .el-pagination.is-background .btn-next,
    .el-pagination.is-background .el-pager li {
      background-color: #fff;
      border: 1px solid #dcdfe6;
      color: #606266;
    }

    .el-pagination.is-background .btn-prev:disabled,
    .el-pagination.is-background .btn-next:disabled {
      color: #c0c4cc;
    }
  }

  .goods .base-table ::v-deep {
    // 展开行样式
    .el-table__expand-icon--expanded {
      transform: none !important; /* 禁用默认旋转 */
    }
    .expanded .el-icon-arrow-right:before {
      display: inline-block;
      width: 16px;
      height: 16px;
      background-size: cover;
      transition: all 0.2s;
      content: '';
      margin-top: -2px;
      background: url('../../assets/table-select-2.png') no-repeat;
      background-size: 16px 16px;
    }
    .el-icon-arrow-right:before {
      display: inline-block;
      width: 16px;
      height: 16px;
      background-size: cover;
      transition: all 0.2s;
      content: '';
      margin-top: -2px;
      background: url('../../assets/table-select-1.png') no-repeat;
      transform: none !important; /* 禁用默认旋转 */
      background-size: 16px 16px;
    }

    .icon-no .el-table__expand-icon {
      display: none;
    }
  }

  .custom-expand-icon {
    width: 16px;
    height: 16px;
    transition: transform 0.2s; /* 保留旋转动画 */
  }
</style>
