<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-03-07 16:49:23
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-26 09:46:40
 * @FilePath: /qst-merchant-admin-2.0/src/components/Public/ChangePassword.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <el-dialog
      destroy-on-close
      :title="userEdit == 'password' ? '修改密码' : userEdit == 'phone' ? '修改手机号' : ''"
      :visible.sync="isPassword"
      :close-on-click-modal="false"
    >
      <el-form :rules="formDataRule" :model="formData" ref="formData" label-width="150px">
        <el-form-item>
          <el-radio-group v-model="formData.type">
            <el-radio :label="1">密码验证</el-radio>
            <el-radio :label="2">手机号验证</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="当前密码" prop="password" v-show="formData.type == 1">
          <base-input
            :showPwdTop="true"
            v-model="formData.password"
            type="password"
            placeholder="请输入当前密码"
          ></base-input>
        </el-form-item>

        <el-form-item label="手机号" v-show="formData.type == 2">
          <el-input v-model="phone" disabled placeholder="请输入手机号"></el-input>
        </el-form-item>
        <el-form-item label="验证码" prop="smsCode" v-show="formData.type == 2">
          <div class="verification-code flex-b">
            <el-input
              v-model.trim="formData.smsCode"
              placeholder="请输入验证码"
              type="text"
              class="verification-code-input"
              :maxlength="6"
              v-numeric
            />
            <el-button type="text" size="big" v-if="verifyTimeOut">{{ verifyTimeOut }}</el-button>
            <el-button type="text" size="big" v-else @click="getVerificationCode('verify')">获取验证码</el-button>
          </div>
        </el-form-item>
      </el-form>
      <base-dialog-footer confirmText="下一步" @confirm="confirm" @cancel="isPassword = false"></base-dialog-footer>
    </el-dialog>

    <!-- 设置新密码 -->
    <el-dialog
      destroy-on-close
      :title="userEdit == 'password' ? '设置新密码' : userEdit == 'phone' ? '设置新手机号' : ''"
      :visible.sync="isEdit"
      :close-on-click-modal="false"
    >
      <el-form :rules="newFormRule" :model="newForm" ref="newForm" label-width="150px">
        <el-form-item label="新密码" prop="newPassword" v-show="userEdit == 'password'">
          <base-input v-model="newForm.newPassword" type="password" placeholder="请输入新密码"></base-input>
        </el-form-item>
        <el-form-item label="确认新密码" prop="confirmNewPassword" v-show="userEdit == 'password'">
          <base-input v-model="newForm.confirmNewPassword" type="password" placeholder="请再次输入新密码"></base-input>
        </el-form-item>

        <el-form-item label="手机号" prop="phone" v-show="userEdit == 'phone'">
          <el-input v-model="newForm.phone" placeholder="请输入手机号"></el-input>
        </el-form-item>

        <el-form-item label="验证码" prop="smsCode" v-show="userEdit == 'phone'">
          <div class="verification-code flex-b">
            <el-input
              v-model.trim="newForm.smsCode"
              placeholder="请输入验证码"
              type="text"
              class="verification-code-input"
              :maxlength="6"
              v-numeric
            />
            <el-button type="text" size="big" v-if="editTimeOut">{{ editTimeOut }}</el-button>
            <el-button type="text" size="big" v-else @click="getVerificationCode('edit')">获取验证码</el-button>
          </div>
        </el-form-item>
      </el-form>
      <base-dialog-footer @confirm="editConfirm" @cancel="isEdit = false"></base-dialog-footer>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import store from '@/store'
import { timeOut } from '@/utils/public'
import {
  passwordVerifyApi,
  setPasswordApi,
  phoneVerifyApi,
  setPhoneApi,
  sendCodeByEditPhonePwdApi,
} from '@/api/user.js'
export default {
  name: 'ChangePassword',
  props: {},
  computed: {
    ...mapGetters({
      userEdit: 'user/userEdit',
      phone: 'user/phone',
    }),
  },
  watch: {
    userEdit(val) {
      if (val == 'password' || val == 'phone') {
        this.isPassword = true
        this.isReashed = false
        this.formData = {
          type: 1,
          password: '',
          phone: '',
          smsCode: '',
          name: '',
        }
        this.newForm = {
          newPassword: '',
          confirmNewPassword: '',
          phone: '',
        }
      }
    },
    isPassword(val) {
      if (!val && !this.isEdit) {
        store.dispatch('user/setUserEdit', '')
      }
    },
    isEdit(val) {
      if (!val && !this.isPassword) {
        store.dispatch('user/setUserEdit', this.isReashed ? 'reashed' : '')
      }
    },
  },

  data() {
    let validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.newForm.newPassword) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      isReashed: false,
      formData: {
        type: 1,
        password: '',
        phone: '',
        smsCode: '',
        name: '',
      },
      formDataRule: {
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' },
        ],
        smsCode: [{ required: true, message: '请输入验证码' }],
      },
      isPassword: false,
      timeOut: '',

      newFormRule: {
        newPassword: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          {
            pattern: /^(?=.*[a-z_])(?=.*\d)(?=.*[^a-z0-9_])[\S]{8,32}$/,
            message: '请输入必须是8-32位且包含数字字母及特殊符号的密码',
          },
        ],
        confirmNewPassword: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          { validator: validatePass2 },
        ],
        smsCode: [{ required: true, message: '请输入验证码' }],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' },
        ],
      },
      newForm: {
        newPassword: '',
        confirmNewPassword: '',
        phone: '',
      },

      isEdit: false,
      timeOutID: null,
      verifyTimeOut: null,
      editTimeOut: null,
      oldPassword: '',
    }
  },
  methods: {
    // 密码 手机号 确认
    confirm() {
      // type == 1 密码验证  smsCode 手机号验证
      let type = this.formData.type
      let validate = type == 1 ? 'password' : 'smsCode'
      this.$refs.formData.validateField(validate, (res) => {
        console.log(res)
        if (!res) {
          type == 1 ? this.passwordVerify() : this.phoneVerify()
        }
      })
    },
    // 修改新 密码 手机号
    editConfirm() {
      let validateArr =
        this.userEdit == 'password' ? ['newPassword', 'confirmNewPassword'] : ['phone', 'smsCode']
      let hasError = false
      this.$refs.newForm.validateField(validateArr, (err) => {
        if (err) {
          hasError = true
          return
        }
      })
      if (!hasError) {
        this.userEdit == 'password' ? this.setPassword() : this.setPhone()
      }
    },

    // 获取验证码
    getVerificationCode(type) {
      let isPhone = true
      let phone = ''
      if (type != 'verify') {
        this.$refs.newForm.validateField('phone', (err) => {
          if (err) {
            isPhone = false
          }
        })
        phone = this.newForm.phone
      } else {
        phone = this.phone
      }
      if (isPhone) {
        this.sendVerificationCode(phone, type)
      }
    },
    // 发送验证码
    sendVerificationCode(phone, type) {
      sendCodeByEditPhonePwdApi({
        phone,
      }).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: '验证码已发送，请注意查收！',
          })
          this.timeOutID = new timeOut(60)
          this.timeOutID.start((e) => {
            if (e.totalSeconds == 0) {
              this[type + 'TimeOut'] = null
            } else {
              this[type + 'TimeOut'] = e.totalSeconds + 's'
            }
          })
        }
      })
    },

    // 密码校验
    passwordVerify() {
      passwordVerifyApi({
        password: this.formData.password,
      }).then((res) => {
        if (res.code == 200) {
          this.isEdit = true
          this.isPassword = false
          this.oldPassword = res.data.oldPassWord
        }
      })
    },
    // 手机号校验
    phoneVerify(smsCodeType) {
      phoneVerifyApi({
        phone: this.phone,
        smsCode: this.formData.smsCode,
        smsCodeType: 'verify',
      }).then((res) => {
        if (res.code == 200) {
          this.isEdit = true
          this.isPassword = false
          this.oldPassword = res.data.oldPassWord
        }
      })
    },

    // 设置密码
    setPassword() {
      setPasswordApi({
        oldPassword: this.oldPassword,
        newPassword: this.newForm.newPassword,
        confirmNewPassword: this.newForm.confirmNewPassword,
      }).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: '修改成功！',
          })
          this.isReashed = true
          this.isEdit = false
        }
      })
    },
    // 设置手机号
    setPhone() {
      setPhoneApi({
        phone: this.newForm.phone,
        smsCode: this.newForm.smsCode,
        smsCodeType: 'edit',
        oldPassword: this.oldPassword,
      }).then((res) => {
        if (res.code == 200) {
          this.$store.dispatch('user/setPhone', this.newForm.phone)
          this.$message({
            type: 'success',
            message: '修改成功！',
          })
          this.isReashed = true
          this.isEdit = false
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.verification-code {
  width: 252px;
  height: 40px;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #e8eaeb;
  padding-right: 6px;
  overflow: hidden;
  &-input {
    width: 150px;
    background: #fff;
    border: 0;
    caret-color: $base-font-color;
  }
  .refresh-image {
    height: 28px;
  }
  .line {
    width: 1px;
    height: 20px;
    background: #f5f7fa;
    margin: 0 10px;
  }
}

::v-deep {
  .el-input {
    box-sizing: border-box;
    input {
      height: 40px;
      font-size: $base-font-size-default;
      line-height: 40px;
      color: $base-font-color;
      background: #fff;
      border: 0;
      caret-color: $base-font-color;
      border-radius: 6px;
      border: 1px solid #e8eaeb;
    }
  }
  .verification-code-input input {
    border: 0;
  }
  .el-form-item--small.el-form-item {
    margin-bottom: 30px;
  }
}
</style>
