<!--
 * @Author: liqian <EMAIL>
 * @Email: <EMAIL>
 * @Date: 2025-07-24 15:48:42
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-08-04 09:49:09
 * @FilePath: \qst-merchant-admin-2.0\src\components\Public\BaseImagesDialog.vue
 * @Description: 
-->
<template>
  <el-dialog :close-on-click-modal="false" :visible.sync="dialogVisible" title="查看示例">
    <div class="id-card-model" v-if="dialogImageUrl.length > 0">
      <div v-for="(item, index) in dialogImageUrl">
        <el-image class="cardimg" :src="item" :preview-src-list="dialogImageUrl" alt></el-image>
      </div>
    </div>

    <el-empty v-else description="暂无示例"></el-empty>
  </el-dialog>
</template>
<script>
  export default {
    name: 'BaseImagesDialog',
    props: {
      itemProps: {
        type: String,
        default: '',
      },
    },
    computed: {},

    data() {
      return {
        dialogImageUrl: [],
        dialogVisible: false,
      }
    },
    created() {},
    methods: {
      //获取实例图片
      getImagesApi() {
        // deleteGoodsSpecList({ template_id: this.itemProps }).then((res) => {
        //   if (res.code == 200) {
        //     this.dialogImageUrl = res.data
        //   }
        // })
      },
      //查看示例
      open() {
        this.dialogVisible = true

        this.$nextTick(() => {
          this.getImagesApi()
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .id-card-model {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    text-align: center;
    .cardimg {
      width: 200px;
      height: 112px;
    }
  }
</style>
