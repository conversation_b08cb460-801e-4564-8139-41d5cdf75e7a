<!--
 * @Description: 待办通知组件
 * @Author: AI Assistant
 * @Date: 2025-07-26
-->
<template>
  <div class="todo-notification">
    <!-- 这个组件不需要模板，只负责逻辑处理 -->
  </div>
</template>

<script>
import { getLatestTodo } from '@/api/shop/message.js'
import { Notification } from 'element-ui'

export default {
  name: 'TodoNotification',
  props: {
    // 轮询间隔（秒）
    pollInterval: {
      type: Number,
      default: 30
    },
    // 是否启用语音播放
    enableAudio: {
      type: Boolean,
      default: true
    },
    // 通知显示时长（毫秒）
    notificationDuration: {
      type: Number,
      default: 8000
    }
  },
  data() {
    return {
      // 定时器
      pollTimer: null,
      // 上次检查的待办ID，用于判断是否有新待办
      lastTodoId: null,
      // 语音播放相关
      audioContext: null,
      isAudioEnabled: true,
      // 是否首次加载
      isFirstLoad: true
    }
  },
  mounted() {
    // 启动定时轮询
    this.startPolling()
    // 初始化音频上下文
    this.initAudioContext()
  },
  beforeDestroy() {
    // 清理定时器
    this.stopPolling()
  },
  methods: {
    // 启动定时轮询
    startPolling() {
      // 立即执行一次
      this.checkLatestTodo()

      // 根据配置的间隔检查新的待办事项
      this.pollTimer = setInterval(() => {
        this.checkLatestTodo()
      }, this.pollInterval * 1000)
    },

    // 停止定时轮询
    stopPolling() {
      if (this.pollTimer) {
        clearInterval(this.pollTimer)
        this.pollTimer = null
      }
    },

    // 检查最新待办事项
    async checkLatestTodo() {
      try {
        const result = await getLatestTodo()


        if (result.code === 200 && result.data && (result.data.title || result.data.message)) {
          const todoData = result.data
          // 检查是否是新的待办事项
          const currentTodoId = this.generateTodoId(todoData)

          // 检查是否需要显示通知
          // 首次加载时显示通知，或者待办ID发生变化时显示通知
          const shouldShowNotification = this.isFirstLoad || this.lastTodoId !== currentTodoId

          if (shouldShowNotification) {
            // 显示通知
            this.showTodoNotification(todoData)

            // 播放语音（如果有且启用）
            if (this.enableAudio && todoData.audio && todoData.audio.trim()) {
              this.playAudio(todoData.audio)
            }
          }

          // 更新最后检查的待办ID
          this.lastTodoId = currentTodoId
          // 标记首次加载完成
          this.isFirstLoad = false
        } else {
          // 没有待办数据时，也标记首次加载完成
          this.isFirstLoad = false
        }
      } catch (error) {
        // 静默处理错误，避免影响用户体验
        this.isFirstLoad = false
      }
    },

    // 生成待办事项唯一ID（用于判断是否是新待办）
    generateTodoId(todoData) {
      return `${todoData.system_todo_id}_${todoData.created_at}_${todoData.message}`
    },

    // 显示待办通知
    showTodoNotification(todoData) {

      const typeMap = {
        1: '订单待发货',
        2: '售后待审核',
        3: '售后审核即将超时',
        4: '售后待商家收货',
        5: '商品库存预警',
        6: '商品评价',
        7: '商家入驻申请',
        8: '卡券超期下架'
      }

      const title = todoData.title || typeMap[todoData.system_todo_id] || '新待办事项'

      // 格式化时间显示 - 完整的年月日时分格式
      const formatTime = (timeStr) => {
        if (!timeStr) return ''
        try {
          const date = new Date(timeStr)
          // 格式化为：2025-07-30 18:07
          return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
          }).replace(/\//g, '-')
        } catch (e) {
          return timeStr
        }
      }

      const timeText = formatTime(todoData.created_at)

      // 构建HTML内容，包含标题、分隔线和数据
      const htmlContent = `
        <div class="todo-notification-header">
          <img src="/static/views/layouts/VabNavBar/<EMAIL>" class="todo-clock-icon" alt="时钟" />
          <span class="todo-header-title">待办消息提醒</span>
        </div>
        <div class="todo-divider"></div>
        <div class="todo-content">
          <div class="todo-title">${title}</div>
          <div class="todo-message">${todoData.message}</div>
          ${timeText ? `<div class="todo-time">${timeText}</div>` : ''}
        </div>
      `

      try {
        // 使用导入的 Notification
        if (Notification) {
          const notification = Notification({
            title: '', // 不使用默认标题，使用自定义HTML
            message: htmlContent,
            type: 'info', // 改为info类型，去掉感叹号
            duration: 5000, // 5秒后自动关闭，让旧通知消失
            position: 'top-right',
            showClose: true,
            offset: 100, // 与系统通知栏白框顶部齐平
            zIndex: 9999,
            customClass: 'todo-notification-stacked',
            dangerouslyUseHTMLString: true, // 允许HTML内容
            iconClass: '', // 去掉默认图标
            onClick: () => {
              this.handleNotificationClick(todoData)
              if (notification && notification.close) {
                notification.close()
              }
            }
          })
        }
      } catch (error) {
        // 静默处理错误，避免影响用户体验
      }
    },

    // 处理通知点击事件
    handleNotificationClick(todoData) {
      // 根据待办类型跳转到相应页面
      const routeMap = {
        1: { name: 'orderList', title: '订单管理' }, // 订单待发货
        2: { name: 'refundList', title: '售后管理' }, // 售后待审核
        3: { name: 'refundList', title: '售后管理' }, // 售后审核即将超时
        4: { name: 'refundList', title: '售后管理' }, // 售后待商家收货
        5: { name: 'goodsList', title: '商品管理' }, // 商品库存预警
        6: { name: 'commentList', title: '评价管理' }, // 商品评价
        7: { name: 'storeApply', title: '商家入驻' }, // 商家入驻申请
        8: { name: 'couponList', title: '卡券管理' } // 卡券超期下架
      }

      const targetRoute = routeMap[todoData.system_todo_id]
      if (targetRoute) {
        try {
          // 构建查询参数
          const query = this.buildRouteQuery(todoData)
          this.$router.push({ name: targetRoute.name, query })
        } catch (error) {
          // 如果路由跳转失败，显示提示信息
          this.$message.info(`请前往${targetRoute.title}处理相关事项`)
        }
      } else {
        this.$message.info('请前往相关页面处理待办事项')
      }
    },

    // 构建路由查询参数
    buildRouteQuery(todoData) {
      const query = {}
      
      // 根据待办类型添加相应的查询参数
      if (todoData.sub_order_no) {
        query.orderNo = todoData.sub_order_no
      }
      if (todoData.refund_order_no) {
        query.refundNo = todoData.refund_order_no
      }
      if (todoData.store_uid) {
        query.storeUid = todoData.store_uid
      }
      if (todoData.goods_id) {
        query.goodsId = todoData.goods_id
      }
      if (todoData.shop_goods_id) {
        query.shopGoodsId = todoData.shop_goods_id
      }
      if (todoData.comment_id) {
        query.commentId = todoData.comment_id
      }
      
      return query
    },

    // 初始化音频上下文
    initAudioContext() {
      try {
        // 现代浏览器的音频上下文
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)()
      } catch (error) {
        // 音频上下文初始化失败，静默处理
        this.isAudioEnabled = false
      }
    },

    // 播放语音
    async playAudio(audioText) {
      if (!this.isAudioEnabled || !audioText) {
        return
      }

      try {
        // 使用浏览器的语音合成API
        if ('speechSynthesis' in window) {
          const utterance = new SpeechSynthesisUtterance(audioText)
          utterance.lang = 'zh-CN' // 中文
          utterance.rate = 1.0 // 语速
          utterance.pitch = 1.0 // 音调
          utterance.volume = 0.8 // 音量
          
          window.speechSynthesis.speak(utterance)
        }
      } catch (error) {
        // 语音播放失败，静默处理
      }
    },

    // 手动检查待办（供外部调用）
    manualCheck() {
      this.checkLatestTodo()
    },

    // 强制显示当前待办通知（用于测试）
    async forceShowNotification() {
      try {
        const result = await getLatestTodo()

        if (result.code === 200 && result.data && result.data.title) {
          this.showTodoNotification(result.data)

          // 播放语音（如果有且启用）
          if (this.enableAudio && result.data.audio && result.data.audio.trim()) {
            this.playAudio(result.data.audio)
          }
        } else {
          this.$message.info('当前没有待办事项')
        }
      } catch (error) {
        this.$message.error('获取待办失败')
      }
    },

    // 启用/禁用音频
    toggleAudio(enabled) {
      this.isAudioEnabled = enabled
    },

    // 手动触发测试通知（用于调试）
    testNotification() {
      const testData = {
        title: '测试待办事项',
        message: '这是一个测试通知，用于验证通知功能是否正常工作',
        system_todo_id: 1,
        audio: '您有新的测试待办事项'
      }
      this.showTodoNotification(testData)
    }
  }
}
</script>

<style lang="scss" scoped>
.todo-notification {
  display: none; // 这个组件不需要显示任何内容
}
</style>

<style>
/* 确保待办通知可见 */
.todo-notification-custom {
  z-index: 9999 !important;
  position: fixed !important;
  top: 80px !important;
  right: 20px !important;
  background-color: #fff !important;
  border: 1px solid #EBEEF5 !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  min-width: 330px !important;
  max-width: 400px !important;
}

.todo-notification-custom .el-notification__title {
  color: #303133 !important;
  font-size: 16px !important;
  font-weight: bold !important;
}

.todo-notification-custom .el-notification__content {
  color: #606266 !important;
  font-size: 14px !important;
  margin-top: 6px !important;
}

/* 堆叠通知样式 */
.todo-notification-stacked {
  z-index: 9999 !important;
  position: fixed !important;
  top: 140px !important; /* 与系统通知栏白框顶部齐平 */
  right: 20px !important;
  background-color: #fff !important;
  border: 1px solid #EBEEF5 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  min-width: 330px !important;
  max-width: 400px !important;
  transition: all 0.3s ease !important;
  margin-bottom: 10px !important;
  padding: 0 !important; /* 去掉默认内边距 */
}

.todo-notification-stacked .el-notification__group {
  margin: 0 !important;
  padding: 0 !important;
}

/* 隐藏默认图标 */
.todo-notification-stacked .el-notification__icon {
  display: none !important;
}

.todo-notification-stacked .el-notification__title {
  color: #303133 !important;
  font-size: 16px !important;
  font-weight: bold !important;
}

.todo-notification-stacked .el-notification__content {
  color: #606266 !important;
  font-size: 14px !important;
  margin-top: 0 !important;
  padding: 0 !important;
  line-height: 1.5 !important;
}

/* 待办通知头部样式 */
.todo-notification-header {
  display: flex !important;
  align-items: center !important;
  padding: 12px 16px 8px 16px !important;
  background-color: #fff !important;
}

.todo-clock-icon {
  width: 16px !important;
  height: 16px !important;
  margin-right: 8px !important;
}

.todo-header-title {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #303133 !important;
}

/* 分隔线样式 */
.todo-divider {
  height: 1px !important;
  background-color: #EBEEF5 !important;
  margin: 0 16px !important;
}

/* 待办内容样式 */
.todo-content {
  padding: 12px 16px !important;
}

.todo-title {
  font-size: 16px !important;
  font-weight: bold !important;
  color: #303133 !important;
  margin-bottom: 6px !important;
}

.todo-message {
  font-size: 14px !important;
  color: #606266 !important;
  margin-bottom: 6px !important;
  line-height: 1.4 !important;
}

.todo-time {
  font-size: 12px !important;
  color: #909399 !important;
  margin-top: 8px !important;
}

/* 确保通知堆叠显示，新通知在下面 */
.el-notification {
  z-index: 9999 !important;
  margin-bottom: 10px !important;
}

/* 通知进入动画 */
.el-notification.right {
  animation: slideInRight 0.3s ease-out !important;
}

/* 通知离开动画 */
.el-notification.right.el-notification-fade-leave-active {
  animation: slideOutRight 0.3s ease-in !important;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}
</style>
