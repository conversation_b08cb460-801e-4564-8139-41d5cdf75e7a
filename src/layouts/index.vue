<template>
  <div :class="classObj" class="vue-admin-better-wrapper">
    <div
      v-if="'horizontal' === layout"
      :class="{
        fixed: header === 'fixed',
        'no-tabs-bar': tabsBar === 'false' || tabsBar === false,
      }"
      class="layout-container-horizontal"
    >
      <div :class="header === 'fixed' ? 'fixed-header' : ''">
        <vab-top-bar />
        <div v-if="tabsBar === 'true' || tabsBar === true" :class="{ 'tag-view-show': tabsBar }">
          <div class="vab-main">
            <vab-tabs-bar />
          </div>
        </div>
      </div>
      <div class="vab-main main-padding">
        <!-- <vab-ad /> -->
        <vab-app-main />
      </div>
    </div>
    <div
      v-else
      :class="{
        fixed: header === 'fixed',
        'no-tabs-bar': tabsBar === 'false' || tabsBar === false,
        'no-message': isMessage === 'false' || isMessage === false,
      }"
      class="layout-container-vertical"
    >
      <div
        v-if="device === 'mobile' && collapse === false"
        class="mask"
        @click="handleFoldSideBar"
      />
      <vab-side-bar-new />
      <div
        :class="{
        'is-collapse-main':collapse ,
        'home-no-left': routeKey == 'Home' || routeKey == 'Personal'
      }"
        class="vab-main"
      >
        <div :class="header === 'fixed' ? 'fixed-header' : ''">
          <vab-nav-bar />
          <vab-tabs-bar
            style="height: 0px;position: absolute;
    z-index: -1;"
            v-if="tabsBar === 'true' || tabsBar === true"
          />
        </div>
        <!-- <vab-ad /> -->
        <vab-message />
        <todo-notification
          ref="todoNotification"
          :poll-interval="30"
          :enable-audio="true"
          :notification-duration="8000"
        />
        <vab-app-main />
      </div>
    </div>
    <el-backtop :bottom="80" />
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import { tokenName } from '@/config'
import TodoNotification from '@/components/TodoNotification/index.vue'

export default {
  name: 'Layout',
  components: {
    TodoNotification
  },
  data() {
    return {
      oldLayout: '',
      controller: new window.AbortController(),
      timeOutID: null,
    }
  },
  computed: {
    ...mapGetters({
      layout: 'settings/layout',
      tabsBar: 'settings/tabsBar',
      collapse: 'settings/collapse',
      header: 'settings/header',
      device: 'settings/device',
      isMessage: 'settings/isMessage',
      routeKey: 'routes/routeKey',
    }),
    classObj() {
      return {
        mobile: this.device === 'mobile',
      }
    },
  },
  beforeMount() {
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    this.controller.abort()
    clearTimeout(this.timeOutID)
  },
  mounted() {
    this.oldLayout = this.layout
    const userAgent = navigator.userAgent
    // const isMobile = this.handleIsMobile()
    const isMobile = false
    if (isMobile) {
      if (isMobile) {
        //横向布局时如果是手机端访问那么改成纵向版
        this.$store.dispatch('settings/changeLayout', 'vertical')
      } else {
        this.$store.dispatch('settings/changeLayout', this.oldLayout)
      }
      this.$store.dispatch('settings/toggleDevice', 'mobile')
      this.timeOutID = setTimeout(() => {
        this.$store.dispatch('settings/foldSideBar')
      }, 2000)
    } else {
      this.$store.dispatch('settings/openSideBar')
    }
    this.$nextTick(() => {
      window.addEventListener(
        'storage',
        (e) => {
          if (e.key === tokenName || e.key === null) window.location.reload()
          if (e.key === tokenName && e.value === null) window.location.reload()
        },
        {
          capture: false,
          signal: this.controller?.signal,
        }
      )

      // 开发环境下暴露测试方法
      if (process.env.NODE_ENV === 'development') {
        window.forceShowTodoNotification = () => this.forceShowTodoNotification()
        window.testTodoNotification = () => this.testTodoNotification()
        console.log('🛠️ 待办通知测试方法已暴露:')
        console.log('  - window.forceShowTodoNotification() // 强制显示真实待办通知')
        console.log('  - window.testTodoNotification() // 显示测试通知')
      }
    })
  },
  methods: {
    ...mapActions({
      handleFoldSideBar: 'settings/foldSideBar',
    }),
    handleIsMobile() {
      return document.body.getBoundingClientRect().width - 1 < 992
    },
    handleResize() {
      if (!document.hidden) {
        const isMobile = this.handleIsMobile()
        if (isMobile) {
          //横向布局时如果是手机端访问那么改成纵向版
          this.$store.dispatch('settings/changeLayout', 'vertical')
        } else {
          this.$store.dispatch('settings/changeLayout', this.oldLayout)
        }

        this.$store.dispatch('settings/toggleDevice', isMobile ? 'mobile' : 'desktop')
      }
    },

    // 手动检查待办事项
    checkTodoManually() {
      if (this.$refs.todoNotification) {
        this.$refs.todoNotification.manualCheck()
      }
    },

    // 切换待办通知音频
    toggleTodoAudio(enabled) {
      if (this.$refs.todoNotification) {
        this.$refs.todoNotification.toggleAudio(enabled)
      }
    },

    // 测试待办通知（用于调试）
    testTodoNotification() {
      if (this.$refs.todoNotification) {
        this.$refs.todoNotification.testNotification()
      }
    },

    // 强制显示待办通知（用于测试真实数据）
    forceShowTodoNotification() {
      if (this.$refs.todoNotification) {
        this.$refs.todoNotification.forceShowNotification()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
@mixin fix-header {
  position: fixed;
  top: 0;
  left: 0;
  z-index: $base-z-index + 1;
  width: 100%;
  overflow: hidden;
}

.vue-admin-better-wrapper {
  position: relative;
  width: 100%;
  height: 100%;

  .layout-container-horizontal {
    position: relative;

    &.fixed {
      padding-top: calc(#{$base-top-bar-height} + #{$base-tabs-bar-height});
    }

    &.fixed.no-tabs-bar {
      padding-top: $base-top-bar-height;
    }

    ::v-deep {
      .vab-main {
        width: 88%;
        margin: auto;
      }

      .fixed-header {
        @include fix-header;
      }

      .tag-view-show {
        background: $base-color-white;
        box-shadow: $base-box-shadow;
      }

      .nav-bar-container {
        .fold-unfold {
          display: none;
        }
      }

      .main-padding {
        .app-main-container {
          margin-top: $base-padding;
          margin-bottom: $base-padding;
          // background: $base-color-white;
        }
      }
    }
  }

  .layout-container-vertical {
    position: relative;

    .mask {
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: $base-z-index - 1;
      width: 100%;
      height: 100vh;
      overflow: hidden;
      background: #000;
      opacity: 0.5;
    }

    &.fixed {
      padding-top: calc(#{$base-nav-bar-height});
    }

    &.fixed.no-tabs-bar {
      padding-top: $base-nav-bar-height;
    }

    &.fixed.no-message {
      padding-top: $base-nav-bar-height;
    }

    .vab-main {
      position: relative;
      min-height: 100%;
      margin-left: $base-left-menu-width;
      background: #f6f8f9;
      transition: $base-transition;

      ::v-deep {
        .fixed-header {
          @include fix-header;

          // left: $base-left-menu-width;
          // width: $base-right-content-width;
          box-shadow: $base-box-shadow;
          transition: $base-transition;
        }

        .nav-bar-container {
          position: relative;
          box-sizing: border-box;
        }

        .tabs-bar-container {
          box-sizing: border-box;
        }

        .app-main-container {
          width: calc(100% - #{$base-padding} - #{$base-padding});
          margin: $base-padding auto;
          // background: $base-color-white;
          border-radius: $base-border-radius;
        }
      }

      &.is-collapse-main {
        margin-left: $base-left-menu-width-min;

        ::v-deep {
          .fixed-header {
            // left: $base-left-menu-width-min;
            // width: calc(100% - #{$base-left-menu-width-min});
          }
        }
      }
      &.home-no-left {
        margin-left: 0;
      }
    }
  }

  /* 手机端开始 */
  &.mobile {
    ::v-deep {
      .el-pager,
      .el-pagination__jump {
        display: none;
      }

      .layout-container-vertical {
        .el-scrollbar.side-bar-container.is-collapse {
          width: 0;
        }

        .vab-main {
          width: 100%;
          margin-left: 0;
        }
      }

      .vab-main {
        .fixed-header {
          left: 0 !important;
          width: 100% !important;
        }
      }
    }
  }

  /* 手机端结束 */
}
</style>
