<template>
  <component
    :is="menuComponent"
    v-if="!item.hidden"
    :full-path="fullPath"
    :item="item"
    :route-children="routeChildren"
  >
    <template v-if="item.children && item.children.length">
      <div class="vab-side-bar-item-tab">
        <div
          v-for="route in item.children"
          :key="route.path"
          :item="route"
          disabled
          class="vab-side-bar-item-title"
          :class="keyClass(item.path + '/' + route.path)"
          @click="goPath(item.path + '/' + route.path)"
          v-if="!route.hidden"
        >
          <div>{{ route.meta.title }}</div>
        </div>
      </div>
    </template>
  </component>
</template>

<script>
import { mapGetters } from 'vuex'
import { isExternal } from '@/utils/validate'
import path from 'path'

export default {
  name: 'VabSideBarItem',
  props: {
    item: {
      type: Object,
      required: true,
    },
    fullPath: {
      type: String,
      default: '',
    },
  },

  data() {
    this.onlyOneChild = null
    return {}
  },
  computed: {
    ...mapGetters({
      collapse: 'settings/collapse',
    }),
    menuComponent() {
      if (
        this.handleChildren(this.item.children, this.item) &&
        (!this.routeChildren.children || this.routeChildren.notShowChildren) &&
        !this.item.alwaysShow
      ) {
        return 'VabMenuItem'
      } else {
        return 'VabSubmenu'
      }
    },
    keyClass() {
      return (path) => {
        if (this.$route.fullPath.split('?')[0] === path) {
          return 'key'
        } else {
          return ''
        }
      }
    },
  },
  methods: {
    handleChildren(children = [], parent) {
      if (children === null) children = []
      const showChildren = children.filter((item) => {
        if (item.hidden) {
          return false
        } else {
          this.routeChildren = item
          return true
        }
      })
      if (showChildren.length === 1) {
        return true
      }

      if (showChildren.length === 0) {
        this.routeChildren = {
          ...parent,
          path: '',
          notShowChildren: true,
        }
        return true
      }
      return false
    },
    handlePath(routePath) {
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(this.fullPath)) {
        return this.fullPath
      }
      return path.resolve(this.fullPath, routePath)
    },
    goPath(routePath) {
      console.log('goPath', routePath)
      this.$router.push(routePath)
    },
  },
}
</script>

<style lang="scss" scoped>
.vab-nav-icon {
  margin-right: 4px;
}

::v-deep {
  .el-tag {
    float: right;
    height: 16px;
    padding-right: 4px;
    padding-left: 4px;
    margin-top: calc((#{$base-menu-item-height} - 16px) / 2);
    line-height: 16px;
    border: 0;
  }
}
.vab-side-bar-item-tab {
  display: flex;
  flex-wrap: wrap;
  & > div {
    width: 50%;
    flex-shrink: 0;
    text-align: left;
    text-indent: 24px;
    &:hover {
      color: $base-menu-color-active;
    }
  }
}
.vab-side-bar-item-title {
  color: $base-color-666;
  line-height: 30px;
  font-size: $base-font-size-small;
  cursor: pointer;
}
.vab-side-bar-item-title.key {
  color: $base-menu-color-active;
}
</style>
