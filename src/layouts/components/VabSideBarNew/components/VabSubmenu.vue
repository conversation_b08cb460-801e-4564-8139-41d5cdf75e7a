<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-02-18 14:31:04
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-03-07 12:09:39
 * @FilePath: /qst-merchant-admin-2.0/src/layouts/components/VabSideBarNew/components/VabSubmenu.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-submenu ref="subMenu" :index="handlePath(item.path)" :popper-append-to-body="false">
    <template slot="title">
      <i v-if="item.meta.icon" :class="item.meta.icon"></i>
      <vab-remix-icon
        v-if="item.meta && item.meta.remixIcon"
        :icon-class="item.meta.remixIcon"
        class="vab-remix-icon"
      />
      <span>{{ item.meta.title }}</span>
    </template>
    <slot />
  </el-submenu>
</template>

<script>
import { isExternal } from '@/utils/validate'
import path from 'path'

export default {
  name: 'VabSubmenu',
  props: {
    routeChildren: {
      type: Object,
      default() {
        return null
      },
    },
    item: {
      type: Object,
      default() {
        return null
      },
    },
    fullPath: {
      type: String,
      default: '',
    },
  },
  methods: {
    handlePath(routePath) {
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(this.fullPath)) {
        return this.fullPath
      }
      return path.resolve(this.fullPath, routePath)
    },
  },
}
</script>
