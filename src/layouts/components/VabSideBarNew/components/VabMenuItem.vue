<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-02-18 14:31:04
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-03-07 12:09:34
 * @FilePath: /qst-merchant-admin-2.0/src/layouts/components/VabSideBarNew/components/VabMenuItem.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-menu-item :index="handlePath(routeChildren.path)" @click="handleLink">
    <i v-if="routeChildren.meta.icon" :class="routeChildren.meta.icon"></i>
    <span>{{ routeChildren.meta.title }}</span>
    <el-tag
      v-if="routeChildren.meta && routeChildren.meta.badge"
      effect="dark"
      type="danger"
    >{{ routeChildren.meta.badge }}</el-tag>
  </el-menu-item>
</template>

<script>
import { isExternal } from '@/utils/validate'
import path from 'path'

export default {
  name: 'VabMenuItem',
  props: {
    routeChildren: {
      type: Object,
      default() {
        return null
      },
    },
    item: {
      type: Object,
      default() {
        return null
      },
    },
    fullPath: {
      type: String,
      default: '',
    },
  },
  methods: {
    handlePath(routePath) {
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(this.fullPath)) {
        return this.fullPath
      }
      return path.resolve(this.fullPath, routePath)
    },
    handleLink() {
      const routePath = this.routeChildren.path
      const target = this.routeChildren.meta.target

      if (target === '_blank') {
        if (isExternal(routePath)) {
          window.open(routePath)
        } else if (isExternal(this.fullPath)) {
          window.open(this.fullPath)
        } else if (this.$route.path !== path.resolve(this.fullPath, routePath)) {
          let routeData = this.$router.resolve(path.resolve(this.fullPath, routePath))
          window.open(routeData.href)
        }
      } else {
        if (isExternal(routePath)) {
          window.location.href = routePath
        } else if (isExternal(this.fullPath)) {
          window.location.href = this.fullPath
        } else if (this.$route.path !== path.resolve(this.fullPath, routePath)) {
          this.$router.push(path.resolve(this.fullPath, routePath))
        }
      }
    },
  },
}
</script>
