<template>
  <el-scrollbar
    :class="{ 'is-collapse': collapse }"
    class="side-bar-container"
    v-if="childrenRoutes.length > 0"
  >
    <div class="side-main-margin">
      <div class="flex-b side-top">
        <div v-if="!collapse">{{title}}</div>
        <i
          :class="collapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'"
          :title="collapse ? '展开' : '收起'"
          class="fold-unfold"
          @click="handleCollapse"
        ></i>
      </div>
      <el-menu
        :active-text-color="variables['menu-color-active']"
        :background-color="variables['menu-background']"
        :collapse="collapse"
        :collapse-transition="false"
        :default-active="activeMenu"
        :default-openeds="defaultOpens"
        :text-color="variables['menu-color']"
        :unique-opened="uniqueOpened"
        mode="vertical"
      >
        <template v-for="route in childrenRoutes">
          <vab-side-bar-item
            v-if="route.name != 'Home'"
            :key="route.path"
            :full-path="route.path"
            :item="route"
          />
        </template>
      </el-menu>
    </div>
  </el-scrollbar>
</template>
<script>
import variables from '@/styles/variables.scss'
import { mapGetters, mapActions } from 'vuex'
import { defaultOopeneds, uniqueOpened } from '@/config'

export default {
  name: 'VabSideBarNew',
  data() {
    return {
      uniqueOpened,
      title: '',
    }
  },
  watch: {
    routeKey: {
      immediate: true,
      handler(val) {
        this.title = this.routesTitle.filter((item) => item.name == val)[0].meta.title
      },
    },
  },
  computed: {
    ...mapGetters({
      collapse: 'settings/collapse',
      childrenRoutes: 'routes/childrenRoutes',
      routeKey: 'routes/routeKey',
      routesTitle: 'routes/routesTitle',
    }),

    defaultOpens() {
      if (this.collapse) {
      }
      return defaultOopeneds
    },
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    variables() {
      return variables
    },
  },
  methods: {
    ...mapActions({
      changeCollapse: 'settings/changeCollapse',
    }),
    handleCollapse() {
      this.changeCollapse()
    },
  },
}
</script>
<style lang="scss" scoped>
.side-top {
  height: 50px;
  line-height: 50px;
  margin: 0 10px;
  background: $base-menu-background;
  font-size: 16px;
  border-bottom: 1px solid #e6e6e6;
  color: #303133;
  font-weight: 600;
  min-width: 200px;
}
@mixin active {
  &:hover {
    background: #f1f3f4 !important;
    color: $base-menu-color-active !important;
    i {
      color: $base-menu-color-active !important;
    }
  }

  &.is-active {
    color: $base-color-white;
    background-color: $base-menu-background-active !important;
  }

  &.is-active .vab-fas-icon {
    color: $base-menu-color-active;
  }
}

.side-bar-container {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: $base-z-index;
  width: $base-left-menu-width;
  height: 100vh;
  overflow: hidden;
  background: $base-menu-background;
  // box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
  transition: width $base-transition-time;
  box-sizing: border-box;
  padding-top: $base-nav-bar-height;

  .search-history-btn {
    padding: 4px 6px;
    background: #f4f6f8;
    border-radius: 4px;
    margin-right: 6px;
    margin-left: 0;
    margin-top: 10px;
  }

  .input-search {
    background: $base-search-background;
  }
  .side-main-margin {
    margin-left: 10px;
  }
  .fold-unfold {
    cursor: pointer;
  }
  &.is-collapse {
    width: $base-left-menu-width-min;
    border-right: 0;
    .fold-unfold {
      margin: 0 auto;
    }
    .side-main-margin {
      margin-left: 0;
    }
    .side-top {
      min-width: 0;
    }
    ::v-deep {
      .el-menu {
        transition: width $base-transition-time;
      }

      .el-menu--collapse {
        border-right: 0;

        .el-submenu__icon-arrow {
          right: 10px;
          margin-top: -3px;
        }

        .el-menu-item,
        .el-submenu {
          text-align: center;
        }
      }
    }
  }

  ::v-deep {
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }

    .el-menu {
      border: 0;

      .vab-fas-icon {
        padding-right: 3px;
        font-size: $base-font-size-default;
        display: inline-block;
        width: 14px;
        color: $base-color-666;
      }

      .vab-remix-icon {
        padding-right: 3px;
        font-size: $base-font-size-default + 2;
      }
    }

    .el-menu-item,
    .el-submenu__title {
      height: $base-menu-item-height;
      line-height: $base-menu-item-height;
      vertical-align: middle;
      padding: 0 !important;
      margin: 0 !important;
      color: #303133;
      font-size: 14px;

      i {
        font-size: 14px;
        font-weight: 400;
        color: #303133;
        margin-right: 0;
        &:hover {
          color: rgb(0, 113, 254);
        }
      }
    }
    .el-menu-item.is-active i {
      color: $base-menu-color-active;
    }
    .el-submenu {
      padding: 0 !important;
      margin: 0 !important;
    }

    .el-menu-item {
      @include active;
    }
    .el-submenu__title {
      @include active;
    }
  }
}
</style>
