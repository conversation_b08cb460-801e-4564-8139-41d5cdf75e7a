<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-02-18 14:31:04
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-03-19 10:29:34
 * @FilePath: /qst-merchant-admin-2.0/src/layouts/components/VabLogo/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div :class="'logo-container-' + layout">
    <router-link to="/">
      <!-- 这里是logo变更的位置 -->
      <el-image v-if="logo" :src="require('@/assets/logo.png')" class="logo"></el-image>
      <span
        :class="{ 'hidden-xs-only': layout === 'horizontal' }"
        :title="title"
        class="title"
      >{{ title }}</span>
    </router-link>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'

export default {
  name: 'VabLogo',
  data() {
    return {
      title: this.$baseTitle,
    }
  },
  computed: {
    ...mapGetters({
      logo: 'settings/logo',
      layout: 'settings/layout',
    }),
  },
}
</script>
<style lang="scss" scoped>
@mixin container {
  position: relative;
  height: $base-top-bar-height;
  overflow: hidden;
  line-height: $base-top-bar-height;
  background: $base-menu-background;
}

@mixin logo {
  display: inline-block;
  width: 36px;
  height: 33px;
  margin-right: 10px;
  color: $base-left-title-color;
  vertical-align: middle;
}

@mixin title {
  display: inline-block;
  overflow: hidden;
  font-size: 16px;
  line-height: 35px;
  color: $base-left-title-color;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
}

.logo-container-horizontal {
  @include container;

  .logo {
    @include logo;
  }

  .title {
    @include title;
  }
}

.logo-container-vertical {
  @include container;

  height: $base-logo-height;
  line-height: $base-logo-height;
  text-align: center;
  width: calc(#{$base-left-menu-width} - 20px);

  .logo {
    @include logo;
  }

  .title {
    @include title;

    max-width: calc(#{$base-left-menu-width} - 60px);
  }
}
</style>
