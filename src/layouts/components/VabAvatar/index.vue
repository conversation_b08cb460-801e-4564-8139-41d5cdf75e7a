<template>
  <div class="user-dropdown-model">
    <el-dropdown @command="handleCommand">
      <span class="avatar-dropdown">
        <!--<el-avatar class="user-avatar" :src="avatar"></el-avatar>-->
        <img :src="avatar" alt class="user-avatar" />
        <div class="user-name">
          {{ username }}
          <i class="el-icon-arrow-down el-icon--right"></i>
        </div>
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item divided>
          <div class="avatar-main">
            <img @click="handleCommand('personalCenter')" :src="avatar" alt class="user-avatar" />
            <div class>
              <div class>{{ username }}</div>
              <div class="flex avatar-main-edit">
                <div @click="handleCommand('personalCenter')">个人中心</div>
                <div @click="handleCommand('logout')">退出</div>
              </div>
            </div>
          </div>
        </el-dropdown-item>

        <el-dropdown-item command="changeLogin" divided v-if="isShowWindows">
          <i class="iconfont icon-qiehuan"></i>
          <span>切换登录商家</span>
        </el-dropdown-item>

        <el-dropdown-item command="editPassword" divided>
          <i class="iconfont icon-jiesuo"></i>
          <span>修改密码</span>
        </el-dropdown-item>

        <el-dropdown-item command="logout" divided>
          <i class="iconfont icon-tuichu"></i>
          <span>退出登录</span>
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { recordRoute } from '@/config'
import store from '@/store'
export default {
  name: 'VabAvatar',
  computed: {
    ...mapGetters({
      avatar: 'user/avatar',
      username: 'user/username',
      accessToken: 'user/accessToken',
      isShowWindows: 'user/isShowWindows',
    }),
  },
  watch: {
    accessToken() {
      let loginBySms = localStorage.getItem('loginBySms')
      if (loginBySms) {
        this.loginBySms = JSON.parse(loginBySms) || {}
      }
    },
  },
  data() {
    return {
      loginBySms: {},
    }
  },
  mounted() {
    let loginBySms = localStorage.getItem('loginBySms')
    if (loginBySms) {
      this.loginBySms = JSON.parse(loginBySms) || {}
    }
  },
  methods: {
    handleCommand(command) {
      console.log(this.loginBySms.loginType)
      switch (command) {
        case 'changeLogin':
          store.dispatch('user/setIsMerchant', this.loginBySms.loginType == 'pwd' ? '1' : this.loginBySms.loginType == 'phone' ? '2' : '')
          break
        case 'personalCenter':
          this.$router.push('/personal/personalCenter')
          break
        case 'editPassword':
          store.dispatch('user/setUserEdit', 'password')
          break
        case 'logout':
          this.logout()
          break
      }
    },
    personalCenter() {},
    logout() {
      this.$baseConfirm('您确定要退出' + this.$baseTitle + '吗?', null, async () => {
        this.$router.push(`/login`)
        this.$store.dispatch('user/logout')
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.avatar-dropdown {
  display: flex;
  align-content: center;
  align-items: center;
  justify-content: center;
  justify-items: center;
  height: 50px;
  padding: 0;

  .user-avatar {
    width: 40px;
    height: 40px;
    cursor: pointer;
    border-radius: 50%;
  }

  .user-name {
    position: relative;
    margin-left: 5px;
    margin-left: 5px;
    cursor: pointer;
  }
}

.avatar-main {
  display: flex;
  font-weight: 600;
  font-size: 14px;
  color: #000000;
  border-bottom: 1px solid #f5f7fa;
  padding-bottom: 10px;
  .avatar-main-edit {
    font-size: 12px;
    color: #606266;
    line-height: 12px;
  }
  .user-avatar {
    width: 50px;
    height: 50px;
    cursor: pointer;
    border-radius: 50%;
    margin-right: 10px;
  }
  &-edit {
    font-weight: 400;
    font-size: 12px;
    color: #606266;
    line-height: 12px;
    > div + div {
      margin-left: 12px;
      padding-left: 12px;
      border-left: 1px solid #606266;
    }
  }
}
::v-deep {
  .el-dropdown-menu__item--divided {
    border: 0;
  }
  .el-dropdown-menu--small {
    min-width: 240px;
  }
  .el-dropdown-menu__item:not(.is-disabled):hover,
  .el-dropdown-menu__item:focus {
    background-color: #fff !important;
    color: #606266 !important;
  }
}
</style>
