<template>
  <div class="nav-bar-container">
    <!-- <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="4">
        <div class="left-panel">
          <i
            :class="collapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'"
            :title="collapse ? '展开' : '收起'"
            class="fold-unfold"
            @click="handleCollapse"
          ></i>
          <vab-breadcrumb class="hidden-xs-only" />
        </div>
    </el-col>-->
    <div class="flex">
      <vab-logo></vab-logo>
      <div class="flex router-list">
        <div
          @click="selectRouter(item)"
          :class="routeKey == item.name ? 'key': ''"
          v-for="item in routesTitle"
          :key="item.name"
          v-if="!item.hidden"
        >{{item.meta.title}}</div>
      </div>
    </div>

    <div class="right-panel">
      <!-- 消息铃铛图标 -->
      <el-popover
        placement="bottom-end"
        width="400"
        trigger="click"
        v-model="messagePopoverVisible"
        popper-class="message-popover"
      >
        <div class="message-dropdown">
          <!-- 自定义标签页头部 -->
          <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 16px; border-bottom: 1px solid #e8e8e8;">
            <div style="display: flex;">
              <div
                :class="['custom-tab', { 'active': activeTab === 'message' }]"
                @click="activeTab = 'message'"
                style="padding: 8px 16px; cursor: pointer; margin-right: 16px; border-radius: 4px; transition: all 0.3s; font-size: 16px;"
              >
                消息 <span class="tab-count">({{ messageCount }})</span>
              </div>
              <div
                :class="['custom-tab', { 'active': activeTab === 'todo' }]"
                @click="activeTab = 'todo'"
                style="padding: 8px 16px; cursor: pointer; border-radius: 4px; transition: all 0.3s; font-size: 16px;"
              >
                待办 <span class="tab-count">({{ todoCount }})</span>
              </div>
            </div>
            <el-button
              type="text"
              @click="activeTab === 'todo' ? clearAllTodos() : markAllAsRead()"
              class="mark-all-read"
              style="font-size: 14px; color: #6E6E7A; padding: 4px 8px; display: flex; align-items: flex-end; gap: 4px;"
            >
              <img
                src="/static/views/layouts/VabNavBar/<EMAIL>"
                :alt="activeTab === 'todo' ? '清空待办' : '全部已读'"
                style="width: 14px; height: 14px; margin-bottom: -1px;"
              />
              {{ activeTab === 'todo' ? '清空待办' : '全部已读' }}
            </el-button>
          </div>

          <!-- 内容区域 -->
          <div v-show="activeTab === 'message'" class="message-list">

                <!-- 消息列表 -->
                <div class="message-items" style="padding: 8px 0;">
                  <div
                    v-for="(item, index) in messageList"
                    :key="item.notice_id"
                    class="message-item"
                    @click="handleMessageClick(item)"
                    @mouseenter="$event.target.style.backgroundColor = '#f5f5f5'"
                    @mouseleave="$event.target.style.backgroundColor = 'transparent'"
                    :style="{
                      display: 'flex',
                      alignItems: 'flex-start',
                      padding: '8px 16px',
                      marginBottom: '6px',
                      cursor: 'pointer',
                      transition: 'background-color 0.3s',
                      position: 'relative'
                    }"
                  >
                    <div style="flex: 1; min-width: 0; padding-right: 80px;">
                      <div
                        :style="getTitleStyle(item.is_read)"
                        style="display: flex; align-items: center; gap: 6px;"
                      >
                        {{ item.type_text }}
                        <span
                          v-if="isUnread(item.is_read)"
                          style="width: 8px; height: 8px; background-color: #FF2727; border-radius: 50%; flex-shrink: 0;"
                        ></span>
                      </div>
                      <div
                        :style="getDescStyle(item.is_read)"
                      >
                        {{ item.title }}
                      </div>
                      <div style="font-size: 12px; color: #999; margin-top: 4px;">{{ item.created_at }}</div>
                    </div>
                    <div style="position: absolute; top: 16px; right: 16px;">
                      <div
                        :style="getImportanceLevelStyle(item.level)"
                        :title="`重要等级: ${item.level_text}`"
                      >
                        {{ item.level_text }}
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 消息底部 -->
                <div style="padding: 12px 16px; border-top: 1px solid #f0f0f0; display: flex; justify-content: center; background-color: #fafafa;">
                  <el-button
                    type="text"
                    @click="viewAllMessages"
                    class="view-more"
                    style="font-size: 12px; color: #0071fe; padding: 0;"
                  >
                    查看更多
                  </el-button>
                </div>
              </div>

          <div v-show="activeTab === 'todo'" class="todo-list">
            <div
              v-for="item in todoList"
              :key="item.id"
              class="todo-item"
              @click="handleTodoClick(item)"
              style="display: flex; align-items: center; padding: 8px 16px; margin-bottom: 6px; cursor: pointer; transition: background-color 0.3s;"
            >
              <div class="todo-icon" style="width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; background-color: #ECF5FF; border: 1px solid #D3E6FF; border-radius: 50%; margin-right: 12px; flex-shrink: 0;">
                <i class="el-icon-bell" style="color: #0071FE; font-size: 16px;"></i>
              </div>
              <div class="todo-content" style="flex: 1; min-width: 0;">
                <div class="todo-title" style="font-size: 14px; line-height: 1.4; color: #222222; font-weight: 500;">{{ item.title }}</div>
              </div>
            </div>
          </div>
        </div>

        <div slot="reference" class="bell-container">
          <el-badge :value="totalUnreadCount" :hidden="totalUnreadCount === 0" class="bell-badge">
            <i class="el-icon-bell bell-icon" title="消息"></i>
          </el-badge>
        </div>
      </el-popover>

      <!-- <vab-error-log />
          <vab-full-screen-bar @refresh="refreshRoute" />
          <vab-theme-bar class="hidden-xs-only" />
      <vab-icon :icon="['fas', 'redo']" :pulse="pulse" title="重载所有路由" @click="refreshRoute" />-->
      <vab-avatar />
      <!--  <vab-icon
            title="退出系统"
            :icon="['fas', 'sign-out-alt']"
            @click="logout"
      />-->
    </div>

    <!-- 消息详情弹窗 -->
    <SystemDetails
      :visible.sync="detailDialogVisible"
      :notification-data="currentNotification"
      @close="handleCloseDetail"
      @closed="handleDetailClosed"
    />
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import store from '@/store'
import messageManager from '@/utils/messageManager'
import SystemDetails from '@/components/SystemDetails/index.vue'
import { remind, clearTodo, listTodoType, noticeList, markNoticeRead, noticeDetail, filterNotices } from '@/api/shop/message.js'

export default {
  name: 'VabNavBar',
  components: {
    SystemDetails
  },
  data() {
    return {
      pulse: false,
      timeOutID: null,
      routeKey: '',
      // 消息弹窗相关
      messagePopoverVisible: false,
      activeTab: 'message',
      messageCount: 0,
      todoCount: 0,
      remindCount: 0, // 铃铛数据（待办数量+未读消息）
      // 消息列表数据
      messageList: [],
      // 待办事项数据
      todoList: [],
      // 详情弹窗相关
      detailDialogVisible: false,
      currentNotification: null,
      // 字典数据
      dictionaries: {
        types: [], // 通知类型字典
        levels: [], // 重要等级字典
        readStatus: [] // 读取状态字典
      }
    }
  },
  computed: {
    ...mapGetters({
      collapse: 'settings/collapse',
      visitedRoutes: 'tabsBar/visitedRoutes',
      device: 'settings/device',
      routes: 'routes/routes',
      routesTitle: 'routes/routesTitle',
    }),
    // 计算总的未读消息数量（使用接口返回的数据）
    totalUnreadCount() {
      return this.remindCount
    }
  },

  async created() {
    // 加载字典数据、统计数据、消息列表和待办列表
    await this.loadDictionaries()
    await this.loadRemindData()
    await this.loadMessageList()
    await this.loadTodoList()

    // 监听消息更新事件（保留以防其他地方需要）
    messageManager.on('message-updated', this.handleMessageUpdate)
    messageManager.on('todos-updated', this.handleTodoUpdate)
  },


  methods: {
    // 加载字典数据
    async loadDictionaries() {
      try {
        const result = await filterNotices()
        if (result.code === 200 && result.data) {
          // 保存字典数据
          this.dictionaries.types = result.data.type || []
          this.dictionaries.levels = result.data.level || []
          this.dictionaries.readStatus = result.data.read || []
        }
      } catch (error) {
        // 静默处理错误
      }
    },
    ...mapActions({
      changeCollapse: 'settings/changeCollapse',
    }),
    handleCollapse() {
      this.changeCollapse()
    },

    // 加载提醒统计数据
    async loadRemindData() {
      try {
        console.log('开始调用 remind 接口...')
        const result = await remind()
        console.log('remind 接口返回结果:', result)

        if (result.code === 200 && result.data) {
          this.messageCount = parseInt(result.data.unread_count) || 0
          this.todoCount = parseInt(result.data.todo_count) || 0
          this.remindCount = parseInt(result.data.remind_count) || 0
          console.log('统计数据更新:', {
            messageCount: this.messageCount,
            todoCount: this.todoCount,
            remindCount: this.remindCount
          })
        } else {
          console.error('获取提醒统计数据失败:', result.msg)
        }
      } catch (error) {
        console.error('加载提醒统计数据失败:', error)
      }
    },

    // 加载系统通知消息列表
    async loadMessageList() {
      try {
        const result = await noticeList({
          page: 1,
          limit: 10,
          is_read: '' // TODO 测试结束之后传N
        })

        if (result.code === 200 && result.data && result.data.list) {
          // 直接使用接口返回的数据，不进行转换
          this.messageList = result.data.list
          // 不重新计算消息数量，以 remind 接口的统计数据为准
        } else {
          this.messageList = []
          // 不重新计算消息数量，以 remind 接口的统计数据为准
        }
      } catch (error) {
        this.messageList = []
        // 不重新计算消息数量，以 remind 接口的统计数据为准
      }
    },

    // 加载待办消息列表
    async loadTodoList() {
      try {
        const result = await listTodoType()
        if (result.code === 200 && result.data) {
          this.todoList = result.data.map(item => ({
            id: item.system_todo_id,
            type: item.system_todo_id,
            title: item.msg,
            count: item.total_num,
            description: item.msg
          }))
        }
      } catch (error) {
        // 静默处理错误
      }
    },

    // 判断是否未读（与系统通知页面保持一致）
    isUnread(isRead) {
      return isRead === 'N' || isRead === 0 || isRead === false || isRead === '0'
    },



    async refreshRoute() {
      this.$baseEventBus.$emit('reload-router-view')
      this.pulse = true
      this.timeOutID = setTimeout(() => {
        this.pulse = false
      }, 1000)
    },
    // 选择切换路由
    selectRouter(item) {
      let path = item.redirect
      if (item.children.length > 0 && item.children[0].children && item.children[0].children.length > 0) {
        path = item?.children[0]?.path + '/' + item?.children[0]?.children[0]?.path
      }
      console.log(path)
      this.$router.push({ path: path })
      this.setTitle(item)
    },

    findNodeByName(data, name) {
      for (const node of data) {
        if (node.name === name) return node
        if (node.children && node.children.length > 0) {
          const found = this.findNodeByName(node.children, name)
          if (found) return found
        }
      }
      return null
    },

    findNodeByUid(data, uid) {
      for (const node of data) {
        if (node.uid === uid) return node
        if (node.children && node.children.length > 0) {
          const found = this.findNodeByUid(node.children, uid)
          if (found) return found
        }
      }
      return null
    },

    //  设置标题
    setTitle(item) {
      console.log(item)
      this.routeKey = item.name
      store.dispatch('routes/setRouteKey', item.name)
      localStorage.setItem('routeKey', this.routeKey)
      if (!item.component) {
        store.dispatch('routes/setChildrenRoutes', item?.children || [])
      } else {
        store.dispatch('routes/setChildrenRoutes', [])
      }
    },

    // 消息相关方法
    async handleMessageClick(item) {
      try {
        // 获取消息详情
        const result = await noticeDetail({ notice_id: item.notice_id })

        if (result.code === 200 && result.data) {
          // 设置当前通知数据
          this.currentNotification = result.data

          // 显示详情弹窗
          this.detailDialogVisible = true

          // 如果是未读状态，自动标记为已读
          if (this.isUnread(item.is_read)) {
            try {
              const markResult = await markNoticeRead({ notice_id: item.notice_id })
              if (markResult.code === 200) {
                // 更新本地状态
                item.is_read = 'Y'
                // 刷新统计数据和消息列表
                await this.loadRemindData()
                await this.loadMessageList()
              }
            } catch (error) {
              // 静默处理标记已读失败
            }
          }
        } else {
          this.$message.error(result.msg || '获取通知详情失败')
        }
      } catch (error) {
        console.error('获取通知详情失败:', error)
        this.$message.error('获取通知详情失败: ' + error.message)
      }

      // 关闭消息弹窗
      this.messagePopoverVisible = false
    },

    handleTodoClick(item) {
      // 根据待办类型跳转到不同页面
      const todoType = item.type

      console.log('待办点击跳转:', { type: todoType, title: item.title })

      // 根据实际路由配置进行跳转
      switch (todoType) {
        case 1: // 订单待发货
          // 跳转到订单列表页面
          this.$router.push('/order/orderList')
          break
        case 2: // 售后待审核
        case 3: // 售后审核即将超时
        case 4: // 售后待商家收货
          // 跳转到售后订单页面
          this.$router.push('/order/orderAfter')
          break
        case 5: // 商品库存预警
          // 跳转到库存预警页面
          this.$router.push('/goodsSet/stock')
          break
        case 6: // 商品评价
          // 跳转到商品评价页面
          this.$router.push('/goodsManagement/productEvaluation')
          break
        case 7: // 商家入驻申请
          // 这个功能在当前路由中没有找到对应页面，显示提示
          this.$message({
            message: '商家入驻申请功能页面暂未配置，请联系管理员',
            type: 'warning',
            duration: 3000
          })
          break
        case 8: // 卡券超期下架
          // 跳转到商品管理页面
          this.$router.push('/goodsManagement/index')
          break
        default:
          // 默认跳转到待办配置页面
          this.$router.push('/message/toBeDone')
          break
      }

      this.messagePopoverVisible = false
    },

    viewAllMessages() {
      // 查看所有消息，跳转到系统通知页面
      this.$router.push({ name: 'systemMessage' })
      this.messagePopoverVisible = false
    },

    viewAllTodos() {
      // 查看所有待办事项，跳转到待办配置页面
      this.$router.push({ name: 'toBeDone' })
      this.messagePopoverVisible = false
    },

    // 全部已读
    async markAllAsRead() {
      // 获取当前未读消息
      const unreadMessages = this.messageList.filter(msg => this.isUnread(msg.is_read))
      if (unreadMessages.length === 0) {
        this.$message.info('暂无未读消息')
        return
      }

      try {
        // 逐个标记未读消息为已读
        const markPromises = unreadMessages.map(msg =>
          markNoticeRead({ notice_id: msg.notice_id })
        )

        await Promise.all(markPromises)

        // 刷新数据
        await this.loadRemindData()
        await this.loadMessageList()

        this.$message.success('已标记全部消息为已读')
      } catch (error) {
        this.$message.error('标记已读失败，请重试')
      }
    },



    // 获取标题样式
    getTitleStyle(isRead) {
      const baseStyle = {
        fontSize: '14px',
        marginBottom: '4px',
        lineHeight: '1.4',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap'
      }

      if (this.isUnread(isRead)) {
        return {
          ...baseStyle,
          color: '#222222',
          fontWeight: 'bold'
        }
      } else {
        return {
          ...baseStyle,
          color: '#8C8C8C',
          fontWeight: 'normal'
        }
      }
    },

    // 获取描述样式
    getDescStyle(isRead) {
      const baseStyle = {
        fontSize: '12px',
        lineHeight: '1.3',
        marginBottom: '6px',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap'
      }

      if (this.isUnread(isRead)) {
        return {
          ...baseStyle,
          color: '#222222'
        }
      } else {
        return {
          ...baseStyle,
          color: '#8C8C8C'
        }
      }
    },

    // 获取重要等级样式
    getImportanceLevelStyle(levelText) {
      const baseStyle = {
        fontSize: '12px',
        padding: '2px 6px',
        borderRadius: '4px',
        whiteSpace: 'nowrap'
      }

      // 根据接口返回的汉字设置颜色
      if (levelText === 1) {
        return {
          ...baseStyle,
          backgroundColor: '#FFF2F2',
          color: '#FF2727',
          border: '1px solid #FFCCCC'
        }
      } else if (levelText === 2) {
        return {
          ...baseStyle,
          backgroundColor: '#FFFBE6',
          color: '#F6A70E',
          border: '1px solid #FFE4AF'
        }
      } else {
        // 普通或其他
        return {
          ...baseStyle,
          backgroundColor: '#F5F7FA',
          color: '#A8ABB2',
          border: '1px solid #E9E9EB'
        }
      }
    },

    clearAllTodos() {
      this.$confirm('确定要清空所有待办事项吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          // 调用清空待办接口
          const result = await clearTodo()

          if (result.code === 200) {
            // 清空本地数据
            messageManager.clearAllTodos()
            this.$message.success('待办事项已清空')
            // 刷新统计数据和待办列表
            await this.loadRemindData()
            await this.loadTodoList()
          }
        } catch (error) {
          console.error('清空待办失败:', error)
        }
      }).catch(() => {})
    },

    // 处理消息更新事件（现在主要用于兼容性，实际数据来自API）
    handleMessageUpdate(messages) {
      // 保留此方法以防其他地方调用，但不再更新数据
    },

    // 处理待办更新事件
    handleTodoUpdate(todos) {
      this.todoList = todos
      this.todoCount = messageManager.getTodoCount()
    },

    // 计算当前消息列表中的未读数量
    updateMessageCount() {
      this.messageCount = this.messageList.filter(msg => this.isUnread(msg.is_read)).length
    },

    // 关闭详情弹窗
    handleCloseDetail() {
      this.detailDialogVisible = false
    },

    // 详情弹窗关闭后的回调
    handleDetailClosed() {
      this.currentNotification = null
    },
  },

  mounted() {
    this.routeKey = localStorage.getItem('routeKey')
  },

  beforeDestroy() {
    clearTimeout(this.timeOutID)
    // 移除事件监听
    messageManager.off('message-updated', this.handleMessageUpdate)
    messageManager.off('todos-updated', this.handleTodoUpdate)
  },

  watch: {
    // 监听消息列表变化（不重新计算数量，以 remind 接口统计为准）
    messageList: {
      handler(newList) {
        // 消息列表变化时不重新计算数量，保持 remind 接口返回的统计数据
      },
      deep: true,
      immediate: true
    },

    $route: {
      immediate: true,
      handler(val) {
        let name = val.name
        // 主逻辑
        const roleDetailNode = this.findNodeByName(this.routesTitle, name)
        console.log(this.routesTitle, name)

        if (roleDetailNode) {
          const parentNode = this.findNodeByUid(this.routesTitle, roleDetailNode.parent_uid)
          if (parentNode) {
            const merchantNode = this.findNodeByUid(this.routesTitle, parentNode.parent_uid)
            this.setTitle(merchantNode ? merchantNode : parentNode)
          }
        }
      },
      deep: true,
    },
  },
}
</script>

<style lang="scss" scoped>
.router-list div {
  margin: 0 20px;
  font-size: 14px;
  color: #595959;
  line-height: $base-nav-bar-height;
  cursor: pointer;
  &.key {
    color: #0071fe;
    font-weight: 600;
    position: relative;
  }
  &.key::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%, 0);
    width: 50%;
    height: 2px;
    background-color: #0071fe;
  }
}
.nav-bar-container {
  position: fixed;
  left: 0;
  height: $base-nav-bar-height;
  padding-right: $base-padding;
  padding-left: $base-padding;
  overflow: hidden;
  user-select: none;
  background: $base-color-white;
  box-shadow: $base-box-shadow;
  display: flex;
  justify-content: space-between;

  .left-panel {
    display: flex;
    align-items: center;
    justify-items: center;
    height: $base-nav-bar-height;

    .fold-unfold {
      color: $base-color-gray;
      cursor: pointer;
    }

    ::v-deep {
      .breadcrumb-container {
        margin-left: 10px;
      }
    }
  }

  .right-panel {
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: flex-end;
    height: $base-nav-bar-height;

    .bell-container {
      margin-right: 15px;

      .bell-icon {
        font-size: 18px;
        color: $base-color-gray;
        cursor: pointer;
        transition: color 0.3s;

        &:hover {
          color: #0071fe;
        }
      }

      .bell-badge {
        ::v-deep .el-badge__content {
          background-color: #ff4d4f;
          border: none;
        }
      }
    }

    i {
      cursor: pointer;
      margin-right: 15px;
    }
    ::v-deep {
      svg {
        width: 1em;
        height: 1em;
        margin-right: 15px;
        font-size: $base-font-size-small;
        color: $base-color-gray;
        cursor: pointer;
        fill: $base-color-gray;
      }

      button {
        svg {
          margin-right: 0;
          color: $base-color-white;
          cursor: pointer;
          fill: $base-color-white;
        }
      }

      .el-badge {
        margin-right: 15px;
      }
    }
  }
}

// 自定义标签页样式
.custom-tab {
  font-size: 16px !important;
  color: #222222 !important;

  &.active {
    color: #222222 !important;
    background-color: #f0f8ff !important;
    font-weight: 500 !important;

    .tab-count {
      color: #222222 !important;
    }
  }

  &:hover {
    color: #222222 !important;
    background-color: #f5f5f5 !important;

    .tab-count {
      color: #222222 !important;
    }
  }

  .tab-count {
    color: #222222 !important;
    font-weight: 500 !important;
  }
}

// 全部已读按钮样式
.mark-all-read {
  color: #6E6E7A !important;
  font-size: 14px !important;
  display: flex !important;
  align-items: flex-end !important;
  gap: 4px !important;

  &:hover {
    color: #6E6E7A !important;
  }

  img {
    width: 14px !important;
    height: 14px !important;
    opacity: 0.7;
    transition: opacity 0.3s;
    margin-bottom: -1px !important;
    margin-top: 1px;
  }

  &:hover img {
    opacity: 1;
  }
}

// 消息弹窗样式 - v2.0
::v-deep .message-popover {
  padding: 0 !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;

  .message-dropdown {
    // 移除了原始标签页样式，使用自定义布局

    .message-list, .todo-list {
      max-height: 350px;
      overflow-y: auto;
    }

      .message-item, .todo-item {
        display: flex !important;
        align-items: flex-start !important;
        padding: 12px 16px !important;
        cursor: pointer !important;
        border-bottom: 1px solid #f0f0f0 !important;
        transition: background-color 0.3s !important;
        position: relative !important;

        &:hover {
          background-color: #f5f5f5 !important;
        }

        &:last-child {
          border-bottom: none !important;
        }

        .message-content, .todo-content {
          flex: 1 !important;
          min-width: 0 !important;
          padding-right: 80px !important; // 为右侧重要等级留出空间

          .message-title, .todo-title {
            font-size: 14px !important;
            margin-bottom: 2px !important;
            line-height: 1.4 !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            white-space: nowrap !important;

            // 未读消息标题样式
            &.unread {
              color: #222222 !important;
              font-weight: bold !important;
            }

            // 已读消息标题样式
            &.read {
              color: #8C8C8C !important;
              font-weight: normal !important;
            }
          }

          .message-desc {
            font-size: 12px !important;
            line-height: 1.2 !important;
            margin-bottom: 4px !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            white-space: nowrap !important;

            // 未读消息内容样式
            &.unread {
              color: #222222 !important;
            }

            // 已读消息内容样式
            &.read {
              color: #8C8C8C !important;
            }
          }

          .message-time, .todo-time {
            font-size: 12px !important;
            color: #999 !important;
          }
        }

        .message-right {
          position: absolute !important;
          top: 12px !important;
          right: 16px !important;
          display: flex !important;
          flex-direction: column !important;
          align-items: flex-end !important;

          .importance-level {
            font-size: 12px !important;
            padding: 2px 6px !important;
            border-radius: 4px !important;
            white-space: nowrap !important;

            &.high {
              background-color: #fff2f0 !important;
              color: #ff4d4f !important;
              border: 1px solid #ffccc7 !important;
            }

            &.medium {
              background-color: #fff7e6 !important;
              color: #fa8c16 !important;
              border: 1px solid #ffd591 !important;
            }

            &.low {
              background-color: #f6ffed !important;
              color: #52c41a !important;
              border: 1px solid #b7eb8f !important;
            }
          }
        }
      }

      .message-footer, .todo-footer {
        padding: 12px 16px;
        border-top: 1px solid #f0f0f0;
        display: flex;
        justify-content: center;
        background-color: #fafafa;

        .view-more {
          font-size: 12px;
          color: #0071fe;
          padding: 0;

          &:hover {
            color: #005bb5;
          }
        }
      }
    }
  }

// 控制消息 Popover 的层级，确保详情弹窗能显示在上面
::v-deep .message-popover {
  z-index: 2000 !important;
}

::v-deep .el-popover.message-popover {
  z-index: 2000 !important;
}

// 确保详情弹窗在消息 Popover 之上
::v-deep .notification-dialog {
  z-index: 3000 !important;
}

</style>
