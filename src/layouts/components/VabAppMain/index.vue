<template>
  <div v-if="routerView" class="app-main-container">
    <!-- <vab-github-corner /> -->
    <transition name="fade" enter-active-class="active" leave-active-class="leave">
      <!-- 项目自带 -->
      <keep-alive :include="cachedRoutes" :max="keepAliveMaxNum">
        <router-view :key="key" class="app-main-height" />
      </keep-alive>
      <!-- <div>
        <keep-alive>
          <router-view v-if="$route.meta.keepAlive == 'Y'" class="app-main-height"></router-view>
        </keep-alive>
        <router-view v-if="$route.meta.keepAlive != 'Y'" class="app-main-height"></router-view>
      </div>-->
    </transition>
    <!-- <footer v-show="footerCopyright" class="footer-copyright">
      Copyright
      <vab-icon :icon="['fas', 'copyright']"></vab-icon>
      vue-admin-better 开源免费版 {{ fullYear }}
    </footer>-->
    <select-company></select-company>
    <change-password></change-password>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import { copyright, footerCopyright, keepAliveMaxNum, title } from '@/config'
import SelectCompany from '@/components/SelectCompany/index.vue'

export default {
  name: 'VabAppMain',
  data() {
    return {
      show: false,
      fullYear: new Date().getFullYear(),
      copyright,
      title,
      keepAliveMaxNum,
      routerView: true,
      footerCopyright,
    }
  },
  components: {
    SelectCompany,
  },
  computed: {
    ...mapGetters({
      visitedRoutes: 'tabsBar/visitedRoutes',
      device: 'settings/device',
    }),
    cachedRoutes() {
      const cachedRoutesArr = []
      this.visitedRoutes.forEach((item) => {
        if (item.meta.keepAlive == 'Y') {
          cachedRoutesArr.push(item.name)
        }
      })
      console.log('cachedRoutesArr' + '0-0-0-0-0-0-0-0-0-0')
      console.log(cachedRoutesArr)
      return cachedRoutesArr
    },
    key() {
      return this.$route.path
    },
  },
  watch: {
    $route: {
      handler(route) {
        if ('mobile' === this.device) this.foldSideBar()
      },
      immediate: true,
    },
  },
  created() {
    const handleReloadRouterView = () => {
      this.routerView = false
      this.$nextTick(() => {
        this.routerView = true
      })
    }

    //重载所有路由
    this.$baseEventBus.$on('reload-router-view', handleReloadRouterView)

    this.$once('hook:beforeDestroy', () => {
      this.$baseEventBus.$off('reload-router-view', handleReloadRouterView)
    })
  },
  mounted() {},
  methods: {
    ...mapActions({
      foldSideBar: 'settings/foldSideBar',
    }),
  },
}
</script>

<style lang="scss" scoped>
.app-main-container {
  position: relative;
  width: 100%;
  overflow: hidden;

  .vab-keel {
    margin: $base-padding;
  }

  .app-main-height {
    min-height: $base-app-main-height;
  }

  .footer-copyright {
    min-height: 55px;
    line-height: 55px;
    color: rgba(0, 0, 0, 0.45);
    text-align: center;
    border-top: 1px dashed $base-border-color;
  }
}

@keyframes bounce-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes bounce-leave {
  0% {
    transform: translateX(0%);
    height: 0;
  }
  100% {
    transform: translateX(100%);
    height: 0;
  }
}
.active {
  transform-origin: left center;
  animation: bounce-in 1s;
}
.leave {
  height: 0px;
  display: none;
  transform-origin: left center;
  animation: bounce-in 0s reverse;
}
</style>
