<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-03-03 14:23:18
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-03-22 09:42:30
 * @FilePath: /qst-merchant-admin-2.0/src/layouts/components/VabMessage/index.vue
 * @Description: 消息提示栏 - 展示最新未读消息
-->
<template>
  <div>
    <div class="vab-message" v-show="latestNotice && latestNotice.title">
      <div class="message-item">
        <img src="/static/views/layouts/VabNavBar/喇叭.png" alt="喇叭" class="message-icon" />
        <div class="message-content">
          <div class="single-message">
            {{ latestNotice.title }}
          </div>
        </div>
      </div>
      <div class="message-btn">
        <el-button type="text" @click="viewCurrentMessage">查看详情</el-button>
        <i @click="closeCurrentMessage" class="el-icon-close"></i>
      </div>
    </div>

    <!-- 通知详情弹窗 -->
    <SystemDetails
      :visible.sync="detailDialogVisible"
      :notification-data="currentNotification"
      @close="handleCloseDetail"
      @closed="handleDetailClosed"
    />
  </div>
</template>

<script>
import store from '@/store'
import messageManager from '@/utils/messageManager'
import SystemDetails from '@/components/SystemDetails/index.vue'
import { getLatestNotice, noticeDetail } from '@/api/shop/message.js'

export default {
  name: 'VabMessage',
  components: {
    SystemDetails
  },
  data() {
    return {
      currentIndex: 0,
      scrollTimer: null,
      unreadMessages: [],
      // 最新喇叭通知
      latestNotice: null,
      // 定时刷新定时器
      refreshTimer: null,
      // 详情弹窗相关
      detailDialogVisible: false,
      currentNotification: null
    }
  },
  computed: {
    hasUnreadMessages() {
      return this.latestNotice && this.latestNotice.notice_id && this.latestNotice.title
    },
    currentMessage() {
      return this.latestNotice || {}
    },
    // 创建用于显示的消息列表，包含重复项以实现无限滚动
    displayMessages() {
      // 喇叭通知只显示单条最新通知，不需要滚动
      return this.latestNotice && this.latestNotice.title ? [this.latestNotice] : []
    }
  },
  async created() {
    // 加载最新喇叭通知
    await this.loadLatestNotice()
  },
  mounted() {
    // 启动定时刷新，每5分钟刷新一次
    this.startRefreshTimer()

    // 开发环境下暴露测试方法
    if (process.env.NODE_ENV === 'development') {
      window.testMessageDialog = () => this.testMessageDialog()
      console.log('🛠️ 喇叭通知测试方法已暴露: window.testMessageDialog()')
    }
  },
  beforeDestroy() {
    // 清理定时器
    this.stopRefreshTimer()
  },

  methods: {
    // 加载最新喇叭通知
    async loadLatestNotice() {
      try {
        const result = await getLatestNotice()
        if (result.code === 200 && result.data && result.data.title) {
          this.latestNotice = result.data
        } else {
          this.latestNotice = null
        }
      } catch (error) {
        this.latestNotice = null
      }
    },

    async viewCurrentMessage() {
      if (this.latestNotice && this.latestNotice.notice_id) {
        console.log('🔍 喇叭通知 - 点击查看详情')
        console.log('🔍 当前通知数据:', this.latestNotice)

        try {
          // 调用详情接口获取完整数据
          const result = await noticeDetail({ notice_id: this.latestNotice.notice_id })
          console.log('🔍 详情接口返回:', result)

          if (result.code === 200 && result.data) {
            // 设置当前通知数据
            this.currentNotification = result.data

            // 显示详情弹窗
            this.detailDialogVisible = true
            console.log('🔍 弹窗已显示')
          } else {
            this.$message.error(result.msg || '获取通知详情失败')
          }
        } catch (error) {
          console.error('🔍 获取通知详情失败:', error)
          this.$message.error('获取通知详情失败: ' + error.message)
        }
      }
    },
    closeCurrentMessage() {
      // 隐藏当前通知
      this.latestNotice = null
    },

    // 启动定时刷新
    startRefreshTimer() {
      // 每5分钟刷新一次最新通知
      this.refreshTimer = setInterval(() => {
        this.loadLatestNotice()
      }, 5 * 60 * 1000) // 5分钟
    },

    // 停止定时刷新
    stopRefreshTimer() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },

    // 关闭详情弹窗
    handleCloseDetail() {
      this.detailDialogVisible = false
    },

    // 详情弹窗关闭后的回调
    handleDetailClosed() {
      this.currentNotification = null
    },

    // 测试弹窗显示（用于调试）
    testMessageDialog() {
      console.log('🧪 测试喇叭通知弹窗')
      this.currentNotification = {
        title: '测试喇叭通知标题',
        content: '这是一个测试喇叭通知的内容，用于验证弹窗是否能正常显示。',
        type_text: '系统通知',
        level_text: '重要',
        is_read_text: '未读',
        created_at: '2025-07-29 10:00:00'
      }
      this.detailDialogVisible = true
      console.log('🧪 测试数据设置完成')
    }
  }
}
</script>
<style lang="scss" scoped>
.vab-message {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  background: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  height: 48px;
  margin: 0;
  box-shadow: none;

  .message-item {
    display: flex;
    align-items: center;
    width: 80%;

    .message-icon {
      margin-right: 12px;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      width: 18px;
      height: 18px;
      object-fit: contain;
    }

    .message-content {
      flex: 1;
      overflow: hidden;

      .single-message {
        font-size: 14px;
        color: #333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .multiple-messages {
        height: 20px;
        overflow: hidden;
        position: relative;

        .message-scroll {
          transition: transform 0.5s ease-in-out;

          .scroll-item {
            height: 20px;
            line-height: 20px;
            font-size: 14px;
            color: #333;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }

  .message-btn {
    min-width: 120px;
    flex-shrink: 0;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .el-button--text {
      color: #1890ff;
      font-size: 13px;
      padding: 4px 8px;

      &:hover {
        color: #40a9ff;
        background-color: rgba(24, 144, 255, 0.1);
      }
    }

    .el-icon-close {
      margin-left: 10px;
      cursor: pointer;
      color: #999;
      font-size: 16px;
      padding: 2px;
      border-radius: 2px;
      transition: all 0.3s;

      &:hover {
        color: #666;
        background-color: rgba(0, 0, 0, 0.1);
      }
    }
  }
}

// 动画效果
.vab-message {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>