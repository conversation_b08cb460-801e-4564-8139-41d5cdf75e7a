@charset "utf-8";


body.el-popup-parent--hidden {
  padding-right: 0 !important;
  overflow: visible !important;
}

/* 强制移除ElementUI弹窗的滚动条补偿 */
.el-popup-parent--hidden {
  padding-right: 0 !important;
}

/* 防止ElementUI弹窗修改body样式 */
body.el-popup-parent--hidden {
  overflow: visible !important;
  padding-right: 0 !important;
  width: auto !important;
}

.el-table-column--selection .cell {
  padding-left: 10px !important;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type='number'] {
  -moz-appearance: textfield;
}

.maxWidth400 {
  max-width: 400px;
}



// 禁用样式调整
.el-input.is-disabled .el-input__inner {
  // background: #fff;
  color: #333;
}

.el-radio__input.is-disabled.is-checked .el-radio__inner {
  border-color: #409eff;
  background: #409eff;
}