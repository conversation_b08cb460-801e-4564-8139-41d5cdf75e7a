@charset "utf-8";

/* Transition
-------------------------- */
$--all-transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
$--fade-transition: opacity 300ms cubic-bezier(0.23, 1, 0.32, 1);
$--fade-linear-transition: opacity 200ms linear;
$--md-fade-transition: transform 300ms cubic-bezier(0.23, 1, 0.32, 1), opacity 300ms cubic-bezier(0.23, 1, 0.32, 1);
$--border-transition-base: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
$--color-transition-base: color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

/* Color
-------------------------- */
/// color|1|Brand Color|0
$--color-primary: $base-color-blue;
/// color|1|Background Color|4
$--color-white: #fff;
/// color|1|Background Color|4
$--color-black: #000;
$--color-primary-light-1: mix($--color-white, $--color-primary, 10%);

/* 53a8ff */
$--color-primary-light-2: mix($--color-white, $--color-primary, 20%);

/* 66b1ff */
$--color-primary-light-3: mix($--color-white, $--color-primary, 30%);

/* 79bbff */
$--color-primary-light-4: mix($--color-white, $--color-primary, 40%);

/* 8cc5ff */
$--color-primary-light-5: mix($--color-white, $--color-primary, 50%);

/* a0cfff */
$--color-primary-light-6: mix($--color-white, $--color-primary, 60%);

/* b3d8ff */
$--color-primary-light-7: mix($--color-white, $--color-primary, 70%);

/* c6e2ff */
$--color-primary-light-8: mix($--color-white, $--color-primary, 80%);

/* d9ecff */
$--color-primary-light-9: mix($--color-white, $--color-primary, 90%);

/* ecf5ff */
/// color|1|Functional Color|1
$--color-success: $base-color-green;
/// color|1|Functional Color|1
$--color-warning: $base-color-yellow;
/// color|1|Functional Color|1
$--color-danger: $base-color-red;
/// color|1|Functional Color|1
$--color-info: #909399;

$--color-success-light: mix($--color-white, $--color-success, 80%);
$--color-warning-light: mix($--color-white, $--color-warning, 80%);
$--color-danger-light: mix($--color-white, $--color-danger, 80%);
$--color-info-light: mix($--color-white, $--color-info, 80%);

$--color-success-lighter: mix($--color-white, $--color-success, 90%);
$--color-warning-lighter: mix($--color-white, $--color-warning, 90%);
$--color-danger-lighter: mix($--color-white, $--color-danger, 90%);
$--color-info-lighter: mix($--color-white, $--color-info, 90%);
/// color|1|Font Color|2
$--color-text-primary: #303133;
/// color|1|Font Color|2
$--color-text-regular: #606266;
/// color|1|Font Color|2
$--color-text-secondary: #909399;
/// color|1|Font Color|2
$--color-text-placeholder: #c0c4cc;
/// color|1|Border Color|3
$--border-color-base: #dcdfe6;
/// color|1|Border Color|3
$--border-color-light: #e4e7ed;
/// color|1|Border Color|3
$--border-color-lighter: #ebeef5;
/// color|1|Border Color|3
$--border-color-extra-light: #f2f6fc;

// Background
/// color|1|Background Color|4
$--background-color-base: #f5f7fa;

/* Link
-------------------------- */
$--link-color: $--color-primary-light-2;
$--link-hover-color: $--color-primary;

/* Border
-------------------------- */
$--border-width-base: 1px;
$--border-style-base: solid;
$--border-color-hover: $--color-text-placeholder;
$--border-base: $--border-width-base $--border-style-base $--border-color-base;
/// borderRadius|1|Radius|0
$--border-radius-base: $base-border-radius;
/// borderRadius|1|Radius|0
$--border-radius-small: $base-border-radius;
/// borderRadius|1|Radius|0
$--border-radius-circle: 100%;
/// borderRadius|1|Radius|0
$--border-radius-zero: 0;

// Box-shadow
/// boxShadow|1|Shadow|1
$--box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
// boxShadow|1|Shadow|1
$--box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
/// boxShadow|1|Shadow|1
$--box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

/* Fill
-------------------------- */
$--fill-base: $--color-white;

/* Typography
-------------------------- */
$--font-path: '~element-ui/lib/theme-chalk/fonts';
$--font-display: 'auto';
/// fontSize|1|Font Size|0
$--font-size-extra-large: 20px;
/// fontSize|1|Font Size|0
$--font-size-large: 18px;
/// fontSize|1|Font Size|0
$--font-size-medium: 16px;
/// fontSize|1|Font Size|0
$--font-size-base: 14px;
/// fontSize|1|Font Size|0
$--font-size-small: 13px;
/// fontSize|1|Font Size|0
$--font-size-extra-small: 12px;
/// fontWeight|1|Font Weight|1
$--font-weight-primary: 500;
/// fontWeight|1|Font Weight|1
$--font-weight-secondary: 100;
/// fontLineHeight|1|Line Height|2
$--font-line-height-primary: 24px;
/// fontLineHeight|1|Line Height|2
$--font-line-height-secondary: 16px;
$--font-color-disabled-base: #bbb;

/* Size
-------------------------- */
$--size-base: 14px;

/* z-index
-------------------------- */
$--index-normal: 1;
$--index-top: 1000;
$--index-popper: 2000;

/* Disable base
-------------------------- */
$--disabled-fill-base: $--background-color-base;
$--disabled-color-base: $--color-text-placeholder;
$--disabled-border-base: $--border-color-light;

/* Icon
-------------------------- */
$--icon-color: #666;
$--icon-color-base: $--color-info;

/* Checkbox
-------------------------- */
/// fontSize||Font|1
$--checkbox-font-size: 14px;
/// fontWeight||Font|1
$--checkbox-font-weight: $--font-weight-primary;
/// color||Color|0
$--checkbox-font-color: $--color-text-regular;
$--checkbox-input-height: 14px;
$--checkbox-input-width: 14px;
/// borderRadius||Border|2
$--checkbox-border-radius: $--border-radius-small;
/// color||Color|0
$--checkbox-background-color: $--color-white;
$--checkbox-input-border: $--border-base;

/// color||Color|0
$--checkbox-disabled-border-color: $--border-color-base;
$--checkbox-disabled-input-fill: #edf2fc;
$--checkbox-disabled-icon-color: $--color-text-placeholder;

$--checkbox-disabled-checked-input-fill: $--border-color-extra-light;
$--checkbox-disabled-checked-input-border-color: $--border-color-base;
$--checkbox-disabled-checked-icon-color: $--color-text-placeholder;

/// color||Color|0
$--checkbox-checked-font-color: $--color-primary;
$--checkbox-checked-input-border-color: $--color-primary;
/// color||Color|0
$--checkbox-checked-background-color: $--color-primary;
$--checkbox-checked-icon-color: $--fill-base;

$--checkbox-input-border-color-hover: $--color-primary;
/// height||Other|4
$--checkbox-bordered-height: 40px;
/// padding||Spacing|3
$--checkbox-bordered-padding: 9px 20px 9px 10px;
/// padding||Spacing|3
$--checkbox-bordered-medium-padding: 7px 20px 7px 10px;
/// padding||Spacing|3
$--checkbox-bordered-small-padding: 5px 15px 5px 10px;
/// padding||Spacing|3
$--checkbox-bordered-mini-padding: 3px 15px 3px 10px;
$--checkbox-bordered-medium-input-height: 14px;
$--checkbox-bordered-medium-input-width: 14px;
/// height||Other|4
$--checkbox-bordered-medium-height: 36px;
$--checkbox-bordered-small-input-height: 12px;
$--checkbox-bordered-small-input-width: 12px;
/// height||Other|4
$--checkbox-bordered-small-height: 32px;
$--checkbox-bordered-mini-input-height: 12px;
$--checkbox-bordered-mini-input-width: 12px;
/// height||Other|4
$--checkbox-bordered-mini-height: 28px;

/// color||Color|0
$--checkbox-button-checked-background-color: $--color-primary;
/// color||Color|0
$--checkbox-button-checked-font-color: $--color-white;
/// color||Color|0
$--checkbox-button-checked-border-color: $--color-primary;

/* Radio
-------------------------- */
/// fontSize||Font|1
$--radio-font-size: $--font-size-base;
/// fontWeight||Font|1
$--radio-font-weight: $--font-weight-primary;
/// color||Color|0
$--radio-font-color: $--color-text-regular;
$--radio-input-height: 14px;
$--radio-input-width: 14px;
/// borderRadius||Border|2
$--radio-input-border-radius: $--border-radius-circle;
/// color||Color|0
$--radio-input-background-color: $--color-white;
$--radio-input-border: $--border-base;
/// color||Color|0
$--radio-input-border-color: $--border-color-base;
/// color||Color|0
$--radio-icon-color: $--color-white;

$--radio-disabled-input-border-color: $--disabled-border-base;
$--radio-disabled-input-fill: $--disabled-fill-base;
$--radio-disabled-icon-color: $--disabled-fill-base;

$--radio-disabled-checked-input-border-color: $--disabled-border-base;
$--radio-disabled-checked-input-fill: $--disabled-fill-base;
$--radio-disabled-checked-icon-color: $--color-text-placeholder;

/// color||Color|0
$--radio-checked-font-color: $--color-primary;
/// color||Color|0
$--radio-checked-input-border-color: $--color-primary;
/// color||Color|0
$--radio-checked-input-background-color: $--color-white;
/// color||Color|0
$--radio-checked-icon-color: $--color-primary;

$--radio-input-border-color-hover: $--color-primary;

$--radio-bordered-height: 40px;
$--radio-bordered-padding: 12px 20px 0 10px;
$--radio-bordered-medium-padding: 10px 20px 0 10px;
$--radio-bordered-small-padding: 8px 15px 0 10px;
$--radio-bordered-mini-padding: 6px 15px 0 10px;
$--radio-bordered-medium-input-height: 14px;
$--radio-bordered-medium-input-width: 14px;
$--radio-bordered-medium-height: 36px;
$--radio-bordered-small-input-height: 12px;
$--radio-bordered-small-input-width: 12px;
$--radio-bordered-small-height: 32px;
$--radio-bordered-mini-input-height: 12px;
$--radio-bordered-mini-input-width: 12px;
$--radio-bordered-mini-height: 28px;

/// fontSize||Font|1
$--radio-button-font-size: $--font-size-base;
/// color||Color|0
$--radio-button-checked-background-color: $--color-primary;
/// color||Color|0
$--radio-button-checked-font-color: $--color-white;
/// color||Color|0
$--radio-button-checked-border-color: $--color-primary;
$--radio-button-disabled-checked-fill: $--border-color-extra-light;

/* Select
-------------------------- */
$--select-border-color-hover: $--border-color-hover;
$--select-disabled-border: $--disabled-border-base;
/// fontSize||Font|1
$--select-font-size: $--font-size-base;
$--select-close-hover-color: $--color-text-secondary;

$--select-input-color: $--color-text-placeholder;
$--select-multiple-input-color: #666;
/// color||Color|0
$--select-input-focus-border-color: $--color-primary;
/// fontSize||Font|1
$--select-input-font-size: 14px;

$--select-option-color: $--color-text-regular;
$--select-option-disabled-color: $--color-text-placeholder;
$--select-option-disabled-background: $--color-white;
/// height||Other|4
$--select-option-height: 34px;
$--select-option-hover-background: $--background-color-base;
/// color||Color|0
$--select-option-selected-font-color: $--color-primary;
$--select-option-selected-hover: $--background-color-base;

$--select-group-color: $--color-info;
$--select-group-height: 30px;
$--select-group-font-size: 12px;

$--select-dropdown-background: $--color-white;
$--select-dropdown-shadow: $--box-shadow-light;
$--select-dropdown-empty-color: #999;
/// height||Other|4
$--select-dropdown-max-height: 274px;
$--select-dropdown-padding: 6px 0;
$--select-dropdown-empty-padding: 10px 0;
$--select-dropdown-border: solid 1px $--border-color-light;

/* Alert
-------------------------- */
$--alert-padding: 8px 16px;
/// borderRadius||Border|2
$--alert-border-radius: $--border-radius-base;
/// fontSize||Font|1
$--alert-title-font-size: 13px;
/// fontSize||Font|1
$--alert-description-font-size: 12px;
/// fontSize||Font|1
$--alert-close-font-size: 12px;
/// fontSize||Font|1
$--alert-close-customed-font-size: 13px;

$--alert-success-color: $--color-success-lighter;
$--alert-info-color: $--color-info-lighter;
$--alert-warning-color: $--color-warning-lighter;
$--alert-danger-color: $--color-danger-lighter;

/// height||Other|4
$--alert-icon-size: 16px;
/// height||Other|4
$--alert-icon-large-size: 28px;

/* MessageBox
-------------------------- */
/// color||Color|0
$--messagebox-title-color: $--color-text-primary;
$--msgbox-width: 420px;
$--msgbox-border-radius: $--border-radius-base;
/// fontSize||Font|1
$--messagebox-font-size: $--font-size-large;
/// fontSize||Font|1
$--messagebox-content-font-size: $--font-size-base;
/// color||Color|0
$--messagebox-content-color: $--color-text-regular;
/// fontSize||Font|1
$--messagebox-error-font-size: 12px;
$--msgbox-padding-primary: 15px;
/// color||Color|0
$--messagebox-success-color: $--color-success;
/// color||Color|0
$--messagebox-info-color: $--color-info;
/// color||Color|0
$--messagebox-warning-color: $--color-warning;
/// color||Color|0
$--messagebox-danger-color: $--color-danger;

/* Message
-------------------------- */
$--message-shadow: $--box-shadow-base;
$--message-min-width: 380px;
$--message-background-color: #edf2fc;
$--message-padding: 15px 15px 15px 20px;
/// color||Color|0
$--message-close-icon-color: $--color-text-placeholder;
/// height||Other|4
$--message-close-size: 16px;
/// color||Color|0
$--message-close-hover-color: $--color-text-secondary;

/// color||Color|0
$--message-success-font-color: $--color-success;
/// color||Color|0
$--message-info-font-color: $--color-info;
/// color||Color|0
$--message-warning-font-color: $--color-warning;
/// color||Color|0
$--message-danger-font-color: $--color-danger;

/* Notification
-------------------------- */
$--notification-width: 330px;
/// padding||Spacing|3
$--notification-padding: 14px 26px 14px 13px;
$--notification-radius: 8px;
$--notification-shadow: $--box-shadow-light;
/// color||Color|0
$--notification-border-color: $--border-color-lighter;
$--notification-icon-size: 24px;
$--notification-close-font-size: $--message-close-size;
$--notification-group-margin-left: 13px;
$--notification-group-margin-right: 8px;
/// fontSize||Font|1
$--notification-content-font-size: $--font-size-base;
/// color||Color|0
$--notification-content-color: $--color-text-regular;
/// fontSize||Font|1
$--notification-title-font-size: 16px;
/// color||Color|0
$--notification-title-color: $--color-text-primary;

/// color||Color|0
$--notification-close-color: $--color-text-secondary;
/// color||Color|0
$--notification-close-hover-color: $--color-text-regular;

/// color||Color|0
$--notification-success-icon-color: $--color-success;
/// color||Color|0
$--notification-info-icon-color: $--color-info;
/// color||Color|0
$--notification-warning-icon-color: $--color-warning;
/// color||Color|0
$--notification-danger-icon-color: $--color-danger;

/* Input
-------------------------- */
$--input-font-size: $--font-size-base;
/// color||Color|0
$--input-font-color: $--color-text-regular;
/// height||Other|4
$--input-width: 140px;
/// height||Other|4
$--input-height: 40px;
$--input-border: $--border-base;
$--input-border-color: $--border-color-base;
/// borderRadius||Border|2
$--input-border-radius: $--border-radius-base;
$--input-border-color-hover: $--border-color-hover;
/// color||Color|0
$--input-background-color: $--color-white;
$--input-fill-disabled: $--disabled-fill-base;
$--input-color-disabled: $--font-color-disabled-base;
/// color||Color|0
$--input-icon-color: $--color-text-placeholder;
/// color||Color|0
$--input-placeholder-color: $--color-text-placeholder;
$--input-max-width: 314px;

$--input-hover-border: $--border-color-hover;
$--input-clear-hover-color: $--color-text-secondary;

$--input-focus-border: $--color-primary;
$--input-focus-fill: $--color-white;

$--input-disabled-fill: $--disabled-fill-base;
$--input-disabled-border: $--disabled-border-base;
$--input-disabled-color: $--disabled-color-base;
$--input-disabled-placeholder-color: $--color-text-placeholder;

/// fontSize||Font|1
$--input-medium-font-size: 14px;
/// height||Other|4
$--input-medium-height: 36px;
/// fontSize||Font|1
$--input-small-font-size: 13px;
/// height||Other|4
$--input-small-height: 32px;
/// fontSize||Font|1
$--input-mini-font-size: 12px;
/// height||Other|4
$--input-mini-height: 28px;

/* Cascader
-------------------------- */
/// color||Color|0
$--cascader-menu-font-color: $--color-text-regular;
/// color||Color|0
$--cascader-menu-selected-font-color: $--color-primary;
$--cascader-menu-fill: $--fill-base;
$--cascader-menu-font-size: $--font-size-base;
$--cascader-menu-radius: $--border-radius-base;
$--cascader-menu-border: solid 1px $--border-color-light;
$--cascader-menu-shadow: $--box-shadow-light;
$--cascader-node-background-hover: $--background-color-base;
$--cascader-node-color-disabled: $--color-text-placeholder;
$--cascader-color-empty: $--color-text-placeholder;
$--cascader-tag-background: #f0f2f5;

/* Group
-------------------------- */
$--group-option-flex: 0 0 (1/5) * 100%;
$--group-option-offset-bottom: 12px;
$--group-option-fill-hover: rgba($--color-black, 0.06);
$--group-title-color: $--color-black;
$--group-title-font-size: $--font-size-base;
$--group-title-width: 66px;

/* Tab
-------------------------- */
$--tab-font-size: $--font-size-base;
$--tab-border-line: 1px solid #e4e4e4;
$--tab-header-color-active: $--color-text-secondary;
$--tab-header-color-hover: $--color-text-regular;
$--tab-header-color: $--color-text-regular;
$--tab-header-fill-active: rgba($--color-black, 0.06);
$--tab-header-fill-hover: rgba($--color-black, 0.06);
$--tab-vertical-header-width: 90px;
$--tab-vertical-header-count-color: $--color-white;
$--tab-vertical-header-count-fill: $--color-text-secondary;

/* Button
-------------------------- */
/// fontSize||Font|1
$--button-font-size: $--font-size-base;
/// fontWeight||Font|1
$--button-font-weight: $--font-weight-primary;
/// borderRadius||Border|2
$--button-border-radius: $--border-radius-base;
/// padding||Spacing|3
$--button-padding-vertical: 12px;
/// padding||Spacing|3
$--button-padding-horizontal: 20px;

/// fontSize||Font|1
$--button-medium-font-size: $--font-size-base;
/// borderRadius||Border|2
$--button-medium-border-radius: $--border-radius-base;
/// padding||Spacing|3
$--button-medium-padding-vertical: 10px;
/// padding||Spacing|3
$--button-medium-padding-horizontal: 20px;

/// fontSize||Font|1
$--button-small-font-size: 12px;
$--button-small-border-radius: $--border-radius-base;
/// padding||Spacing|3
$--button-small-padding-vertical: 9px;
/// padding||Spacing|3
$--button-small-padding-horizontal: 15px;
/// fontSize||Font|1
$--button-mini-font-size: 12px;
$--button-mini-border-radius: $--border-radius-base;
/// padding||Spacing|3
$--button-mini-padding-vertical: 7px;
/// padding||Spacing|3
$--button-mini-padding-horizontal: 15px;

/// color||Color|0
$--button-default-font-color: $--color-text-regular;
/// color||Color|0
$--button-default-background-color: $--color-white;
/// color||Color|0
$--button-default-border-color: $--border-color-base;

/// color||Color|0
$--button-disabled-font-color: $--color-text-placeholder;
/// color||Color|0
$--button-disabled-background-color: $--color-white;
/// color||Color|0
$--button-disabled-border-color: $--border-color-lighter;

/// color||Color|0
$--button-primary-border-color: $--color-primary;
/// color||Color|0
$--button-primary-font-color: $--color-white;
/// color||Color|0
$--button-primary-background-color: $--color-primary;
/// color||Color|0
$--button-success-border-color: $--color-success;
/// color||Color|0
$--button-success-font-color: $--color-white;
/// color||Color|0
$--button-success-background-color: $--color-success;
/// color||Color|0
$--button-warning-border-color: $--color-warning;
/// color||Color|0
$--button-warning-font-color: $--color-white;
/// color||Color|0
$--button-warning-background-color: $--color-warning;
/// color||Color|0
$--button-danger-border-color: $--color-danger;
/// color||Color|0
$--button-danger-font-color: $--color-white;
/// color||Color|0
$--button-danger-background-color: $--color-danger;
/// color||Color|0
$--button-info-border-color: $--color-info;
/// color||Color|0
$--button-info-font-color: $--color-white;
/// color||Color|0
$--button-info-background-color: $--color-info;

$--button-hover-tint-percent: 20%;
$--button-active-shade-percent: 10%;

/* cascader
-------------------------- */
$--cascader-height: 200px;

/* Switch
-------------------------- */
/// color||Color|0
$--switch-on-color: $--color-primary;
/// color||Color|0
$--switch-off-color: $--border-color-base;
/// fontSize||Font|1
$--switch-font-size: $--font-size-base;
$--switch-core-border-radius: 10px;
// height||Other|4 TODO: width 代码写死的40px 所以下面这三个属性都没意义
$--switch-width: 40px;
// height||Other|4
$--switch-height: 20px;
// height||Other|4
$--switch-button-size: 16px;

/* Dialog
-------------------------- */
$--dialog-background-color: $--color-white;
$--dialog-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
/// fontSize||Font|1
$--dialog-title-font-size: $--font-size-large;
/// fontSize||Font|1
$--dialog-content-font-size: 14px;
/// fontLineHeight||LineHeight|2
$--dialog-font-line-height: $--font-line-height-primary;
/// padding||Spacing|3
$--dialog-padding-primary: 20px;

/* Table
-------------------------- */
/// color||Color|0
$--table-border-color: $--border-color-lighter;
$--table-border: 1px solid $--table-border-color;
/// color||Color|0
$--table-font-color: $--color-text-regular;
/// color||Color|0
$--table-header-font-color: $--color-text-secondary;
/// color||Color|0
$--table-row-hover-background-color: $--background-color-base;
$--table-current-row-background-color: $--color-primary-light-9;
/// color||Color|0
$--table-header-background-color: $--color-white;
$--table-fixed-box-shadow: 0 0 10px rgba(0, 0, 0, 0.12);

/* Pagination
-------------------------- */
/// fontSize||Font|1
$--pagination-font-size: 13px;
/// color||Color|0
$--pagination-background-color: $--color-white;
/// color||Color|0
$--pagination-font-color: $--color-text-primary;
$--pagination-border-radius: $--border-radius-base;
/// color||Color|0
$--pagination-button-color: $--color-text-primary;
/// height||Other|4
$--pagination-button-width: 35.5px;
/// height||Other|4
$--pagination-button-height: 28px;
/// color||Color|0
$--pagination-button-disabled-color: $--color-text-placeholder;
/// color||Color|0
$--pagination-button-disabled-background-color: $--color-white;
/// color||Color|0
$--pagination-hover-color: $--color-primary;

/* Popup
-------------------------- */
/// color||Color|0
$--popup-modal-background-color: $--color-black;
/// opacity||Other|1
$--popup-modal-opacity: 0.5;

/* Popover
-------------------------- */
/// color||Color|0
$--popover-background-color: $--color-white;
/// fontSize||Font|1
$--popover-font-size: $--font-size-base;
/// color||Color|0
$--popover-border-color: $--border-color-lighter;
$--popover-arrow-size: 6px;
/// padding||Spacing|3
$--popover-padding: 12px;
$--popover-padding-large: 18px 20px;
/// fontSize||Font|1
$--popover-title-font-size: 16px;
/// color||Color|0
$--popover-title-font-color: $--color-text-primary;

/* Tooltip
-------------------------- */
/// color|1|Color|0
$--tooltip-fill: $--color-text-primary;
/// color|1|Color|0
$--tooltip-color: $--color-white;
/// fontSize||Font|1
$--tooltip-font-size: 12px;
/// color||Color|0
$--tooltip-border-color: $--color-text-primary;
$--tooltip-arrow-size: 6px;
/// padding||Spacing|3
$--tooltip-padding: 10px;

/* Tag
-------------------------- */
/// color||Color|0
$--tag-info-color: $--color-info;
/// color||Color|0
$--tag-primary-color: $--color-primary;
/// color||Color|0
$--tag-success-color: $--color-success;
/// color||Color|0
$--tag-warning-color: $--color-warning;
/// color||Color|0
$--tag-danger-color: $--color-danger;
/// fontSize||Font|1
$--tag-font-size: 12px;
$--tag-border-radius: $--border-radius-base;
$--tag-padding: 0 10px;

/* Tree
-------------------------- */
/// color||Color|0
$--tree-node-hover-background-color: $--background-color-base;
/// color||Color|0
$--tree-font-color: $--color-text-regular;
/// color||Color|0
$--tree-expand-icon-color: $--color-text-placeholder;

/* Dropdown
-------------------------- */
$--dropdown-menu-box-shadow: $--box-shadow-light;
$--dropdown-menuItem-hover-fill: $--color-primary-light-9;
$--dropdown-menuItem-hover-color: $--link-color;

/* Badge
-------------------------- */
/// color||Color|0
$--badge-background-color: $--color-danger;
$--badge-radius: 10px;
/// fontSize||Font|1
$--badge-font-size: 12px;
/// padding||Spacing|3
$--badge-padding: 6px;
/// height||Other|4
$--badge-size: 18px;

/* Card
-------------------------- */
/// color||Color|0
$--card-border-color: $--border-color-lighter;
$--card-border-radius: $--border-radius-base;
/// padding||Spacing|3
$--card-padding: 20px;

/* Slider
-------------------------- */
/// color||Color|0
$--slider-main-background-color: $--color-primary;
/// color||Color|0
$--slider-runway-background-color: $--border-color-light;
$--slider-button-hover-color: mix($--color-primary, black, 97%);
$--slider-stop-background-color: $--color-white;
$--slider-disable-color: $--color-text-placeholder;
$--slider-margin: 16px 0;
$--slider-border-radius: $--border-radius-base;
/// height|1|Other|4
$--slider-height: 6px;
/// height||Other|4
$--slider-button-size: 16px;
$--slider-button-wrapper-size: 36px;
$--slider-button-wrapper-offset: -15px;

/* Steps
-------------------------- */
$--steps-border-color: $--disabled-border-base;
$--steps-border-radius: $--border-radius-base;
$--steps-padding: 20px;

/* Menu
-------------------------- */
/// fontSize||Font|1
$--menu-item-font-size: $--font-size-base;
/// color||Color|0
$--menu-item-font-color: $--color-text-primary;
/// color||Color|0
$--menu-background-color: $--color-white;
$--menu-item-hover-fill: $--color-primary-light-9;

/* Rate
-------------------------- */
$--rate-height: 20px;
/// fontSize||Font|1
$--rate-font-size: $--font-size-base;
/// height||Other|3
$--rate-icon-size: 18px;
/// margin||Spacing|2
$--rate-icon-margin: 6px;
$--rate-icon-color: $--color-text-placeholder;

/* DatePicker
-------------------------- */
$--datepicker-font-color: $--color-text-regular;
/// color|1|Color|0
$--datepicker-off-font-color: $--color-text-placeholder;
/// color||Color|0
$--datepicker-header-font-color: $--color-text-regular;
$--datepicker-icon-color: $--color-text-primary;
$--datepicker-border-color: $--disabled-border-base;
$--datepicker-inner-border-color: #e4e4e4;
/// color||Color|0
$--datepicker-inrange-background-color: $--border-color-extra-light;
/// color||Color|0
$--datepicker-inrange-hover-background-color: $--border-color-extra-light;
/// color||Color|0
$--datepicker-active-color: $--color-primary;
/// color||Color|0
$--datepicker-hover-font-color: $--color-primary;
$--datepicker-cell-hover-color: #fff;

/* Loading
-------------------------- */
/// height||Other|4
$--loading-spinner-size: 42px;
/// height||Other|4
$--loading-fullscreen-spinner-size: 50px;

/* Scrollbar
-------------------------- */
$--scrollbar-background-color: rgba($--color-text-secondary, 0.3);
$--scrollbar-hover-background-color: rgba($--color-text-secondary, 0.5);

/* Carousel
-------------------------- */
/// fontSize||Font|1
$--carousel-arrow-font-size: 12px;
$--carousel-arrow-size: 36px;
$--carousel-arrow-background: rgba(31, 45, 61, 0.11);
$--carousel-arrow-hover-background: rgba(31, 45, 61, 0.23);
/// width||Other|4
$--carousel-indicator-width: 30px;
/// height||Other|4
$--carousel-indicator-height: 2px;
$--carousel-indicator-padding-horizontal: 4px;
$--carousel-indicator-padding-vertical: 12px;
$--carousel-indicator-out-color: $--border-color-hover;

/* Collapse
-------------------------- */
/// color||Color|0
$--collapse-border-color: $--border-color-lighter;
/// height||Other|4
$--collapse-header-height: 48px;
/// color||Color|0
$--collapse-header-background-color: $--color-white;
/// color||Color|0
$--collapse-header-font-color: $--color-text-primary;
/// fontSize||Font|1
$--collapse-header-font-size: 13px;
/// color||Color|0
$--collapse-content-background-color: $--color-white;
/// fontSize||Font|1
$--collapse-content-font-size: 13px;
/// color||Color|0
$--collapse-content-font-color: $--color-text-primary;

/* Transfer
-------------------------- */
$--transfer-border-color: $--border-color-lighter;
$--transfer-border-radius: $--border-radius-base;
/// height||Other|4
$--transfer-panel-width: 200px;
/// height||Other|4
$--transfer-panel-header-height: 40px;
/// color||Color|0
$--transfer-panel-header-background-color: $--background-color-base;
/// height||Other|4
$--transfer-panel-footer-height: 40px;
/// height||Other|4
$--transfer-panel-body-height: 246px;
/// height||Other|4
$--transfer-item-height: 30px;
/// height||Other|4
$--transfer-filter-height: 32px;

/* Header
  -------------------------- */
$--header-padding: 0 20px;

/* Footer
-------------------------- */
$--footer-padding: 0 20px;

/* Main
-------------------------- */
$--main-padding: 20px;

/* Timeline
-------------------------- */
$--timeline-node-size-normal: 12px;
$--timeline-node-size-large: 14px;
$--timeline-node-color: $--border-color-light;

/* Backtop
-------------------------- */
/// color||Color|0
$--backtop-background-color: $--color-white;
/// color||Color|0
$--backtop-font-color: $--color-primary;
/// color||Color|0
$--backtop-hover-background-color: $--border-color-extra-light;

/* Link
-------------------------- */
/// fontSize||Font|1
$--link-font-size: $--font-size-base;
/// fontWeight||Font|1
$--link-font-weight: $--font-weight-primary;
/// color||Color|0
$--link-default-font-color: $--color-text-regular;
/// color||Color|0
$--link-default-active-color: $--color-primary;
/// color||Color|0
$--link-disabled-font-color: $--color-text-placeholder;
/// color||Color|0
$--link-primary-font-color: $--color-primary;
/// color||Color|0
$--link-success-font-color: $--color-success;
/// color||Color|0
$--link-warning-font-color: $--color-warning;
/// color||Color|0
$--link-danger-font-color: $--color-danger;
/// color||Color|0
$--link-info-font-color: $--color-info;

/* Calendar
-------------------------- */
/// border||Other|4
$--calendar-border: $--table-border;
/// color||Other|4
$--calendar-selected-background-color: #f2f8fe;
$--calendar-cell-width: 85px;

/* Form
-------------------------- */
/// fontSize||Font|1
$--form-label-font-size: $--font-size-base;

/* Avatar
-------------------------- */
/// color||Color|0
$--avatar-font-color: #fff;
/// color||Color|0
$--avatar-background-color: #c0c4cc;
/// fontSize||Font Size|1
$--avatar-text-font-size: 14px;
/// fontSize||Font Size|1
$--avatar-icon-font-size: 18px;
/// borderRadius||Border|2
$--avatar-border-radius: $--border-radius-base;
/// size|1|Avatar Size|3
$--avatar-large-size: 40px;
/// size|1|Avatar Size|3
$--avatar-medium-size: 36px;
/// size|1|Avatar Size|3
$--avatar-small-size: 28px;

/* Break-point
-------------------------- */
$--sm: 768px;
$--md: 992px;
$--lg: 1200px;
$--xl: 1920px;

$--breakpoints: (
  'xs': (
    max-width: $--sm - 1,
  ),
  'sm': (
    min-width: $--sm,
  ),
  'md': (
    min-width: $--md,
  ),
  'lg': (
    min-width: $--lg,
  ),
  'xl': (
    min-width: $--xl,
  ),
);

$--breakpoints-spec: (
  'xs-only': (
    max-width: $--sm - 1,
  ),
  'sm-and-up': (
    min-width: $--sm,
  ),
  'sm-only': '(min-width: #{$--sm}) and (max-width: #{$--md - 1})',
  'sm-and-down': (
    max-width: $--md - 1,
  ),
  'md-and-up': (
    min-width: $--md,
  ),
  'md-only': '(min-width: #{$--md}) and (max-width: #{$--lg - 1})',
  'md-and-down': (
    max-width: $--lg - 1,
  ),
  'lg-and-up': (
    min-width: $--lg,
  ),
  'lg-only': '(min-width: #{$--lg}) and (max-width: #{$--xl - 1})',
  'lg-and-down': (
    max-width: $--xl - 1,
  ),
  'xl-only': (
    min-width: $--xl,
  ),
);

@import '~element-ui/packages/theme-chalk/src/index';
