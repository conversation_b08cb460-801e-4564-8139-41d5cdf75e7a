/**
 * 
 * @description 全局样式
 */

@charset "utf-8";

@import './normalize.scss';
@import './transition.scss';
@import './loading.scss';
@import './public.scss';
@import './icon/iconfont.css';

$base: '.vab';

@mixin scrollbar {
  max-height: 88vh;
  margin-bottom: 0.5vh;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(144, 147, 153, 0.3);
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(144, 147, 153, 0.3);
  }
}

@mixin base-scrollbar {
  &::-webkit-scrollbar {
    width: 13px;
    height: 13px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.4);
    background-clip: padding-box;
    border: 3px solid transparent;
    border-radius: 7px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.5);
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }

  &::-webkit-scrollbar-track:hover {
    background-color: #f8fafc;
  }
}

img {
  object-fit: cover;
}

a {
  color: $base-color-blue;
  text-decoration: none;
  cursor: pointer;
}

* {
  transition: $base-transition;
}

svg {
  transition: none;

  * {
    transition: none;
  }
}

html {
  body {
    position: relative;
    height: 100vh;
    padding: 0;
    margin: 0;
    font-family: Avenir, Helvetica, Arial, sans-serif;
    font-size: $base-font-size-default;
    color: #2c3e50;
    background: #f6f8f9;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    @include base-scrollbar;

    div {
      @include base-scrollbar;
    }

    svg,
    i {
      &:hover {
        opacity: 0.8;
      }
    }

    .v-modal {
      backdrop-filter: blur(10px);
    }

    .el-tag+.el-tag {
      margin-left: 10px;
    }

    .editor-toolbar {

      .no-mobile,
      .fa-question-circle {
        display: none;
      }
    }

    .el-divider--horizontal {
      margin: 10px 0 25px 0;

      .el-divider__text {
        display: -webkit-box;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
    }

    .el-image-viewer {
      &__close {
        .el-icon-circle-close {
          color: $base-color-white;
        }
      }
    }

    .vue-admin-better-wrapper {
      .app-main-container {
        @include base-scrollbar;

        >[class*='-container'] {
          * {
            transition: none;
          }

          padding: $base-padding;
          background: $base-color-white;
        }
      }
    }

    /* 进度条开始 */
    #nprogress {
      position: fixed;
      z-index: $base-z-index;

      .bar {
        background: $base-color-blue !important;
      }

      .peg {
        box-shadow: 0 0 10px $base-color-blue, 0 0 5px $base-color-blue !important;
      }
    }

    .el-table {
      .el-table__body-wrapper {
        @include base-scrollbar;
      }

      th {
        background: #f5f7fa;
      }

      td,
      th {
        position: relative;
        box-sizing: border-box;
        padding: 7.5px 0;

        .cell {
          font-size: $base-font-size-default;
          font-weight: normal;
          color: #606266;

          .el-image {
            width: 50px;
            height: 50px;
            border-radius: $base-border-radius;
          }
        }
      }
    }

    .el-pagination {
      padding: 2px 5px;
      margin: 15px 0 0 0;
      font-weight: normal;
      color: $base-color-black;
      text-align: center;
    }

    .el-menu.el-menu--popup.el-menu--popup-right-start {
      @include scrollbar;
    }

    .el-menu.el-menu--popup.el-menu--popup-bottom-start {
      @include scrollbar;
    }

    .el-submenu__title i {
      color: $base-color-black;
    }

    .el-dialog {
      width: 480px;
      // top: 50% !important;
      // transform: translate(-50%, -50%);
      // left: 50%;
      // margin: 0 !important;
    }

    .el-dialog,
    .el-message-box {
      &__body {
        border-top: 1px solid $base-border-color;

        .el-form {
          // padding-right: 30px;
        }
      }

      &__footer {
        padding: $base-padding;
        text-align: right;
        border-top: 1px solid $base-border-color;
      }

      &__content {
        padding: 20px 20px 20px 20px;
      }
    }

    .el-card {
      margin-bottom: 15px;

      &__body {
        padding: $base-padding;
      }
    }

    .select-tree-popper {
      .el-scrollbar {
        .el-scrollbar__view {
          .el-select-dropdown__item {
            height: auto;
            max-height: 274px;
            padding: 0;
            overflow-y: auto;
            line-height: 26px;
          }
        }
      }
    }
  }

  .side-bar-container {

    .el-menu-item,
    .el-submenu {
      margin: 7px !important;
      border-radius: 5px !important;

      &:hover {
        border-radius: 5px !important;
      }

      &.is-active {
        background: $base-color-default !important;
      }
    }
  }
}

.flex {
  display: flex;
  align-items: center;
}

.flex-a {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.flex-b {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-c {
  display: flex;
  justify-content: center;
  align-items: center;
}

.line1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.line2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.app-main-container-pd {
  padding: 20px;
  box-sizing: border-box;
}

// 公共操作按钮样式
.public-operate-btn {
  &>.el-button {
    padding: 0;
  }

  &>.el-button+.el-button {
    margin-left: 4px !important;
    position: relative;
    padding: 0 0 0 5px;

    &::before {
      content: '';
      display: block;
      width: 1px;
      height: 8px;
      background: rgba(0, 0, 0, 0.08);
      box-shadow: inset 0px -1px 0px 0px #ebeef5;
      position: absolute;
      left: 0;
      top: 2px;
    }
  }
}

// 卡片头部样式
.card-header {
  display: flex;

  div:first-child {
    width: 2px;
    height: 14px;
    background: #0071fe;
    border-radius: 3px;
    margin-right: 8px;
  }
}

//
.w100 {
  width: 100%;
}

.el-popconfirm {
  max-width: 400px;
}

// .el-input--small .el-input__inner {
//   line-height: 1px !important;
// }



.icon_wenhao {
  width: 14px;
  height: 14px;
  cursor: pointer;
}