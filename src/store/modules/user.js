/*
 * @Author: shangfei <EMAIL>
 * @Date: 2025-02-18 14:31:04
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-26 09:45:43
 * @FilePath: /qst-merchant-admin-2.0/src/store/modules/user.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * 
 * @description 登录、获取用户信息、退出登录、清除accessToken逻辑，不建议修改
 */

import Vue from 'vue'
import { getUserInfo, login, logout, refreshTokenApi, merchantInfoApi } from '@/api/user'
import { getAccessToken, removeAccessToken, setAccessToken } from '@/utils/accessToken'
import { resetRouter } from '@/router'
import { title, tokenName } from '@/config'

const state = () => ({
  accessToken: getAccessToken(),
  username: '',
  avatar: '',
  phone: '',
  permissions: [],
  permissionsBtn: [],
  userEdit: '',
  isOpenMerchant: 0,
  isShowWindows: false
})
const getters = {
  accessToken: (state) => state.accessToken,
  username: (state) => state.username,
  avatar: (state) => state.avatar,
  phone: (state) => state.phone,
  permissions: (state) => state.permissions,
  permissionsBtn: (state) => state.permissionsBtn,
  userEdit: (state) => state.userEdit,
  isOpenMerchant: (state) => state.isOpenMerchant,
  isShowWindows: (state) => state.isShowWindows
}
const mutations = {
  setAccessToken(state, accessToken) {
    state.accessToken = accessToken
    setAccessToken(accessToken)
  },
  setPermissionsBtn(state, permissionsBtn) {
    state.permissionsBtn = permissionsBtn
  },
  setUsername(state, username) {
    state.username = username
  },
  setAvatar(state, avatar) {
    state.avatar = avatar
  },
  setPhone(state, phone) {
    state.phone = phone
  },
  setPermissions(state, permissions) {
    state.permissions = permissions
  },
  setUserEdit(state, userEdit) {
    state.userEdit = userEdit
  },
  setIsMerchant(state, isOpenMerchant) {
    state.isOpenMerchant = isOpenMerchant
  },

  setShowWindows(state, isShowWindows) {
    console.log(isShowWindows, 'isShowWindows')
    state.isShowWindows = isShowWindows
  }
}
const actions = {
  setPhone({ commit }, phone) {
    console.log(22)

    commit('setPhone', phone)
  },
  setShowWindows({ commit }, isShowWindows) {
    commit('setShowWindows', isShowWindows)
  },
  setUserEdit({ commit }, userEdit) {
    commit('setUserEdit', userEdit)
  },
  setIsMerchant({ commit }, isOpenMerchant) {
    commit('setIsMerchant', isOpenMerchant)
  },
  setPermissions({ commit }, permissions) {
    commit('setPermissions', permissions)
  },
  setAvatar({ commit }, avatar) {
    console.log(11)
    commit('setAvatar', avatar)
  },
  setPermissionsBtn({ commit }, permissionsBtn) {
    // 提交mutation，将权限按钮的状态保存到Vuex的state中
    commit('setPermissionsBtn', permissionsBtn)
  },
  async login({ commit }, userInfo) {
    if (userInfo.accessToken && userInfo.accessToken != 'undefined') {
      commit('setAccessToken', userInfo.accessToken)
      const hour = new Date().getHours()
      if (!userInfo.noTips) {
        const thisTime = hour < 8 ? '早上好' : hour <= 11 ? '上午好' : hour <= 13 ? '中午好' : hour < 18 ? '下午好' : '晚上好'
        Vue.prototype.$baseNotify(`欢迎登录${title}`, `${thisTime}！`)
      }
      localStorage.setItem('userInfo', JSON.stringify(userInfo))
    }
  },
  // 刷新token
  async refreshTokenApi({ commit }) {
    const { data, code } = await refreshTokenApi()
    if (data && code === 200) {
      let userInfo = localStorage.getItem('userInfo')
      userInfo = JSON.parse(userInfo)
      userInfo.accessToken = data.access_token
      userInfo.refreshToken = data.refresh_token
      localStorage.setItem('userInfo', JSON.stringify(userInfo))
      commit('setAccessToken', data.access_token)
      return {
        code: 200,
        message: '刷新成功',
        data: data,
      }
    } else {
      // await dispatch('logout')
      // Vue.prototype.$baseMessage('刷新失败，请重新登录...', 'error')
      // return {
      //   code: 401,
      //   message: '刷新失败，请重新登录...',
      //   data: null,
      // }
    }
  },

  async getUserInfo({ commit, state }) {
    const { data } = await merchantInfoApi()
    // if (!data) {
    //   Vue.prototype.$baseMessage('验证失败，请重新登录...', 'error')
    //   return false
    // }
    let { username, logo, phone, merchantList } = data
    if (username) {
      commit('setPermissions', ['admin'])
      commit('setUsername', username)
      commit('setAvatar', logo)
      commit('setPhone', phone)
      let list = merchantList.filter(item => item.isHide == 0)
      commit('setShowWindows', list.length > 1 ? true : false)
      return ['admin']
    } else {
      Vue.prototype.$baseMessage('用户信息接口异常', 'error')
      return false
    }
  },
  async logout({ dispatch }) {
    await logout(state.accessToken)
    await dispatch('resetAccessToken')
    localStorage.clear()
    await resetRouter()
  },
  resetAccessToken({ commit }) {
    commit('setPermissions', [])
    commit('setAccessToken', '')
    removeAccessToken()
  },
}
export default { state, getters, mutations, actions }
