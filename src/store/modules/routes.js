/*
 * @Author: shangfei <EMAIL>
 * @Date: 2025-02-18 14:31:04
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-03-22 15:56:54
 * @FilePath: /new_qst_manage_web/src/store/modules/routes.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * 
 * @description 路由拦截状态管理，目前两种模式：all模式与intelligence模式，其中partialRoutes是菜单暂未使用
 */
import { asyncRoutes, constantRoutes } from '@/router'
import { getRouterList } from '@/api/router'
import { convertRouter, filterAsyncRoutes } from '@/utils/handleRoutes'

const state = () => ({
  routes: [],
  routesTitle: [], // 路由标题，用于面包屑导航菜单展示
  routesList: [],
  childrenRoutes: [], // 子路由，用于面包屑导航菜单展示
  partialRoutes: [],
  routeKey: 'Home' // 当前路由key，用于刷新页面时回到当前页
})
const getters = {
  routes: (state) => state.routes,
  routesTitle: (state) => state.routesTitle,
  routesList: (state) => state.routesList,
  childrenRoutes: (state) => state.childrenRoutes,
  partialRoutes: (state) => state.partialRoutes,
  routeKey: (state) => state.routeKey,
}
const mutations = {
  setAllRoutes(state, routes) {
    state.allRoutes = constantRoutes.concat(routes)
  },
  setRouteKey(state, key) {
    state.routeKey = key
  },
  setRoutesList(state, routes) {
    state.routesList = routes
  },
  setChildrenRoutes(state, routes) {
    state.childrenRoutes = routes
  },
  setRoutes(state, routes) {
    state.routes = constantRoutes.concat(routes)
  },
  setRoutesTitle(state, routes) {
    state.routesTitle = routes
  },
  setAllRoutes(state, routes) {
    state.routes = constantRoutes.concat(routes)
  },
  setPartialRoutes(state, routes) {
    state.partialRoutes = constantRoutes.concat(routes)
  },
}
const actions = {
  async setRoutes({ commit }, permissions) {
    //开源版只过滤动态路由permissions，admin不再默认拥有全部权限
    const finallyAsyncRoutes = await filterAsyncRoutes([...asyncRoutes], permissions)
    console.log(finallyAsyncRoutes)
    commit('setRoutes', finallyAsyncRoutes)
    return finallyAsyncRoutes
  },
  setRouteKey({ commit }, key) {
    commit('setRouteKey', key)
  },
  setChildrenRoutes({ commit }, routes) {
    commit('setChildrenRoutes', routes)
  },
  async setAllRoutes({ commit }) {
    let { data } = await getRouterList()
    commit('setRoutesList', data)

    commit('setRoutesTitle', data)
    data.push({ path: '*', redirect: '/404', hidden: true })
    let accessRoutes = convertRouter(data)
    let addRoutes = []
    accessRoutes.map(item => {
      if (item.component) {
        addRoutes.push([item])
      } else if (item.children && item.children.length > 0) {
        item.children = item.children.filter(child => child.component)
        addRoutes.push(item.children)
      }
    })
    console.log(addRoutes)


    if (!localStorage.getItem('routeKey')) {
      localStorage.setItem('routeKey', 'Home')
    } else {
      let routeKey = localStorage.getItem('routeKey')
      commit('setRouteKey', routeKey)
      if (routeKey != 'Home') {
        let childrenRoutes = data.filter(item => item.name == routeKey) || []
        childrenRoutes.length > 0 ? commit('setChildrenRoutes', childrenRoutes[0].children || []) : '';
      }
    }
    commit('setAllRoutes', addRoutes)
    return accessRoutes
  },
  setPartialRoutes({ commit }, accessRoutes) {
    commit('setPartialRoutes', accessRoutes)
    return accessRoutes
  },
}
export default { state, getters, mutations, actions }
