/*
 * @Author: shangfei <EMAIL>
 * @Date: 2025-04-25 09:45:15
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-06-27 16:47:46
 * @FilePath: \qst-merchant-admin-2.0\src\store\modules\goodsDetaile.js
 * @Description: 商品详情中的公共参数配置
 */
/**
 *
 * @description 商品详情公共参数
 */

const state = () => ({
  goodsId: 0,
  tabKeys: '1',
  // 可切换值 规格类型 1 单规格 2 多规格
  specsType: 1,
  // add 新增 edit 编辑
  saveType: 'add',
  //核销数据
  hxOrderList: '',
  // 刷新商品列表的次数，用于触发商品列表的重新渲染
  refreshShopNum: 0,
  // 适用门店是否开启
  isApplyStores: false,
  // 门店id
  shopId: ''
})
const getters = {
  getGoodsId: (state) => state.goodsId,
  getTabKeys: (state) => state.tabKeys,
  getSaveType: (state) => state.saveType,
  getSpecsType: (state) => state.specsType,
  hxOrderList: (state) => state.hxOrderList,
  getRefreshShopNum: (state) => state.refreshShopNum,
  getIsApplyStores: (state) => state.isApplyStores,
  getShopId: (state) => state.shopId
}
const mutations = {
  // 重置状态
  resetState(state) {
    console.log('重置状态', '--------')
    state.goodsId = 0
    state.tabKeys = '1'
    state.specsType = 1
    state.saveType = 'add'
    state.shopId = ''
  },
  setGoodsId(state, goodsId) {
    state.goodsId = goodsId
  },
  setTabKeys(state, tabKeys) {
    state.tabKeys = tabKeys
  },
  setSaveType(state, saveType) {
    state.saveType = saveType
  },
  setSpecsType(state, specsType) {
    state.specsType = specsType
  },

  sethxOrderList(state, hxOrderList) {
    state.hxOrderList = hxOrderList
  },

  setRefreshShopNum(state) {
    console.log(state.refreshShopNum, '刷新商品列表的次数')
    state.refreshShopNum += 1
  },

  // 设置适用门店是否开启
  setApplyStores(state, isApplyStores) {
    state.isApplyStores = isApplyStores
  },
  // 设置门店id
  setShopId(state, shopId) {
    state.shopId = shopId
  }
}
const actions = {
  resetState({ commit }) {
    commit('resetState')
  },
  setGoodsId({ commit }, goodsId) {
    commit('setGoodsId', goodsId)
  },
  setTabKeys({ commit }, tabKeys) {
    commit('setTabKeys', tabKeys)
  },
  setSaveType({ commit }, saveType) {
    commit('setSaveType', saveType)
  },
  setSpecsType({ commit }, specsType) {
    commit('setSpecsType', specsType)
  },

  sethxOrderList({ commit }, hxOrderList) {
    console.log(11)
    commit('sethxOrderList', hxOrderList)
  },
  setRefreshShopNum({ commit }) {
    commit('setRefreshShopNum')
  },

  // 设置适用门店是否开启
  setApplyStores({ commit }, isApplyStores) {
    commit('setApplyStores', isApplyStores)
  },
  // 设置门店id
  setShopId({ commit }, shopId) {
    commit('setShopId', shopId)
  }
}
export default { state, getters, mutations, actions }
