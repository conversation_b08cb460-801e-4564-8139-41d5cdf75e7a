<template>
  <div class="personal-canter">
    <el-card class="user-card">
      <div class="user-info">
        <img class="user-header" :src="personalInfo.logo" />
        <el-form label-width="70px">
          <el-form-item label="昵称">
            <div class="flex-b">
              <div style="min-width: 165px">{{ personalInfo.username }}</div>
              <el-button @click="editPersonalInfo('password')" type="text">修改密码</el-button>
            </div>
          </el-form-item>

          <el-form-item label="姓名">
            <div class="flex-b" v-if="!isEditPersonal">
              <div style="min-width: 165px">{{ personalInfo.realName }}</div>
              <el-button @click="isEditPersonal = true" type="text">修改</el-button>
            </div>
            <div class="flex-b" v-else>
              <el-input type="text" style="width: 165px" v-model="realName"></el-input>
              <el-button @click="setRealName" type="text">保存</el-button>
              <el-button @click="isEditPersonal = false" type="text">取消</el-button>
            </div>
          </el-form-item>

          <el-form-item label="手机号">
            <div class="flex-b">
              <div style="min-width: 165px">{{ personalInfo.phone }}</div>
              <el-button @click="editPersonalInfo('phone')" type="text">修改手机号</el-button>
            </div>
          </el-form-item>

          <el-form-item label="公司名称">
            <div>{{ personalInfo.merchantName }}</div>
          </el-form-item>

          <el-form-item label="创建时间">
            <div>{{ personalInfo.createdAt }}</div>
          </el-form-item>
        </el-form>
        <div class="user-footer">
          <el-button type="text" @click="userCancel" class="user-footer-btm">账号不用了？注销该账号</el-button>
          <div class="user-footer-tip">注销不可恢复，请谨慎操作！</div>
        </div>
      </div>
    </el-card>
    <el-card class="enterprise-card">
      <template #header>
        <div class="card-header">
          <div></div>
          <div>登录企业</div>
        </div>
      </template>

      <div class="info-item" v-for="(item, index) in personalInfo.merchantList" :key="index">
        <el-form>
          <el-form-item :label="item.merchantName">
            <div class="flex">
              <div style="margin-right: 20px" @click.stop="changeLogin(item)">
                <el-radio v-if="currentMchUid == item.mchStoreUid">当前登录商家</el-radio>
                <el-radio v-else :value="false">切换登录商家</el-radio>
              </div>
              <div class="flex" v-if="(!isNoOperation && item.isHide == 0) || item.isHide == 1">
                <span style="margin-right: 20px">在切换列表中显示</span>
                <div @click="changeSwitch(item)">
                  <el-switch :value="item.isHide == 0"></el-switch>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-form>
        <!-- <div>
          <i class="el-icon-user"></i> 店长
        </div>
        <div class="mt20">
          <i class="el-icon-house"></i> 广州企叮咚-华南大区-线下店群-深圳店
        </div>-->
      </div>
    </el-card>
  </div>
</template>

<script>
import {
  merchantInfoApi,
  switchIsShowApi,
  cancelApi,
  selectLoginApi,
  setRealNameApi,
} from '@/api/user'
import store from '@/store'
import { mapGetters } from 'vuex'
export default {
  name: 'PersonalCenter',
  data() {
    return {
      tabPosition: 'left',
      nickname: '',
      form: {},
      personalInfo: {},
      currentMchUid: '',
      isNoOperation: false,
      realName: '',
      isEditPersonal: false,
    }
  },
  created() {
    this.info()
  },
  computed: {
    ...mapGetters({
      userEdit: 'user/userEdit',
    }),
  },
  activated() {
    this.info()
  },
  watch: {
    userEdit(val) {
      if (val == 'reashed') {
        this.info()
      }
    },
    // $route: {
    //   handler(route) {
    //     debugger
    //     console.log(11)
    //   },
    //   immediate: true,
    // },
  },
  methods: {
    // 初始化
    info() {
      merchantInfoApi().then((res) => {
        if (res.code == 200) {
          this.personalInfo = res.data
          this.realName = res.data.realName
          this.currentMchUid = localStorage.getItem('currentMchUid')
          let keyNum = 0
          res.data.merchantList.forEach((item) => {
            if (item.isHide == 0) {
              keyNum++
            }
          })
          console.log(keyNum)
          this.isNoOperation = keyNum == 1
          store.dispatch('user/setShowWindows', !this.isNoOperation)
        }
      })
    },

    // 修改真实姓名
    saveRealName() {},

    // 修改账号密码
    editPersonalInfo(type) {
      store.dispatch('user/setUserEdit', type)
    },

    // 切换登录商家
    changeLogin(item) {
      console.log(item)
      if (this.currentMchUid == item.mchStoreUid) {
        return
      } else {
        this.currentMchUid = item.mchStoreUid
        selectLoginApi({
          mchStoreUid: item.mchStoreUid,
          mchAdminUid: localStorage.getItem('mchAdminUid'),
        }).then((res) => {
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: '切换成功！',
            })
            localStorage.setItem('currentMchUid', res.data.mchStoreUid)
            store.dispatch('user/login', {
              ...res.data,
              noTips: true,
            })
            this.info()
          }
        })
      }
    },

    // 切换显示隐藏状态
    changeSwitch(item) {
      switchIsShowApi({
        isHide: item.isHide == 0 ? 1 : '0',
        mchStoreUid: item.mchStoreUid,
      }).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: '操作成功！',
          })
          this.info()
        }
      })
    },

    // 注销账号
    userCancel() {
      this.$confirm('注销账号后不可恢复，确认仍需进行注销？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          cancelApi().then((res) => {
            if (res.code == 200) {
              this.$message({
                type: 'success',
                message: '注销成功!',
              })
              localStorage.clear()
              setTimeout(() => {
                window.location.reload()
              }, 1000)
            }
          })

          // 注销账号
        })
        .catch(() => {})
    },
    // 修改昵称
    setRealName() {
      setRealNameApi({
        realName: this.realName,
      }).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: '修改成功!',
          })
          this.isEditPersonal = false
          this.info()
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.personal-canter {
  display: flex;
  align-items: self-start;
  background: #f6f8f9;
  .user-card {
    width: 320px;
    margin-right: 20px;
  }
  .enterprise-card {
    flex: 1;
  }
}
@media (max-width: 700px) {
  .personal-canter {
    flex-direction: column;
    .user-card {
      width: 100%;
      margin-right: 0;
    }
    .enterprise-card {
      width: 100%;
    }
  }
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .user-header {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 32px 0;
    display: block;
  }
  .user-footer {
    width: 100%;
    border-top: 1px dashed #f0f0f0;
    padding-top: 19px;
    text-align: center;
    &-btn {
      text-decoration: line-through;
    }
    &-tip {
      color: #a8abb2;
    }
  }
}

.info-item {
  // padding-bottom: 42px;
  border-bottom: 1px dashed #f0f0f0;
  color: #595959;
  .mt20 {
    margin-top: 8px;
  }
  & + & {
    margin-top: 35px;
  }
}

::v-deep {
  .user-info .el-form-item--small.el-form-item {
    margin-bottom: 10px;
  }
  .user-info .el-form-item__label {
    font-weight: 400;
    font-size: 14px;
    color: #8c8c8c;
  }
  .info-item {
    .el-radio__label {
      font-size: 14px;
      color: #8c8c8c;
    }
    .el-form-item__label {
      font-size: 14px;
      color: #303133;
    }
  }
}
</style>
