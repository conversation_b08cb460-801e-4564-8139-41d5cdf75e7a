<!--
 * @Author: shang<PERSON>i <EMAIL>
 * @Date: 2025-04-19 11:17:22
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-04 10:16:20
 * @FilePath: \qst-merchant-admin-2.0\src\views\finance\fund\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div v-visibility-change="changePage">
    <el-card v-if="flag">
      <div class="topmain">
        <div class="topmainLeft">
          <img class="tip_icon" :src="imgOssPath + '20250517_shuoming.png'" alt />
        </div>
        <div class="topmainright">
          <span>当前小程序支付方式为通联认证，需先完成通联认证，方可进行交易</span>
          <span>仅企业认证的账户交易支持开票</span>
          <el-row>
            <el-button @click="enterpriseCenter(2)" v-if="showSelectBtn.includes(2)" type="primary">
              企业认证（支持对公户及法人个人银行卡）
            </el-button>
            <el-button @click="enterpriseCenter(3)" v-if="showSelectBtn.includes(3)">个人认证（支持法人个人银行卡）</el-button>
          </el-row>
        </div>
      </div>
    </el-card>
    <el-card v-if="showUseBtn">
      <template #header>
        <div class="card-header">
          <div></div>
          <div>您已在企商通其他业务下完成通联{{ tabindex == 2 ? '企业' : '个人' }}认证</div>
        </div>
      </template>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column v-if="tabindex == 2" prop="company_name" label="企业名称"></el-table-column>
        <el-table-column prop="member_type" label="认证类型">
          <template slot-scope="scope">
            <div>
              {{ scope.row.member_type == 2 ? '企业认证' : '个人认证' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="tabindex == 3" prop="name" label="法人姓名"></el-table-column>
        <el-table-column v-if="tabindex == 2" prop="legal_name" label="法人姓名"></el-table-column>
        <el-table-column v-if="tabindex == 3" prop="identity_card_no" label="法人身份证号">
          <template slot-scope="scope">
            <div @click="showIdcardContent(scope.row, 'identity_card_no')">
              {{ scope.row.identity_card_no }}
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="tabindex == 2" prop="status" label="认证状态">
          <template slot-scope="scope">
            <div>
              {{
                scope.row.is_phone_checked == 'Y' && scope.row.is_contract_signed == 'Y' && scope.row.is_protocol_signed == 'Y'
                  ? '已认证'
                  : '未认证'
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="tabindex == 3" prop="is_identity_checked" label="认证状态">
          <template slot-scope="scope">
            <div>
              {{
                scope.row.is_identity_checked == 'Y' && scope.row.is_contract_signed == 'Y' && scope.row.is_protocol_signed == 'Y'
                  ? '已认证'
                  : '未认证'
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="finished_at" label="认证通过时间">
          <template slot-scope="scope">
            <div>
              {{ scope.row.is_contract_signed == 'Y' && scope.row.is_protocol_signed == 'Y' ? scope.row.finished_at : '-' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="绑定手机号"></el-table-column>
      </el-table>
      <el-button style="margin-top: 10px" @click="sureUseBtn">使用此认证账户</el-button>
    </el-card>
    <enterprisePage
      v-if="showInfo"
      :companyInfo="companyInfo"
      :companyAuthInfo="companyAuthInfo"
      :tabActive="tabActive"
      @jump="jump"
      @jumpFlag="jumpFlag"
      @close="close"
    />
    <personPage
      v-if="showInfo1"
      :companyInfo="companyInfo"
      :companyAuthInfo="companyAuthInfo"
      :tabActive="tabActive"
      @jump="jump"
      @jumpFlag="jumpFlag"
      @close="close"
    />
    <payContent v-if="showPayContent" />
    <el-dialog title="身份证号" :visible.sync="dialogVisible" :close-on-click-modal="false">
      <div class="view-phone">{{ modelIdcard }}</div>
    </el-dialog>
  </div>
</template>

<script>
  import {
    getCompanyAuthInfo,
    getCompanyMember,
    getPersonalMember,
    bindAllinpayMemberToCompany,
    getCompleteIdentityNo,
  } from '@/api/finance/fund.js'
  import { getCompanyInfoApi } from '@/api/enterpriseManagement'
  import enterprisePage from './modules/enterprisePage.vue'
  import personPage from './modules/personPage.vue'
  import payContent from './modules/payContent.vue'
  export default {
    name: 'storeManagement',
    components: {
      enterprisePage,
      personPage,
      payContent,
    },
    data() {
      return {
        dialogVisible: false,
        showInfo: false,
        showInfo1: false,
        showPayContent: false,
        flag: true,
        companyInfo: {},
        showUseBtn: false,
        tableData: [],
        tabindex: '',
        modelIdcard: '',
        tabActive: 0,
        companyAuthInfo: {}, //认证信息
        biz_user_id: '', //个人或者公司的biz_user_id
        showSelectBtn: [2, 3],
        jumpFlags: false,
      }
    },
    mounted() {
      this.getCompanyInfo()
    },
    methods: {
      //得到公司信息
      getCompanyInfo() {
        getCompanyInfoApi().then((res) => {
          if (res.code == 200) {
            this.companyInfo = res.data
            this.getCompanyAuthInfoFNn()
          }
        })
      },
      //判断是否有认证
      getCompanyAuthInfoFNn() {
        getCompanyAuthInfo({ company_uid: this.companyInfo.company_uid }).then((res) => {
          if (res.code == 200) {
            this.jumpFlags = false
            this.companyAuthInfo = res.data
            if (res.data?.msg == '无认证记录') {
              this.flag = true
            } else {
              this.showSelectBtn = [res.data.member_type]
              //认证类型（2企业3个人）
              if (res.data.protocol_no != '' && res.data.contract_no != '') {
                this.close()
                this.flag = false
                this.showPayContent = true
                return false
              }
              //认证类型（2企业3个人）
              if (res.data.member_type == 2) {
                //企业认证状态（1、认证中 2、认证成功 3、认证失败）
                if (res.data.status != '2') {
                  this.showInfo = true
                  this.flag = false
                  this.showUseBtn = false
                  return false
                }
                if (res.data.status == '2') {
                  this.showInfo = true
                  this.flag = false
                  this.showUseBtn = false
                  this.tabActive = 1
                  return false
                }
              }
              if (res.data.member_type == 3) {
                //个人认证状态（ Y、认证成功 N、认证失败）
                if (res.data.is_identity_checked != 'Y') {
                  this.showInfo1 = true
                  this.flag = false
                  this.showUseBtn = false
                  return false
                }
                if (res.data.is_identity_checked == 'Y') {
                  this.showInfo1 = true
                  this.flag = false
                  this.showUseBtn = false
                  this.tabActive = 1
                  return false
                }
              }
            }
          }
        })
      },
      showIdcardContent() {
        let params = {
          biz_user_id: this.biz_user_id,
        }
        getCompleteIdentityNo(params).then((res) => {
          if (res.code === 200) {
            this.dialogVisible = true
            this.modelIdcard = res.data.data
          }
        })
      },
      //企业认证  个人认证
      enterpriseCenter(value) {
        this.close()
        this.tabindex = value
        if (value == 2) {
          this.getCompanyMemberFn()
        } else {
          this.getPersonalMemberFn()
        }
      },
      sureUseBtnApi() {
        let params = {
          company_uid: this.companyInfo.company_uid,
          biz_user_id: this.biz_user_id,
          member_type: this.tabindex, //通联认证类型（2企业认证3个人认证）
        }
        bindAllinpayMemberToCompany(params).then((res) => {
          if (res.data.status) {
            this.$message({
              type: 'success',
              message: res.data.msg,
            })
          } else {
            this.$message({
              type: 'error',
              message: res.data.msg,
            })
          }
          console.log(res)
        })
      },
      //根据公司统一信用代码查询企业认证记录
      getCompanyMemberFn() {
        getCompanyMember({ uni_credit: this.companyInfo.organization_code }).then((res) => {
          if (res.code == 200) {
            if (res.data.length == 0) {
              this.companyAuthInfo = {}
              this.flag = false
              this.showInfo = true
              this.tabActive = 0
              return false
            }
            if (res.data.length > 0) {
              this.tableData = [res.data[0]]
              this.companyAuthInfo = res.data[0]
              this.biz_user_id = res.data[0].biz_user_id
              this.showUseBtn = true
            }
          }
        })
      },
      //根据身份证号查询个人认证记录
      getPersonalMemberFn() {
        getPersonalMember({ identity_card_no: this.companyInfo.legal_code }).then((res) => {
          if (res.code == 200) {
            if (res.data.length == 0) {
              this.companyAuthInfo = {}
              this.flag = false
              this.showInfo1 = true
              this.tabActive = 0
              return false
            }
            if (res.data.length > 0) {
              this.companyAuthInfo = res.data[0]
              this.tableData = [res.data[0]]
              this.biz_user_id = res.data[0].biz_user_id
              this.showUseBtn = true
            }
          }
        })
      },
      //使用此认证账户
      sureUseBtn() {
        this.sureUseBtnApi()
        this.getAccountInfo(this.tableData)
      },
      getAccountInfo(res) {
        //认证类型（2企业3个人）
        if (res[0].member_type == 2 && this.tabindex == 2) {
          //企业认证状态（1、认证中 2、认证成功 3、认证失败）
          if (res[0].status != '2') {
            this.showInfo = true
            this.flag = false
            this.showUseBtn = false
            return false
          }
          if (res[0].status == '2') {
            this.showInfo = true
            this.flag = false
            this.showUseBtn = false
            this.tabActive = 1
            return false
          }
          if (res[0].is_phone_checked == 'Y' && res[0].is_contract_signed == 'Y' && res[0].is_protocol_signed == 'Y') {
            this.close()
            this.flag = false
            this.showPayContent = true
            return false
          }
        }
        if (res[0].member_type == 3 && this.tabindex == 3) {
          if (
            res[0].is_identity_checked == 'Y' &&
            res[0].is_phone_checked == 'Y' &&
            res[0].is_contract_signed == 'Y' &&
            res[0].is_protocol_signed == 'Y'
          ) {
            this.close()
            this.flag = false
            this.showPayContent = true
            return false
          }
          //个人认证状态（ Y、认证成功 N、认证失败）
          if (res[0].is_identity_checked != 'Y') {
            this.showInfo1 = true
            this.showUseBtn = false
            this.flag = false
            return false
          }
          if (res[0].is_identity_checked == 'Y') {
            this.showInfo1 = true
            this.showUseBtn = false
            this.flag = false
            this.tabActive = 1
            return false
          }
        }
      },
      close() {
        this.showInfo = false
        this.showInfo1 = false
        this.showPayContent = false
        this.showUseBtn = false
        this.flag = true
        this.tabActive = 0
      },
      jump() {
        this.close()
        this.flag = false
        this.showPayContent = true
      },
      jumpFlag() {
        this.jumpFlags = true
      },
      changePage(evt, hidden) {
        //hidden为false的时候，表示从别的页面切换回当前页面
        //hidden为true的时候，表示从当前页面切换到别的页面
        if (hidden === false) {
          console.log('回到当前页了！')
          if (this.jumpFlags) {
            setTimeout(() => {
              this.getCompanyAuthInfoFNn()
            }, 3000)
          }
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .topmain {
    box-sizing: border-box;
    color: rgba(0, 0, 0, 0.88);
    font-size: 14px;
    display: flex;
    border-radius: 8px;
    background: #fffbe6;
    border: 1px solid #ffe58f;
    padding: 20px 24px;
    .topmainLeft {
      padding: 0 15px;
    }
    .topmainright {
      display: flex;
      flex-direction: column;
      span {
        margin-bottom: 12px;
      }
      span:first-child {
        font-size: 16px;
      }
    }
  }
  .view-phone {
    text-align: center;
  }
</style>
