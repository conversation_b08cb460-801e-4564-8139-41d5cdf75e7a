<!--
 * @Author: liqian liqian@123
 * @Date: 2025-04-23 15:14:26
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-07 10:35:55
 * @FilePath: \qst-merchant-admin-2.0\src\views\finance\fund\modules\enterprisePage.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <el-card>
      <template #header>
        <div class="card-header">
          <div></div>
          <div style="font-size: 16px; font-weight: 600">通联企业认证</div>
        </div>
      </template>
      <el-steps :active="active">
        <el-step title="企业信息补充" description="补充企业账户信息"></el-step>
        <el-step title="签订协议" description="签订服务协议"></el-step>
        <el-step title="认证完成" description="完成企业认证"></el-step>
      </el-steps>

      <div class="formDiv" v-if="active == 0">
        <el-form
          label-width="180px"
          :rules="informationRule"
          :model="information"
          ref="information"
          :label-position="labelPosition"
        >
          <div class="card-header">
            <div></div>
            <div class="titletip">基本信息</div>
          </div>

          <el-descriptions class="margin-top" size="medium" border title="" :column="2">
            <el-descriptions-item label="企业名称">
              {{ companyInfo.organization_name }}
            </el-descriptions-item>
            <el-descriptions-item label="统一社会信用代码">
              {{ companyInfo.organization_code }}
            </el-descriptions-item>
            <el-descriptions-item label="法定代表人">
              {{ companyInfo.legal_name }}
            </el-descriptions-item>
            <el-descriptions-item label="法人身份证号码">
              {{ companyInfo.legal_code }}
            </el-descriptions-item>
          </el-descriptions>

          <el-row class="inputRow" :gutter="24">
            <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
              <el-form-item label="开户行" prop="bank_name">
                <el-select
                  style="width: 100%"
                  v-model="information.bank_name"
                  filterable
                  remote
                  :disabled="companyAuthInfo.status == 2"
                  reserve-keyword
                  placeholder="请输入开户银行名称"
                  :remote-method="remoteMethod"
                  :loading="loading"
                >
                  <el-option
                    v-for="item in BankList"
                    :key="item.code"
                    :label="item.bank_name"
                    :value="item.bank_name"
                  ></el-option>
                </el-select>
                <!-- <el-input v-model="information.bank_name" placeholder="请输入开户银行名称" type="text"></el-input> -->
              </el-form-item>
            </el-col>
            <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
              <el-form-item label="开户支行" prop="sub_bank_name">
                <el-select
                  style="width: 100%"
                  v-model="information.sub_bank_name"
                  filterable
                  remote
                  :disabled="companyAuthInfo.status == 2"
                  reserve-keyword
                  placeholder="请输入开户支行名称"
                  :remote-method="remoteMethod1"
                  :loading="loading"
                  @change="getsubbankchange"
                >
                  <el-option
                    v-for="item in BankBranchList"
                    :key="item.union_bank"
                    :label="item.bank_name"
                    :value="item.bank_name"
                  ></el-option>
                </el-select>
                <!-- <el-input v-model="information.sub_bank_name" placeholder="请输入开户支行名称" type="text"></el-input> -->
              </el-form-item>
            </el-col>
            <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
              <el-form-item label="对公账户账号" prop="bank_card_no">
                <el-input
                  :disabled="companyAuthInfo.status == 2"
                  v-model="information.bank_card_no"
                  placeholder="请输入对公账户账号"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="formDiv" v-if="active == 1">
        <div class="topmain">
          <div class="topmainLeft">
            <img class="tip_icon" :src="imgOssPath + '20250517_shuoming.png'" alt />
          </div>
          <div class="topmainright">
            <span>请签订以下协议</span>
            <span class="topmainright1">
              请绑定手机号后，点击按钮前往签订以下两份协议，两份协议签订完成后将自动进入下一步
            </span>
          </div>
        </div>
        <el-form
          label-width="180px"
          :rules="informationRule"
          :model="information"
          ref="information"
          :label-position="labelPosition"
        >
          <el-row class="inputRow" :gutter="20">
            <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="24">
              <el-form-item label="绑定手机号" prop="phone">
                <div style="display: flex; gap: 15px">
                  <el-input
                    v-model="information.phone"
                    :disabled="btnDisabled"
                    placeholder="请输入手机号"
                    type="text"
                  ></el-input>
                  <el-button type="text" @click="changePhone" v-if="btnDisabled">
                    修改手机号
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12" v-if="!btnDisabled">
              <el-form-item label="验证码" prop="verification_code">
                <div style="display: flex; gap: 15px">
                  <el-input
                    v-model="information.verification_code"
                    placeholder="请输入验证码"
                    type="text"
                  ></el-input>
                  <el-button v-if="verifyTimeOut">{{ getverifyTimeOut }}</el-button>
                  <el-button v-else @click="getPhoneCode('verify', 9)">获取验证码</el-button>
                  <el-button
                    @click="submitSure"
                    :disabled="information.verification_code == ''"
                    type="primary"
                  >
                    验证
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <div>
            <h2>通联支付商户服务协议</h2>
            <el-button
              :disabled="!btnDisabled"
              v-if="companyAuthInfo.is_contract_signed == 'N'"
              @click="jumpAsign(0)"
            >
              前往签订
            </el-button>
            <div v-if="companyAuthInfo.is_contract_signed == 'Y'">
              <el-button type="text" style="color: #67c23a">
                已签订《通联支付商户服务协议》
              </el-button>
              <span>协议号：{{ companyAuthInfo.contract_no }}</span>
            </div>
          </div>
          <div>
            <h2>通联支付免密支付协议</h2>
            <el-button
              :disabled="!btnDisabled"
              v-if="companyAuthInfo.is_protocol_signed == 'N'"
              @click="jumpAsign(1)"
            >
              前往签订
            </el-button>
            <div v-if="companyAuthInfo.is_protocol_signed == 'Y'">
              <el-button type="text" style="color: #67c23a">
                已签订《通联支付免密支付协议》
              </el-button>
              <span>协议号：{{ companyAuthInfo.protocol_no }}</span>
            </div>
          </div>
        </el-form>
      </div>
      <div class="formDiv" v-if="active == 3">
        <div class="thirdDiv">
          <img src="" alt="" />
          <h1>恭喜您完成企业认证！</h1>
          <div class="thirdDiv1">您已成功完成通联支付企业认证，现在可以使用资金管理相关功能。</div>
          <el-button type="primary" @click="jumpFundMount">前往资金管理</el-button>
        </div>
      </div>
      <div class="ui_spane">
        <el-button @click="reback" v-if="active == 0">返回选择</el-button>
        <el-button @click="nextForm" type="primary" v-if="active == 0">下一步</el-button>
        <el-button @click="preForm" v-if="active == 1">上一步</el-button>
      </div>
    </el-card>
    <el-dialog title="解绑手机号" :visible.sync="dialogVisible" :close-on-click-modal="false">
      <el-form :rules="formInfoRule" :model="formInfo" ref="formInfoReuse">
        <el-form-item label="手机验证码" prop="verification_code">
          <div style="display: flex; gap: 15px">
            <el-input
              v-model="formInfo.verification_code"
              placeholder="请输入验证码"
              type="text"
            ></el-input>
            <el-button v-if="verifyTimeOut">{{ getverifyTimeOut }}</el-button>
            <el-button v-else @click="getPhoneCode('verify', 6)">获取验证码</el-button>
          </div>
        </el-form-item>

        <base-dialog-footer
          confirmText="确认解绑"
          @cancel="dialogVisible = false"
          @confirm="confirm"
        ></base-dialog-footer>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
  import {
    getCompanyBankList,
    getBankBranchList,
    getimproveCompanyInfo,
    sendVerificationCode,
    bindPhoneCompany,
    signContract,
    signBalanceProtocol,
    unBindPhone,
  } from '@/api/finance/fund.js'
  import { getBankCardList } from '@/api/finance/bankCard.js'
  import { timeOut } from '@/utils/public'

  export default {
    name: 'enterprisePage',
    components: {},
    props: {
      companyInfo: {
        type: Object,
        default: () => {},
      },
      companyAuthInfo: {
        type: Object,
        default: () => {},
      },
      tabActive: {
        type: Number,
        default: 0,
      },
    },
    watch: {
      tabActive: {
        handler(val) {
          this.active = val
        },
        immediate: true,
      },
      companyAuthInfo: {
        handler(val) {
          if (val.is_phone_checked == 'Y') {
            this.btnDisabled = true
            this.information.phone = val.phone
            // this.is_contract_signed = val.is_contract_signed
            // this.is_protocol_signed = val.is_protocol_signed
          } else {
            // this.is_contract_signed = val.is_contract_signed || 'N'
            // this.is_protocol_signed = val.is_protocol_signed || 'N'
          }
        },
        immediate: true,
      },
    },
    computed: {
      getverifyTimeOut() {
        if (this.information.verification_code) {
          this.verifyTimeOut = null
          this.timeOutID.end()
        } else {
          return this.verifyTimeOut
        }
      },
    },
    data() {
      return {
        dialogVisible: false,
        formInfo: {
          verification_code: '',
        },
        is_contract_signed: 'N',
        is_protocol_signed: 'N',
        active: 0,
        loading: false,
        labelPosition: 'top',
        BankList: [],
        BankBranchList: [],
        information: {
          bank_name: '',
          sub_bank_name: '',
          bank_card_no: '',
          union_bank: '',
          phone: '',
          verification_code: '',
        },
        btnDisabled: false,
        asignFlag: false,
        asignFlag1: false,
        informationRule: {
          bank_name: [{ required: true, message: '请输入开户银行名称', trigger: 'blur' }],
          sub_bank_name: [{ required: true, message: '请输入开户支行名称 ', trigger: 'blur' }],
          bank_card_no: [{ required: true, message: '请输入对公账户账号', trigger: 'blur' }],
          phone: [
            { required: true, message: '请输入手机号', trigger: 'blur' },
            { pattern: /^1[3456789]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' },
          ],
          verification_code: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
        },
        formInfoRule: {
          verification_code: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
        },
        verifyTimeOut: null,
        timeOutID: null,
        timeOut: '',
      }
    },
    methods: {
      reback() {
        this.$emit('close')
      },
      nextForm() {
        this.$refs.information.validate((valid) => {
          if (valid) {
            if (this.companyAuthInfo.status == 2) {
              this.active++
              return false
            }
            let params = {
              ...this.information,
              company_uid: this.companyInfo.company_uid,
            }
            getimproveCompanyInfo(params).then((res) => {
              if (res.code == 200) {
                if (!res.data.status) {
                  this.$message.error(res.data.msg)
                  return false
                } else {
                  this.active++
                  this.companyAuthInfo.biz_user_id = res.data.biz_user_id
                  this.companyAuthInfo.is_contract_signed = 'N'
                  this.companyAuthInfo.is_protocol_signed = 'N'
                }
              }
            })
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
      preForm() {
        this.getBankCardListFn()
        this.active--
      },
      getBankCardListFn() {
        getBankCardList({
          biz_user_id: this.companyAuthInfo.biz_user_id, //'397e3428-2d86-11f0-8a14-0242e6515671', // this.companyAuthInfo.biz_user_id,
          bank_name: '', //银行名
          bank_type: '', //类型（1.法人个人银行卡 2.企业对公银行卡）
        }).then((res) => {
          if (res.data.length > 0) {
            this.information.bank_name = res.data[0].bank_name
            this.information.sub_bank_name = res.data[0].sub_bank_name
            this.information.bank_card_no = res.data[0].bank_card_no
          }
        })
      },
      // 根据keyword查询公户（公司开户银行）列表
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          getCompanyBankList({ keyword: query }).then((res) => {
            if (res.code == 200) {
              this.loading = false
              this.BankList = res.data.content
            }
          })
        } else {
          this.BankList = []
        }
      },
      // 根据keyword查询开户行支行列表
      remoteMethod1(query) {
        if (query !== '') {
          this.loading = true
          getBankBranchList({ keyword: query }).then((res) => {
            if (res.code == 200) {
              this.loading = false
              this.BankBranchList = res.data.content
            }
          })
        } else {
          this.BankBranchList = []
        }
      },
      getsubbankchange(value) {
        // this.information.union_bank = value
        getBankBranchList({ keyword: value }).then((res) => {
          if (res.code == 200) {
            this.information.union_bank = res.data.content[0].union_bank
          }
        })
      },
      //修改手机号
      changePhone() {
        this.dialogVisible = true
        this.$nextTick(() => {
          this.$refs.formInfoReuse.resetFields()
        })
      },
      // 获取验证码
      getPhoneCode(type, num) {
        this.$refs['information'].validateField('phone', (errorMessage) => {
          let valid = errorMessage == '' ? true : false
          if (valid) {
            sendVerificationCode({
              phone: this.information.phone,
              biz_user_id: this.companyAuthInfo.biz_user_id,
              type: num, //验证码类型（9绑定6解绑）
            }).then((res) => {
              if (res.code == 200) {
                this.$message.success(res.data.msg)
                this.timeOutID = new timeOut(60)
                this.timeOutID.start((e) => {
                  if (e.totalSeconds == 0) {
                    this[type + 'TimeOut'] = null
                  } else {
                    this[type + 'TimeOut'] = e.totalSeconds + 's'
                  }
                })
              }
            })
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
      // 验证
      submitSure() {
        this.$refs.information.validate((valid) => {
          if (valid) {
            let params = {
              phone: this.information.phone,
              biz_user_id: this.companyAuthInfo.biz_user_id,
              verification_code: this.information.verification_code,
            }
            bindPhoneCompany(params).then((res) => {
              if (res.code == 200) {
                if (!res.data.status) {
                  this.$message.error(res.data.msg)
                  // this.btnDisabled = true
                } else {
                  this.$message.success(res.data.msg)
                  this.btnDisabled = true
                }
              }
            })
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
      //前往签订
      jumpAsign(value) {
        if (value == 0) {
          signContract({ biz_user_id: this.companyAuthInfo.biz_user_id, jump_url: '' }).then(
            (res) => {
              console.log(res)
              if (res.code == 200) {
                if (!res.data.status) {
                  this.$message.error(res.data.msg)
                } else {
                  this.$message.success(res.data.msg)
                  this.$emit('jumpFlag')
                  window.open(res.data.url)
                }
              }
            }
          )
        }
        if (value == 1) {
          signBalanceProtocol({ biz_user_id: this.companyAuthInfo.biz_user_id, jump_url: '' }).then(
            (res) => {
              console.log(res)
              if (res.code == 200) {
                if (!res.data.status) {
                  this.$message.error(res.data.msg)
                } else {
                  this.$message.success(res.data.msg)
                  this.$emit('jumpFlag')
                  window.open(res.data.url)
                }
              }
            }
          )
        }
      },
      //确认解绑手机号
      confirm() {
        this.$refs.formInfoReuse.validate((valid) => {
          if (valid) {
            let params = {
              phone: this.information.phone,
              biz_user_id: this.companyAuthInfo.biz_user_id,
              verification_code: this.formInfo.verification_code,
              member_type: 2,
            }
            unBindPhone(params).then((res) => {
              if (res.code == 200) {
                if (!res.data.status) {
                  this.$message.error(res.data.msg)
                  // this.btnDisabled = true
                } else {
                  this.$message.success(res.data.msg)
                  this.information.phone = ''
                  this.information.verification_code = ''
                  this.companyAuthInfo.is_phone_checked = 'N'
                  this.btnDisabled = false
                  this.dialogVisible = false
                  this.verifyTimeOut = null
                  this.timeOutID.end()
                }
              }
            })
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
      //前往资金管理
      jumpFundMount() {
        this.$emit('jump')
      },
    },
  }
</script>
<style lang="scss" scoped>
  .card-header {
    display: flex;
    align-items: center;
    .titletip {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #303133;
    }
  }

  .formDiv {
    margin-top: 20px;
    .topmain {
      box-sizing: border-box;
      color: rgba(0, 0, 0, 0.88);
      font-size: 14px;
      display: flex;
      border-radius: 8px;
      background: #eef6ff;
      border: 1px solid #94c3ff;
      padding: 20px 24px;
      .topmainLeft {
        padding: 15px;
      }
      .topmainright {
        display: flex;
        flex-direction: column;
        span {
          margin-bottom: 8px;
        }
        span:first-child {
          font-weight: 600;
          font-size: 16px;
        }
        .topmainright1 {
          font-size: 14px;
          color: #666666;
        }
      }
    }
    .inputRow {
      margin-top: 20px;
    }
    .thirdDiv {
      text-align: center;
      .thirdDiv1 {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #86909c;
        line-height: 14px;
        margin-bottom: 30px;
      }
    }
  }
</style>
