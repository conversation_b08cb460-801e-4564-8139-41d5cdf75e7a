<template>
  <div>
    <el-card>
      <template #header>
        <div class="card-header">
          <div></div>
          <div>通联个人认证</div>
        </div>
      </template>
      <el-steps :active="active">
        <el-step title="个人账户开通"></el-step>
        <el-step title="签订电子协议"></el-step>
        <el-step title="认证完成" description="完成个人认证"></el-step>
      </el-steps>
      <div class="formDiv" v-if="active == 0">
        <div class="card-header">
          <div></div>
          <div class="titletip">个人信息确认</div>
        </div>
        <div class="titletip1">请确认以下个人信息准确无误，如需修改请联系客服。</div>

        <el-form
          label-width="180px"
          :model="companyInfo"
          ref="information"
          :label-position="labelPosition"
        >
          <el-row class="inputRow" :gutter="24">
            <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
              <el-form-item label="法人姓名">
                <el-input
                  disabled
                  v-model="companyInfo.legal_name"
                  placeholder="请输入法人姓名"
                  type="text"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
              <el-form-item label="身份证号码" prop="legal_code">
                <el-input
                  disabled
                  v-model="companyInfo.legal_code"
                  placeholder="请输入身份证号码"
                  type="text"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="formDiv" v-if="active == 1">
        <div class="topmain">
          <div class="topmainLeft">
            <img class="tip_icon" :src="imgOssPath + '20250517_shuoming.png'" alt />
          </div>
          <div class="topmainright">
            <span>请签订以下协议</span>
            <span class="topmainright1">
              请绑定手机号后，点击按钮前往签订以下两份协议，两份协议签订完成后将自动进入下一步
            </span>
          </div>
        </div>
        <el-form
          label-width="180px"
          :rules="informationRule"
          :model="information"
          ref="information"
          :label-position="labelPosition"
        >
          <el-row class="inputRow" :gutter="20">
            <el-col :lg="8" :md="8" :sm="12" :xl="12" :xs="24">
              <el-form-item label="绑定手机号" prop="phone">
                <div style="display: flex; gap: 15px">
                  <el-input
                    v-model="information.phone"
                    :disabled="btnDisabled"
                    placeholder="请输入手机号"
                    type="text"
                  ></el-input>
                  <el-button type="text" @click="changePhone" v-if="btnDisabled">
                    修改手机号
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :lg="8" :md="8" :sm="12" :xl="12" :xs="24" v-if="!btnDisabled">
              <el-form-item label="验证码" prop="verification_code">
                <div style="display: flex; gap: 15px">
                  <el-input
                    v-model="information.verification_code"
                    placeholder="请输入验证码"
                    type="text"
                  ></el-input>
                  <el-button v-if="verifyTimeOut">{{ getverifyTimeOut }}</el-button>
                  <el-button v-else @click="getPhoneCode('verify', 9)">获取验证码</el-button>
                  <el-button
                    @click="submitSure"
                    :disabled="information.verification_code == ''"
                    type="primary"
                  >
                    验证
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <div>
            <h2>通联支付商户服务协议</h2>
            <el-button
              :disabled="!btnDisabled"
              v-if="companyAuthInfo.is_contract_signed == 'N'"
              @click="jumpAsign(0)"
            >
              前往签订
            </el-button>
            <div v-if="companyAuthInfo.is_contract_signed == 'Y'">
              <span style="color: #67c23a">已签订《通联支付商户服务协议》</span>
              <span>协议号：{{ companyAuthInfo.contract_no }}</span>
            </div>
          </div>
          <div>
            <h2>通联支付免密支付协议</h2>
            <el-button
              :disabled="!btnDisabled"
              v-if="companyAuthInfo.is_protocol_signed == 'N'"
              @click="jumpAsign(1)"
            >
              前往签订
            </el-button>
            <div v-if="companyAuthInfo.is_protocol_signed == 'Y'">
              <span style="color: #67c23a">已签订《通联支付免密支付协议》</span>
              <span>协议号：{{ companyAuthInfo.protocol_no }}</span>
            </div>
          </div>
        </el-form>
      </div>
      <div class="formDiv" v-if="active == 3">
        <div class="thirdDiv">
          <img :src="imgOssPath + '20250517_success.png'" alt="" />
          <h1>恭喜您完成个人认证！</h1>
          <div class="thirdDiv1">您已成功完成通联支付个人认证，现在可以使用资金管理相关功能。</div>
          <el-button type="primary" @click="jumpFundMount">前往资金管理</el-button>
        </div>
      </div>
      <div class="ui_spane" style="text-align: left">
        <el-button @click="submitOpen" type="primary" v-if="active == 0">
          信息无误，确认开通
        </el-button>
        <el-button @click="reback" v-if="active == 1">返回选择</el-button>
        <el-button @click="preForm" v-if="active == 2">上一步</el-button>
        <!-- <el-button @click="submitForm" type="primary" v-if="active == 2">提交</el-button> -->
      </div>
    </el-card>
    <el-dialog title="解绑手机号" :visible.sync="dialogVisible" :close-on-click-modal="false">
      <el-form :rules="formInfoRule" :model="formInfo" ref="formInfoReuse">
        <el-form-item label="手机验证码" prop="verification_code">
          <div style="display: flex; gap: 15px">
            <el-input
              v-model="formInfo.verification_code"
              placeholder="请输入验证码"
              type="text"
            ></el-input>
            <el-button v-if="verifyTimeOut">{{ getverifyTimeOut }}</el-button>
            <el-button v-else @click="getPhoneCode('verify', 6)">获取验证码</el-button>
          </div>
        </el-form-item>

        <base-dialog-footer
          confirmText="确认解绑"
          @cancel="dialogVisible = false"
          @confirm="confirm"
        ></base-dialog-footer>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
  import { timeOut } from '@/utils/public'
  import {
    sendVerificationCode,
    bindPhonePersonal,
    improvePersonalInfoy,
    signContract,
    signBalanceProtocol,
    unBindPhone,
  } from '@/api/finance/fund.js'
  export default {
    name: 'personPage',
    components: {},
    props: {
      companyInfo: {
        type: Object,
        default: () => {},
      },
      companyAuthInfo: {
        type: Object,
        default: () => {},
      },
      tabActive: {
        type: Number,
        default: 0,
      },
    },
    watch: {
      tabActive: {
        handler(val) {
          this.active = val
        },
        immediate: true,
      },
      companyAuthInfo: {
        handler(val) {
          if (val.is_phone_checked == 'Y') {
            this.btnDisabled = true
            this.information.phone = val.phone
            // this.is_contract_signed = val.is_contract_signed
            // this.is_protocol_signed = val.is_protocol_signed
          } else {
            // this.is_contract_signed = val.is_contract_signed || 'N'
            // this.is_protocol_signed = val.is_protocol_signed || 'N'
          }
        },
        immediate: true,
      },
    },
    computed: {
      getverifyTimeOut() {
        if (this.information.verification_code) {
          this.verifyTimeOut = null
          this.timeOutID.end()
        } else {
          return this.verifyTimeOut
        }
      },
    },
    data() {
      return {
        dialogVisible: false,
        formInfo: {
          verification_code: '',
        },
        is_contract_signed: 'N',
        is_protocol_signed: 'N',
        active: 0,
        labelPosition: 'top',
        information: {
          name: '',
          name1: '',
          account: '',
          phone: '',
          verification_code: '',
        },
        btnDisabled: false,
        asignFlag: false,
        asignFlag1: false,
        informationRule: {
          name: [{ required: true, message: '请输入开户银行名称', trigger: 'blur' }],
          name1: [{ required: true, message: '请输入开户支行名称 ', trigger: 'blur' }],
          account: [{ required: true, message: '请输入对公账户账号', trigger: 'blur' }],
          phone: [
            { required: true, message: '请输入手机号', trigger: 'blur' },
            { pattern: /^1[3456789]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' },
          ],
          verification_code: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
        },
        formInfoRule: {
          verification_code: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
        },
        verifyTimeOut: null,
        timeOutID: null,
        timeOut: '',
      }
    },
    methods: {
      reback() {
        this.$emit('close')
      },
      preForm() {
        this.active--
      },
      submitForm() {
        this.active = 3
      },
      //修改手机号 解绑
      changePhone() {
        this.dialogVisible = true
        this.$nextTick(() => {
          this.$refs.formInfoReuse.resetFields()
        })
      },
      // 获取验证码
      getPhoneCode(type, num) {
        this.$refs['information'].validateField('phone', (errorMessage) => {
          let valid = errorMessage == '' ? true : false
          if (valid) {
            sendVerificationCode({
              phone: this.information.phone,
              biz_user_id: this.companyAuthInfo.biz_user_id,
              type: num, //验证码类型（9绑定6解绑）
            }).then((res) => {
              if (res.code == 200) {
                this.$message.success(res.data.msg)
                this.timeOutID = new timeOut(60)
                this.timeOutID.start((e) => {
                  if (e.totalSeconds == 0) {
                    this[type + 'TimeOut'] = null
                  } else {
                    this[type + 'TimeOut'] = e.totalSeconds + 's'
                  }
                })
              }
            })
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
      // 确定
      submitSure() {
        this.$refs.information.validate((valid) => {
          if (valid) {
            let params = {
              phone: this.information.phone,
              biz_user_id: this.companyAuthInfo.biz_user_id,
              verification_code: this.information.verification_code,
            }
            bindPhonePersonal(params).then((res) => {
              if (res.code == 200) {
                if (!res.data.status) {
                  this.$message.error(res.data.msg)
                  // this.btnDisabled = true
                } else {
                  this.$message.success(res.data.msg)
                  this.btnDisabled = true
                }
              }
            })
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
      // 个人开通
      submitOpen() {
        if (this.companyAuthInfo.is_identity_checked == 'Y') {
          this.active++
          return false
        }
        improvePersonalInfoy({ company_uid: this.companyInfo.company_uid }).then((res) => {
          if (res.code == 200) {
            if (!res.data.status) {
              this.$message.error(res.data.msg)
              return false
            } else {
              this.$message.success(res.data.msg)
              this.companyAuthInfo.biz_user_id = res.data.biz_user_id
              this.companyAuthInfo.is_contract_signed = 'N'
              this.companyAuthInfo.is_protocol_signed = 'N'
              this.active++
            }
          }
        })
      },
      //前往签订
      jumpAsign(value) {
        if (value == 0) {
          signContract({ biz_user_id: this.companyAuthInfo.biz_user_id, jump_url: '' }).then(
            (res) => {
              console.log(res)
              if (res.code == 200) {
                if (!res.data.status) {
                  this.$message.error(res.data.msg)
                } else {
                  this.$message.success(res.data.msg)
                  this.$emit('jumpFlag')
                  window.open(res.data.url)
                }
              }
            }
          )
        }
        if (value == 1) {
          signBalanceProtocol({ biz_user_id: this.companyAuthInfo.biz_user_id, jump_url: '' }).then(
            (res) => {
              console.log(res)
              if (res.code == 200) {
                if (!res.data.status) {
                  this.$message.error(res.data.msg)
                } else {
                  this.$message.success(res.data.msg)
                  this.$emit('jumpFlag')
                  window.open(res.data.url)
                }
              }
            }
          )
        }
      },
      //确认解绑手机号
      confirm() {
        this.$refs.formInfoReuse.validate((valid) => {
          if (valid) {
            let params = {
              phone: this.information.phone,
              biz_user_id: this.companyAuthInfo.biz_user_id,
              verification_code: this.formInfo.verification_code,
              member_type: 3,
            }
            unBindPhone(params).then((res) => {
              if (res.code == 200) {
                if (!res.data.status) {
                  this.$message.error(res.data.msg)
                  // this.btnDisabled = true
                } else {
                  this.$message.success(res.data.msg)
                  this.information.phone = ''
                  this.information.verification_code = ''
                  this.companyAuthInfo.is_phone_checked = 'N'
                  this.btnDisabled = false
                  this.dialogVisible = false
                  this.verifyTimeOut = null
                  this.timeOutID.end()
                }
              }
            })
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
      //前往资金管理
      jumpFundMount() {
        this.$emit('jump')
      },
    },
  }
</script>
<style lang="scss" scoped>
  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    .titletip {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #303133;
    }
  }
  .titletip1 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
  }
  .formDiv {
    margin-top: 20px;
    .topmain {
      box-sizing: border-box;
      color: rgba(0, 0, 0, 0.88);
      font-size: 14px;
      display: flex;
      border-radius: 8px;
      background: #eef6ff;
      border: 1px solid #94c3ff;
      padding: 20px 24px;
      .topmainLeft {
        padding: 0 15px;
      }
      .topmainright {
        display: flex;
        flex-direction: column;
        span {
          margin-bottom: 8px;
        }
        span:first-child {
          font-weight: 600;
          font-size: 16px;
        }
        .topmainright1 {
          font-size: 14px;
          color: #666666;
        }
      }
    }
    .inputRow {
      margin-top: 20px;
    }
    .thirdDiv {
      text-align: center;
      .thirdDiv1 {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #86909c;
        line-height: 14px;
        margin-bottom: 30px;
      }
    }
  }
</style>
