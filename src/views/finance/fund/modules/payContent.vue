<!--
 * @Author: liq<PERSON> liqian@123
 * @Date: 2025-04-24 14:26:32
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-01 16:42:30
 * @FilePath: \qst-merchant-admin-2.0\src\views\finance\fund\modules\payContent.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <div v-if="!showDetail">
      <el-card>
        <template #header>
          <div class="card-header">
            <div></div>
            <div style="font-size: 16px; font-weight: 600">资金管理</div>
          </div>
        </template>
        <el-row :gutter="24">
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <div class="transaction-item">
              <div class="transaction-tips">
                <span class="tip">总余额</span>
                <el-tooltip class="item" effect="dark" content="所有支付渠道的可用余额总和" placement="top">
                  <img
                    :src="imgOssPath + '20250619_sf_wenhao.png'"
                    alt
                    class="icon_wenhao"
                    style="transform: translateY(2px); margin-left: 4px"
                  />
                </el-tooltip>
              </div>
              <div class="transaction-num">
                <span>￥{{ fundInfoList.total_amount }}</span>
              </div>
            </div>
          </el-col>
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <div class="transaction-item">
              <div class="transaction-tips">
                <span class="tip">累计提现金额</span>
                <el-tooltip class="item" effect="dark" content="历史累计已提现到结算账户的总金额" placement="top">
                  <img
                    :src="imgOssPath + '20250619_sf_wenhao.png'"
                    alt
                    class="icon_wenhao"
                    style="transform: translateY(2px); margin-left: 4px"
                  />
                </el-tooltip>
              </div>
              <div class="transaction-num">
                <span>￥{{ fundInfoList.total_withdrawal_amount }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>
      <div class="contentpay">
        <!-- 通联账户 -->
        <el-card>
          <template #header>
            <div class="card-header">
              <div></div>
              <div style="font-size: 16px; font-weight: 600">通联账户</div>
            </div>
          </template>
          <el-row :gutter="24">
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <span>账户类型:{{ tonglian_account.account_type }}</span>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <span>商户号:{{ tonglian_account.merchant_no }}</span>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12" v-for="(item, index) in codeInfo" :key="index">
              <div class="infomation">
                <div class="transaction-item">
                  <div class="transaction-tips">
                    <span>{{ item.name }}</span>
                    <el-tooltip class="item" effect="dark" :content="item.tip" placement="top">
                      <img
                        :src="imgOssPath + '20250619_sf_wenhao.png'"
                        alt
                        class="icon_wenhao"
                        style="transform: translateY(2px); margin-left: 4px"
                      />
                    </el-tooltip>
                  </div>
                  <div class="transaction-num">
                    <span>¥{{ tonglian_account[item.code] }}</span>
                    <div>
                      <i class="el-icon-link"></i>
                      <el-button type="text" @click="lookDetail(tonglian_account, item)">查看明细</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
        <!-- 微信账户 -->
        <el-card>
          <template #header>
            <div class="card-header">
              <div></div>
              <div style="font-size: 16px; font-weight: 600">微信账户</div>
            </div>
          </template>
          <el-row :gutter="24">
            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
              <span>商户号:{{ wechat_account.merchant_no }}</span>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12" v-for="(item, index) in codeInfo1" :key="index">
              <div class="infomation">
                <div class="transaction-item">
                  <div class="transaction-tips">
                    <span>{{ item.name }}</span>
                    <el-tooltip class="item" effect="dark" :content="item.tip" placement="top">
                      <img
                        :src="imgOssPath + '20250619_sf_wenhao.png'"
                        alt
                        class="icon_wenhao"
                        style="transform: translateY(2px); margin-left: 4px"
                      />
                    </el-tooltip>
                  </div>
                  <div class="transaction-num">
                    <span>¥{{ wechat_account[item.code] }}</span>
                    <div>
                      <i class="el-icon-link"></i>
                      <el-button type="text" @click="lookDetail(wechat_account, item)">查看明细</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
          <!-- <div class="infomation">
        <div class="content" v-for="(item, index) in 2" :key="index">
          <div class="contenttop">
            <span>账户类型:企业账户</span>
            <span>商户号 **********</span>
          </div>
          <div class="contentBottom">
            <div class="transaction-item" v-for="(item, index) in 5" :key="index">
              <div class="transaction-tips">
                <span>今日支付订单数</span>
                <el-tooltip class="item" effect="dark" content="Top Center 提示文字" placement="top">
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
              </div>
              <div class="transaction-num">
                <span>¥6,000.00</span>
                <div>
                  <i class="el-icon-link"></i>
                  <el-button type="text">查看明细</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
          </div>-->
        </el-card>
      </div>
    </div>
    <div v-if="showDetail">
      <el-card>
        <template #header>
          <div class="topSty">
            <el-button class="btn" icon="el-icon-back" @click="reback">返回</el-button>
            <div class="card-header">
              <div></div>
              <div style="font-size: 16px; font-weight: 600">{{ title }}</div>
            </div>
          </div>
        </template>
        <base-form :tableForm="tableForm" :formArray="formArray" @searchForm="searchForm"></base-form>

        <base-table
          v-if="!loading"
          :tableColumn="tableColumn"
          :tableRequest="getGoodsSpecList"
          :tableForm="tableForm"
          ref="tableRef"
        ></base-table>
      </el-card>
    </div>
  </div>
</template>
<script>
  import { fundInfo } from '@/api/finance/fund.js'
  export default {
    name: 'payContent',
    components: {},
    data() {
      return {
        showDetail: false,
        title: '',
        formArray: [
          {
            label: '时间',
            type: 'time',
            key: 'time',
            timeKey: ['createAtStart', 'createAtEnd'],
          },
          {
            label: '类型',
            type: 'select',
            key: 'billing_method1',
            placeholder: '全部',
            options: [],
          },
          {
            label: '状态',
            type: 'select',
            key: 'billing_method',
            placeholder: '全部',
            options: [],
          },
        ],

        tableColumn: [
          {
            label: '时间',
            prop: 'name',
            width: '150px',
          },
          {
            label: '订单号',
            prop: 'count',
          },
          {
            label: '类型',
            prop: 'spec_list',
            type: 'customize',
          },
          {
            label: '金额',
            prop: 'spec_list',
            type: 'customize',
          },
          {
            label: '状态',
            prop: 'spec_list',
            type: 'customize',
          },
          {
            label: '备注',
            prop: 'spec_list',
            type: 'customize',
          },
        ],
        loading: true,
        tableForm: {
          billing_method1: '',
          billing_method: '',
        },
        fundInfoList: {},
        tonglian_account: {},
        wechat_account: {},
        codeInfo: [
          { name: '可提现余额', code: 'approve_withdrawal_amount', tip: '当前可以提现到结算账户的金额，不包含待结算和冻结金额' },
          { name: '累计提现金额', code: 'summation_withdrawal_amount', tip: '历史累计已提现到结算账户的总金额' },
          { name: '待结算收益', code: 'pending_settlement_amount', tip: '交易完成但尚未结算的收益，通常在T+1日结算到可提现余额' },
          { name: '冻结收益', code: 'freeze_amount', tip: '因交易争议、风控等原因暂时冻结的资金，解除冻结后将转入可提现余额' },
          {
            name: '预消费待结算金额',
            code: 'consumption_amount',
            tip: '预消费场景下已收取但尚未完成服务的交易金额，服务完成后转入待结算收益',
          },
        ],
        codeInfo1: [
          { name: '可提现金额', code: 'approve_withdrawal_amount', tip: '当前可以提现到结算账户的金额，不包含待结算和冻结金额' },
          { name: '累计提现金额', code: 'summation_withdrawal_amount', tip: '历史累计已提现到结算账户的总金额' },
          { name: '待结算收益', code: 'pending_settlement_amount', tip: '交易完成但尚未结算的收益，通常在T+1日结算到可提现余额' },
          { name: '冻结收益', code: 'freeze_amount', tip: '因交易争议、风控等原因暂时冻结的资金，解除冻结后将转入可提现余额' },
          // { name: '预消费待结算金额', code: 'consumption_amount' },
        ],
      }
    },
    mounted() {
      fundInfo().then((res) => {
        if ((res.code = 200)) {
          this.fundInfoList = res.data
          this.tonglian_account = this.fundInfoList.tonglian_account
          this.wechat_account = this.fundInfoList.wechat_account
        }
        console.log(res.data)
      })
    },
    methods: {
      reback() {
        this.showDetail = false
      },
      // 表单搜索事件
      searchForm(form) {
        this.tableForm = Object.assign(this.tableForm, form)
      },
      lookDetail(data, item) {
        this.showDetail = true
        this.title = item.name
        console.log(data, item)
      },
    },
  }
</script>
<style lang="scss" scoped>
  .topSty {
    display: flex;
    align-items: center;
    .btn {
      margin-right: 20px;
    }
  }
  .transaction {
    &-tips {
      font-weight: 400;
      font-size: 14px;
      color: #8d8d8d;
      > span {
        margin-right: 5px;
      }
    }
    &-num {
      font-weight: 600;
      font-size: 25px;
      color: rgba(0, 0, 0, 0.85);
      margin-top: 8px;
      > span + span {
        font-weight: 400;
        font-size: 14px;
        color: rgba(141, 141, 141, 0.85);
        margin-left: 2px;
      }
    }
  }
  .contentpay {
    display: flex;
  }
  .infomation {
    .transaction-item {
      padding: 20px 0;
    }
  }
</style>
