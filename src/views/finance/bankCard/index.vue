<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-04-19 11:18:05
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-05 14:09:55
 * @FilePath: \qst-merchant-admin-2.0\src\views\finance\bankCard\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <el-card>
      <base-form :formArray="formArray" :tableForm="tableForm" @searchForm="searchForm"></base-form>
      <div class="operate">
        <el-button type="primary" icon="el-icon-plus" @click="addStore">添加银行卡</el-button>
      </div>
      <div class="n_tips">
        <img class="tip_icon" :src="imgOssPath + '20250517_shuoming.png'" alt />
        <div>
          <div class="tip_title">
            微信支付：仅支持绑定1张银行卡，切换是否默认会进行换绑，微信侧每天最多换绑5次
          </div>
          <div class="tip_title">通联支付：企业认证最多支持绑定10个对公账户和1个法人个人银行卡</div>
        </div>
      </div>

      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="bank_name" label="银行名称"></el-table-column>
        <el-table-column prop="company_name" label="所属企业"></el-table-column>
        <el-table-column prop="channel" label="支付渠道"></el-table-column>
        <el-table-column prop="legal_name" label="开户名"></el-table-column>
        <el-table-column prop="bank_card_no" label="银行卡号">
          <template slot-scope="scope">
            <div @click="showPhoneContent(scope.row, 'bank_card_no')">
              {{ scope.row.bank_card_no }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="member_type" label="账户类型">
          <template slot-scope="scope">
            <div>{{ scope.row.bank_type == 2 ? '企业认证' : '个人认证' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="预留手机号">
          <template slot-scope="scope">
            <div @click="showPhoneContent(scope.row, 'phone')">{{ scope.row.phone }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="is_default" label="状态">
          <template slot-scope="scope">
            <div class="flex">
              <el-switch
                :disabled="scope.row.is_default == 'Y'"
                @change="changeStatus(scope)"
                active-value="Y"
                inactive-value="N"
                v-model="scope.row['is_default']"
              ></el-switch>
              <span>{{ scope.row.is_default == 'Y' ? '已默认' : '设置默认' }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <div class="public-operate-btn">
              <el-button
                v-if="scope.row.channel == '通联支付' && scope.row.bank_type != 2"
                :disabled="scope.row.is_default == 'Y'"
                size="mini"
                type="text"
                @click="handelReuse(scope.row)"
              >
                解绑
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!--  解绑银行卡 -->
    <el-dialog
      title="解绑银行卡"
      destroy-on-close
      :visible.sync="reuseBankCard"
      :close-on-click-modal="false"
      :before-close="cancel"
    >
      <el-form :rules="formInfoAddRule" :model="formInfoAdd" ref="formInfoReuse">
        <el-form-item label="预留手机号" prop="phone">
          <el-input v-model="formInfoAdd.phone" disabled placeholder="请输入预留手机号"></el-input>
        </el-form-item>
        <el-form-item label="验证码" prop="verification_code">
          <el-input
            v-model.trim="formInfoAdd.verification_code"
            placeholder="请输入验证码"
            :maxlength="6"
            v-numeric
          >
            <el-button slot="append" v-if="verifyTimeOut">{{ verifyTimeOut }}</el-button>
            <el-button
              slot="append"
              class="uploadImagesDiv1"
              v-else
              @click="getVerificationCode('verify')"
            >
              获取验证码
            </el-button>
          </el-input>
        </el-form-item>
        <base-dialog-footer @cancel="cancel" @confirm="confirmReuse"></base-dialog-footer>
      </el-form>
    </el-dialog>
    <!-- 添加银行卡 -->
    <el-dialog
      title="添加银行卡"
      destroy-on-close
      :visible.sync="addAndReuseBankCard"
      :close-on-click-modal="false"
      :before-close="cancel"
    >
      <el-form :rules="formInfoAddRule" :model="formInfoAdd" ref="formInfoAdd" label-position="top">
        <el-form-item label="支付渠道" prop="type">
          <el-radio-group
            disabled
            v-model="formInfoAdd.type"
            @change="changePay(formInfoAdd.type, 'type')"
          >
            <el-radio :label="1">微信支付</el-radio>
            <el-radio :label="2">通联支付</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="账户类型" prop="bank_type">
          <el-radio-group
            :disabled="bank_typeFlag"
            v-model="formInfoAdd.bank_type"
            @change="changePay(formInfoAdd.bank_type, 'bank_type')"
          >
            <el-radio :label="1">法人个人银行卡</el-radio>
            <el-radio :label="2">企业对公账户</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 支付渠道==通联支付&&账户类型==法人个人银行卡 -->
        <div v-if="formInfoAdd.type == 2 && formInfoAdd.bank_type == 1">
          <el-form-item label="开户名" prop="name">
            <el-input disabled v-model="formInfoAdd.name" placeholder="请输入开户名"></el-input>
          </el-form-item>
          <el-form-item label="银行卡号" prop="card_no">
            <el-input v-model="formInfoAdd.card_no" placeholder="请输入银行卡号"></el-input>
          </el-form-item>

          <el-form-item label="预留手机号" prop="phone">
            <el-input v-model="formInfoAdd.phone" placeholder="请输入预留手机号"></el-input>
          </el-form-item>
          <el-form-item label="验证码" prop="verification_code">
            <el-input
              v-model.trim="formInfoAdd.verification_code"
              placeholder="请输入验证码"
              :maxlength="6"
              v-numeric
            >
              <el-button slot="append" v-if="verifyTimeOut">{{ verifyTimeOut }}</el-button>
              <el-button
                slot="append"
                class="uploadImagesDiv1"
                v-else
                @click="getVerificationCode('verify')"
              >
                获取验证码
              </el-button>
            </el-input>
          </el-form-item>
        </div>
        <!-- 支付渠道==通联支付&&账户类型==企业对公账户  || 支付渠道==微信支付 -->
        <div v-if="(formInfoAdd.type == 2 && formInfoAdd.bank_type == 2) || formInfoAdd.type == 1">
          <el-form-item label="开户名" prop="name" v-if="formInfoAdd.type != 1">
            <el-input disabled v-model="formInfoAdd.name" placeholder="请输入开户名"></el-input>
          </el-form-item>
          <el-form-item label="开户银行" prop="parent_bank_name">
            <el-select
              v-model="formInfoAdd.parent_bank_name"
              filterable
              remote
              reserve-keyword
              placeholder="请输入开户银行名称"
              :remote-method="remoteMethod"
              style="width: 100%"
            >
              <el-option
                v-for="item in BankList"
                :key="item.code"
                :label="item.bank_name"
                :value="item.bank_name"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="开户支行" prop="bank_name">
            <el-select
              v-model="formInfoAdd.bank_name"
              filterable
              remote
              reserve-keyword
              placeholder="请输入开户支行名称"
              :remote-method="remoteMethod1"
              @change="getsubbankchange"
              style="width: 100%"
            >
              <el-option
                v-for="item in BankBranchList"
                :key="item.union_bank"
                :label="item.bank_name"
                :value="item.bank_name"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="企业对公账户账号" prop="account_no">
            <el-input v-model="formInfoAdd.account_no" placeholder="请输入企业对公账号"></el-input>
          </el-form-item>
        </div>
        <base-dialog-footer @cancel="cancel" @confirm="confirmAdd"></base-dialog-footer>
      </el-form>
    </el-dialog>
    <el-dialog :title="showTitle" :visible.sync="dialogVisible" :close-on-click-modal="false">
      <div class="view-phone">{{ modelPhone }}</div>
    </el-dialog>
  </div>
</template>

<script>
  import { timeOut } from '@/utils/public'
  import { getCompanyInfoApi } from '@/api/enterpriseManagement'
  import { getCompanyAuthInfo, getBankBranchList, getCompanyBankList } from '@/api/finance/fund.js'
  import {
    getBankCardList,
    applyBindPersonalBankCard,
    bindBankCard,
    bindCompanyBankCard,
    unbindBankCard,
    defaultBankCard,
    getRealBankCardNo,
    getRealBankPhone,
  } from '@/api/finance/bankCard.js'
  import { getmaparrList } from '@/utils/index'

  export default {
    name: 'storeManagement',
    components: {},
    data() {
      return {
        tableData: [],
        showTitle: '',
        modelPhone: '',

        dialogVisible: false,
        addAndReuseBankCard: false,
        reuseBankCard: false,
        verifyTimeOut: null,
        timeOutID: null,
        timeOut: '',
        formInfoAdd: {
          type: 2,
          bank_type: '',
          name: '',
          card_no: '',
          phone: '',
          verification_code: '',
          parent_bank_name: '',
          bank_name: '',
          account_no: '',
          union_bank: '',
        },
        bankList: [],
        bankList1: [],
        formInfoAddRule: {
          type: [{ required: true, message: '请选择支付渠道', trigger: 'blur' }],
          bank_type: [{ required: true, message: '请选择账户类型', trigger: 'blur' }],
          name: [{ required: true, message: '请输入开户名', trigger: 'blur' }],
          card_no: [
            { required: true, message: '请输入银行卡号', trigger: 'blur' },
            // { pattern: /^[0-9]{9,18}$/, message: '银行卡号格式不正确', trigger: 'blur' },
          ],
          phone: [
            { required: true, message: '请输入手机号', trigger: 'blur' },
            { pattern: /^1[3456789]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' },
          ],
          verification_code: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
          parent_bank_name: [{ required: true, message: '请选择开户银行', trigger: 'blur' }],
          bank_name: [{ required: true, message: '请选择开户支行', trigger: 'blur' }],
          account_no: [{ required: true, message: '请输入对公账户', trigger: 'blur' }],
        },
        formArray: [
          {
            label: '银行名称',
            type: 'input',
            key: 'bank_name',
            placeholder: '请输入银行名称',
          },
          {
            label: '账户类型',
            type: 'select',
            key: 'bank_type',
            placeholder: '全部',
            options: [
              {
                label: '法人个人银行卡',
                value: '1',
              },
              {
                label: '企业对公银行卡',
                value: '2',
              },
            ],
          },
          {
            label: '支付渠道',
            type: 'select',
            key: 'mch_store_uid',
            placeholder: '全部',
            options: [
              // { label: '微信支付', value: 1 },
              { label: '通联支付', value: 2 },
            ],
          },
        ],
        tableForm: {
          biz_user_id: '',
          bank_name: '', //银行名
          bank_type: '', //类型（1.法人个人银行卡 2.企业对公银行卡）
        },
        clickRow: '',
        companyInfo: {},
        companyAuthInfo: {},
        loading: true,
        biz_user_id: '',
        BankList: [],
        BankBranchList: [],
        bank_typeFlag: false, //企业可选择法人个人银行卡及对公账户  个人时，仅可添加法人个人银行卡
      }
    },
    created() {
      this.getCompanyInfo()
    },
    methods: {
      //得到公司信息
      getCompanyInfo() {
        getCompanyInfoApi().then((res) => {
          if (res.code == 200) {
            this.companyInfo = res.data
            this.getCompanyAuthInfoFn()
          }
        })
      },
      getCompanyAuthInfoFn() {
        getCompanyAuthInfo({ company_uid: this.companyInfo.company_uid }).then((res) => {
          if (res.code == 200) {
            if (res.data?.msg == '无认证记录') {
              this.showAuthInfo(res.data?.msg)
              return
            }
            this.biz_user_id = res.data.biz_user_id //'397e3428-2d86-11f0-8a14-0242e6515671' //
            this.companyAuthInfo = res.data
            this.tableForm = { biz_user_id: this.biz_user_id, bank_name: '', bank_typeL: '' }
            this.getBankCardListFn()
          }
          console.log(res.data)
        })
      },
      //未开通支付渠道
      showAuthInfo(value) {
        this.$confirm(`${value},请先开通后再添加银行卡`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          showCancelButton: false,
          showClose: false,
          type: 'warning',
        }).then(() => {
          this.$router.push('/fund/index')
        })
      },
      getBankCardListFn() {
        getBankCardList(this.tableForm).then((res) => {
          if (res.code == 200) {
            this.tableData = res.data
          }
        })
      },
      // 表单搜索事件
      searchForm(form, isResetFields) {
        if (isResetFields) {
          form.biz_user_id = this.biz_user_id
          this.tableForm = Object.assign({}, this.tableForm, form)
        } else {
          this.tableForm = Object.assign({}, this.tableForm, form)
          this.getBankCardListFn()
        }
      },
      // 新增银行卡
      addStore() {
        this.formInfoAdd.type = 2

        let personList = getmaparrList(this.tableData, 'bank_type', '1')

        this.formInfoAdd.bank_type = this.companyAuthInfo.member_type == 2 ? 2 : 1
        if (this.formInfoAdd.bank_type == 1) {
          this.bank_typeFlag = true
          this.formInfoAdd.name = this.companyInfo.legal_name
        }
        if (this.formInfoAdd.bank_type == 2) {
          this.bank_typeFlag = false
          this.formInfoAdd.name = this.companyInfo.organization_name
        }
        if (this.formInfoAdd.bank_type == 2 && personList.length > 0) {
          this.bank_typeFlag = true
          this.formInfoAdd.name = this.companyInfo.organization_name
        }
        this.addAndReuseBankCard = true
      },
      // 解绑银行卡
      handelReuse(row) {
        this.$confirm(`确定要解绑该银行卡吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            let params = {
              biz_user_id: this.biz_user_id,
              // card_no: this.modelPhone, //卡号,
              id: row.id, //银行卡列表id
            }
            console.log(params)
            unbindBankCard(params).then((res) => {
              if (res.data.status) {
                this.$message({
                  type: 'success',
                  message: '解绑成功！',
                })
                this.getBankCardListFn()
              } else {
                this.$message({
                  type: 'error',
                  message: res.data.msg,
                })
              }
            })
          })
          .catch((action) => {})
      },
      //切换模版状态
      changeStatus(scope) {
        let statusTip = scope.row.is_default == 'Y' ? '设置默认' : '取消默认'
        this.$confirm(`是否${statusTip}银行卡?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            defaultBankCard({
              biz_user_id: this.biz_user_id,
              // bank_card_no: this.modelPhone,
              id: scope.row.id, //银行卡列表id
              is_default: scope.row.is_default,
            }).then((res) => {
              if (res.data.status) {
                this.$message.success(`${statusTip}银行卡成功`)
                this.getBankCardListFn()
              } else {
                this.$message.error(res.data.msg)
                this.getBankCardListFn()
              }
            })
          })
          .catch((action) => {})
      },

      // 获取验证码
      getVerificationCode(type) {
        applyBindPersonalBankCard({
          biz_user_id: this.biz_user_id,
          card_no: this.formInfoAdd.card_no, //卡号,
          name: this.companyInfo.legal_name, //姓名,
          phone: this.formInfoAdd.phone, //手机号,
          identity_no: this.companyInfo.legal_code, //身份证号,
          cvv2: '', //cvv2【可为空】
          validate: '', //有效期【可为空】,
        }).then((res) => {
          if (res.code == 200) {
            if (res.data.status) {
              this.$message({
                type: 'success',
                message: '验证码已发送，请注意查收！',
              })
              this.timeOutID = new timeOut(60)
              this.timeOutID.start((e) => {
                if (e.totalSeconds == 0) {
                  this[type + 'TimeOut'] = null
                } else {
                  this[type + 'TimeOut'] = e.totalSeconds + 's'
                }
              })
            } else {
              this.$message({
                type: 'error',
                message: res.data.msg,
              })
            }
          }
        })
      },
      showPhoneContent(data, code, flag = true) {
        let getuseApi = ''
        if (code == 'phone') {
          this.showTitle = '手机号'
          getuseApi = getRealBankPhone
        }
        if (code == 'bank_card_no') {
          this.showTitle = '银行卡号'
          getuseApi = getRealBankCardNo
        }
        let params = {
          id: data.id,
          biz_user_id: this.biz_user_id,
        }
        getuseApi(params).then((res) => {
          if (res.code === 200) {
            this.dialogVisible = flag
            this.modelPhone = res.data.data
          }
        })
      },
      // 解绑银行卡
      confirmReuse() {
        this.$refs.formInfoReuse.validate((valid) => {
          if (valid) {
            let params = {
              biz_user_id: this.biz_user_id,
              card_no: this.clickRow.bank_card_no, //卡号,
            }
            console.log(params)
            unbindBankCard(params).then((res) => {
              if (res.code == 200) {
                this.$message({
                  type: 'success',
                  message: '解绑成功！',
                })
                this.cancel()
              }
            })
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
      //  绑定银行卡
      addBankCard() {
        let params = {
          biz_user_id: this.biz_user_id,
          account_no: this.formInfoAdd.card_no, //卡号,
          phone: this.formInfoAdd.phone, //手机号,
          verification_code: this.formInfoAdd.verification_code, //验证码
        }
        console.log(params)
        bindBankCard(params).then((res) => {
          if (res.data.status) {
            this.$message({
              type: 'success',
              message: ' 绑定成功！',
            })
            this.cancel()
            this.getBankCardListFn()
          } else {
            this.$message({
              type: 'error',
              message: res.data.msg,
            })
          }
        })
      },
      //绑定对公户
      addBankComCard() {
        let params = {
          ...this.formInfoAdd,
          biz_user_id: this.biz_user_id,
        }
        console.log(params)
        bindCompanyBankCard(params).then((res) => {
          if (res.data.status) {
            this.$message({
              type: 'success',
              message: ' 绑定成功！',
            })
            this.cancel()
            this.getBankCardListFn()
          } else {
            this.$message({
              type: 'error',
              message: res.data.msg,
            })
          }
        })
      },
      // 根据keyword查询公户（公司开户银行）列表
      remoteMethod(query) {
        console.log(query)
        if (query !== '') {
          this.loading = true
          getCompanyBankList({ keyword: query }).then((res) => {
            if (res.code == 200) {
              this.loading = false
              this.BankList = res.data.content
            }
          })
        }
      },
      // 根据keyword查询开户行支行列表
      remoteMethod1(query) {
        if (query !== '') {
          this.loading = true
          getBankBranchList({ keyword: query }).then((res) => {
            if (res.code == 200) {
              this.loading = false
              this.BankBranchList = res.data.content
            }
          })
        }
      },
      getsubbankchange(value) {
        getBankBranchList({ keyword: value }).then((res) => {
          if (res.code == 200) {
            this.formInfoAdd.union_bank = res.data.content[0].union_bank
          }
        })
      },
      //确认按钮
      confirmAdd() {
        this.$refs.formInfoAdd.validate((valid) => {
          if (valid) {
            if (this.formInfoAdd.type == 2 && this.formInfoAdd.bank_type == 1) {
              this.addBankCard() //绑定个人
            }
            if (this.formInfoAdd.type == 2 && this.formInfoAdd.bank_type == 2) {
              this.addBankComCard()
            }
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
      changePay(value, type) {
        this.formInfoAdd[type] = value
        for (let key in this.formInfoAdd) {
          if (key != 'type' && key != 'bank_type') {
            this.formInfoAdd[key] = ''
          }
        }
        if (this.formInfoAdd.bank_type == 1) {
          this.formInfoAdd.name = this.companyInfo.legal_name
        }
        if (this.formInfoAdd.bank_type == 2) {
          this.formInfoAdd.name = this.companyInfo.organization_name
        }
      },

      cancel() {
        for (let key in this.formInfoAdd) {
          this.formInfoAdd[key] = ''
        }
        this.addAndReuseBankCard = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  .operate {
    margin-top: 20px;
  }

  .n_tips {
    display: flex;
    align-items: flex-start;
    padding: 18px 20px;
    background: #eef6ff;
    border-radius: 8px;
    border: 1px solid #94c3ff;
    margin: 15px 0;
    .tip_icon {
      display: block;
      width: 24px;
      height: 24px;
      margin-right: 10px;
    }
    .tip_title {
      margin-bottom: 10px;
      font-size: 15px;
      color: #222222;
      line-height: 15px;
      font-weight: 600;
    }
    .tip_name {
      font-size: 14px;
      color: #666666;
      line-height: 20px;
      margin-top: 8px;
    }
  }
  .uploadImagesDiv1 {
    color: #fff !important;
    background: #0071fe !important;
    border-radius: 0px 4px 4px 0px !important;
  }

  .n_tips {
    display: flex;
    align-items: flex-start;
    padding: 18px 20px;
    background: #eef6ff;
    border-radius: 8px;
    border: 1px solid #94c3ff;
    margin: 15px 0;
    .tip_icon {
      display: block;
      width: 24px;
      height: 24px;
      margin-right: 10px;
    }
    .tip_title {
      margin-bottom: 10px;
      font-size: 15px;
      color: #222222;
      line-height: 15px;
      font-weight: 600;
    }
    .tip_name {
      font-size: 14px;
      color: #666666;
      line-height: 20px;
      margin-top: 8px;
    }
  }
  .uploadImagesDiv1 {
    color: #fff !important;
    background: #0071fe !important;
    border-radius: 0px 4px 4px 0px !important;
  }
  .view-phone {
    text-align: center;
  }
</style>
