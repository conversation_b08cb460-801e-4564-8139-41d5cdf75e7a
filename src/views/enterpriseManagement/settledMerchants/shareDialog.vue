<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-03-22 15:00:49
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-26 14:57:06
 * @FilePath: /qst-merchant-admin-2.0/src/views/enterpriseManagement/merchantManagement/shareDialog.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <!-- 邀请入驻商家 -->
  <el-dialog :show-close="false" :visible.sync="isShare" :close-on-click-modal="false">
    <el-form :rules="formDataRule" :model="formData" ref="formData">
      <el-form-item label="商家类型：" prop="type">
        <div class="flex">
          <el-select class="share_select" v-model="formData.type" placeholder="请选商家类型">
            <el-option
              v-for="item in typeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <el-button @click="qrcodeFn" type="text">生成邀请二维码</el-button>
        </div>
      </el-form-item>
    </el-form>
    <div class="share" v-if="shareCode">
      <el-image :src="shareCode" class="cord-image"></el-image>
      <div>请将以上二维码转发给商家进行入驻</div>
    </div>
    <base-dialog-footer :isCancel="false" :confirmText="'关闭'" @confirm="isShare=false"></base-dialog-footer>
  </el-dialog>
</template>

<script>
import { qrcodeApi } from '@/api/common'
export default {
  name: 'ShareDialog',
  props: {
    typeList: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      formDataRule: {
        type: [{ required: true, message: '请选择商家类型', trigger: 'change' }],
      },
      formData: {
        type: '',
      },
      isShare: false,
      shareCode: '',
      dialogVisible: false,
      loading: true,
    }
  },
  watch: {
    'formData.type': function () {
      this.shareCode = ''
    },
  },
  methods: {
    open() {
      this.isShare = true
      this.loading = true
    },
    qrcodeFn() {
      let _this = this
      this.$refs.formData.validate((valid) => {
        if (valid) {
          qrcodeApi({
            scene: 'join_store',
            page: 'pages/registerPage/registerMerchant',
            is_new: '1',
            share_id: localStorage.getItem('currentMchUid') || '',
            type: _this.formData.type,
          }).then((res) => {
            _this.loading = false
            if (res.code == 200) {
              _this.shareCode = res.data.url
            }
          })
        } else {
          return false
        }
      })
    },

    confirm() {
      this.$emit('update:isShare', false)
    },
  },
}
</script>

<style lang="scss" scoped>
// 邀请
.share {
  text-align: center;
  font-weight: 400;
  font-size: 14px;
  color: #32363a;
  line-height: 14px;
  > * {
    margin-top: 16px;
  }
  &-title {
    font-size: 18px;
    margin-bottom: 18px;
  }
}
.cord-image {
  width: 200px;
  height: 200px;
}
.share_select {
  flex: 1;
  margin-right: 20px;
}
::v-deep .el-dialog__header {
  padding: 0;
}
</style>