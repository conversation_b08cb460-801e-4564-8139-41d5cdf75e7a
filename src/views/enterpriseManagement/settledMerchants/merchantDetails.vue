<!--
 * @FilePath: /qst-merchant-admin-2.0/src/views/enterpriseManagement/informationManagement/index.vue
 * @Description: 商家详情
-->
<template>
  <div class="information-bg">
    <el-card>
      <template #header>
        <div class="card-header">
          <div></div>
          <div>基础商家信息</div>
        </div>
      </template>
      <el-form label-width="100px">
        <el-row :gutter="20">
          <!-- <el-col :lg="8" :md="8" :sm="12" :xl="12" :xs="24">
            <el-form-item label="企业ID">
              <el-input v-model="info.basicInfo.id" type="text" disabled></el-input>
            </el-form-item>
          </el-col>-->

          <el-col :lg="8" :md="8" :sm="12" :xl="12" :xs="24">
            <el-form-item label="企业名称">
              <el-input type="text" v-model="info.basicInfo.name" disabled></el-input>
            </el-form-item>
          </el-col>

          <el-col :lg="8" :md="8" :sm="12" :xl="12" :xs="24">
            <el-form-item label="所属行业">
              <el-input type="text" v-model="info.basicInfo.tradeName" disabled></el-input>
            </el-form-item>
          </el-col>

          <el-col :lg="8" :md="8" :sm="12" :xl="12" :xs="24">
            <el-form-item label="负责人">
              <el-input type="text" v-model="info.basicInfo.manage_name" disabled></el-input>
            </el-form-item>
          </el-col>

          <el-col :lg="8" :md="8" :sm="12" :xl="12" :xs="24">
            <el-form-item label="联系电话">
              <el-input type="text" v-model="info.basicInfo.contact_phone" disabled></el-input>
            </el-form-item>
          </el-col>

          <!-- <el-col :lg="8" :md="8" :sm="12" :xl="12" :xs="24">
            <el-form-item label="企商通账号">
              <el-input type="text" v-model="info.basicInfo.username" disabled></el-input>
            </el-form-item>
          </el-col>-->
        </el-row>
      </el-form>
    </el-card>

    <el-card>
      <template #header>
        <div class="card-header">
          <div></div>
          <div>企业认证信息</div>
        </div>
      </template>
      <el-form label-width="180px">
        <el-form-item label="营业执照">
          <el-image
            class="license_url"
            :src="info.authenticationInfo.license"
            :preview-src-list="[info.authenticationInfo.license]"
          ></el-image>
        </el-form-item>
        <el-form-item label="企业名称">
          <el-input
            v-model="info.authenticationInfo.organization_name"
            type="text"
            disabled
            placeholder="请输入企业名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="企业类型">
          <el-input type="text" disabled v-model="info.authenticationInfo.organization_type"></el-input>
        </el-form-item>
        <el-form-item label="统一社会信用代码">
          <el-input
            v-model="info.authenticationInfo.organization_code"
            type="text"
            disabled
            placeholder="请输入统一社会信用代码"
          ></el-input>
        </el-form-item>
        <el-form-item label="注册地址">
          <el-input
            v-model="info.authenticationInfo.address"
            type="text"
            disabled
            placeholder="请输入注册地址"
          ></el-input>
        </el-form-item>
        <el-form-item label="法人身份证">
          <div class="id-photo">
            <el-image class="id-photo-image" :src="cardList[0]" :preview-src-list="cardList"></el-image>
            <el-image class="id-photo-image" :src="cardList[1]" :preview-src-list="cardList"></el-image>
          </div>
        </el-form-item>
        <el-form-item label="法人姓名">
          <el-input
            type="text"
            disabled
            v-model="info.authenticationInfo.legal_name"
            placeholder="请输入营业执照法人姓名"
          ></el-input>
        </el-form-item>
        <el-form-item label="法人身份证">
          <el-input
            type="text"
            disabled
            v-model="info.authenticationInfo.legal_code"
            placeholder="请输入营业执照法人姓名"
          ></el-input>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="log-main">
      <template #header>
        <div class="card-header">
          <div></div>
          <div>日志</div>
        </div>
      </template>
      <div>
        <div class="log-item" v-for="item in info.logInfo" :key="item.id">
          <el-image class="log-item-header" :src="item.logo"></el-image>
          <div>
            <span>{{item.username}}</span>
            于{{item.created_at}} {{item.status}}
            <span
              style="color: #666666;"
              v-if="item.remark"
            >，备注信息：{{item.remark}}</span>
          </div>
        </div>
      </div>
    </el-card>

    <el-button class="foot-btn" @click="backFn" type="primary">返回</el-button>
  </div>
</template>

<script>
import { enterShopDetailApi } from '@/api/enterpriseManagement'
export default {
  name: 'InformationManagement',
  components: {},
  data() {
    return {
      imageUrl: '',
      licenseUrl: '',
      info: {
        basicInfo: {},
        authenticationInfo: {},
        logInfo: [],
      },
      cardList: [],
    }
  },
  methods: {
    backFn() {
      this.$router.back()
    },
    getShopDetail() {
      enterShopDetailApi({
        storeUid: this.$route.query.storeUid,
      }).then((res) => {
        this.cardList = [res.data.authenticationInfo.legal_card_front, res.data.authenticationInfo.legal_card_back]
        this.info = res.data
      })
    },
  },
  mounted() {
    this.getShopDetail()
  },
}
</script>

<style lang="scss" scoped>
.information-bg {
  background: #f6f8f9;
}
.foot-btn {
  position: fixed;
  bottom: 50px;
  right: calc(#{$base-right-content-width} / 2);
  transform: translateX(50%);
}

.id-photo {
  display: flex;
  &-image {
    width: 100px;
    height: 100px;
    display: block;
    margin-right: 24px;
  }
}
.log-main {
  padding-bottom: 150px;
}
.log-item {
  display: flex;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  &-header {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    border-radius: 50%;
  }
  span {
    color: $base-color-default;
  }
  & + & {
    margin-top: 16px;
  }
}
.log-item-header {
  flex-shrink: 0;
}
::v-deep {
  .el-input__inner {
    max-width: 400px;
  }

  .license_url {
    width: 360px;
    height: 180px;
    img {
      object-fit: contain;
    }
  }
}

::v-deep {
  .el-input.is-disabled .el-input__inner {
  }
}
</style>