<template>
  <div>
    <el-dialog title="新建入驻商家" :visible.sync="isAddAccount" :close-on-click-modal="false">
      <el-form :rules="formDataRule" :model="formData" ref="formData" label-width="150px">
        <el-form-item label="企业名称" prop="merchantName">
          <el-input v-model="formData.merchantName" placeholder="请输入企业名称"></el-input>
        </el-form-item>
        <el-form-item label="负责人" prop="manageName">
          <el-input maxlength="10" v-model="formData.manageName" placeholder="请输入负责人"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="formData.phone" maxlength="11" placeholder="请输入手机号"></el-input>
        </el-form-item>

        <el-form-item label="商家类型" prop="type">
          <el-select
            style="width: 260px;"
            v-model="formData.type"
            @change="changeType"
            placeholder="请选商家类型"
          >
            <el-option
              v-for="item in typeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="权益项" prop="privilege">
          <el-select
            style="width: 260px;"
            v-model="formData.privilege"
            filterable
            placeholder="请选择权益项"
            multiple
            v-loading="isLoading"
          >
            <el-option
              v-for="item in privilegeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <base-dialog-footer confirmText="下一步" @confirm="confirm" @cancel="isAddAccount = false"></base-dialog-footer>
    </el-dialog>

    <el-dialog title="新建入驻商家" :visible.sync="isMerchant" :close-on-click-modal="false">
      <!-- 不存在相同账号 -->
      <div v-if="!isAccount">
        <div class="error-dialog-tip">
          <i class="el-icon-warning"></i>
          检测到该手机号尚未注册过企商通系统，请填写登录账号及密码注册
        </div>
        <el-form
          :rules="merchantFormRule"
          :model="merchantForm"
          ref="merchantForm"
          label-width="150px"
        >
          <el-form-item label="登录账号" prop="username">
            <el-input maxlength="50" v-model="merchantForm.username" placeholder="请输入登录账号"></el-input>
          </el-form-item>
          <el-form-item label="初始密码" prop="password">
            <base-input
              maxlength="50"
              autocomplete="new-password"
              type="password"
              v-model="merchantForm.password"
              placeholder="请输入初始密码"
            ></base-input>
          </el-form-item>
        </el-form>
      </div>

      <!-- 有重复账号 -->
      <div v-if="isAccount">
        <div class="error-dialog-tip">
          <i class="el-icon-warning"></i>
          检测到本公司该手机号已注册过企商通系统，可直接使用！
        </div>
        <el-form
          :rules="merchantFormRule"
          :model="merchantForm"
          ref="merchantForm"
          label-width="150px"
        >
          <el-form-item label="登录账号">
            <el-input v-model="merchantForm.username" placeholder="请输入登录账号" disabled></el-input>
          </el-form-item>
        </el-form>
        <div class="message-dialog-tip">不想用这个账号？ 点击上一步更换手机号注册</div>
      </div>

      <base-dialog-footer
        :cancelText="'上一步'"
        @confirm="merchantConfirm"
        @cancel=";(isMerchant = false), (isAddAccount = true)"
      ></base-dialog-footer>
    </el-dialog>
  </div>
</template>

<script>
import {
  enterShopCreateFirstApi,
  enterShopCreateSecondApi,
  checkBeforeSubmitApi,
  enterShopSelectListApi,
} from '@/api/enterpriseManagement'

export default {
  name: 'AddAccountDialog',
  props: {
    typeList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      // 新增账号
      isAddAccount: false,
      formDataRule: {
        merchantName: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
        manageName: [{ required: true, message: '请输入负责人', trigger: 'blur' }],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },
        ],
        type: [{ required: true, message: '请选择商家类型', trigger: 'change' }],
        privilege: [{ required: true, message: '请选择权益项', trigger: 'change' }],
      },
      formData: {
        merchantName: '',
        manageName: '',
        phone: '',
        type: '',
        privilege: [],
      },

      // 入驻商家
      merchantForm: {
        username: '',
        password: '',
      },
      merchantFormRule: {
        username: [
          { required: true, message: '请输入登录账号', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (!value) return callback(new Error('请输入登录账号'))
              checkBeforeSubmitApi({ username: value }).then((res) => {
                if (res.code == 200) {
                  callback()
                } else {
                  callback(new Error('该账号已存在，请重新输入！'))
                }
              })
            },
            trigger: 'blur',
          },
        ],
        password: [
          { required: true, message: '请输入初始密码', trigger: 'blur' },
          {
            pattern: /^(?=.*[a-z_])(?=.*\d)(?=.*[^a-z0-9_])[\S]{8,32}$/,
            message: '初始密码必须为8～32位字母+数字+特殊字符的组合',
          },
        ],
      },
      isAccount: false,
      isMerchant: false,
      mchAdminUid: '',
      privilegeList: [],

      isLoading: false,
    }
  },
  methods: {
    open() {
      this.isAddAccount = true
      this.changeType()
      this.formData = Object.assign({}, this.formData)
      this.$nextTick(() => {
        this.$refs['formData'].resetFields()
      })
    },

    // 切换商家类型
    changeType() {
      this.isLoading = true
      enterShopSelectListApi({ type: this.formData.type }).then((res) => {
        if (res.code == 200) {
          this.privilegeList = res.data.storePrivilege
          if (this.formData.type) {
            this.formData.privilege = this.privilegeList.map((item) => item.value)
          }
        }
        setTimeout(() => {
          this.isLoading = false
        }, 500)
      })
    },

    confirm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          enterShopCreateFirstApi({
            merchantName: this.formData.merchantName,
            manageName: this.formData.manageName,
            phone: this.formData.phone,
            type: this.formData.type,
            privilege: this.formData.privilege,
          }).then((res) => {
            if (res.code == 200) {
              if (res.data.username) {
                this.merchantForm.username = res.data.username
                this.isAccount = true
                this.mchAdminUid = res.data.mchAdminUid
              } else {
                this.merchantForm.password = ''
                this.merchantForm.username = ''
                this.isAccount = false
              }

              this.isMerchant = true
              this.$nextTick(() => {
                this.$refs['merchantForm'].resetFields()
              })
              this.isAddAccount = false
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 商家账号确认按钮事件
    merchantConfirm() {
      enterShopCreateSecondApi({
        merchantName: this.formData.merchantName,
        manageName: this.formData.manageName,
        phone: this.formData.phone,
        username: this.merchantForm.username,
        password: this.merchantForm.password,
        mchAdminUid: this.mchAdminUid,
        type: this.formData.type,
        privilege: this.formData.privilege,
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success('创建成功')
          this.isMerchant = false
          this.$emit('update')
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.error-dialog-tip {
  color: #f52f3e;
  font-size: 14px;
  text-align: center;
  margin-top: 20px;
  margin-bottom: 17px;
}
.message-dialog-tip {
  margin: 16px 0;
  text-align: center;
  font-weight: 400;
  font-size: 14px;
  color: #999999;
}
</style>
