<template>
  <div>
    <el-dialog title="编辑入驻商家" :visible.sync="isEditShopType" :close-on-click-modal="false">
      <el-form :rules="formDataRule" :model="formData" ref="formData" label-width="150px">
        <el-form-item label="商家类型" prop="type">
          <el-select
            style="width: 260px;"
            v-model="formData.type"
            @change="changeType"
            placeholder="请选商家类型"
          >
            <el-option
              v-for="item in typeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="权益项" prop="privilege">
          <el-select
            style="width: 260px;"
            v-model="formData.privilege"
            filterable
            placeholder="请选择权益项"
            multiple
            v-loading="isLoading"
          >
            <el-option
              v-for="item in privilegeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <base-dialog-footer @confirm="confirm" @cancel="isEditShopType = false"></base-dialog-footer>
    </el-dialog>
  </div>
</template>

<script>
import { enterShopEditApi } from '@/api/enterpriseManagement/storeManagement'
import { enterShopSelectListApi } from '@/api/enterpriseManagement'
export default {
  name: 'editShopType',
  props: {
    typeList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      // 新增账号
      isEditShopType: false,
      formDataRule: {
        type: [{ required: true, message: '请选择商家类型', trigger: 'change' }],
        privilege: [{ required: true, message: '请选择权益项', trigger: 'change' }],
      },
      formData: {
        type: '',
        privilege: [],
      },
      storeUid: '',
      privilegeList: [],

      isLoading: false,
    }
  },
  methods: {
    open(row) {
      this.isEditShopType = true
      this.formData.type = row.type || ''
      this.formData.privilege = row.privilege || []
      this.changeType('init')
      this.storeUid = row.storeUid
      console.log(this.formData)
    },

    // 切换商家类型
    changeType(isOnce) {
      this.isLoading = true
      enterShopSelectListApi({ type: this.formData.type }).then((res) => {
        if (res.code == 200) {
          this.privilegeList = res.data.storePrivilege
          if (isOnce != 'init') {
            if (this.formData.type) {
              let list = this.privilegeList.map((item) => item.value)
              this.$set(this.formData, 'privilege', list)
              this.$nextTick(() => {
                this.$refs.formData.clearValidate()
              })
            }
          }
        }
        setTimeout(() => {
          this.isLoading = false
        }, 500)
      })
    },
    confirm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          enterShopEditApi({
            type: this.formData.type,
            privilege: this.formData.privilege,
            storeUid: this.storeUid,
          }).then((res) => {
            if (res.code == 200) {
              this.$message.success('编辑成功')
              this.isEditShopType = false
              this.$emit('update')
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
  },
}
</script>


