<template>
  <div>
    <el-card>
      <base-form :tableForm="tableForm" :formArray="formArray" @searchForm="searchForm"></base-form>
      <div class="operate">
        <el-button type="primary" icon="el-icon-plus" @click="addSettled">新建</el-button>
        <el-button @click="openShare">邀请商家自行入驻</el-button>
      </div>
      <base-table
        :tableColumn="tableColumn"
        :tableRequest="enterShopListApi"
        :tableForm="tableForm"
        ref="tableRef"
      >
        <template #operate="{ scope }">
          <div class="public-operate-btn">
            <el-button size="mini" type="text" @click="openDetail(scope.row)">查看</el-button>

            <el-button size="mini" type="text" @click="ediShopFn(scope.row)">修改</el-button>

            <!-- status 0  2-->
            <!-- 新建 审核通过 审核驳回 -->
            <el-button
              v-if="scope.row.status == 0 || scope.row.status == 2 || scope.row.status == 3"
              size="mini"
              type="text"
              @click="closeShopFn(scope.row)"
            >关闭</el-button>

            <!-- status 1 待审核 3审核驳回 -->
            <el-button
              v-if="scope.row.status == 1 || scope.row.status == 3"
              size="mini"
              type="text"
              @click="checkEnterShop(scope.row, 2)"
            >通过</el-button>

            <!-- status 1 待审核 -->
            <el-button
              v-if="scope.row.status == 1"
              class="del-color"
              size="mini"
              type="text"
              @click="checkEnterShop(scope.row, 3)"
            >驳回</el-button>

            <!-- status 4 重新提交 -->
            <el-button
              v-if="scope.row.status == 4"
              class="del-color"
              size="mini"
              type="text"
              @click="resubmit(scope.row)"
            >重新提交</el-button>
          </div>
        </template>
      </base-table>
    </el-card>
    <share-dialog ref="share" :typeList="typeList"></share-dialog>
    <add-account-dialog
      @update="update"
      :typeList="typeList"
      :privilegeList="privilegeList"
      ref="account"
    ></add-account-dialog>
    <edit-shop-type
      @update="update"
      ref="editShopType"
      :typeList="typeList"
      :privilegeList="privilegeList"
    ></edit-shop-type>
  </div>
</template>

<script>
import {
  enterShopListApi,
  enterShopSelectListApi,
  showPhoneApi,
  checkEnterShopApi,
  closeShopApi,
  resubmitShopApi,
} from '@/api/enterpriseManagement'
import shareDialog from './shareDialog.vue'
import addAccountDialog from './addAccountDialog.vue'
import editShopType from './editShopType.vue'
export default {
  name: 'merchantManagement',
  components: {
    shareDialog,
    addAccountDialog,
    editShopType,
  },
  data() {
    return {
      formArray: [
        {
          label: '企业名称',
          type: 'input',
          key: 'merchantName',
          placeholder: '请输入企业名称',
        },
        {
          label: '所属行业',
          type: 'select',
          key: 'tradeUid',
          placeholder: '全部',
          options: [],
        },
        {
          label: '负责人/电话',
          type: 'input',
          key: 'manageNameOrContactPhone',
          placeholder: '请输入负责人/电话',
        },
        {
          label: '企商通账号',
          type: 'input',
          key: 'username',
          placeholder: '请输入企商通账号',
        },
        {
          label: '状态',
          type: 'select',
          key: 'status',
          placeholder: '全部',
          options: [],
        },
        {
          label: '申请时间',
          type: 'time',
          key: 'time',
          timeKey: ['createAtStart', 'createAtEnd'],
        },

        {
          label: '商家类型',
          type: 'select',
          key: 'type',
          placeholder: '请选择商家类型',
          options: [],
        },

        {
          label: '权益项',
          type: 'select',
          key: 'privilege',
          placeholder: '请选择权益',
          options: [],
        },
      ],

      tableColumn: [
        // {
        //   label: 'ID',
        //   prop: 'id',
        //   width: '100px',
        // },
        {
          label: '企业名称',
          prop: 'name',
          width: '150px',
        },
        {
          label: '所属行业',
          prop: 'trade_uid',
        },
        {
          label: '负责人',
          prop: 'manage_name',
        },
        {
          label: '联系电话',
          prop: 'contact_phone',
          fun: showPhoneApi,
          funKey: {
            storeUid: 'storeUid',
          },
          type: 'customizePhone',
          width: '110px',
        },
        {
          label: '企商通账号',
          prop: 'username',
        },

        {
          label: '商家类型',
          prop: 'type_name',
        },
        {
          label: '权益项',
          prop: 'privilege_name',
        },

        {
          label: '申请时间',
          prop: 'created_at',
          width: '200px',
        },
        {
          label: '状态',
          prop: 'status',
          type: 'template',
          stateType: 'select',
          stateObj: {
            0: '新建',
            1: '待审核',
            2: '已通过',
            3: '未通过',
            4: '关闭',
          },
        },
        {
          label: '操作',
          prop: 'operate',
          type: 'customize',
          width: '150px',
          fixed: 'right',
        },
      ],
      enterShopListApi,
      tableForm: {
        tradeUid: '',
        status: '',
        type: '',
        privilege: '',
      },
      typeList: [],
      privilegeList: [],
    }
  },
  created() {
    this.enterShopSelectListFn()
  },
  methods: {
    // 初始化
    enterShopSelectListFn() {
      enterShopSelectListApi().then((res) => {
        let tradeList = res.data.tradeList.map((item) => {
          return {
            label: item.name,
            value: item.uid,
          }
        })
        this.formArray[1].options = [{ label: '全部', value: '' }, ...tradeList]
        this.formArray[4].options = [{ label: '全部', value: '' }, ...res.data.status]
        this.formArray[6].options = [{ label: '全部', value: '' }, ...res.data.storeType]
        this.formArray[7].options = [{ label: '全部', value: '' }, ...res.data.storePrivilege]
        this.typeList = res.data.storeType
        this.privilegeList = res.data.storePrivilege
      })
    },
    // 表单搜索事件
    searchForm(form) {
      console.log(this.tableForm, form)
      this.tableForm = Object.assign({}, this.tableForm, form)
    },
    // 重新提交
    resubmit(row) {
      let _this = this
      this.$confirm(`确定要重新提交吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        resubmitShopApi({
          storeUid: row.storeUid,
        }).then((res) => {
          if (res.code == 200) {
            _this.$message({
              type: 'success',
              message: '重新提交成功',
            })
            _this.update()
          }
        })
        console.log('编辑', e)
      })
    },

    // 审核
    checkEnterShop(row, status) {
      if (status == 2) {
        this.$confirm(`确定要审核通过吗?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          this.checkEnterShopFn(row, status)
        })
      } else if (status == 3) {
        this.$prompt(``, '填写驳回备注', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /.+/,
          inputErrorMessage: '填写驳回备注',
        }).then(({ value }) => {
          this.checkEnterShopFn(row, status, value)
        })
      }
    },
    checkEnterShopFn(row, status, value) {
      checkEnterShopApi({
        storeUid: row.storeUid,
        status,
        remark: value,
      }).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: status == 2 ? '审核通过' : '驳回成功',
          })
          this.update()
        }
      })
    },
    // 查看详情
    openDetail(row) {
      this.$router.push({
        path: '/settledMerchants/merchantDetails',
        query: {
          storeUid: row.uid,
        },
      })
    },

    // 修改
    ediShopFn(row) {
      row.privilege = row.privilege.map((item) => parseInt(item))
      row.type = parseInt(row.type)
      this.$refs.editShopType.open(row)
    },

    // 新增
    addSettled() {
      this.$refs.account.open()
    },
    // 关闭
    closeShopFn(row) {
      let _this = this
      this.$confirm(`确定要关闭该商户吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        closeShopApi({
          storeUid: row.uid,
        }).then((res) => {
          if (res.code == 200) {
            _this.$message({
              type: 'success',
              message: '关闭成功!',
            })
            _this.update()
          }
        })
      })
    },
    // 分享
    openShare() {
      this.$refs.share.open()
    },
    // 更新表格数据
    update() {
      this.$refs.tableRef.tableRequestFn()
    },
  },
  mounted() {},
}
</script>

<style lang="scss" scoped>
.operate {
  margin-top: 20px;
}
</style>