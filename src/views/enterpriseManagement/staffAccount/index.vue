<template>
  <div>
    <el-card>
      <base-form :formArray="formArray" :tableForm="tableForm" @searchForm="searchForm"></base-form>
      <div class="operate">
        <el-button type="primary" icon="el-icon-plus" @click="addSettled">新建</el-button>
        <el-button type="primary" @click="bulkOperation('N')">批量启用</el-button>
        <el-button type="primary" @click="bulkOperation('Y')">批量停用</el-button>
      </div>
      <base-table
        :tableColumn="tableColumn"
        :tableRequest="accountListApi"
        :tableForm="tableForm"
        :isSelect="true"
        rowKey="uid"
        @selectiKey="selectiKey"
        ref="table"
      >
        <template #operate="{ scope }">
          <div class="public-operate-btn">
            <el-button size="mini" type="text" @click="openRoles(scope.row)">分配角色</el-button>
            <el-button
              v-if="scope.row.type == 1"
              size="mini"
              type="text"
              @click="openStore(scope.row)"
            >分配门店</el-button>
            <el-button
              class="del-color"
              size="mini"
              type="text"
              @click="changeRowState(scope.row)"
            >{{ scope.row.is_forbidden == 'Y' ? '启用' : '停用' }}</el-button>
            <el-button
              v-if="scope.row.is_delete == 'Y'"
              class="del-color"
              size="mini"
              type="text"
              @click="del(scope.row)"
            >删除</el-button>
          </div>
        </template>
      </base-table>
    </el-card>
    <add-account-dialog @success="refreshList" ref="account"></add-account-dialog>
    <roles-dialog ref="rolesDialog" @success="refreshList"></roles-dialog>
    <store-dialog ref="storeDialog" @success="refreshList"></store-dialog>
  </div>
</template>

<script>
import {
  accountListApi,
  getMerchantListByAdminIdApi,
  getRoleListByAdminIdApi,
  enableAccountApi,
  deleteApi,
} from '@/api/enterpriseManagement'

import addAccountDialog from './components/addAccountDialog.vue'
import rolesDialog from './components/rolesDialog.vue'
import storeDialog from './components/storeDialog.vue'

export default {
  name: 'merchantManagement',
  components: {
    addAccountDialog,
    rolesDialog,
    storeDialog,
  },
  data() {
    return {
      formArray: [
        {
          label: '登录账号',
          type: 'input',
          key: 'user_name',
          placeholder: '请输入登录账号',
        },
        {
          label: '姓名',
          type: 'input',
          key: 'real_name',
          placeholder: '请输入姓名',
        },

        {
          label: '手机号',
          type: 'input',
          key: 'phone',
          placeholder: '请输入手机号',
        },
        {
          label: '所属商家',
          type: 'select',
          key: 'mch_store_uid',
          placeholder: '请选择',
          options: [],
        },
        {
          label: '关联角色',
          type: 'select',
          key: 'mch_role_uid',
          placeholder: '请选择',
          options: [],
        },
        {
          label: '状态',
          type: 'select',
          key: 'is_forbidden',
          placeholder: '请选择',
          options: [
            { label: '全部', value: '' },
            { label: '启用', value: 'N' },
            { label: '停用', value: 'Y' },
          ],
        },
      ],

      tableColumn: [
        {
          label: '登录账号',
          prop: 'username',
          width: '150px',
        },
        {
          label: '姓名',
          prop: 'real_name',
        },
        {
          label: '手机号',
          prop: 'phone',
          width: '150px',
        },
        {
          label: '所属商家',
          prop: 'merchant_name',
          width: '150px',
        },

        {
          label: '关联角色',
          prop: 'role_name',
        },
        {
          label: '状态',
          prop: 'is_forbidden',
          type: 'template',
          stateType: 'select',
          width: '100px',
          stateObj: {
            Y: '停用',
            N: '启用',
          },
        },
        {
          label: '创建人',
          prop: 'created_username',
        },
        {
          label: '创建时间',
          prop: 'created_at',
          width: '200px',
        },
        {
          label: '操作',
          prop: 'operate',
          type: 'customize',
          width: '200px',
          fixed: 'right',
        },
      ],
      accountListApi,
      tableForm: {
        mch_store_uid: '',
        mch_role_uid: '',
        is_forbidden: '',
      },
      selectListKey: [],

      isAssigningRoles: false,
      roleItem: {},
    }
  },
  created() {
    let id = this.$route.query.id
    if (id) {
      this.tableForm = {
        ...this.tableForm,
        mch_role_uid: id + '' || '',
      }
    }

    this.getMerchantListByAdmin()
    this.getRoleListByAdmin()
  },
  methods: {
    // 表单搜索事件
    searchForm(form, isResetFields) {
      if (isResetFields) {
        if (this.$route.query.id) {
          form.mch_role_uid = id + '' || ''
        }
        this.tableForm = Object.assign({}, this.tableForm, form)
      } else {
        this.tableForm = Object.assign({}, this.tableForm, form)
      }
    },
    // 获取商家列表
    refreshList() {
      this.$refs.table.tableRequestFn()
    },
    // 分配角色
    openRoles(roleItem) {
      this.$refs.rolesDialog.openRolesDialog(roleItem)
    },

    // 获取商家列表
    openStore(storeItem) {
      this.$refs.storeDialog.openStoreDialog(storeItem)
    },

    // 删除账号
    del(row) {
      this.$confirm('是否删除该账号?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        deleteApi({ uid: row.uid, mch_store_uid: row.mch_store_uid }).then((res) => {
          if (res.code == 200) {
            this.$message.success('删除成功')
            this.$refs.table.tableRequestFn()
          }
        })
      })
    },
    // 批量操作
    bulkOperation(key) {
      if (this.selectListKey.length == 0) {
        this.$message.warning('请选择要操作的账号')
        return
      }
      let uids = this.selectListKey.map((item) => {
        return {
          uid: item.uid,
          mch_store_uid: item.mch_store_uid,
        }
      })
      let _this = this
      let name = key == 'Y' ? '停用' : '启用'
      this.$confirm(`是否${name}账号?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        _this.enableAccountFn(key, uids, true)
      })
    },
    // 停用/启用账号
    changeRowState(row) {
      let _this = this
      let name = row.is_forbidden == 'N' ? '停用' : '启用'
      this.$confirm(`是否${name}该账号?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        _this.enableAccountFn(row.is_forbidden == 'N' ? 'Y' : 'N', [
          {
            mch_store_uid: row.mch_store_uid,
            uid: row.uid,
          },
        ])
      })
    },
    // 接口请求
    enableAccountFn(is_forbidden, info, isAll) {
      enableAccountApi({
        is_forbidden,
        info,
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success('操作成功')
          if (isAll) {
            this.selectListKey = []
            this.$refs.table.clearSelection()
          }
          this.$refs.table.tableRequestFn()
        } else {
          this.$message.error(res.msg)
        }
      })
    },

    // 新增
    addSettled() {
      this.$refs.account.open()
    },
    // 分享
    openShare() {
      this.$refs.share.open()
    },

    // 获取商家列表筛选条件
    getMerchantListByAdmin() {
      getMerchantListByAdminIdApi().then((res) => {
        if (res.code == 200) {
          this.formArray[3].options = [
            {
              label: '全部',
              value: '',
            },
          ]
          let list = res.data.map((item) => {
            return { label: item.merchant_name, value: item.mch_store_uid }
          })
          this.formArray[3].options = [...this.formArray[3].options, ...list]
        }
      })
    },

    // 获取账号商家下员工角色列表
    getRoleListByAdmin() {
      getRoleListByAdminIdApi().then((res) => {
        if (res.code == 200) {
          this.formArray[4].options = [
            {
              label: '全部',
              value: '',
            },
          ]
          let list = res.data.map((item) => {
            return { label: item.role_name, value: item.role_id }
          })
          this.formArray[4].options = [...this.formArray[4].options, ...list]
        }
      })
    },

    selectiKey(e) {
      this.selectListKey = e
    },
  },
  mounted() {},
}
</script>

<style lang="scss" scoped>
.operate {
  margin-top: 20px;
}
</style>
