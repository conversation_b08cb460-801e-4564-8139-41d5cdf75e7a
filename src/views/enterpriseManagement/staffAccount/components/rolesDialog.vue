<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-04-02 13:53:42
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-04-07 08:57:15
 * @FilePath: /qst-merchant-admin-2.0/src/views/enterpriseManagement/staffAccount/rolesDialog.vue
 * @Description: 分配角色弹窗
-->
<template>
  <div>
    <el-dialog
      title="分配角色"
      :visible.sync="isAssigningRoles"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <div class="flex-c">
        <el-select allow-create v-model="role_id">
          <el-option
            v-for="item in roleList"
            :key="item.role_id"
            :label="item.role_name"
            :value="item.role_id"
          ></el-option>
        </el-select>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="isAssigningRoles = false">关 闭</el-button>
          <el-button type="primary" @click="assignRoles">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { getRoleListByMerchantIdApi, assignPermissionsApi } from '@/api/enterpriseManagement'
export default {
  props: {},
  data() {
    return {
      role_id: null,
      roleList: [],
      isAssigningRoles: false,
      roleItem: {},
    }
  },
  methods: {
    // 打开分配角色弹窗
    openRolesDialog(roleItem) {
      this.roleItem = roleItem
      this.role_id = roleItem.mch_role_uid || ''
      this.getRoleListByMerchant(roleItem.mch_store_uid)
    },
    // 获取角色列表
    getRoleListByMerchant(mch_store_uid) {
      getRoleListByMerchantIdApi({
        mch_store_uid,
      }).then((res) => {
        if (res.code == 200) {
          this.roleList = res.data
          this.isAssigningRoles = true
        }
      })
    },
    // 修改角色
    assignRoles() {
      assignPermissionsApi({
        uid: this.roleItem.uid,
        role_id: this.role_id,
        mch_store_uid: this.roleItem.mch_store_uid,
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success('修改成功')
          this.isAssigningRoles = false
          this.$emit('success')
        } else {
          this.$message.error(res.msg)
        }
      })
    },
  },
}
</script>