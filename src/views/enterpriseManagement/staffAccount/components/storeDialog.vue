<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-04-02 13:53:42
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-07 09:54:50
 * @FilePath: /qst-merchant-admin-2.0/src/views/enterpriseManagement/staffAccount/components/AssignStoreDialog.vue
 * @Description: 分配门店弹窗
-->
<template>
  <div>
    <el-dialog
      title="分配门店"
      :visible.sync="isAssigningStore"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <div class="flex-c" v-loading="isLoading">
        <el-select allow-create multiple v-model="store_ids" v-if="!isLoading">
          <el-option
            v-for="item in storeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="isAssigningStore = false">关 闭</el-button>
          <el-button type="primary" @click="assignStore">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { getShopListByIntersectionApi, assignShopsApi } from '@/api/enterpriseManagement'
export default {
  props: {},
  data() {
    return {
      store_ids: [],
      storeList: [],
      isAssigningStore: false,
      storeItem: {},
      isLoading: true,
    }
  },
  methods: {
    // 打开店铺弹窗
    openStoreDialog(storeItem) {
      this.isLoading = true
      this.storeItem = storeItem
      this.getStoreList(storeItem.mch_store_uid)
    },
    // 获取店铺列表
    getStoreList(mch_store_uid) {
      getShopListByIntersectionApi({
        mch_store_uid,
        uid: this.storeItem.uid,
      }).then((res) => {
        if (res.code == 200) {
          this.storeList = res.data
          let list = res.data.reduce((pre, cur) => {
            if (cur.selected) {
              pre.push(Number(cur.value))
            }
            return pre
          }, [])
          this.$set(this, 'store_ids', list)

          this.isAssigningStore = true
          setTimeout(() => {
            this.isLoading = false
          }, 100)
        }
      })
    },
    // 修改角色
    assignStore() {
      assignShopsApi({
        uid: this.storeItem.uid,
        shop_ids: this.store_ids.toString(),
        mch_store_uid: this.storeItem.mch_store_uid,
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success('修改成功')
          this.isAssigningStore = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
  },
}
</script>