<template>
  <div>
    <!-- 新增 -->
    <el-dialog
      title="新建账号"
      :close-on-click-modal="false"
      :visible.sync="isAddAccount"
      destroy-on-close
    >
      <el-form :rules="formDataRule" :model="asscountForm" ref="asscountForm" label-width="150px">
        <el-form-item label="所属商家" prop="mch_store_uid">
          <el-select
            allow-create
            v-model="asscountForm.mch_store_uid"
            placeholder="请选择所属商家"
            style="width: 100%"
            @change="getRoleListByMerchant"
          >
            <el-option
              v-for="item in merchantList"
              :key="item.mch_store_uid"
              :label="item.merchant_name"
              :value="item.mch_store_uid"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="姓名" prop="real_name">
          <el-input maxlength="10" v-model="asscountForm.real_name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input maxlength="11" v-model="asscountForm.mobile" placeholder="请输入手机号"></el-input>
        </el-form-item>
        <el-form-item label="关联角色" prop="role_id">
          <el-select
            allow-create
            v-model="asscountForm.role_id"
            placeholder="请选择关联角色"
            style="width: 100%"
          >
            <el-option
              v-for="item in roleList"
              :key="item.role_id"
              :label="item.role_name"
              :value="item.role_id"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="分配店铺" v-if="isShop && shopList.length > 0">
          <el-select
            allow-create
            multiple
            v-model="asscountForm.shop_ids"
            placeholder="请选择分配店铺"
            style="width: 100%"
          >
            <el-option
              v-for="item in shopList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <base-dialog-footer @confirm="confirm" confirmText="下一步" @cancel="isAddAccount = false"></base-dialog-footer>
    </el-dialog>

    <el-dialog
      title="新建账号"
      :visible.sync="isMerchant"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <!-- 不存在相同账号 -->
      <div v-if="!isAccount">
        <el-form
          :rules="merchantFormRule"
          :model="merchantForm"
          ref="merchantForm"
          label-width="150px"
        >
          <el-form-item label="账号" prop="username">
            <el-input maxlength="50" v-model="merchantForm.username" placeholder="请输入账号"></el-input>
          </el-form-item>
          <el-form-item label="初始密码" prop="password">
            <base-input
              autocomplete="new-password"
              type="password"
              v-model="merchantForm.password"
              placeholder="请输入初始密码"
              maxlength="50"
            ></base-input>
            <!-- 初始密码必须为8～32位字母+数字+特殊字符的组合～ -->
          </el-form-item>
        </el-form>
      </div>

      <!-- 有重复账号 -->
      <div v-if="isAccount">
        <div class="error-dialog-tip">
          <i class="el-icon-warning"></i>
          检测到本公司该手机号已注册过企商通系统，可直接使用！
        </div>
        <el-form
          :rules="merchantFormRule"
          :model="merchantForm"
          ref="merchantForm"
          label-width="150px"
        >
          <el-form-item label="登录账号">
            <el-input v-model="merchantForm.username" placeholder="请输入登录账号" disabled></el-input>
          </el-form-item>
        </el-form>
        <div class="message-dialog-tip">不想用这个账号？ 点击上一步更换手机号注册</div>
      </div>

      <base-dialog-footer cancelText="上一步" @confirm="merchantConfirm" @cancel="merchantCancel"></base-dialog-footer>
    </el-dialog>
  </div>
</template>

<script>
import {
  getMerchantListByAdminIdApi,
  getRoleListByMerchantIdApi,
  getShopListByMchAdminIdApi,
  checkExistUserApi,
  checkUserNameApi,
  addAccountApi,
} from '@/api/enterpriseManagement'
export default {
  name: 'AddAccountDialog',
  data() {
    return {
      // 新增账号
      isAddAccount: false,
      formDataRule: {
        mch_store_uid: [{ required: true, message: '请选择所属商家', trigger: 'blur' }],
        real_name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        mobile: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },
        ],
        role_id: [{ required: true, message: '请选择关联角色', trigger: 'blur' }],
      },
      asscountForm: {
        mch_store_uid: '',
        real_name: '',
        mobile: '',
        role_id: '',
        shop_ids: [],
      },

      // 入驻商家
      merchantForm: {
        username: '',
        password: '',
      },
      merchantFormRule: {
        username: [
          { required: true, message: '请输入账号', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (!value) return callback(new Error('请输入登录账号'))
              checkUserNameApi({ username: value }).then((res) => {
                if (res.code == 200) {
                  callback()
                } else {
                  callback(new Error('该账号已存在，请重新输入！'))
                }
              })
            },
            trigger: 'blur',
          },
        ],
        password: [
          { required: true, message: '请输入初始密码', trigger: 'blur' },
          {
            pattern: /^(?=.*[a-z_])(?=.*\d)(?=.*[^a-z0-9_])[\S]{8,32}$/,
            message: '初始密码必须为8～32位字母+数字+特殊字符的组合',
          },
        ],
      },
      isAccount: false,
      isMerchant: false,
      merchantList: [],
      roleList: [],
      shopList: [],
      isShop: true,
    }
  },
  methods: {
    // 打开组件方法
    open() {
      this.asscountForm = {
        mch_store_uid: '',
        real_name: '',
        mobile: '',
        role_id: '',
        shop_ids: [],
      }
      this.getMerchantListByAdmin()
      this.isAddAccount = true
    },
    // 获取商家列表
    getMerchantListByAdmin() {
      getMerchantListByAdminIdApi().then((res) => {
        if (res.code == 200) {
          this.merchantList = res.data
        }
      })
    },
    // 获取角色列表
    getRoleListByMerchant(e) {
      getRoleListByMerchantIdApi({
        mch_store_uid: this.asscountForm.mch_store_uid,
      }).then((res) => {
        if (res.code == 200) {
          this.asscountForm.role_id = ''
          this.roleList = res.data
        }
      })
      console.log(this.asscountForm.mch_store_uid, this.merchantList)

      this.isShop = this.merchantList.some(
        (e) => e.mch_store_uid == this.asscountForm.mch_store_uid && e.type == 1
      )
      if (this.isShop) {
        getShopListByMchAdminIdApi({
          mch_store_uid: this.asscountForm.mch_store_uid,
        }).then((res) => {
          if (res.code == 200) {
            this.shopList = res.data
            this.asscountForm.shop_ids = []
          }
        })
      }
    },
    // 确定
    confirm() {
      this.$refs.asscountForm.validate((valid) => {
        if (valid) {
          checkExistUserApi({
            mch_store_uid: this.asscountForm.mch_store_uid,
            mobile: this.asscountForm.mobile,
          }).then((res) => {
            if (res.code == 200) {
              this.accountInfo = res.data
              if (res.data.username) {
                this.merchantForm.username = res.data.username
              } else {
                this.merchantForm = {
                  username: '',
                  password: '',
                }
              }
              this.isAddAccount = false

              // 2 账号已存在，直接新增角色和门店权限
              // 3 账号不存在，新增用户和角色门店权限
              this.isAccount = res.data.related == 2 || res.data.related == 3
              this.isMerchant = true
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 商家取消 / 上一步
    merchantCancel() {
      this.isMerchant = false
      this.isAddAccount = true
    },

    merchantConfirm() {
      this.$refs.merchantForm.validate(async (valid) => {
        if (valid) {
          // 判断账号是否存在，不存在则创建用户
          if (!this.isAccount || this.accountInfo.related == 2) {
            try {
              let res = await addAccountApi({
                mobile: this.asscountForm.mobile,
                real_name: this.asscountForm.real_name,
                role_id: this.asscountForm.role_id,
                username: this.merchantForm.username,
                password: this.merchantForm.password,
                mch_store_uid: this.asscountForm.mch_store_uid,
                shop_ids: this.isShop ? this.asscountForm.shop_ids.toString(',') : '',
              })
              if (res.code == 200) {
                this.$message.success('新增账号成功')
                this.$emit('success', res.data)
                this.isAddAccount = false
                this.isMerchant = false
              }
            } catch (err) {
              this.$message.error(err)
              return false
            }
          } else {
            // 创建时不需处理
            this.isAddAccount = false
            this.isMerchant = false
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.error-dialog-tip {
  color: #f52f3e;
  font-size: 14px;
  text-align: center;
  margin-top: 20px;
  margin-bottom: 17px;
}
.message-dialog-tip {
  margin: 16px 0;
  text-align: center;
  font-weight: 400;
  font-size: 14px;
  color: #999999;
}
</style>
