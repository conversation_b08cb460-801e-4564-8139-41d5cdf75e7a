<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-03-05 18:00:06
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-26 10:33:00
 * @FilePath: /qst-merchant-admin-2.0/src/views/enterpriseManagement/informationManagement/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="information-bg">
    <el-form label-width="180px" :rules="informaRules" :model="information" ref="information">
      <el-card>
        <template #header>
          <div class="card-header">
            <div></div>
            <div>企业基础信息</div>
          </div>
        </template>
        <el-row :gutter="20">
          <!-- <el-col :lg="8" :md="8" :sm="12" :xl="12" :xs="24">
            <el-form-item label="企业ID">
              <input type="text" disabled />
            </el-form-item>
          </el-col>-->

          <el-col :lg="8" :md="8" :sm="12" :xl="12" :xs="24">
            <el-form-item label="企业名称" label-width="100px">
              <el-input v-model="organization_name" type="text" disabled></el-input>
            </el-form-item>
          </el-col>

          <el-col :lg="8" :md="8" :sm="12" :xl="12" :xs="24">
            <el-form-item label="负责人" label-width="100px">
              <el-input v-model="information.manage_name" type="text" disabled></el-input>
            </el-form-item>
          </el-col>

          <el-col :lg="8" :md="8" :sm="12" :xl="12" :xs="24">
            <el-form-item label="联系电话" label-width="100px">
              <el-input v-model="information.contact_phone" type="text" disabled></el-input>
            </el-form-item>
          </el-col>

          <el-col :lg="8" :md="8" :sm="12" :xl="12" :xs="24" v-if="information.account_name">
            <el-form-item label="企商通账号" label-width="100px">
              <el-input v-model="information.account_name" type="text" disabled></el-input>
            </el-form-item>
          </el-col>

          <el-col :lg="8" :md="8" :sm="12" :xl="12" :xs="24">
            <el-form-item label="所属行业" prop="trade_name" label-width="100px">
              <el-cascader
                :disabled="!isEdit"
                :props="props"
                :options="options"
                v-model="information.trade_name"
              ></el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card>
        <template #header>
          <div class="card-header">
            <div></div>
            <div>企业认证信息</div>
          </div>
        </template>
        <el-form-item label="营业执照" prop="license">
          <div v-if="isEdit">
            <div class="flex" style="align-items: flex-end;">
              <PictureComponent
                v-if="information.license && information.license != ''"
                :imgParams="information.license"
                imgWidth="358px"
                imgHeight="178px"
                @deleteImg="deleteImg('license')"
              ></PictureComponent>
              <div v-else class="show-upload">
                <el-upload
                  class="upload-demo"
                  action="fakeaction"
                  :show-file-list="false"
                  multiple
                  drag
                  accept=".jpg, .png, .jpeg, .bmp"
                  :http-request="(e) => upLoadImg(e, 'license')"
                >
                  <el-image
                    :src="information.license"
                    style="width: 358px; height: 178px"
                    v-if="information.license"
                    fit="contain"
                  ></el-image>
                  <div v-else>
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">
                      将文件拖到此处，或
                      <em>点击上传</em>
                    </div>
                  </div>
                </el-upload>
              </div>

              <el-button style="margin-left: 10px" type="text" @click="isBusinessLcense = true">查看示例</el-button>
            </div>

            <div class="el-upload__tip">
              <div>1、请上传清晰的营业执照的照片，否则无法通过审核</div>
              <div>2、请自行确认营业执照的状态，确保在正常经营中，否则无法通过审核</div>
              <div>3、依据相关法律法规，将向用户展示该资质材料，请确认图片清晰、完整、无水印，信息真实有效</div>
              <div>4、支持格式jpg/png/jpeg/bmp，文件大小不能超过2.0M</div>
            </div>
          </div>

          <el-image
            :src="information.license"
            :preview-src-list="[information.license]"
            class="license_image"
            fit="contain"
            v-else
          ></el-image>
        </el-form-item>
        <el-form-item label="企业名称" prop="organization_name">
          <el-input
            :disabled="!isEdit"
            type="text"
            v-model="information.organization_name"
            placeholder="请输入企业名称"
          ></el-input>
        </el-form-item>

        <el-form-item label="企业类型" prop="organization_type">
          <el-input
            :disabled="!isEdit"
            type="text"
            v-model="information.organization_type"
            placeholder="请输入企业类型"
          ></el-input>
        </el-form-item>

        <el-form-item label="统一社会信用代码" prop="organization_code">
          <el-input
            :disabled="!isEdit"
            type="text"
            v-model="information.organization_code"
            placeholder="请输入统一社会信用代码"
          ></el-input>
        </el-form-item>
        <el-form-item label="注册地址" prop="address">
          <el-input
            :disabled="!isEdit"
            type="text"
            v-model="information.address"
            placeholder="请输入注册地址"
          ></el-input>
        </el-form-item>
        <el-form-item label="营业执照法人姓名" prop="name">
          <el-input
            :disabled="!isEdit"
            type="text"
            v-model="information.name"
            placeholder="请输入营业执照法人姓名"
          ></el-input>
        </el-form-item>

        <el-form-item label="法人身份证" prop="legal_card_back" v-if="isEdit">
          <div class="flex avatar-flex">
            <PictureComponent
              v-if="information.legal_card_back && information.legal_card_back != ''"
              :imgParams="information.legal_card_back"
              imgWidth="100px"
              imgHeight="100px"
              @deleteImg="deleteImg('legal_card_back')"
            ></PictureComponent>
            <el-upload
              v-else
              class="avatar-uploader"
              :show-file-list="false"
              action="fakeaction"
              accept=".jpg, .png, .jpeg, .bmp"
              :http-request="(e) => upLoadImg(e, 'legal_card_back')"
            >
              <i class="el-icon-plus avatar-uploader-icon"></i>
              <div class="el-upload__text">上传人像面</div>
            </el-upload>

            <PictureComponent
              v-if="information.legal_card_front && information.legal_card_front != ''"
              :imgParams="information.legal_card_front"
              imgWidth="100px"
              imgHeight="100px"
              @deleteImg="deleteImg('legal_card_front')"
            ></PictureComponent>
            <el-upload
              v-else
              class="avatar-uploader"
              :show-file-list="false"
              action="fakeaction"
              accept=".jpg, .png, .jpeg, .bmp"
              :http-request="(e) => upLoadImg(e, 'legal_card_front')"
            >
              <i class="el-icon-plus avatar-uploader-icon"></i>
              <div class="el-upload__text">上传国徽面</div>
            </el-upload>

            <el-button type="text" @click="isIdcard = true">查看示例</el-button>
          </div>

          <div class="el-upload__tip">
            <div>1、请上传清晰可用的证件照片，否则无法通过审核</div>
            <div>2、支持格式jpg/png/jpeg/bmp，文件大小不能超过2.0M</div>
          </div>
        </el-form-item>
        <el-form-item label="法人身份证" prop="legal_card_back" v-else>
          <div class="flex avatar-flex">
            <el-image
              :src="information.legal_card_back"
              class="avatar"
              :preview-src-list="[information.legal_card_back]"
              fit="contain"
            ></el-image>
            <el-image
              :src="information.legal_card_front"
              class="avatar"
              :preview-src-list="[information.legal_card_front]"
              fit="contain"
            ></el-image>
          </div>
        </el-form-item>

        <el-form-item label="法人姓名" prop="legal_name">
          <el-input
            :disabled="!isEdit"
            v-model="information.legal_name"
            type="text"
            placeholder="请输入法人姓名"
          ></el-input>
        </el-form-item>

        <el-form-item label="法人身份证号码" prop="legal_code">
          <el-input
            :disabled="!isEdit"
            v-model="information.legal_code"
            type="text"
            placeholder="请输入法人身份证号码"
          ></el-input>
        </el-form-item>

        <el-form-item label="企业LOGO" prop="logo" v-if="isEdit">
          <PictureComponent
            v-if="information.logo"
            :imgParams="information.logo"
            imgWidth="100px"
            imgHeight="100px"
            @deleteImg="deleteImg('logo')"
          ></PictureComponent>
          <el-upload
            v-else
            class="logo-uploader"
            :show-file-list="false"
            action="fakeaction"
            accept=".jpg, .png, .jpeg, .bmp"
            :http-request="(e) => upLoadImg(e, 'logo')"
          >
            <img v-if="information.logo" :src="information.logo" class="avatar" />
            <div class="logo" v-else>
              <img src="@/assets/Management/logo-updata.png" alt />
              <div>替换LOGO</div>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="企业LOGO" prop="logo" v-else>
          <el-image
            :src="information.logo"
            class="logo-uploader"
            :preview-src-list="[information.logo]"
          ></el-image>
        </el-form-item>

        <el-form-item v-if="isEdit">
          <div class="flex">
            <!-- <el-button>取消</el-button> -->
            <el-button type="primary" @click="submitForm">提交</el-button>
          </div>
        </el-form-item>
      </el-card>
    </el-form>

    <el-card class="log-main">
      <template #header>
        <div class="card-header">
          <div></div>
          <div>备注</div>
        </div>
      </template>
      <el-form>
        <el-form-item label="当前状态" v-if="!isEdit || information.remark">
          <div>{{information.status == 0 ? '新建' : information.status == 1 ? '审核中' : information.status == 3 ? '驳回' : information.status == 2 ? '正常': '' }}</div>
        </el-form-item>
        <el-form-item label="驳回原因" v-if="isEdit && information.remark">
          <div>{{ information.remark }}</div>
        </el-form-item>
      </el-form>
    </el-card>

    <el-dialog title="营业执照示例" :visible.sync="isBusinessLcense" :close-on-click-modal="false">
      <img class="business-lcense" src="@/assets/enteroruse/20250306_id_lcense.png" alt />
    </el-dialog>

    <el-dialog title="身份证示例" :visible.sync="isIdcard" :close-on-click-modal="false">
      <div class="id-card-model">
        <div>
          <img src="@/assets/enteroruse/20250306_id_card1.png" alt />
          <div>身份证人像面</div>
        </div>
        <div>
          <img src="@/assets/enteroruse/20250306_id_card2.png" alt />
          <div>身份证国徽面</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCompanyInfoApi, saveCompanyInfoApi } from '@/api/enterpriseManagement'
import { ocrUpdata, getCateListApi } from '@/api/common'
import { mapGetters } from 'vuex'
export default {
  name: 'InformationManagement',
  components: {},
  computed: {
    ...mapGetters({
      avatar: 'user/avatar',
    }),
  },
  data() {
    return {
      imageUrl: '',
      isBusinessLcense: false,
      isIdcard: false,
      organization_name: '',
      information: {
        license: '',
        organization_name: '',
        organization_code: '',
        address: '',
        legal_card_front: '',
        legal_card_back: '',
        legal_name: '',
        legal_code: '',
        logo: '',
        enterprise_type: '',
        name: '',
        card: '',
      },
      informaRules: {
        trade_name: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value) {
                callback()
              } else {
                return callback(new Error('请选择行业'))
              }
            },
            trigger: 'change',
          },
        ],
        license: [{ required: true, message: '请上传营业执照', trigger: 'change' }],
        organization_name: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
        organization_code: [{ required: true, message: '请输入统一社会信用代码', trigger: 'blur' }],
        address: [{ required: true, message: '请输入注册地址', trigger: 'blur' }],
        organization_type: [{ required: true, message: '请输入企业类型', trigger: 'blur' }],
        legal_card_back: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (this.information.legal_card_front && this.information.legal_card_back) {
                callback()
              } else {
                return callback(new Error('请上传身份证两面照片'))
              }
            },
            trigger: 'change',
          },
        ],
        logo: [{ required: true, message: '请上传企业LOGO', trigger: 'change' }],
        legal_name: [{ required: true, message: '请输入法人姓名', trigger: 'blur' }],
        legal_code: [{ required: true, message: '请输入法人身份证号码', trigger: 'blur' }],
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        card: [{ required: true, message: '请输入身份证号码', trigger: 'blur' }],
      },
      disabled: true,
      licenseData: {},
      cardData: {},
      isEdit: false,

      props: {
        lazy: true,
        lazyLoad(node, resolve) {
          const { level } = node
          getCateListApi({
            parent_uid: node?.value || '0',
          }).then((res) => {
            const nodes = res.data.map((item) => ({
              label: item.name,
              value: item.uid,
              leaf: item.is_child == 0,
            }))
            resolve(nodes)
          })
        },
      },
      options: [],
    }
  },
  created() {
    this.init()
  },
  methods: {
    // 删除图片
    deleteImg(key) {
      this.$set(this.information, key, '')
    },
    submitForm() {
      this.$refs.information.validate((valid) => {
        if (valid) {
          if (this.information.name != this.information.legal_name) {
            return this.$message.error('法人身份证与营业执照法人不符，请检查后重新上传！')
          }
          // if (this.information.organization_name != this.organization_name) {
          //   return this.$message.error('公司姓名不一致，请重新录入')
          // }

          if (
            this.licenseData.name &&
            this.cardData.name &&
            // 营业执照内容是不一致，需要穿is_edit
            (this.information.organization_name != this.licenseData.name ||
              this.information.organization_type != this.licenseData.company_type ||
              this.information.organization_code != this.licenseData.registration_number ||
              this.information.name != this.licenseData.legal_representative ||
              this.information.address != this.licenseData.address ||
              // 身份证
              this.information.legal_name != this.cardData.name ||
              this.information.legal_code != this.cardData.idNumber)
          ) {
            this.information.is_edit = 'Y'
          } else {
            this.information.is_edit = 'N'
          }
          this.information.parent_trade_uid = this.information.trade_name[0]
          this.information.trade_uid =
            this.information.trade_name[this.information.trade_name.length - 1]
          console.log(this.information)
          saveCompanyInfoApi(this.information).then((res) => {
            if (res.code == 200) {
              this.$message.success('提交成功')
              this.init()
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

    init() {
      getCompanyInfoApi().then((res) => {
        let trade_name = [res.data.parent_trade_uid + '']
        if (res.data.trade_uid) {
          trade_name.push(res.data.trade_uid + '')
        }
        this.information = {
          ...res.data,
          name: res.data.legal_name,
          card: res.data.legal_code,
          organization_name: res.data.organization_code ? res.data.organization_name : '',
          trade_name,
        }
        this.organization_name = res.data.organization_name
        // 修改条件 租户非审核通过 或者 入驻商户新建、审核驳回 可以修改信息
        this.isEdit =
          (res.data.mch_type == 1 && res.data.status != 2) ||
          (res.data.mch_type == 2 && (res.data.status == 0 || res.data.status == 3))
        console.log(res.data)
        this.$store.dispatch('user/setAvatar', res.data.logo)
      })
    },

    upLoadImg(file, key) {
      // 上传
      this.$upLoadImg(file.file).then((res) => {
        let url = res.data.url
        if (key != 'legal_card_back' && key != 'legal_card_front') {
          this.information[key] = url
        }
        if (!(key == 'legal_card_back' || key == 'legal_card_front')) {
          console.log('验证通过111')
          this.$refs.information.validateField(key, (res) => {})
        }
        // 上传完执行的操作
        switch (key) {
          case 'license':
            ocrUpdata({
              url: url,
              type: '0',
            }).then((res) => {
              if (res.code == 200) {
                this.licenseData = res.data?.ocr_business?.content?.result || {}
                if (this.licenseData.legal_representative) {
                  this.information.organization_name = this.licenseData.name
                  this.information.organization_type = this.licenseData.company_type
                  this.information.organization_code = this.licenseData.registration_number
                  this.information.name = this.licenseData.legal_representative
                  this.information.address = this.licenseData.address
                }
              }
            })
            break
          case 'legal_card_front':
            ocrUpdata({
              url: res.data.url,
              type: '1',
            }).then((res) => {
              if (res.code == 200) {
                let ocr_id_card = res.data?.ocr_id_card || {}
                if (!ocr_id_card.validPeriod || !ocr_id_card.issueAuthority) {
                  this.$message.error('请上传正确的图片')
                  this.information.legal_card_front = ''
                } else {
                  this.information.legal_card_front = url
                  this.$refs.information.validateField('legal_card_front')
                }
              }
            })
            break
          case 'legal_card_back':
            ocrUpdata({
              url: res.data.url,
              type: '1',
            }).then((res) => {
              if (res.code == 200) {
                this.cardData = res.data?.ocr_id_card || {}
                this.information.legal_name = this.cardData.name
                this.information.legal_code = this.cardData.idNumber
                if (!this.cardData.name || !this.cardData.idNumber) {
                  this.information.legal_card_back = ''
                  this.$message.error('请上传正确的图片')
                } else {
                  this.information.legal_card_back = url
                  this.$refs.information.validateField('legal_card_front')
                }
              }
            })
            break
          case 'logo':
            this.information.enterprise_type = 3
            break
        }
      })
    },
  },
  mounted() {},
}
</script>

<style lang="scss" scoped>
.information-bg {
  background: #f6f8f9;
}
// 营业执照图片
.license_image {
  width: 358px;
  height: 178px;
  object-fit: contain;
}

// 营业执照示例图片样式
.business-lcense {
  width: 400px;
  height: 272px;
  display: block;
  margin: 0 auto;
}
// 身份证示例图片样式
.id-card-model {
  display: flex;
  justify-content: space-between;
  text-align: center;
  img {
    width: 200px;
    height: 112px;
    margin-right: 24px;
  }
  div {
    font-weight: 400;
    font-size: 14px;
    color: #8c8c8c;
    line-height: 14px;
    margin-top: 15px;
  }
}

// 身份证上传样式
.avatar-flex {
  align-items: flex-end;
  > * {
    margin-right: 10px;
  }
}
.avatar-uploader {
  margin-right: 24px;
  width: 100px;
  height: 100px;
  border: 1px dashed #dcdfe6;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  text-align: center;
  &:hover {
    border-color: #409eff;
  }

  .el-upload__text {
    font-weight: 400;
    font-size: 12px;
    color: #606266;
    line-height: 17px;
  }
}
.avatar-uploader-icon {
  font-size: 15px;
  color: #8c939d;
  line-height: 20px;
  text-align: center;
  margin: 32px auto 0px;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
  object-fit: contain;
  margin-right: 10px;
}

// 公司logo上传样式
.logo-uploader {
  width: 100px;
  height: 100px;
  border: 1px dashed #dcdfe6;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  text-align: center;
  &:hover {
    border-color: #409eff;
  }
  .logo {
    width: 100px;
    height: 100px;
    position: relative;
    img {
      width: 100%;
      height: 100%;
    }
    > div {
      width: 100px;
      height: 24px;
      background: #000000;
      border-radius: 0px 0px 6px 6px;
      opacity: 0.6;
      text-align: center;
      line-height: 24px;
      font-weight: 400;
      font-size: 12px;
      color: #ffffff;
      position: absolute;
      bottom: 0;
      left: 0;
    }
  }
}

.show-upload {
  width: 360px;
  height: 180px;
  overflow: hidden;
}
::v-deep .el-input__inner {
  max-width: 400px;
}
</style>
