<!--
 * @Author: liqian liqian@123
 * @Date: 2025-03-26 10:34:49
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-29 10:50:00
 * @FilePath: \qst-merchant-admin-2.0\src\views\enterpriseManagement\storeManagement\component\storeConfigInfo\storeConfigInfo.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->

<template>
  <div class="storeBasicInfoPage">
    <!-- <div>物流设置-配送方式</div> -->
    <el-form
      label-width="180px"
      :model="basicConfig"
      :rules="basicInfoRule"
      ref="basicConfig"
      :label-position="labelPosition"
    >
      <div v-if="false">
        <el-form-item label="配送方式" prop="delivery_methods">
          <el-checkbox-group v-model="basicConfig.delivery_methods">
            <el-checkbox label="express">快递发货</el-checkbox>
            <!-- <el-checkbox label="local">同城快递</el-checkbox> -->
            <el-checkbox label="pickup">自提</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item
          label="运费设置"
          prop="shipping_fee_type"
          v-if="basicConfig.delivery_methods && basicConfig.delivery_methods.includes('express')"
        >
          <el-radio-group v-model="basicConfig.shipping_fee_type">
            <el-radio label="free">包邮</el-radio>
            <el-radio label="fixed">固定运费</el-radio>
            <el-radio label="template">使用运费模板</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          label="固定运费"
          prop="fixed_fee"
          v-if="basicConfig.shipping_fee_type == 'fixed'"
        >
          <el-input
            type="text"
            @blur="handleNNDInput($event, 'fixed_fee')"
            v-model="basicConfig.fixed_fee"
            placeholder="请输入固定运费"
          >
            <template slot="prepend">￥</template>
          </el-input>
        </el-form-item>

        <el-form-item
          label="运费模板"
          prop="delivery_id"
          v-if="basicConfig.shipping_fee_type == 'template'"
        >
          <div class="flex">
            <el-select
              v-model="basicConfig.delivery_id"
              style="width: 100%"
              placeholder="请选择运费模板"
            >
              <el-option
                v-for="item in datailTemplateList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
            <el-tooltip
              class="item"
              effect="dark"
              :disabled="basicInfo.is_forbidden != 'Y'"
              content="该门店已被禁用暂不能添加运费模版"
              placement="top-start"
            >
              <el-button
                type="text"
                :disabled="basicInfo.is_forbidden == 'Y'"
                style="margin-left: 15px"
                @click="addTemplate"
                icon="el-icon-plus"
              >
                新建模版
              </el-button>
            </el-tooltip>
          </div>
        </el-form-item>

        <el-form-item
          label="自提点"
          prop="local_id"
          v-if="basicConfig.delivery_methods.includes('pickup')"
        >
          <el-select
            v-model="basicConfig.local_id"
            style="width: 100%"
            placeholder="请选择自提点"
            @change="selectLocal"
          >
            <el-option
              v-for="item in tableData"
              :key="item.id"
              :label="item.address"
              :value="item.id"
            >
              <template>
                <div class="selectdiv">
                  <span class="top">
                    {{ item.address }}
                    <!-- <span class="spanstyle" v-if="item.is_default == 'Y'">{{ item.is_default == 'Y' ? '默认' : '' }}</span> -->
                  </span>
                  <span class="bottom">
                    联系人:{{ item.manage_name }}({{ item.contact_phone }})
                  </span>
                </div>
              </template>
            </el-option>
          </el-select>
        </el-form-item>
      </div>
      <el-form-item prop="stock_type">
        <template slot="label">
          库存设置
          <el-tooltip
            class="item"
            effect="dark"
            content="独立库存修改为共享库存时，门店前期设置的独立库存会被共享库存覆盖，后续库存操作均基于共享库存进行处理！反向修改同理，请谨慎操作！"
            placement="top"
          >
            <img
              :src="imgOssPath + '20250619_sf_wenhao.png'"
              alt
              class="icon_wenhao"
              style="transform: translateY(2px)"
            />
          </el-tooltip>
        </template>
        <el-radio-group v-model="basicConfig.stock_type" @input="changeStocktype">
          <el-radio label="1">独立库存</el-radio>
          <el-radio label="2">共享总部库存</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="库存预警">
        <!-- 预警方式 -->
        <el-row :gutter="24">
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="预警方式" prop="method">
              <el-select
                style="width: 100%"
                v-model="basicConfig.method"
                placeholder="请选择预警方式"
              >
                <el-option label="按商品类型" value="1" />
                <el-option label="按商品分类" value="2" />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 实物商品库存预警值 -->
          <template v-if="basicConfig.method == '1'">
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item prop="physical">
                <template slot="label">
                  实物商品库存预警值
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="当实物商品库存低于此值时将触发预警"
                    placement="top"
                  >
                    <img
                      :src="imgOssPath + '20250619_sf_wenhao.png'"
                      alt
                      class="icon_wenhao"
                      style="transform: translateY(2px)"
                    />
                  </el-tooltip>
                </template>

                <el-input
                  v-model.number="basicConfig.physical"
                  placeholder="请输入预警值"
                ></el-input>
              </el-form-item>
            </el-col>

            <!-- 卡券商品库存预警值 -->
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item prop="coupon">
                <template slot="label">
                  卡券商品库存预警值
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="当卡券商品库存低于此值时将触发预警"
                    placement="top"
                  >
                    <img
                      :src="imgOssPath + '20250619_sf_wenhao.png'"
                      alt
                      class="icon_wenhao"
                      style="transform: translateY(2px)"
                    />
                  </el-tooltip>
                </template>
                <el-input v-model.number="basicConfig.coupon" placeholder="请输入预警值"></el-input>
              </el-form-item>
            </el-col>

            <!-- 虚拟商品库存预警值 -->
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item prop="virtual">
                <template slot="label">
                  虚拟商品库存预警值
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="当虚拟商品库存低于此值时将触发预警"
                    placement="top"
                  >
                    <img
                      :src="imgOssPath + '20250619_sf_wenhao.png'"
                      alt
                      class="icon_wenhao"
                      style="transform: translateY(2px)"
                    />
                  </el-tooltip>
                </template>
                <el-input
                  v-model.number="basicConfig.virtual"
                  placeholder="请输入预警值"
                ></el-input>
              </el-form-item>
            </el-col>
          </template>
        </el-row>

        <!-- 按商品分类的预警值设置 -->
        <template v-if="basicConfig.method == '2'">
          <div v-for="(item, index) in basicConfig.detail" :key="index" class="warning-group">
            <div class="warning-group-header">
              <span>
                <span style="color: #f56c6c; margin-right: 4px">*</span>
                商品分类{{ index + 1 }}预警值
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="当该分类商品库存低于此值时将触发预警"
                  placement="top"
                >
                  <img
                    :src="imgOssPath + '20250619_sf_wenhao.png'"
                    alt
                    class="icon_wenhao"
                    style="transform: translateY(2px)"
                  />
                </el-tooltip>
              </span>
            </div>
            <el-form
              label-width="180px"
              :model="item"
              ref="basicInfo1"
              :rules="basicInfoRule1"
              label-position="top"
            >
              <el-row :gutter="24">
                <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
                  <el-form-item prop="cate_id">
                    <el-cascader
                      style="width: 100%"
                      v-model="item.cate_id"
                      :options="orderGroupList"
                      :props="{
                        checkStrictly: true,
                        emitPath: false,
                        value: 'cate_id',
                        label: 'name',
                        children: 'children',
                      }"
                      placeholder="请选择商品分类"
                      class="warning-select"
                      filterable
                      clearable
                    />
                  </el-form-item>
                </el-col>

                <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
                  <el-form-item prop="value">
                    <el-input v-model.number="item.value" placeholder="请输入预警值"></el-input>
                  </el-form-item>
                </el-col>

                <el-col :xs="4" :sm="4" :md="4" :lg="4" :xl="4">
                  <el-form-item>
                    <el-button
                      type="text"
                      class="delete-btn"
                      @click="removeOrderGroup(index)"
                      v-if="basicConfig.detail.length > 1"
                    >
                      删除
                    </el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
          <div class="add-btn-wrapper">
            <el-button type="dashed" @click="addOrderGroup" icon="el-icon-plus">
              添加分类预警值
            </el-button>
          </div>
        </template>
      </el-form-item>
    </el-form>
    <!-- 切换库存设置  -->
    <el-dialog append-to-body :visible.sync="showShopStock" width="400px">
      <template slot="title">
        <div class="flex">
          <img class="tip_icon" :src="imgPath" alt />
          <span class="title">是否恢复最近一次独立库存相关数据？</span>
        </div>
      </template>
      <div class="content">若选择恢复，则商品库存恢复为最近一次独立库存数据</div>
      <div class="content">若选择清空，则商品库存全部清空为0，需重新设置</div>
      <div class="dialog-footer">
        <el-button @click="reuseShopStock">取 消</el-button>
        <el-button type="primary" @click="reuseShopStock">恢复</el-button>
        <el-button type="primary" @click="clearShopStock">清空</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getmaparrList } from '@/utils/index'
  export default {
    name: 'storeConfigInfo',
    props: {
      basicConfig: {
        type: Object,
        default: () => {},
      },
      basicInfo: {
        type: Object,
        default: () => {},
      },
      tableData: {
        type: Array,
        default: () => [],
      },
      datailTemplateList: {
        type: Array,
        default: () => [],
      },
      orderGroupList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        labelPosition: 'top',
        basicInfoRule: {
          delivery_methods: [{ required: true, message: '请选择配送方式', trigger: 'blur' }],
          shipping_fee_type: [{ required: true, message: '请选择运费设置', trigger: 'blur' }],
          fixed_fee: [
            { required: true, message: '请输入固定运费', trigger: 'blur' },
            {
              required: true,
              validator: (rule, value, callback) => {
                if (value > 0) {
                  callback()
                } else {
                  return callback(new Error('输入必须大于0'))
                }
              },
              trigger: 'blur',
            },
          ],
          delivery_id: [{ required: true, message: '请选择运费模板', trigger: 'blur' }],
          local_id: [{ required: true, message: '请选择自提点', trigger: 'blur' }],
          stock_type: [{ required: true, message: '请选择库存设置', trigger: 'change' }],
          method: [{ required: true, message: '请选择预警方式', trigger: 'change' }],
          physical: [
            { required: true, message: '请输入实物商品库存预警值', trigger: 'blur' },
            {
              required: true,
              validator: (rule, value, callback) => {
                if (!isNaN(value) && value > 0) {
                  callback()
                } else {
                  this.basicInfo.physical = ''
                  return callback(new Error('预警值必须为正整数'))
                }
              },
              trigger: 'blur',
            },
          ],
          coupon: [
            { required: true, message: '请输入卡券商品库存预警值', trigger: 'blur' },
            {
              required: true,
              validator: (rule, value, callback) => {
                if (!isNaN(value) && value > 0) {
                  callback()
                } else {
                  this.basicInfo.coupon = ''
                  return callback(new Error('预警值必须为正整数'))
                }
              },
              trigger: 'blur',
            },
          ],
          virtual: [
            { required: true, message: '请输入虚拟商品库存预警值', trigger: 'blur' },
            {
              required: true,
              validator: (rule, value, callback) => {
                if (!isNaN(value) && value > 0) {
                  callback()
                } else {
                  this.basicInfo.virtual = ''
                  return callback(new Error('预警值必须为正整数'))
                }
              },
              trigger: 'blur',
            },
          ],
        },
        basicInfoRule1: {
          cate_id: [{ required: true, message: '请选择商品分类 ', trigger: 'change' }],
          value: [{ required: true, message: '请输入预警值', trigger: 'blur' }],
        },
        local_id: '',
        imgPath: require('../../../../../assets/warn_20250626.png'),
        showShopStock: false,
      }
    },
    watch: {
      basicConfig: {
        handler(newVal) {
          if (newVal) {
            this.basicConfig = newVal
            console.log(this.basicConfig)
            this.basicConfig.delivery_id =
              newVal.delivery_id != '' ? Number(newVal.delivery_id) : ''
            this.basicConfig.stock_type = newVal?.stock_type || '1'
          }
          if (newVal?.takes) {
            this.$set(this.basicConfig, 'local_id', newVal.takes[0]?.id || '')
          }
        },
        immediate: true,
        deep: true,
      },
    },
    created() {},
    methods: {
      changeCheckList(val) {
        console.log(val)
      },
      selectLocal(val) {
        let currentList = getmaparrList(this.tableData, 'id', val)
        // this.local_id = currentList[0].id || ''
        let selectJson = {
          id: currentList[0].id || '',
          address: currentList[0].address,
          manage_name: currentList[0].manage_name,
          contact_phone: currentList[0].contact_phone,
        }
        this.basicConfig.takes = [selectJson]
        console.log(this.basicConfig.takes)
      },
      addTemplate() {
        this.$router.push({
          path: '/goodsSet/freightTemplate',
          query: {
            id: this.basicInfo.shop_id,
          },
        })
      },
      handleNNDInput(e, code) {
        let val = e.target.value
        if (isNaN(val) || val < 0) {
          this.basicConfig[code] = ''
        } else {
          this.basicConfig[code] = Number(val).toFixed(2)
        }
      },
      changeStocktype(value) {
        console.log(value)
        if (value == 1) {
          this.showShopStock = true
        }
      },
      reuseShopStock() {
        this.basicConfig.stock_tips_type = 1
        this.showShopStock = false
      },
      //恢复
      reuseShopStock() {
        this.basicConfig.stock_tips_type = 2
        this.showShopStock = false
      },
      //清空
      clearShopStock() {
        this.basicConfig.stock_tips_type = 3
        this.showShopStock = false
      },

      // 添加商品分类预警设置
      addOrderGroup() {
        this.basicConfig.detail.push({
          cate_id: '',
          value: '',
        })
      },

      // 移除商品分类预警设置
      removeOrderGroup(index) {
        this.basicConfig.detail.splice(index, 1)
      },

      // 处理输入，确保值大于0
      handleInput(value, field) {
        if (value < 0) {
          this.$message.warning('预警值必须大于0')
          // 不再自动赋值为1
        }
      },
    },
  }
</script>

<style lang="scss">
  .el-select-dropdown__item {
    height: auto;
  }
  .selectdiv {
    display: flex;
    flex-direction: column;
    .top {
      font-size: 15px;
      line-height: 23px;
    }
    .spanstyle {
      color: #0958d9;
      padding: 2px 5px;
      background-color: #e6f4ff;
      border-radius: 4px;
      border: 1px solid #91caff;
    }
    .bottom {
      color: rgb(153, 153, 153);
      font-size: 12px;
      line-height: 23px;
    }
  }
  .content {
    font-size: 14px;
    color: #303133;
    font-weight: 600;
    margin: 10px 0;
  }
  .dialog-footer {
    display: flex;
    justify-content: center;
    gap: 14px; /* 按钮间距统一 */
    margin-top: 40px; /* 与内容部分间距 */
  }
</style>
