<!--
 * @Author: liq<PERSON> liqian@123
 * @Date: 2025-03-26 14:59:12
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-25 11:28:05
 * @FilePath: \qst-merchant-admin-2.0\src\views\enterpriseManagement\storeManagement\component\storeBasicInfo\storeBasicInfoDialog.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-dialog
    :title="title"
    width="50%"
    top="15vh"
    :visible.sync="isShare"
    :close-on-click-modal="false"
    append-to-body
  >
    <div class="share" v-loading="loading">
      <div class="storeBasicInfoPage">
        <el-form
          label-width="180px"
          :rules="basicInfoRule"
          :model="basicInfo1"
          ref="basicInfo1"
          :label-position="labelPosition"
        >
          <el-form-item label="自提地址" prop="address">
            <el-input v-model="basicInfo1.address" type="text" placeholder="请输入自提地址">
              <el-button
                @click="selectMap"
                class="selectMap"
                slot="append"
                icon="el-icon-location-outline"
              >
                地图选点
              </el-button>
            </el-input>
          </el-form-item>
          <el-form-item label="联系人" prop="manage_name">
            <el-input
              type="text"
              v-model="basicInfo1.manage_name"
              placeholder="请输入联系人"
            ></el-input>
          </el-form-item>
          <el-form-item label="联系电话" prop="contact_phone">
            <el-input
              type="text"
              v-model="basicInfo1.contact_phone"
              placeholder="请输入联系电话"
            ></el-input>
          </el-form-item>

          <el-form-item label="营业日期" prop="legal_code">
            <div>
              <el-radio-group v-model="basicInfo1.legal_code" @change="changeTimeoptions">
                <el-radio-button label="1">工作日</el-radio-button>
                <el-radio-button label="2">周末</el-radio-button>
                <el-radio-button label="3">全部时间</el-radio-button>
              </el-radio-group>
            </div>
            <div class="weekSelect">
              <el-select
                v-model="basicInfo1.business_day_name"
                multiple
                placeholder="请选择"
                @change="changeTime"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="营业时间" prop="business_day">
            <div class="timeSelect">
              <el-time-select
                placeholder="起始时间"
                v-model="basicInfo1.start_time"
                :picker-options="{
                  start: '00:00:00',
                  step: '00:15',
                  end: '23:59:59',
                }"
              ></el-time-select>
              -
              <el-time-select
                placeholder="结束时间"
                v-model="basicInfo1.end_time"
                :picker-options="{
                  start: '00:00:00',
                  step: '00:15',
                  end: '23:59:59',
                  minTime: basicInfo1.start_time,
                }"
              ></el-time-select>
            </div>
          </el-form-item>
        </el-form>
        <div>
          <el-checkbox v-model="checked">设为默认自提地址</el-checkbox>
        </div>
      </div>
    </div>
    <base-dialog-footer @cancel="cancel" @confirm="confirm"></base-dialog-footer>
    <selectAddressMap
      ref="selectAddressMap"
      :basicInfoList="basicInfo1"
      @chooseOrgAddr="chooseOrgAddr"
    />
  </el-dialog>
</template>

<script>
  import selectAddressMap from '../selectAddressMap.vue'
  import { appendDestjsonFromsrcjson } from '@/utils/index'
  export default {
    name: 'storeBasicInfoDialog',
    components: { selectAddressMap },
    props: {
      operateFlag: {
        type: String,
        default: 'ADD',
      },
      addAndEditFlag: {
        type: String,
        default: 'ADD',
      },
      title: {
        type: String,
        default: '新建自提地址',
      },
      tableDataCur: {
        type: Object,
        default: () => {},
      },
    },

    data() {
      return {
        checked: false,
        isShare: false,
        dialogVisible: false,
        loading: true,
        options: [
          {
            value: '1',
            label: '周一',
          },
          {
            value: '2',
            label: '周二',
          },
          {
            value: '3',
            label: '周三',
          },
          {
            value: '4',
            label: '周四',
          },
          {
            value: '5',
            label: '周五',
          },
          {
            value: '6',
            label: '周六',
          },
          {
            value: '0',
            label: '周日',
          },
        ],
        labelPosition: 'top',
        basicInfo1: {
          legal_code: '',
          region: '', //地址
          address: '', // 自提地址
          manage_name: '', // 联系人
          contact_phone: '', // 联系电话
          business_day: '', // 营业日期
          business_day_name: [], // 显示营业日期
          start_time: '',
          end_time: '',
          longitude: '', //经度
          latitude: '', //维度
          is_default: '',
        },
        basicInfoRule: {
          address: [{ required: true, message: '请输入自提地址', trigger: 'blur' }],
          manage_name: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
          contact_phone: [
            { required: true, message: '请输入联系电话', trigger: 'blur' },
            { pattern: /^1[3456789]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' },
          ],
        },
      }
    },
    created() {},
    watch: {
      // tableDataCur: {
      //   handler(newVal) {
      //     appendDestjsonFromsrcjson(newVal, this.basicInfo1)
      //     this.checked = newVal.is_default == 'Y' ? true : false
      //   },
      // },
      // immediate: true,
      // deep: true,
    },
    methods: {
      open(data) {
        this.isShare = true
        this.loading = false
        this.basicInfo1 = data

        if (this.addAndEditFlag == 'ADD') {
          this.$nextTick(() => {
            this.$refs.basicInfo1.resetFields()
          })
        }
      },
      confirm() {
        this.$refs.basicInfo1.validate((valid) => {
          if (valid) {
            this.basicInfo1.is_default = this.checked ? 'Y' : 'N'
            let params = {
              ...this.basicInfo1,
            }
            this.$emit('confirmDialog', params)
            this.cancel()
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
      cancel() {
        this.isShare = false
      },
      selectMap() {
        this.$refs.selectAddressMap.openDialog()
      },
      //选择营业时间
      changeTimeoptions() {
        if (this.basicInfo1.legal_code == 1) {
          this.basicInfo1.business_day_name = ['1', '2', '3', '4', '5']
          this.basicInfo1.business_day = this.basicInfo1.business_day_name.join()
        } else if (this.basicInfo1.legal_code == 2) {
          this.basicInfo1.business_day_name = ['6', '0']
          this.basicInfo1.business_day = this.basicInfo1.business_day_name.join()
        } else {
          this.basicInfo1.business_day_name = ['1', '2', '3', '4', '5', '6', '0']
          this.basicInfo1.business_day = this.basicInfo1.business_day_name.join()
        }
      },
      changeTime() {
        this.basicInfo1.legal_code = ''
        this.basicInfo1.business_day = this.basicInfo1.business_day_name.join()
        if (this.areArraysEqual(this.basicInfo1.business_day_name, ['1', '2', '3', '4', '5'])) {
          this.basicInfo1.legal_code = 1
        }
        if (this.areArraysEqual(this.basicInfo1.business_day_name, ['6', '0'])) {
          this.basicInfo1.legal_code = 2
        }
        if (
          this.areArraysEqual(this.basicInfo1.business_day_name, [
            '1',
            '2',
            '3',
            '4',
            '5',
            '6',
            '0',
          ])
        ) {
          this.basicInfo1.legal_code = 3
        }
        console.log(this.basicInfo1.legal_code)
      },
      areArraysEqual(arr1, arr2) {
        return arr1.length === arr2.length && !arr1.some((item) => !arr2.includes(item))
      },
      // 选取地理位置后的回调
      chooseOrgAddr(data) {
        this.basicInfo1.address = data.region + '' + data.address
        this.basicInfo1.region = data.region
        this.basicInfo1.latitude = data.latitude
        this.basicInfo1.longitude = data.longitude
        console.log(this.basicInfo1)
        this.$refs.selectAddressMap.closeDialog()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .storeBasicInfoPage {
    .uploadspan {
      .uploadlogo {
        width: 100px;
        height: 100px;
        border: 1px dashed #d9d9d9;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        .el-icon-plus {
          font-size: 20px;
          color: #c0c4cc;
        }
        .el-upload__text {
          color: #c0c4cc;
          font-size: 12px;
        }
        .el-upload__tip {
          color: #c0c4cc;
          font-size: 12px;
          margin-top: 10px;
        }
        .license_image {
          width: 100px;
          height: 100px;
          border-radius: 4px;
        }
      }
    }
    .selectMap {
      color: #fff;
      background: #0071fe;
      border-radius: 0px 4px 4px 0px;
    }
    .weekSelect {
      display: flex;
      justify-content: space-between;
      margin-top: 15px;
      .el-select {
        width: 60%;
      }
    }
    .timeSelect {
      .el-date-editor.el-input {
        width: 130px;
        margin: 0 10px;
      }
    }
  }
</style>
