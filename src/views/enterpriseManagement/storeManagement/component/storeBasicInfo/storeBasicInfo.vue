<!--
 * @Author: liqian liqian@123
 * @Date: 2025-03-26 10:33:27
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-14 11:32:42
 * @FilePath: \qst-merchant-admin-2.0\src\views\enterpriseManagement\storeManagement\component\storeBasicInfo\storeBasicInfo.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="storeBasicInfoPage">
    <el-form
      :rules="basicInfoRule"
      label-width="180px"
      :model="basicInfo"
      ref="basicInfo"
      :label-position="labelPosition"
    >
      <el-form-item label="门店LOGO" prop="logo">
        <div class="uploadspan">
          <el-upload
            class="uploadlogo"
            action="fakeaction"
            :show-file-list="false"
            multiple
            accept=".jpg, .png, .jpeg, .bmp"
            :http-request="(e) => upLoadImg(e, 'logo')"
          >
            <img v-if="basicInfo.logo" :src="basicInfo.logo" class="license_image" />
            <div v-else>
              <i class="el-icon-plus"></i>
              <div class="el-upload__text">上传logo</div>
            </div>
          </el-upload>
          <div class="el-upload__tip" slot="tip">
            <div>建议尺寸：200x200像素，支持jpg、png格式，文件大小不超过2MB</div>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="门店名称" prop="name">
        <el-input type="text" v-model="basicInfo.name" placeholder="请输入门店名称"></el-input>
      </el-form-item>

      <el-form-item label="所属商家" prop="mch_store_uid">
        <el-select
          v-model="basicInfo.mch_store_uid"
          style="width: 100%"
          placeholder="请选择所属商家"
        >
          <el-option
            v-for="item in merchantList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="所属行业" prop="trade_uid_name">
        <el-cascader
          style="width: 100%"
          :props="props"
          :options="cascaderOptions"
          v-model="basicInfo.trade_uid_name"
          @change="cascaderChange"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="负责人" prop="manage_name">
        <el-input type="text" v-model="basicInfo.manage_name" placeholder="请输入负责人"></el-input>
      </el-form-item>
      <el-form-item label="联系电话" prop="contact_phone">
        <el-input
          type="text"
          v-model="basicInfo.contact_phone"
          placeholder="请输入联系电话"
        ></el-input>
      </el-form-item>
      <el-form-item label="门店地址（若存在到店业务场景，请务必填写该内容）" prop="address">
        <div class="flex">
          <el-input
            v-model="basicInfo.address"
            v-if="basicInfo.address"
            disabled
            type="text"
            placeholder="请输入门店地址"
          ></el-input>
          <el-button
            @click="selectMap"
            class="selectMap"
            slot="append"
            icon="el-icon-location-outline"
          >
            地图选点
          </el-button>
        </div>
      </el-form-item>

      <el-form-item label="营业时间（若存在到店业务场景，请务必填写该内容）" prop="business_day">
        <div>
          <el-radio-group v-model="timeactive" @change="changeTimeoptions">
            <el-radio-button label="1">工作日</el-radio-button>
            <el-radio-button label="2">周末</el-radio-button>
            <el-radio-button label="3">全部时间</el-radio-button>
          </el-radio-group>
        </div>
        <div class="weekSelect">
          <el-select
            v-model="basicInfo.business_day_name"
            multiple
            placeholder="请选择"
            @change="changeTime"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <div class="timeSelect">
            <el-time-select
              placeholder="起始时间"
              v-model="basicInfo.start_time"
              :picker-options="{
                start: '00:00',
                step: '00:15',
                end: '23:59',
              }"
            ></el-time-select>
            -
            <el-time-select
              placeholder="结束时间"
              v-model="basicInfo.end_time"
              :picker-options="{
                start: '00:00',
                step: '00:15',
                end: '23:59',
                minTime: basicInfo.start_time,
              }"
            ></el-time-select>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <!-- <div>
      <p>自提地址列表（用于顾客上门自提商品的地址）</p>
      <div>
        <el-button @click="createNewAddress" type="primary" icon="el-icon-plus">新建自提地址</el-button>
        <el-button @click="getStoreAddress" icon="el-icon-location-outline">同步门店地址</el-button>
      </div>
    </div>

    <el-table :data="tableData" stripe style="width: 100%">
      <el-table-column prop="address" label="自提地址"></el-table-column>
      <el-table-column prop="manage_name" label="联系人"></el-table-column>
      <el-table-column prop="contact_phone" label="联系电话"></el-table-column>
      <el-table-column prop="business_day" label="营业时间">
        <template slot-scope="scope">
          <div v-if="scope.row.legal_code != ''">
            <span v-if="scope.row.legal_code == '1'">工作日</span>
            <span v-if="scope.row.legal_code == '2'">周末</span>
            <span v-if="scope.row.legal_code == '3'">全部时间</span>
          </div>
          <div v-else>
            <span v-for="(item, index) in scope.row.business_day_name" :key="index">{{ weekTransName[item] }}</span>
          </div>
          <div>
            <span>{{ scope.row.start_time }} - {{ scope.row.end_time }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="is_default" label="默认">
        <template slot-scope="scope">
          <div class="flex">
            <el-switch
              :disabled="scope.row.is_default == 'Y'"
              @change="changeStatus(scope)"
              active-value="Y"
              inactive-value="N"
              v-model="scope.row['is_default']"
            ></el-switch>
            <span>{{ scope.row.is_default == 'Y' ? '是' : '否' }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作">
        <template slot-scope="scope">
          <div class="public-operate-btn">
            <el-button size="mini" type="text" @click="openDetail(scope.row, scope.$index)">修改</el-button>
            <el-tooltip
              class="item"
              effect="dark"
              :disabled="scope.row.is_default != 'Y'"
              content="默认地址不允许删除"
              placement="top-start"
            >
              <el-button class="del-color" size="mini" type="text" :disabled="scope.row.is_default == 'Y'" @click="handledelete(scope.row)">
                删除
              </el-button>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <template slot="empty">
        <base-tabel-empty />
      </template>
    </el-table> -->
    <store-BasicInfo-Dialog
      :operateFlag="operateFlag"
      :title="title"
      :tableDataCur="tableDataCur"
      :addAndEditFlag="addAndEditFlag"
      @confirmDialog="confirmDialog"
      ref="storeBasicInfoDialog"
    />
    <selectAddressMap
      ref="selectAddressMap"
      :basicInfoList="basicInfo"
      @chooseOrgAddr="chooseOrgAddr"
    />
  </div>
</template>

<script>
  import storeBasicInfoDialog from './storeBasicInfoDialog.vue'
  import selectAddressMap from '../selectAddressMap.vue'
  import { getCateListApi } from '@/api/common'

  import {
    getPickupAddressList,
    getcreatePickupAddress,
    editPickupAddress,
    deletePickupAddress,
    updateDefaultPickupAddress,
    syncPickupAddress,
  } from '@/api/enterpriseManagement/storeManagement'
  export default {
    name: 'storeBasicInfo',
    components: {
      storeBasicInfoDialog,
      selectAddressMap,
    },
    props: {
      basicInfo: {
        type: Object,
        default: () => {},
      },
      operateFlag: {
        type: String,
        default: 'ADD',
      },
      tableData: {
        type: Array,
        default: () => [],
      },
      merchantList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        options: [
          {
            value: '1',
            label: '周一',
          },
          {
            value: '2',
            label: '周二',
          },
          {
            value: '3',
            label: '周三',
          },
          {
            value: '4',
            label: '周四',
          },
          {
            value: '5',
            label: '周五',
          },
          {
            value: '6',
            label: '周六',
          },
          {
            value: '0',
            label: '周日',
          },
        ],
        weekTransName: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
        props: {
          lazy: true,
          lazyLoad(node, resolve) {
            const { level } = node
            getCateListApi({
              parent_uid: node?.value || '0',
            }).then((res) => {
              const nodes = res.data.map((item) => ({
                label: item.name,
                value: item.uid,
                leaf: item.is_child == 0,
              }))
              resolve(nodes)
            })
          },
        },
        cascaderOptions: [],
        addAndEditFlag: '',
        tableDataCur: {
          legal_code: '',
          region: '', //地址
          address: '', // 自提地址
          manage_name: '', // 联系人
          contact_phone: '', // 联系电话
          business_day: '', // 营业日期
          business_day_name: [], // 显示营业日期
          start_time: '00:00:00',
          end_time: '23:59:59',
          longitude: '', //经度
          latitude: '', //维度
          is_default: '',
        },
        editIndex: '', //点击修改的那条数据index
        timeactive: '',
        labelPosition: 'top',
        basicInfoRule: {
          name: [{ required: true, message: '请输入门店名称', trigger: 'blur' }],
          mch_store_uid: [{ required: true, message: '请输入所属商家', trigger: 'blur' }],
          trade_uid_name: [{ required: true, message: '请输入所属行业', trigger: 'blur' }],
          manage_name: [{ required: true, message: '请输入负责人', trigger: 'blur' }],
          contact_phone: [
            { required: true, message: '请输入手机号', trigger: 'blur' },
            { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' },
          ],
        },
        getPickupAddressList,
        selectedAddress: '',
        title: '新建自提地址',
      }
    },
    watch: {
      basicInfo: {
        handler(newVal) {
          if (this.merchantList.length == 1) {
            this.basicInfo.mch_store_uid = this.merchantList[0].value
          }
          this.timeactive = ''
          if (this.areArraysEqual(newVal.business_day.split(','), ['1', '2', '3', '4', '5'])) {
            this.timeactive = 1
          }
          if (this.areArraysEqual(newVal.business_day.split(','), ['6', '0'])) {
            this.timeactive = 2
          }
          if (
            this.areArraysEqual(newVal.business_day.split(','), ['1', '2', '3', '4', '5', '6', '0'])
          ) {
            this.timeactive = 3
          }
        },
        immediate: true,
        deep: true,
      },
      tableData: {
        handler(newVal) {
          console.log(newVal)
        },
        immediate: true,
        deep: true,
      },
    },
    mounted() {},
    methods: {
      upLoadImg(file, key) {
        // 上传
        this.$upLoadImg(file.file).then((res) => {
          this.basicInfo.logo = res.data.url
        })
      },
      //选择营业时间
      changeTimeoptions() {
        if (this.timeactive == 1) {
          this.basicInfo.business_day_name = ['1', '2', '3', '4', '5']
          this.basicInfo.business_day = ['1', '2', '3', '4', '5'].join()
        } else if (this.timeactive == 2) {
          this.basicInfo.business_day_name = ['6', '0']
          this.basicInfo.business_day = ['6', '0'].join()
        } else {
          this.basicInfo.business_day_name = ['1', '2', '3', '4', '5', '6', '0']
          this.basicInfo.business_day = ['1', '2', '3', '4', '5', '6', '0'].join()
        }
      },
      createNewAddress() {
        this.title = '新建自提地址'
        this.addAndEditFlag = 'ADD'
        this.tableDataCur = {
          legal_code: '',
          region: '', //地址
          address: '', // 自提地址
          manage_name: '', // 联系人
          contact_phone: '', // 联系电话
          business_day: '', // 营业日期
          business_day_name: [], // 显示营业日期
          start_time: '00:00:00',
          end_time: '23:59:59',
          longitude: '', //经度
          latitude: '', //维度
          is_default: '',
        }
        this.$refs.storeBasicInfoDialog.open(this.tableDataCur)
      },
      //同步地址
      getStoreAddress() {
        if (this.basicInfo.address == '') {
          this.$message.error('请先选择门店地址')
          return
        }
        if (this.operateFlag == 'ADD') {
          this.$set(this.tableData, this.tableData.length, this.basicInfo)
        }
        if (this.operateFlag == 'Edit') {
          let params = {
            ...this.basicInfo,
            shop_id: this.basicInfo.shop_id,
          }
          syncPickupAddress(params).then((res) => {
            if (res.code == 200) {
              this.$message.success('同步成功')
              this.getadressList()
            }
          })
        }
      },
      changeTime() {
        this.basicInfo.legal_code = ''
        this.basicInfo.business_day = this.basicInfo.business_day_name.join()
        if (this.areArraysEqual(this.basicInfo.business_day_name, ['1', '2', '3', '4', '5'])) {
          this.basicInfo.legal_code = 1
        }
        if (this.areArraysEqual(this.basicInfo.business_day_name, ['6', '0'])) {
          this.basicInfo.legal_code = 2
        }
        if (
          this.areArraysEqual(this.basicInfo.business_day_name, ['1', '2', '3', '4', '5', '6', '0'])
        ) {
          this.basicInfo.legal_code = 3
        }
        console.log(this.basicInfo.legal_code)
      },
      selectMap() {
        this.$refs.selectAddressMap.openDialog()
      },
      //新增自提地址
      confirmDialog(data) {
        //新增门店时的  自提点逻辑   手动保存到表格
        if (this.operateFlag == 'ADD') {
          if (this.addAndEditFlag == 'ADD') {
            this.$set(this.tableData, this.tableData.length, data)
          }
          if (this.addAndEditFlag == 'Edit') {
            this.$set(this.tableData, this.editIndex, data)
          }
        }
        //编辑门店时的  自提点逻辑  掉接口
        if (this.operateFlag == 'Edit') {
          if (this.addAndEditFlag == 'ADD') {
            this.createPickupAddress(data)
          }
          if (this.addAndEditFlag == 'Edit') {
            this.editPickupAddress(data)
          }
        }
      },
      //编辑门店时，新建自提点
      createPickupAddress(data) {
        let params = {
          shop_id: this.basicInfo.shop_id,
          ...data,
          // longitude: '1',
          // latitude: '1',
          // region: '111',
        }
        getcreatePickupAddress(params).then((res) => {
          if (res.code == 200) {
            this.$message.success('添加成功')
            this.getadressList()
          }
        })
      },
      //编辑门店时，修改自提点
      editPickupAddress(data) {
        let params = {
          ...data,
          // longitude: '1',
          // latitude: '1',
          // region: '111',
        }
        editPickupAddress(params).then((res) => {
          if (res.code == 200) {
            this.$message.success('修改成功')
            this.getadressList()
          }
        })
      },
      //刷新得到新的自提数据
      getadressList() {
        this.$emit('getAddress', { shop_id: this.basicInfo.shop_id })
      },
      cascaderChange(val) {
        this.basicInfo.trade_uid = val.join()
        console.log(val)
      },
      openDetail(row, index) {
        // this.tableDataCur = Object.assign({}, {}, row)
        this.addAndEditFlag = 'Edit'
        this.title = '编辑自提地址'
        this.editIndex = index
        this.$refs.storeBasicInfoDialog.open(row)
      },
      // 选取地理位置后的回调
      chooseOrgAddr(data) {
        this.basicInfo.address = data.region + '' + data.address
        this.basicInfo.region = data.region
        this.basicInfo.latitude = data.latitude
        this.basicInfo.longitude = data.longitude
        console.log(this.basicInfo)
        this.$refs.selectAddressMap.closeDialog()
      },
      // 删除表格
      handledelete(row) {
        if (this.operateFlag == 'ADD') {
          this.tableData = this.tableData.filter((item) => item !== row)
        }
        if (this.operateFlag == 'Edit') {
          this.$confirm('是否删除该自提点?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              deletePickupAddress({ shop_id: row.shop_id, id: row.id }).then((res) => {
                if (res.code == 200) {
                  this.$message.success('删除成功')
                  this.getadressList()
                }
              })
            })
            .catch((action) => {
              // this.$message({
              //   type: 'info',
              //   message: action === 'cancel' ? '取消删除' : '',
              // })
            })
        }
      },
      //切换自提点状态
      changeStatus(scope) {
        if (this.operateFlag == 'ADD') {
          this.$confirm(`是否设置为默认地址?`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              this.tableData.forEach((item, inedx) => {
                if (inedx != scope.$index) {
                  item.is_default = scope.row.is_default == 'Y' ? 'N' : 'Y'
                }
              })
            })
            .catch((action) => {
              this.tableData[scope.$index].is_default = scope.row.is_default == 'Y' ? 'N' : 'Y'
              // this.$message({
              //   type: 'info',
              //   message: action === 'cancel' ? '取消设置' : '停留在当前页面',
              // })
            })
        }
        if (this.operateFlag == 'Edit') {
          let statusTip = scope.row.is_default == 'Y' ? '启用' : '停用'
          this.$confirm(`是否设置为默认地址?`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              updateDefaultPickupAddress({
                id: scope.row.id,
                is_default: scope.row.is_default,
              }).then((res) => {
                if (res.code == 200) {
                  this.$message.success(`设置成功`)
                  this.getadressList()
                }
              })
            })
            .catch((action) => {
              this.getadressList()
              // this.$message({
              //   type: 'info',
              //   message: action === 'cancel' ? '取消设置' : '停留在当前页面',
              // })
            })
        }
      },
      // 如果 arr1 中存在任何一个元素不在 arr2 中，则返回 true；否则返回 false。
      areArraysEqual(arr1, arr2) {
        return arr1.length === arr2.length && !arr1.some((item) => !arr2.includes(item))
      },
    },
  }
</script>

<style lang="scss" scoped>
  .storeBasicInfoPage {
    .uploadspan {
      .uploadlogo {
        width: 100px;
        height: 100px;
        border: 1px dashed #d9d9d9;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        .el-icon-plus {
          font-size: 20px;
          color: #c0c4cc;
        }
        .el-upload__text {
          color: #c0c4cc;
          font-size: 12px;
        }
        .el-upload__tip {
          color: #c0c4cc;
          font-size: 12px;
          margin-top: 10px;
        }
        .license_image {
          width: 100px;
          height: 100px;
          border-radius: 4px;
        }
      }
    }
    .selectMap {
      color: #fff;
      background: #0071fe;
      border-radius: 0px 4px 4px 0px;
    }
    .weekSelect {
      display: flex;
      justify-content: space-between;
      margin-top: 15px;
      .el-select {
        width: 60%;
      }
    }
    .timeSelect {
      .el-date-editor.el-input {
        width: 130px;
        margin: 0 10px;
      }
    }
  }
</style>
