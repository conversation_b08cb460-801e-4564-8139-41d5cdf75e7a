<!--
 * @Author: liqian liqian@123
 * @Date: 2025-03-26 09:57:48
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-08-02 15:38:09
 * @FilePath: \qst-merchant-admin-2.0\src\views\enterpriseManagement\storeManagement\createStoreDialog.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->

<template>
  <!--新建门店 -->
  <el-dialog
    :title="title"
    width="50%"
    top="15vh"
    :visible.sync="createStoreDialog"
    :close-on-click-modal="false"
  >
    <div class="share" v-loading="loading">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="门店基础信息" name="first" v-if="activeName == 'first'">
          <storeBasicInfo
            :merchantList="merchantList"
            :operateFlag="operateFlag"
            @getAddress="getAddress"
            ref="storeBasicInfo"
            :tableData="tableData"
            :basicInfo="basicInfo"
          ></storeBasicInfo>
        </el-tab-pane>
        <el-tab-pane label="门店配置信息" name="second" v-if="activeName == 'second'">
          <storeConfigInfo
            :datailTemplateList="datailTemplateList"
            :tableData="tableData"
            ref="storeConfigInfo"
            :basicConfig="basicConfig"
            :basicInfo="basicInfo"
            :orderGroupList="orderGroupList"
          ></storeConfigInfo>
        </el-tab-pane>
      </el-tabs>

      <base-dialog-footer @cancel="cancel" @confirm="confirm"></base-dialog-footer>
    </div>
  </el-dialog>
</template>

<script>
  import { getClassificationList, getStockWarning } from '@/api/shop/stockWarning'
  import { getdeliveryList } from '@/api/shop/goodsSet/freightTemplate'
  import storeBasicInfo from './component/storeBasicInfo/storeBasicInfo.vue'
  import storeConfigInfo from './component/storeConfigInfo/storeConfigInfo.vue'
  import {
    createshopList,
    updateshopList,
    detailshopList,
    updateShopConfig,
  } from '@/api/enterpriseManagement/storeManagement'

  export default {
    name: 'createStoreDialog',
    components: {
      storeBasicInfo,
      storeConfigInfo,
    },
    props: {
      operateFlag: {
        type: String,
        default: 'ADD',
      },
      activeName: {
        type: String,
        default: 'first',
      },
      merchantList: {
        type: Array,
        default: () => [],
      },
    },
    watch: {
      activeName: {
        handler(newVal) {
          if (newVal == 'second') {
          }
        },
        immediate: true,
        deep: true,
      },
    },
    data() {
      return {
        title: '新建门店',
        createStoreDialog: false,
        dialogVisible: false,
        loading: true,
        tableData: [], //自提点数据
        basicInfo: {
          logo: '', // 门店logo
          name: '', // 门店名称
          mch_store_uid: '', // 所属商家
          trade_uid: '', // 所属行业
          trade_uid_name: [],
          manage_name: '', // 负责人
          contact_phone: '', // 联系电话
          region: '', //地址
          address: '', // 门店地址（若存在到店业务场景，请务必填写该内容）
          business_day: '', // 营业时间（若存在到店业务场景，请务必填写该内容）
          business_day_name: [],
          longitude: '', //经度
          latitude: '', //维度
          start_time: '',
          end_time: '',
        },
        basicConfig: {
          delivery_methods: [],
          delivery_methods_name: [], //
          shipping_fee_type: '',
          fixed_fee: '', // 固定运费
          delivery_id: '', // 运费模板
          local_id: '', // 自提点
          takes: [], //自提点选项
          stock_type: '1', //库存设置
          stock_tips_type: '',
          method: '', //预警方式：1 按照商品类型 2 按照商品分类
          physical: '0', //实物商品库存预警值
          coupon: '0', //卡券商品库存预警值
          virtual: '0', //虚拟商品库存预警值
          detail: [
            //method = 2 才会有该值下面的数组对象，method =1时 detail 传空数组或者不传
            {
              cate_id: '', // 商品分类id
              value: '', //预警值
            },
          ],
        },

        shop_id: '',
        datailTemplateList: [], //门店运费模版
        // 商品分类列表
        orderGroupList: [],
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab)
      },
      getDeliveryTemplete() {
        getdeliveryList({ shop_id: this.shop_id, limit: 100, page: 1 }).then((res) => {
          if (res.code == 200) {
            this.datailTemplateList = res.data.list
          }
        })
      },

      // 获取分类列表
      async getOrderGroupList() {
        try {
          const res = await getClassificationList({ type: 'list' })
          if (res.code == 200) {
            this.orderGroupList = res.data
          }
        } catch (error) {
          this.$message.error('获取分类列表失败')
        }
      },
      open(row) {
        this.createStoreDialog = true
        this.$nextTick(() => {
          if (this.operateFlag == 'ADD') {
            this.$refs.storeBasicInfo.$refs.basicInfo.resetFields()
            this.title = '新建门店'
            this.tableData = []
            for (let key in this.basicInfo) {
              this.basicInfo[key] = ''
            }
            this.basicInfo.start_time = '00:00:00'
            this.basicInfo.end_time = '23:59:59'
          } else {
            this.title = '编辑门店'
            this.shop_id = row.shop_id
            if (this.activeName == 'second') {
              this.$refs.storeConfigInfo.$refs.basicConfig.resetFields()
              this.getOrderGroupList()
              // this.getDeliveryTemplete()
            }
            this.getdetailshopList(row)
          }
        })
        this.loading = false
      },
      //获取门店详情数据
      getdetailshopList(row) {
        detailshopList({ shop_id: row.shop_id }).then((res) => {
          if (res.code == 200) {
            this.basicInfo = res.data.shop_info
            console.log(1)
            this.basicConfig = Object.assign(
              {},
              this.basicConfig,
              res.data.shop_config,
              res.data.warning_detail
            )
            this.basicConfig.method = this.basicConfig.method
              ? this.basicConfig.method.toString()
              : ''
            this.basicConfig.stock_type = this.basicConfig.stock_type
              ? this.basicConfig.stock_type.toString()
              : '1'
            console.log(this.basicConfig)
            this.tableData = res.data.shop_info.take_list
            this.transOriginData()
          }
        })
      },
      // 转换数据结构
      transOriginData() {
        let business_day =
          this.basicInfo.business_day != '' ? this.basicInfo.business_day.split(',') : []
        this.$set(this.basicInfo, 'business_day_name', business_day)
        // this.basicInfo.business_day_name = this.basicInfo.business_day.split(',')
        this.basicInfo.trade_uid_name = this.basicInfo.trade_uid.split(',')

        this.tableData.forEach((item) => {
          item.business_day_name = item.business_day != '' ? item.business_day.split(',') : []
          item.legal_code = ''
          if (this.areArraysEqual(item.business_day_name, ['1', '2', '3', '4', '5'])) {
            item.legal_code = 1
          }
          if (this.areArraysEqual(item.business_day_name, ['6', '0'])) {
            item.legal_code = 2
          }
          if (this.areArraysEqual(item.business_day_name, ['1', '2', '3', '4', '5', '6', '0'])) {
            item.legal_code = 3
          }
        })
      },
      // 如果 arr1 中存在任何一个元素不在 arr2 中，则返回 true；否则返回 false。
      areArraysEqual(arr1, arr2) {
        return arr1.length === arr2.length && !arr1.some((item) => !arr2.includes(item))
      },
      // 新建门店
      createshopListfn() {
        let params = {
          ...this.basicInfo,
          ...this.basicConfig,
          trade_uid: this.basicInfo.trade_uid_name.join(),
          take_list: this.tableData,
        }
        console.log(params)
        createshopList(params).then((res) => {
          if (res.code == 200) {
            this.$message.success('添加成功')
            this.createStoreDialog = false
            this.$emit('confirm')
          }
        })
      },
      //修改门店基础信息
      updateshopListfn() {
        let params = {
          ...this.basicInfo,
          ...this.basicConfig,
          take_list: this.tableData,
        }
        console.log(params)
        updateshopList(params).then((res) => {
          if (res.code == 200) {
            this.$message.success('修改成功')
            this.createStoreDialog = false
            this.$emit('confirm')
          }
        })
      },
      //修改门店配置信息
      updateShopConfigfn() {
        let params = {
          // ...this.basicInfo,
          shop_id: this.shop_id,
          ...this.basicConfig,
          // take_list: this.tableData,
        }
        if (this.basicConfig.method == 2) {
          delete params.physical
          delete params.coupon
          delete params.virtual
        }
        if (this.basicConfig.method == 1) {
          params.detail = []
        }
        console.log(params)
        updateShopConfig(params).then((res) => {
          if (res.code == 200) {
            this.$message.success('修改成功')
            this.createStoreDialog = false
            this.$emit('confirm')
          }
        })
      },
      //掉接口获取新的门店数据
      getAddress(data) {
        console.log(data)
        this.getdetailshopList(data)
      },
      //确定按钮
      confirm() {
        if (this.activeName == 'first') {
          this.$refs.storeBasicInfo.$refs.basicInfo.validate((valid) => {
            if (valid) {
              if (this.operateFlag == 'ADD') {
                this.createshopListfn()
              }
              if (this.operateFlag == 'Edit') {
                this.updateshopListfn()
              }
            } else {
              console.log('error submit!!')
              return false
            }
          })
        }
        if (this.activeName == 'second') {
          if (this.basicConfig.method == 2) {
            this.$refs.storeConfigInfo.$refs.basicInfo1.forEach((item, index) => {
              item.validate((valid) => {
                if (valid) {
                  this.$refs.storeConfigInfo.$refs.basicConfig.validate((valid) => {
                    if (valid) {
                      if (this.operateFlag == 'ADD') {
                        this.createshopListfn()
                      }
                      if (this.operateFlag == 'Edit') {
                        this.updateShopConfigfn()
                      }
                    } else {
                      console.log('error submit!!')
                      return false
                    }
                  })
                } else {
                  console.log('error submit!!')
                  return false
                }
              })
            })
          } else {
            this.$refs.storeConfigInfo.$refs.basicConfig.validate((valid) => {
              if (valid) {
                if (this.operateFlag == 'ADD') {
                  this.createshopListfn()
                }
                if (this.operateFlag == 'Edit') {
                  this.updateShopConfigfn()
                }
              } else {
                console.log('error submit!!')
                return false
              }
            })
          }
        }
      },
      cancel() {
        this.createStoreDialog = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  html body {
    .el-dialog {
      top: 10% !important;
      transform: none;
      left: 0 !important;
      margin: auto !important;
    }
    .el-dialog__body {
      padding: 10px 20px;
    }
  }
</style>
