<!--
 * @Author: liqian liqian@123
 * @Date: 2025-04-02 11:43:19
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-28 14:48:59
 * @FilePath: \qst-merchant-admin-2.0\src\views\enterpriseManagement\storeManagement\index.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <el-card>
      <base-form :tableForm="tableForm" :formArray="formArray" @searchForm="searchForm"></base-form>
      <div class="operate">
        <el-button type="primary" icon="el-icon-plus" @click="addStore">新建门店</el-button>
      </div>
      <base-table
        :tableColumn="tableColumn"
        :tableRequest="getshopList"
        :tableForm="tableForm"
        ref="tableRef"
      >
        <template #logo="{ scope }">
          <el-image
            class="delfate-image"
            fit="cover"
            :src="scope.row.logo"
            :preview-src-list="[scope.row.logo]"
          ></el-image>
        </template>
        <template #is_forbidden="{ scope }">
          <div class="flex">
            <el-switch
              @change="changeStatus(scope)"
              active-value="N"
              inactive-value="Y"
              v-model="scope.row['is_forbidden']"
            ></el-switch>
            <span>{{ scope.row.is_forbidden == 'Y' ? '停用' : '启用' }}</span>
          </div>
        </template>
        <template #operate="{ scope }">
          <div class="public-operate-btn">
            <el-button size="mini" type="text" @click="openDetail(scope.row)">修改基础</el-button>
            <el-button size="mini" type="text" @click="openConfigDetail(scope.row)">
              修改配置
            </el-button>
            <el-button size="mini" type="text" @click="handelDelete(scope.row)">删除</el-button>
          </div>
        </template>
      </base-table>
    </el-card>
    <createStoreDialog
      :activeName="activeName"
      :merchantList="merchantList"
      :operateFlag="operateFlag"
      ref="createStore"
      @confirm="confirm"
    ></createStoreDialog>
  </div>
</template>

<script>
  import { getMerchantListByAdminIdApi } from '@/api/enterpriseManagement'
  import { showPhoneApi } from '@/api/enterpriseManagement'
  import {
    getshopList,
    deleteshopList,
    enterShopSelectListApi,
    changeupdateStatus,
    createShopValidate,
  } from '@/api/enterpriseManagement/storeManagement'
  import createStoreDialog from './createStoreDialog.vue'
  export default {
    name: 'storeManagement',
    components: {
      createStoreDialog,
    },
    data() {
      return {
        operateFlag: 'ADD',
        merchantList: [], //所属商家
        formArray: [
          {
            label: '门店编码',
            type: 'input',
            key: 'shop_id',
            placeholder: '请输入门店编码',
          },
          {
            label: '门店名称',
            type: 'input',
            key: 'name',
            placeholder: '请输入门店名称',
          },
          {
            label: '所属商家',
            type: 'select',
            key: 'mch_store_uid',
            placeholder: '全部',
            options: [],
          },

          {
            label: '负责人/电话',
            type: 'input',
            key: 'manage_name_or_contact_phone',
            placeholder: '请输入负责人或电话',
          },
          {
            label: '状态',
            type: 'select',
            key: 'status',
            placeholder: '全部',
            options: [],
          },
          {
            label: '创建时间',
            type: 'time',
            key: 'time',
            timeKey: ['createAtStart', 'createAtEnd'],
          },
        ],

        tableColumn: [
          {
            label: '门店编码',
            prop: 'shop_id',
            width: '100',
          },
          {
            label: '门店logo',
            prop: 'logo',
            type: 'customize',
            width: '100',
          },
          {
            label: '门店名称',
            prop: 'name',
          },

          {
            label: '所属商家',
            prop: 'merchant_name',
          },
          {
            label: '所属行业',
            prop: 'trade_name',
          },
          {
            label: '负责人',
            prop: 'manage_name',
          },
          {
            label: '联系电话',
            prop: 'contact_phone',
            // fun: showPhoneApi,
            // funKey: {
            //   storeUid: 'storeUid',
            // },
            // type: 'customizePhone',
            // width: '110px',
          },
          {
            label: '门店地址',
            prop: 'address',
            width: '200px',
          },
          {
            label: '创建时间',
            prop: 'created_at',
            width: '200px',
          },

          {
            label: '状态',
            prop: 'is_forbidden',
            type: 'customize',
          },
          {
            label: '操作',
            prop: 'operate',
            type: 'customize',
            width: '200px',
          },
        ],
        getshopList,
        tableForm: {
          mch_store_uid: '',
          status: '',
        },
        activeName: 'first',
      }
    },
    created() {
      this.enterShopSelectListFn()
      this.getMerchantList()
    },
    methods: {
      // 初始化
      enterShopSelectListFn() {
        enterShopSelectListApi().then((res) => {
          let merchant = res.data.merchant.map((item) => {
            return {
              label: item.label,
              value: item.value,
            }
          })
          let status = res.data.is_forbidden.map((item) => {
            return {
              label: item.label,
              value: item.value,
            }
          })
          this.formArray[2].options = [{ label: '全部', value: '' }, ...merchant]
          this.formArray[4].options = [{ label: '全部', value: '' }, ...status]
        })
      },
      //所属商家
      getMerchantList() {
        getMerchantListByAdminIdApi().then((res) => {
          if (res.code == 200) {
            let merchant = res.data.map((item) => {
              return {
                label: item.merchant_name,
                value: item.mch_store_uid,
              }
            })
            this.merchantList = merchant || []
          }
        })
      },
      // 表单搜索事件
      searchForm(form) {
        this.tableForm = Object.assign({}, this.tableForm, form)
      },
      // 删除
      handelDelete(row) {
        this.$confirm('是否删除该门店?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            deleteshopList({ shop_id: row.shop_id }).then((res) => {
              if (res.code == 200) {
                this.$message.success('删除成功')
                this.$refs.tableRef.tableRequestFn()
              }
            })
          })
          .catch((action) => {
            // this.$message({
            //   type: 'info',
            //   message: action === 'cancel' ? '取消删除' : '',
            // })
          })
      },
      //切换模版状态
      changeStatus(scope) {
        let statusTip = scope.row.is_forbidden == 'Y' ? '停用' : '启用'
        this.$confirm(`是否${statusTip}该商铺?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            changeupdateStatus({
              shop_id: scope.row.shop_id,
              is_forbidden: scope.row.is_forbidden,
            }).then((res) => {
              if (res.code == 200) {
                this.$message.success(`${statusTip}成功`)
                this.$refs.tableRef.tableRequestFn()
              } else {
                this.$refs.tableRef.tableRequestFn()
              }
            })
          })
          .catch((action) => {
            this.$refs.tableRef.tableRequestFn()
            // this.$message({
            //   type: 'info',
            //   message: action === 'cancel' ? '取消切换' : '停留在当前页面',
            // })
          })
      },
      // 查看详情
      openDetail(row) {
        this.operateFlag = 'Edit'
        this.activeName = 'first'
        this.$refs.createStore.open(row)
      },
      //修改配置信息
      openConfigDetail(row) {
        this.operateFlag = 'Edit'
        this.activeName = 'second'
        this.$refs.createStore.open(row)
      },
      // 新增
      addStore() {
        createShopValidate().then((res) => {
          if (res.data.length == 0) {
            this.operateFlag = 'ADD'
            this.activeName = 'first'
            this.$refs.createStore.open()
          }
        })
      },
      // 新增确定返回   更新表格数据
      confirm() {
        this.$refs.tableRef.tableRequestFn()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .operate {
    margin-top: 20px;
  }
  .del-color {
    color: #6e6e7a;
  }
  .el-dialog__wrapper {
    overflow: auto !important;
  }
</style>
