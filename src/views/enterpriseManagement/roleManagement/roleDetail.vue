<template>
  <div>
    <el-card>
      <template #header>
        <div class="card-header">
          <div></div>
          <div>角色基本信息</div>
        </div>
      </template>
      <div
        :class="{'role-form': !(collapse === 'false' || collapse === false)}"
        class="role-header"
      >
        <el-form ref="roleForm" :model="role" :inline="true" :rules="rules">
          <el-form-item label="角色名称" prop="rolesName">
            <el-input
              maxlength="20"
              :disabled="!!id"
              v-model="role.rolesName"
              placeholder="请输入角色名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="参考角色">
            <el-select
              style="min-width: 300px"
              v-model="role.referenceRoleUid"
              placeholder="请选择参考角色，基于该角色配置进行修改"
              @change="rolesCreateGetRoule(role.referenceRoleUid)"
            >
              <el-option
                v-for="item in roleList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="保存后自动启用">
            <el-switch :disabled="!!id" v-model="role.status"></el-switch>
          </el-form-item>

          <el-form-item label="角色描述">
            <el-input v-model="role.rolesDescribe" type="textarea" style="width: 300px;"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <el-card class="card-model">
      <template #header>
        <div class="card-header">
          <div></div>
          <div>权限分配</div>
        </div>
      </template>
      <div>
        <base-form :formArray="roleTableForm" @searchForm="searchForm" :labelWidth="'78px'"></base-form>

        <div class="border-header">
          <el-checkbox
            class="check-select"
            @change="setAll"
            v-model="checked"
            :indeterminate="isIndeterminate"
          ></el-checkbox>
          <div class="flex-b border-header-main">
            <div class>功能权限</div>
            <div>类型</div>
          </div>
        </div>
        <el-tree
          :data="routerList"
          show-checkbox
          node-key="id"
          default-expand-all
          :expand-on-click-node="false"
          ref="tree"
          :filter-node-method="filterNode"
          @check-change="checkChange"
        >
          <div class="border-td flex-b" slot-scope="{  data }">
            <div class="border-td-title">{{data.meta.title}}</div>
            <div class="border-td-right">{{ data.type == 2 ? '按钮' : '菜单' }}</div>
          </div>
        </el-tree>
      </div>
      <div class="role-btn">
        <el-button @click="goBack">取消</el-button>
        <el-button
          v-if="type == 'add' || type == 'edit'"
          @click="sumbitFn"
          type="primary"
        >{{type == 'edit'? '修改' : '提交'}}</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import {
  rolesCreateGetRouleApi,
  rolesSelectListByCreateApi,
  rolesCreateApi,
  rolesDetailApi,
  rolesUpdateApi,
} from '@/api/role'
export default {
  name: 'RoleDetail',
  data() {
    return {
      allNum: 0,
      id: '',
      role: {
        referenceRoleUid: null,
        status: true,
        rolesDescribe: '',
        rolesName: '',
      },
      roleList: [],
      value: '',

      rules: {
        rolesName: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
      },
      searchFormData: {
        type: '',
      },

      roleTableForm: [
        {
          label: '功能权限',
          type: 'input',
          placeholder: '请输入功能权限',
          key: 'rolesName',
        },
        // {
        //   label: '类型',
        //   type: 'select',
        //   options: [
        //     {
        //       label: '管理员',
        //       value: 1,
        //     },
        //     {
        //       label: '普通用户',
        //       value: 2,
        //     },
        //   ],
        // },
      ],
      checked: false,
      isIndeterminate: false,

      routerList: [],
      type: '',
      uid: '',
    }
  },
  computed: {
    ...mapGetters({
      collapse: 'settings/collapse',
    }),
  },
  mounted() {
    this.id = this.$route.query.id
    // 获取菜单列表接口
    this.rolesCreateGetRoule()
    // 获取角色列表接口
    this.rolesSelectListByCreateFn()
    this.type = this.$route.query.type
    // this.rolesSelectListByCreateFn()
    if (this.$route.query.id) {
      this.rolesDetailFn(this.$route.query.id)
    }
  },
  methods: {
    // 角色详情接口
    rolesDetailFn(rolesUid) {
      rolesDetailApi({
        rolesUid,
      }).then((res) => {
        if (res.code == 200) {
          let role = res.data.role
          this.uid = role?.uid
          this.role = {
            rolesName: role?.name,
            status: role?.status == 2,
            rolesDescribe: role?.describe,
          }
          this.selectInit(res.data.currentRolesid)
        }
      })
    },
    // 角色列表接口
    rolesSelectListByCreateFn() {
      rolesSelectListByCreateApi().then((res) => {
        if (res.code == 200) {
          this.roleList = [
            {
              label: '无参考角色',
              value: '',
            },
            ...res.data.rolesList.map((item) => ({ label: item.name, value: item.uid })),
          ]
        }
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.meta.title.indexOf(value) !== -1
    },
    // 多选按钮
    setAll() {
      if (this.checked) {
        this.$refs.tree.setCheckedNodes(this.routerList)
      } else {
        this.$refs.tree.setCheckedKeys([])
      }
    },
    // 选择单节点
    checkChange(data, checked) {
      let currentKey = this.$refs.tree.getCheckedNodes(false, true)
      if (currentKey.length == 0) {
        this.checked = false
        this.isIndeterminate = false
      } else {
        this.isIndeterminate = currentKey.length != this.allNum
        this.checked = currentKey.length == this.allNum
      }
    },
    // 菜单列表接口
    rolesCreateGetRoule(referenceRoleUid) {
      rolesCreateGetRouleApi({
        referenceRoleUid,
      }).then((res) => {
        if (res.code == 200) {
          this.routerList = this.routerListEdit(res.data.tree)
          this.allNum = this.getAllNodes(this.routerList)
          if (referenceRoleUid) {
            this.$refs.tree.setCheckedKeys([])
            this.selectInit(res.data.referenceRoleUids)
          }
        }
      })
    },
    // 初始化选中状态
    selectInit(referenceRoleUids) {
      console.log(this.routerList, referenceRoleUids)
      let ids = this.selectChecked(this.routerList, referenceRoleUids, [])
      this.$refs.tree.setCheckedKeys(ids)
      this.$nextTick((res) => {
        let currentKey = this.$refs.tree.getCheckedNodes(false, true)
        this.isIndeterminate = currentKey.length != this.allNum && currentKey.length != 0
        this.checked = currentKey.length == this.allNum
      })
    },
    selectChecked(list, referenceRoleUids, ids = []) {
      list.map((item) => {
        console.log(!item.hidden && referenceRoleUids.includes(item.id))
        if (item.children && item.children.length > 0) {
          ids = this.selectChecked(item.children, referenceRoleUids, ids)
        } else if (!item.hidden && referenceRoleUids.includes(item.id)) {
          ids.push(item.id)
        }
      })
      return ids
    },

    // 获取所有节点数量
    getAllNodes(list, allNum = 0) {
      list.map((item) => {
        allNum++
        if (item.children && item.children.length > 0) {
          allNum = this.getAllNodes(item.children, allNum)
        }
      })
      return allNum
    },
    // 编辑菜单列表接口
    routerListEdit(list) {
      return list.map((item) => {
        if (item.component == 'Layout') {
          return this.routerListComputed(item, item.children)
        } else if (item.component == '') {
          return {
            ...item,
            children: this.routerListEdit(item.children),
          }
        }
      })
    },
    // 递归菜单列表接口
    routerListComputed(item, children) {
      if (children.length == 0) {
        delete item.children
      }
      let ids = []
      let idsNum = 0
      let childrenItem = {}
      children.map((items) => {
        if (items.meta.title == item.meta.title || items.hidden) {
          if (items.meta.title == item.meta.title) {
            childrenItem = items
          }
          ids.push(items.id)
        } else if (!items.hidden) {
          idsNum++
        }
      })
      if (idsNum == 0) {
        if (childrenItem.children && childrenItem.children.length > 0) {
          item.children = childrenItem.children
        } else {
          delete item.children
        }
        item.ids = ids
      }
      return item
    },
    // 提交 -- 修改
    sumbitFn() {
      this.$refs.roleForm.validate((valid) => {
        if (valid) {
          let currentKey = this.$refs.tree.getCheckedNodes(false, true)
          let ids = []
          currentKey.map((item) => {
            ids.push(item.id)
            if (item.ids) ids.push(...item.ids)
          })
          if (ids.length == 0) {
            this.$message.error('请选择权限')
            return
          }
          let api = this.uid ? rolesUpdateApi : rolesCreateApi
          let params = {}
          if (this.uid) {
            params = {
              rolesUid: this.uid,
            }
          }
          api({
            rules: ids.join(','),
            rolesName: this.role.rolesName,
            rolesDescribe: this.role.rolesDescribe,
            status: this.role.status ? 2 : 1,
            ...params,
          }).then((res) => {
            if (res.code == 200) {
              this.$message.success(this.uid ? '修改成功' : '创建成功')
              this.goBack()
            }
          })
        } else {
          return false
        }
      })
    },

    goBack() {
      this.$router.go(-1)
    },
    searchForm(form) {
      this.$refs.tree.filter(form.rolesName)
    },
  },
}
</script>

<style lang="scss" scoped>
.card-model {
  min-height: 600px;
  padding-bottom: 250px;
  .role-btn {
    position: fixed;
    right: calc(#{$base-right-content-width} / 2);
    bottom: 100px;
    transform: translateX(50%);
    .el-button {
      margin-left: 20px;
    }
  }
}

// tree
.border-header {
  background: #f5f7fa;
  box-shadow: inset 0px -1px 0px 0px #ebeef5, inset 0px -1px 0px 0px #ebeef5;
  display: flex;
  height: 40px;
  line-height: 40px;
  align-items: center;
  .check-select {
    width: 80px;
    padding-left: 24px;
  }
}

.border-td {
  flex: 1;
  &-right {
    padding-right: 80px;
  }
}
.border-header-main {
  flex: 1;
  padding-right: 68px;
}

::v-deep {
  .el-tree-node__content {
    position: relative;
  }
  .el-tree-node__content > label.el-checkbox {
    position: absolute;
    left: 24px;
  }
  .el-tree-node__content > .el-tree-node__expand-icon {
    margin-left: 100px;
  }

  .el-tree-node__content {
    padding: 10px 0;
    border-bottom: 1px solid #f5f7fa;
  }

  @media (max-width: 768px) {
    .role-header .el-input__inner {
      width: 300px;
    }
    .role-header .el-textarea__inner {
      width: 300px;
    }
  }
  @media (min-width: 1250px) {
    .role-form .el-input__inner {
      width: 300px;
    }
    .role-form .el-textarea__inner {
      width: 300px;
    }
  }
  @media (min-width: 1450px) {
    .role-header .el-input__inner {
      width: 300px;
    }
    .role-header .el-textarea__inner {
      width: 300px;
    }
  }
}
</style>