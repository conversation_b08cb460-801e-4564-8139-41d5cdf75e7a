<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-03-06 17:01:29
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-26 10:07:21
 * @FilePath: /qst-merchant-admin-2.0/src/views/permissionManagement/role/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <el-card>
      <base-form :tableForm="tableForm" :formArray="formArray" @searchForm="searchForm"></base-form>
      <div class="operate">
        <el-button type="primary" @click="addRole" icon="el-icon-plus">新建</el-button>
      </div>
      <base-table
        :tableColumn="tableColumn"
        :tableRequest="rolesListApi"
        :tableForm="tableForm"
        rowKey="id"
        ref="baseTable"
      >
        <template #merchantRoleNum1="{scope}">
          <span>{{ scope.row.merchantRoleNum }}</span>
          <!-- <el-button type="text" @click="viewStaff(scope.row)">{{scope.row.merchantRoleNum}}</el-button> -->
        </template>
        <template #operate="{ scope }">
          <div class="public-operate-btn" v-if="scope.row.roleTypeNum == 1">
            <!-- 平台展示启用 -->
            <el-button size="mini" @click="viewDetaile(scope.row)" type="text">查看</el-button>
          </div>
          <div class="public-operate-btn" v-else>
            <!-- 商户端--启用 -->
            <el-button
              @click="rolesDeactivateFn(scope.row, 3)"
              v-if="scope.row.status == 2"
              size="mini"
              type="text"
            >停用</el-button>
            <el-button
              v-if="scope.row.status == 2"
              @click="editRole(scope.row)"
              size="mini"
              type="text"
            >修改</el-button>

            <!-- 商户端--停用 -->
            <el-button
              @click="rolesDeactivateFn(scope.row, 2)"
              v-if="scope.row.status == 3 || scope.row.status == 1"
              size="mini"
              type="text"
            >启用</el-button>
            <el-button
              v-if="scope.row.status == 3|| scope.row.status == 1"
              @click="editRole(scope.row)"
              size="mini"
              type="text"
            >修改</el-button>
            <el-button
              @click="delRole(scope.row)"
              v-if="scope.row.status == 3|| scope.row.status == 1"
              size="mini"
              type="text"
            >删除</el-button>
          </div>
        </template>
      </base-table>
    </el-card>
  </div>
</template>

<script>
import { rolesListApi, rolesDeactivateApi, rolesSelectListApi, rolesDeleteApi } from '@/api/role'
export default {
  name: 'Role',
  data() {
    return {
      formArray: [
        {
          label: '角色名称',
          type: 'input',
          key: 'name',
          placeholder: '请输入角色名称',
        },
        {
          label: '角色状态',
          type: 'select',
          key: 'status',
          placeholder: '请选择角色状态',
          options: [],
        },
        {
          label: '角色类型',
          type: 'select',
          key: 'useType',
          placeholder: '请选择角色类型',
          options: [],
        },
      ],
      tableColumn: [
        {
          label: '角色名称',
          prop: 'name',
        },
        {
          label: '角色描述',
          prop: 'describe',
          width: '200px',
        },
        {
          label: '用户数',
          prop: 'merchantRoleNum1',
          type: 'customize',
        },
        {
          label: '状态',
          prop: 'status',
          type: 'template',
          stateType: 'select',
          stateObj: {
            1: '新建',
            2: '启用',
            3: '停用',
          },
        },
        {
          label: '角色类型',
          prop: 'roleType',
        },
        {
          label: '角色使用对象',
          prop: 'use_type',
          width: '120px',
        },
        {
          label: '操作',
          prop: 'operate',
          type: 'customize',
          width: '140px',
        },
      ],
      rolesListApi,
      tableForm: {
        status: '',
        useType: '',
      },
    }
  },
  mounted() {
    this.rolesSelectListFn()
  },
  methods: {
    rolesSelectListFn() {
      rolesSelectListApi().then((res) => {
        if (res.code == 200) {
          let status = res.data.status.map((item) => ({
            label: item.name,
            value: item.id,
          }))
          this.formArray[1].options = [
            {
              label: '全部',
              value: '',
            },
            ...status,
          ]

          let roleTypeNum = res.data.roleTypeNum.map((item) => ({
            label: item.name,
            value: item.id,
          }))
          console.log(this.formArray)
          this.formArray[2].options = [
            {
              label: '全部',
              value: '',
            },
            ...roleTypeNum,
          ]
        }
      })
    },

    // 表单搜索事件
    searchForm(form) {
      this.tableForm = Object.assign({}, this.tableForm, form)
    },

    // 停用角色
    rolesDeactivateFn(row, status) {
      rolesDeactivateApi({
        rolesUid: row.uid,
        status,
      }).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: '修改成功',
          })
          this.$refs.baseTable.tableRequestFn()
        }
      })
    },

    // 删除角色
    delRole(row) {
      this.$confirm('是否删除该角色?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          rolesDeleteApi({ rolesUid: row.uid }).then((res) => {
            if (res.code == 200) {
              this.$message({
                type: 'success',
                message: '删除成功!',
              })
              this.$refs.baseTable.tableRequestFn()
            }
          })
        })
        .catch(() => {})
    },

    viewDetaile(row) {
      this.$router.push({
        path: '/roleManagement/roleDetail',
        query: {
          id: row.uid,
          type: 'view',
        },
      })
    },
    // 编辑角色信息
    editRole(row) {
      this.$router.push({
        path: '/roleManagement/roleDetail',
        query: {
          id: row.uid,
          type: 'edit',
        },
      })
    },

    addRole() {
      this.$router.push({
        path: '/roleManagement/roleDetail?type=add',
      })
    },

    // 查看角色用户列表
    viewStaff(row) {
      this.$router.push({
        path: '/staffAccount/index',
        query: {
          id: row.uid,
        },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.operate {
  margin-top: 20px;
}
</style>