<!--
 * @Author: liqian liqian@123
 * @Date: 2025-04-02 11:43:19
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-31 14:15:29
 * @FilePath: \qst-merchant-admin-2.0\src\views\enterpriseManagement\storeAddress\index.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <el-card>
      <base-form :tableForm="tableForm" :formArray="formArray" @searchForm="searchForm"></base-form>
      <div class="operate">
        <el-button type="primary" icon="el-icon-plus" @click="addStoreAddress">新建地址</el-button>
      </div>
      <base-table
        :tableColumn="tableColumn"
        :tableRequest="getaddressList"
        :tableForm="tableForm"
        ref="tableRef"
      >
        <template #shop_list="{ scope }">
          <span v-for="item in scope.row.shop_list">{{ item.name }}</span>
        </template>

        <template #business_day="{ scope }">
          <div v-if="scope.row.business_day.length">
            <span
              v-if="getShowDay1(scope.row.business_day, that)"
            >{{ scope.row.business_day | getShowDay(that) }}</span>
            <div v-else>
              <span
                v-for="(item, index) in scope.row.business_day"
                :key="index"
              >{{ weekTransName[item] }}</span>
            </div>
          </div>

          <div>
            <span>{{ scope.row.start_time }} - {{ scope.row.end_time }}</span>
          </div>
        </template>

        <template #is_forbidden="{ scope }">
          <span style="color: rgb(82, 196, 26)" v-if="scope.row.is_forbidden == 'N'">启用</span>
          <span style="color: #ff4d4f" v-if="scope.row.is_forbidden == 'Y'">停用</span>
        </template>

        <template #operate="{ scope }">
          <div class="public-operate-btn">
            <el-button size="mini" type="text" @click="openDetail(scope.row)">查看详情</el-button>
            <!-- <el-button size="mini" type="text" @click="changeShop(scope.row)">分配店铺</el-button> -->
            <el-button
              v-if="scope.row.is_forbidden == 'N'"
              size="mini"
              type="text"
              @click="changeStatus(scope.row, 'Y')"
            >停用</el-button>
            <el-button
              v-if="scope.row.is_forbidden == 'Y'"
              size="mini"
              type="text"
              @click="changeStatus(scope.row, 'N')"
            >启用</el-button>
            <el-button size="mini" type="text" @click="handelDelete(scope.row)">删除</el-button>
          </div>
        </template>
      </base-table>
    </el-card>
    <!-- 删除失败弹框-->
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="showDeleteDialog"
      width="480px"
    >
      <template slot="title">
        <span class="title">删除提示</span>
      </template>
      <div v-if="this.deleteflag == 1">
        <div @click="jumpLookOrder">
          <span v-html="highlightedContent"></span>
        </div>
      </div>
      <div v-if="this.deleteflag == 2">
        <div @click="jumpEditOrder">
          <span v-html="highlightedContent"></span>
        </div>
      </div>
      <div v-if="this.deleteflag == 3">
        <div @click="copyOrder">
          <span v-html="highlightedContent"></span>
        </div>
      </div>
      <!-- <base-dialog-footer confirmText="确认" @cancel="showDeleteDialog = false" @confirm="confirmHXBtn"></base-dialog-footer> -->
    </el-dialog>
    <storeAddressDialog
      :operateFlag="operateFlag"
      :title="title"
      ref="createStoreAddress"
      @confirm="confirm"
    ></storeAddressDialog>
  </div>
</template>

<script>
import {
  getaddressList,
  changeupdateStatus,
  deleteAddressList,
  getselectList,
} from '@/api/enterpriseManagement/storeAddress'
import storeAddressDialog from './component/storeAddressDialog.vue'

export default {
  name: 'storeAddress',
  components: {
    storeAddressDialog,
  },
  data() {
    return {
      that: this,
      operateFlag: 'ADD',
      showDeleteDialog: false,
      deleteflag: '',
      weekTransName: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
      formArray: [
        {
          label: '地址类型',
          type: 'select',
          key: 'type',
          placeholder: '全部',
          options: [],
        },
        // {
        //   label: '可用店铺',
        //   type: 'store',
        //   key: 'shop_id',
        //   placeholder: '请选择可用店铺',
        //   options: [],
        //   callback: (e) => {},
        // },

        {
          label: '地址名称',
          type: 'input',
          key: 'name',
          placeholder: '请输入地址名称',
        },
        {
          label: '联系人',
          type: 'input',
          key: 'manage_name',
          placeholder: '请输入联系人',
        },
        {
          label: '联系电话',
          type: 'input',
          key: 'phone',
          placeholder: '请输入联系电话',
        },
      ],

      tableColumn: [
        {
          label: '地址名称',
          prop: 'name',
        },
        {
          label: '地址类型',
          prop: 'type',
        },
        {
          label: '详细地址',
          prop: 'address',
          width: '300px',
        },

        {
          label: '联系人',
          prop: 'manage_name',
        },
        {
          label: '联系电话',
          prop: 'phone',
          // fun: showPhoneApi,
          // funKey: {
          //   storeUid: 'storeUid',
          // },
          // type: 'customizePhone',
          // width: '110px',
        },
        // {
        //   label: '可用店铺',
        //   prop: 'shop_list',
        //   type: 'customize',
        // },

        {
          label: '营业时间',
          prop: 'business_day',
          type: 'customize',
          width: '200px',
        },
        {
          label: '状态',
          prop: 'is_forbidden',
          type: 'customize',
        },
        {
          label: '操作',
          prop: 'operate',
          type: 'customize',
          width: '150px',
          fixed: 'right',
        },
      ],
      getaddressList,
      tableForm: {
        mch_store_uid: '',
        status: '',
      },
      title: '',
      originalContent: '',
      copyErrorMsg: '', //复制的错误单号
      clickRowItem: {},
    }
  },
  computed: {
    highlightedContent() {
      if ([1, 2].includes(this.deleteflag)) {
        return this.originalContent.replace(
          /点击跳转/g,
          `<span style="color:red; text-decoration: underline;cursor:pointer">点击跳转</span>`
        )
      }
      if (this.deleteflag == 3) {
        return this.originalContent.replace(
          /点击复制/g,
          `<span style="color:red; text-decoration: underline;cursor:pointer">点击复制</span>`
        )
      }
    },
  },
  created() {
    this.enterShopSelectListFn()
  },
  filters: {
    getShowDay(data, that) {
      // 1 工作日 2周末 3全部时间
      if (that.areArraysEqual(data, ['1', '2', '3', '4', '5'])) {
        return '工作日'
      }
      if (that.areArraysEqual(data, ['6', '0'])) {
        return '周末'
      }
      if (that.areArraysEqual(data, ['1', '2', '3', '4', '5', '6', '0'])) {
        return '全部时间'
      }
    },
  },
  methods: {
    // 初始化
    enterShopSelectListFn() {
      getselectList().then((res) => {
        let addressList = res.data.type.map((item) => {
          return {
            label: item.value,
            value: item.label,
          }
        })
        this.formArray[0].options = [{ label: '全部', value: '' }, ...addressList]
      })
    },
    // 表单搜索事件
    searchForm(form) {
      this.tableForm = Object.assign({}, this.tableForm, form)
    },
    // 删除
    handelDelete(row) {
      this.$confirm('是否删除该地址?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          deleteAddressList({ id: row.id }).then((res) => {
            if (res.code == 200) {
              if (res.data.length == 0) {
                this.$message.success('删除成功')
                this.$refs.tableRef.tableRequestFn()
              } else {
                this.showDeleteDialog = true
                this.originalContent = res.data.msg
                this.deleteflag = res.data.type
                this.copyErrorMsg = res.data?.ext
              }
            }
          })
        })
        .catch((action) => {})
    },
    jumpLookOrder() {
      this.$router.push({
        path: `/order/orderList`,
        query: {
          id: this.clickRowItem.id,
        },
      })
    },
    //跳转承运商
    jumpEditOrder() {
      this.$router.push({
        path: '/waybill/index',
      })
    },
    async copyOrder() {
      // 创建临时输入框来复制文本
      const textarea = document.createElement('textarea')
      textarea.value = this.copyErrorMsg
      document.body.appendChild(textarea)
      textarea.select()

      try {
        await navigator.clipboard.writeText(this.copyErrorMsg)
        this.$message.success('复制成功！')
      } catch (err) {
        this.$message.error('复制失败，请手动复制')
      }

      document.body.removeChild(textarea)
    },
    //切换模版状态
    changeStatus(row, flag) {
      let statusTip = flag == 'N' ? '启用' : '停用'
      this.$confirm(`是否${statusTip}该地址?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          changeupdateStatus({ id: row.id, is_forbidden: flag }).then((res) => {
            if (res.code == 200) {
              this.$message.success(`${statusTip}成功`)
              this.$refs.tableRef.tableRequestFn()
            } else {
              this.$refs.tableRef.tableRequestFn()
            }
          })
        })
        .catch((action) => {
          this.$refs.tableRef.tableRequestFn()
        })
    },
    // 查看详情
    openDetail(row) {
      this.operateFlag = 'Edit'
      this.title = '地址详情'
      this.$nextTick(() => {
        this.$refs.createStoreAddress.open(row, 'Edit')
      })
    },
    // 新增
    addStoreAddress() {
      this.operateFlag = 'ADD'
      this.title = '新建地址'
      this.$nextTick(() => {
        this.$refs.createStoreAddress.open()
      })
    },
    // 新增确定返回   更新表格数据
    confirm() {
      this.$refs.tableRef.tableRequestFn()
    },
    // 如果 arr1 中存在任何一个元素不在 arr2 中，则返回 true；否则返回 false。
    areArraysEqual(arr1, arr2) {
      return arr1.length === arr2.length && !arr1.some((item) => !arr2.includes(item))
    },
    getShowDay1(data, that) {
      // 1 工作日 2周末 3全部时间
      if (that.areArraysEqual(data, ['1', '2', '3', '4', '5'])) {
        return '工作日'
      }
      if (that.areArraysEqual(data, ['6', '0'])) {
        return '周末'
      }
      if (that.areArraysEqual(data, ['1', '2', '3', '4', '5', '6', '0'])) {
        return '全部时间'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.operate {
  margin-top: 20px;
}
.el-dialog__body .highlight::v-deep {
  text-decoration: dashed;
  color: red;
}
</style>
