<!--
 * @Author: liq<PERSON> liqian@123
 * @Date: 2025-04-09 16:30:31
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-06-25 14:10:38
 * @FilePath: \qst-merchant-admin-2.0\src\views\enterpriseManagement\storeAddress\component\selectAddressMap.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-dialog width="60%" title="选择地址" :close-on-click-modal="false" :visible.sync="isDialog" append-to-body>
    <iframe ref="myIframe" width="100%" height="100%" style="border: none; width: 100%; height: 500px" :src="map_src"></iframe>
    <el-form :rules="infoRule" label-width="180px" :model="basicInfo" ref="basicInfo" :label-position="labelPosition">
      <el-form-item label="已选中地址" label-width="100px" prop="region" clearable>
        <el-input disabled v-model="basicInfo.region" placeholder="请选择地址"></el-input>
      </el-form-item>
      <el-form-item label="详细地址" label-width="100px" prop="address" clearable>
        <el-input v-model="basicInfo.address" placeholder="请输入详细地址，如楼层、门牌号等"></el-input>
      </el-form-item>
    </el-form>
    <base-dialog-footer @cancel="cancel" @confirm="confirm"></base-dialog-footer>
  </el-dialog>
</template>

<script>
  export default {
    props: {
      basicInfoList: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        isDialog: false, // 控制模态框
        labelPosition: 'top',
        // map_src:
        //   'https://mapapi.qq.com/web/mapComponents/locationPicker/v/index.html?search=1&type=1&key=KD2BZ-NNTKT-WE5XC-LNGCZ-IRLJ5-G3BB4&referer=location',
        map_src: '',
        form: {
          //省市区ID
          id_area: [0, 0, 0],
          address: '',
          lng: '',
          lat: '',
        },
        basicInfo: {
          longitude: '', //经度
          latitude: '', //维度
          region: '', //省市区
          address: '', //详细地址
        },
        infoRule: {
          region: [{ required: true, message: '请选择地址', trigger: 'blur' }],
          address: [{ required: true, message: '请输入详细地址，如楼层、门牌号等', trigger: 'blur' }],
        },
      }
    },
    created() {
      this.getInfo()
    },
    methods: {
      // 选择
      // 选择
      getInfo() {
        let that = this
        window.addEventListener(
          'message',
          function (event) {
            // 接收位置信息，用户选择确认位置点后选点组件会触发该事件，回传用户的位置信息
            var loc = event.data
            //   console.log(loc)
            if (loc && loc.module == 'locationPicker') {
              //防止其他应用也会向该页面post信息，需判断module是否为'locationPicker'
              that.basicInfo.region = loc.poiaddress
              that.basicInfo.latitude = loc.latlng.lat
              that.basicInfo.longitude = loc.latlng.lng
              //调用父组件方法并传值给父组件
              console.log('map_data', loc)
              // that.$emit('chooseOrgAddr', that.map_data)
            }
          },
          false
        )
      },
      // 父组件调用方法,打开模态框
      openDialog() {
        this.isDialog = true
        this.$nextTick(() => {
          this.$refs.basicInfo.resetFields()
        })
        if (this.basicInfoList?.latitude && this.basicInfoList?.longitude) {
          let detailAddress = this.basicInfoList.address.split(this.basicInfoList.region)
          this.$nextTick(() => {
            this.basicInfo.address = detailAddress.length > 1 ? detailAddress[1] : ''
            this.basicInfo.region = this.basicInfoList.region
          })
          this.map_src = `https://mapapi.qq.com/web/mapComponents/locationPicker/v/index.html?search=1&type=1&coord=${this.basicInfoList.latitude},${this.basicInfoList.longitude}&key=PEXBZ-WSCKT-E2BXK-VPOVZ-KOP46-Q7F26&referer=myapp`
        } else {
          this.map_src = `https://mapapi.qq.com/web/mapComponents/locationPicker/v/index.html?search=1&type=1&key=PEXBZ-WSCKT-E2BXK-VPOVZ-KOP46-Q7F26&referer=myapp`
        }
      },
      // 关闭模态框
      closeDialog() {
        this.isDialog = false
      },
      cancel() {
        this.isDialog = false
      },
      confirm() {
        // if (this.basicInfo.region == '') {
        //   this.$message.error('请选择地图位置')
        //   return false
        // }
        this.$refs.basicInfo.validate((valid) => {
          if (valid) {
            this.$emit('chooseOrgAddr', this.basicInfo)
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
    },
  }
</script>

<style></style>
