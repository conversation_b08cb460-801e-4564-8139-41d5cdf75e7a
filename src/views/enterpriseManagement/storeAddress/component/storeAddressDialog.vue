<!--
 * @Author: liq<PERSON> liqian@123
 * @Date: 2025-03-26 14:59:12
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-06-25 15:44:42
 * @FilePath: \qst-merchant-admin-2.0\src\views\enterpriseManagement\storeAddress\component\storeAddressDialog.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-dialog :title="title" width="50%" top="15vh" :visible.sync="addressDialog" :close-on-click-modal="false">
    <div class="share">
      <div class="storeBasicInfoPage">
        <el-form label-width="180px" :rules="basicInfoRule" :model="basicInfo" ref="basicInfo" :label-position="labelPosition">
          <el-form-item label="地址类型" prop="type">
            <el-select :disabled="isLook" v-model="basicInfo.type" style="width: 100%" placeholder="请选择地址类型">
              <el-option v-for="item in addressType" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>

          <!-- <el-form-item prop="shop_ids" v-if="basicInfo.type == 1">
            <template slot="label">
              <span class="flex-l">
                <span>可用店铺</span>
                <div style="font-size: 12px">可用店铺仅对自提地址、退货地址生效</div>
              </span>
            </template>
            <el-select v-model="basicInfo.shop_ids" multiple style="width: 100%" placeholder="请选择可用店铺">
              <el-option v-for="item in merchantList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="地址名称" prop="name">
            <el-input :disabled="isLook" type="text" v-model="basicInfo.name" placeholder="请输入地址名称"></el-input>
          </el-form-item>
          <el-form-item label="详细地址" label-width="100px" prop="address" clearable>
            <el-input :disabled="isLook" type="textarea" v-model="basicInfo.address" placeholder="请输入详细地址"></el-input>
          </el-form-item>
          <el-button :disabled="isLook" style="width: 100%" @click="selectMap" class="selectMap" icon="el-icon-location-outline">
            地图选择
          </el-button>

          <el-form-item label="联系人" prop="manage_name">
            <el-input :disabled="isLook" type="text" v-model="basicInfo.manage_name" placeholder="请输入联系人"></el-input>
          </el-form-item>
          <el-form-item label="联系电话" prop="phone">
            <el-input :disabled="isLook" type="text" v-model="basicInfo.phone" placeholder="请输入联系电话"></el-input>
          </el-form-item>

          <el-form-item label="营业日期" prop="legal_code">
            <span v-if="basicInfo.type == 1" slot="label">
              <span style="color: red">*</span>
              营业日期
            </span>
            <div>
              <el-radio-group :disabled="isLook" v-model="basicInfo.legal_code" @change="changeTimeoptions">
                <el-radio-button label="1">工作日</el-radio-button>
                <el-radio-button label="2">周末</el-radio-button>
                <el-radio-button label="3">全部时间</el-radio-button>
              </el-radio-group>
            </div>
            <div class="weekSelect">
              <el-checkbox-group :disabled="isLook" v-model="basicInfo.business_day" @change="changeTime">
                <el-checkbox v-for="item in options" :key="item.value" :label="item.value" :value="item.value">
                  {{ item.label }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </el-form-item>
          <el-form-item label="营业时间">
            <span v-if="basicInfo.type == 1" slot="label">
              <span style="color: red">*</span>
              营业时间
            </span>
            <div class="timeSelect">
              <el-form-item prop="start_time">
                <el-time-select
                  :disabled="isLook"
                  placeholder="开始时间"
                  v-model="basicInfo.start_time"
                  :picker-options="{
                    start: '00:00',
                    step: '00:15',
                    end: '23:59',
                  }"
                ></el-time-select>
              </el-form-item>

              -
              <el-form-item prop="end_time">
                <el-time-select
                  :disabled="isLook"
                  placeholder="结束时间"
                  v-model="basicInfo.end_time"
                  :picker-options="{
                    start: '00:00',
                    step: '00:15',
                    end: '23:59',
                    minTime: basicInfo.start_time,
                  }"
                ></el-time-select>
              </el-form-item>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <base-dialog-footer v-if="!isLook" @cancel="cancel" @confirm="confirm"></base-dialog-footer>
    <selectAddressMap ref="selectAddressMap" :basicInfoList="basicInfo" @chooseOrgAddr="chooseOrgAddr" />
  </el-dialog>
</template>

<script>
  import { saveAddressList } from '@/api/enterpriseManagement/storeAddress'
  import selectAddressMap from './selectAddressMap.vue'
  export default {
    name: 'storeAddressDialog',
    components: { selectAddressMap },
    props: {
      operateFlag: {
        type: String,
        default: 'ADD',
      },
      title: {
        type: String,
        default: '新建地址',
      },
    },

    data() {
      return {
        addressDialog: false,
        addressType: [
          { label: '自提地址', value: 1 },
          { label: '发货地址', value: 2 },
          { label: '退货地址', value: 3 },
        ],
        merchantList: [],
        options: [
          {
            value: '1',
            label: '周一',
          },
          {
            value: '2',
            label: '周二',
          },
          {
            value: '3',
            label: '周三',
          },
          {
            value: '4',
            label: '周四',
          },
          {
            value: '5',
            label: '周五',
          },
          {
            value: '6',
            label: '周六',
          },
          {
            value: '0',
            label: '周日',
          },
        ],
        labelPosition: 'top',
        isLook: false,
        basicInfo: {
          type: '',
          name: '',
          legal_code: '',
          region: '', //地址
          address: '', // 自提地址
          manage_name: '', // 联系人
          phone: '', // 联系电话
          business_day: '', // 营业日期
          start_time: '',
          end_time: '',
          longitude: '', //经度
          latitude: '', //维度
        },
        basicInfoRule: {
          type: [{ required: true, message: '请选择地址类型', trigger: 'change' }],
          // shop_ids: [{ required: true, message: '请选择可用店铺', trigger: 'change' }],
          address: [{ required: true, message: '请输入自提地址', trigger: 'blur' }],
          name: [{ required: true, message: '请输入地址名称', trigger: 'blur' }],
          manage_name: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
          legal_code: [
            {
              required: false,
              validator: (rule, value, callback) => {
                if (this.basicInfo.type == 1) {
                  if (this.basicInfo.business_day.length != 0) {
                    callback()
                  } else {
                    return callback(new Error('请选择营业日期'))
                  }
                } else {
                  callback()
                }
              },
              message: '请选择营业日期',
              trigger: 'change',
            },
          ],
          start_time: [
            // { required: false, message: '请选择营业时间', trigger: 'change' },
            {
              required: false,
              validator: (rule, value, callback) => {
                if (this.basicInfo.type == 1) {
                  if (this.basicInfo.start_time) {
                    callback()
                  } else {
                    return callback(new Error('请选择营业开始时间'))
                  }
                } else {
                  callback()
                }
              },
              message: '请选择营业开始时间',
              trigger: 'change',
            },
          ],
          end_time: [
            // { required: false, message: '请选择营业时间', trigger: 'change' },
            {
              required: false,
              validator: (rule, value, callback) => {
                if (this.basicInfo.type == 1) {
                  if (this.basicInfo.end_time) {
                    callback()
                  } else {
                    return callback(new Error('请选择营业结束时间'))
                  }
                } else {
                  callback()
                }
              },
              message: '请选择营业结束时间',
              trigger: 'change',
            },
          ],
          phone: [
            { required: true, message: '请输入联系电话', trigger: 'blur' },
            { pattern: /^1[3456789]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' },
          ],
        },
      }
    },
    created() {},

    methods: {
      open(data = {}) {
        this.addressDialog = true
        this.$nextTick(() => {
          this.$refs.basicInfo.resetFields()
          if (this.operateFlag == 'ADD') {
            this.isLook = false
            for (let key in this.basicInfo) {
              this.basicInfo[key] = ''
            }
            this.basicInfo.business_day = []
            this.basicInfo.start_time = '00:00:00'
            this.basicInfo.end_time = '23:59:59'
          } else {
            this.isLook = true
            this.basicInfo = Object.assign({}, this.basicInfo, data)
            this.showBasicsDay()
          }
        })
      },
      confirm() {
        console.log(this.basicInfo)
        this.$refs.basicInfo.validate((valid) => {
          if (valid) {
            let params = {
              ...this.basicInfo,
            }
            if (this.operateFlag == 'ADD') {
              this.saveAddressListfn(params)
            }
            if (this.operateFlag == 'edit') {
              this.saveAddressListfn(params)
            }
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
      showBasicsDay() {
        if (this.areArraysEqual(this.basicInfo.business_day, ['1', '2', '3', '4', '5'])) {
          this.basicInfo.legal_code = 1
        }
        if (this.areArraysEqual(this.basicInfo.business_day, ['6', '0'])) {
          this.basicInfo.legal_code = 2
        }
        if (this.areArraysEqual(this.basicInfo.business_day, ['1', '2', '3', '4', '5', '6', '0'])) {
          this.basicInfo.legal_code = 3
        }
      },
      saveAddressListfn(params) {
        saveAddressList(params).then((res) => {
          if (res.code == 200) {
            this.$message.success('添加成功')
            this.cancel()
            this.$emit('confirm')
          }
        })
      },
      cancel() {
        this.addressDialog = false
      },
      selectMap() {
        this.$refs.selectAddressMap.openDialog()
      },
      //选择营业时间
      changeTimeoptions() {
        if (this.basicInfo.legal_code == 1) {
          this.basicInfo.business_day = ['1', '2', '3', '4', '5']
        } else if (this.basicInfo.legal_code == 2) {
          this.basicInfo.business_day = ['6', '0']
        } else {
          this.basicInfo.business_day = ['1', '2', '3', '4', '5', '6', '0']
        }
      },
      changeTime() {
        this.basicInfo.legal_code = ''
        if (this.areArraysEqual(this.basicInfo.business_day, ['1', '2', '3', '4', '5'])) {
          this.basicInfo.legal_code = 1
        }
        if (this.areArraysEqual(this.basicInfo.business_day, ['6', '0'])) {
          this.basicInfo.legal_code = 2
        }
        if (this.areArraysEqual(this.basicInfo.business_day, ['1', '2', '3', '4', '5', '6', '0'])) {
          this.basicInfo.legal_code = 3
        }
      },
      areArraysEqual(arr1, arr2) {
        return arr1.length === arr2.length && !arr1.some((item) => !arr2.includes(item))
      },
      // 选取地理位置后的回调
      chooseOrgAddr(data) {
        this.basicInfo.address = data.region + '' + data.address
        this.basicInfo.region = data.region
        this.basicInfo.latitude = data.latitude
        this.basicInfo.longitude = data.longitude
        console.log(this.basicInfo)
        this.$refs.selectAddressMap.closeDialog()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .storeBasicInfoPage {
    .uploadspan {
      .uploadlogo {
        width: 100px;
        height: 100px;
        border: 1px dashed #d9d9d9;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        .el-icon-plus {
          font-size: 20px;
          color: #c0c4cc;
        }
        .el-upload__text {
          color: #c0c4cc;
          font-size: 12px;
        }
        .el-upload__tip {
          color: #c0c4cc;
          font-size: 12px;
          margin-top: 10px;
        }
        .license_image {
          width: 100px;
          height: 100px;
          border-radius: 4px;
        }
      }
    }
    .selectMap {
      border-radius: 0px 4px 4px 0px;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #dcdfe6;
    }
    .weekSelect {
      display: flex;
      justify-content: space-between;
      margin-top: 15px;
      .el-select {
        width: 60%;
      }
    }
    .timeSelect {
      display: flex;
      .el-date-editor.el-input {
        width: 130px;
        margin: 0 10px;
      }
    }
  }
</style>
