<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-02-18 14:31:04
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-10 10:52:45
 * @FilePath: /qst-merchant-admin-2.0/src/views/login/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="login-container">
    <div class="login-main">
      <!-- <div class="wx-login">
        <div class="sub-title">微信扫码登录</div>
        <div id="login_container"></div>
        <div class="wx-tip">
          <span>打开</span>
          <span class="tip">微信</span>右上角扫一扫
        </div>
      </div>-->
      <div class="phone-pwd">
        <div class="phone-pwd-title flex-c">
          <el-image :src="require('@/assets/logo.png')" class="logo"></el-image>
          {{ title }}
        </div>
        <div class="sub-title flex-a">
          <div :class="tabKey == 1 ? 'key' : ''" @click="changeTabKey(1)">账号密码登录</div>
          <div :class="tabKey == 2 ? 'key' : ''" @click="changeTabKey(2)">手机号登录</div>
        </div>
        <div>
          <!-- 账号密码登录 -->
          <el-form
            v-show="tabKey == 1"
            ref="form"
            class="login-form"
            label-position="left"
            :model="form"
            :rules="rules"
            :validate-on-rule-change="false"
          >
            <el-form-item prop="username">
              <el-input
                class="element-input"
                v-model.trim="form.username"
                placeholder="请输入用户名"
                type="text"
                @keyup.enter.native="tabKey == 1 ? handleLogin() : phoneLogin()"
              />
            </el-form-item>
            <el-form-item prop="password">
              <base-input
                class="element-input"
                ref="password"
                v-model.trim="form.password"
                placeholder="请输入密码"
                :showPwdTop="true"
                @keyup.enter.native="tabKey == 1 ? handleLogin() : phoneLogin()"
              />
            </el-form-item>
            <el-form-item prop="captchaCode">
              <div class="verification-code flex-b">
                <el-input
                  v-model.trim="form.captchaCode"
                  placeholder="请输入图片验证码"
                  type="text"
                  class="verification-code-input"
                  :maxlength="6"
                  @keyup.enter.native="tabKey == 1 ? handleLogin() : phoneLogin()"
                />
                <div class="flex-b">
                  <el-button @click.stop="captchaFn" type="text">刷新</el-button>
                  <div class="line"></div>
                  <el-image :src="yzm.base64" class="refresh-image"></el-image>
                </div>
              </div>
            </el-form-item>
          </el-form>

          <!-- 手机号验证码登陆 -->
          <el-form
            v-show="tabKey == 2"
            ref="formPhone"
            class="login-form"
            label-position="left"
            :model="formPhone"
            :rules="phoneRule"
            :validate-on-rule-change="false"
          >
            <el-form-item prop="phone">
              <el-input
                class="element-input"
                v-model.trim="formPhone.phone"
                placeholder="请输入手机号"
                :maxlength="11"
                type="text"
                @keyup.enter.native="tabKey == 1 ? handleLogin() : phoneLogin()"
              />
            </el-form-item>
            <el-form-item prop="smsCode">
              <div class="verification-code flex-b">
                <el-input
                  v-model.trim="formPhone.smsCode"
                  placeholder="请输入验证码"
                  type="text"
                  class="verification-code-input"
                  :maxlength="6"
                  v-numeric
                  @keyup.enter.native="tabKey == 1 ? handleLogin() : phoneLogin()"
                />
                <el-button type="text" size="big" v-if="timeOut">{{timeOut}}</el-button>
                <el-button
                  type="text"
                  size="big"
                  v-else
                  v-loading="isLoading"
                  @click="getVerificationCode"
                >获取验证码</el-button>
              </div>
            </el-form-item>
          </el-form>
        </div>

        <el-button
          class="login-btn"
          :loading="loading"
          type="primary"
          @click="tabKey == 1 ? handleLogin() : phoneLogin()"
        >登录</el-button>
      </div>
    </div>

    <select-company></select-company>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import SelectCompany from '@/components/SelectCompany/index.vue'
import {
  phoneRule,
  userNameRule,
  passwordRule,
  imageCodeRule,
  phoneCodeRule,
} from '@/utils/validations.js'
import '@/utils/wxLogin'
import { timeOut } from '@/utils/public'
import { captcha, loginByPassword, loginBySms, sendCodeApi } from '@/api/user'
import store from '@/store'

export default {
  name: 'Login',
  components: {
    SelectCompany,
  },
  computed: {
    ...mapGetters({
      logo: 'settings/logo',
    }),
  },
  directives: {
    focus: {
      inserted(el) {
        el.querySelector('input').focus()
      },
    },
  },

  data() {
    let validatePhone = (rule, value, callback) => {
      if ('' == value) {
        callback(new Error('手机号不能为空'))
      } else if (!/^1\d{10}$/.test(value)) {
        callback(new Error('请填写正确的手机号'))
      } else {
        callback()
      }
    }

    return {
      src: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
      title: this.$baseTitle,

      // 账号密码
      form: {
        username: '',
        password: '',
        captchaCode: '',
      },
      rules: {
        username: userNameRule,
        password: passwordRule,
        captchaCode: imageCodeRule,
      },

      // 手机号登录表单验证规则
      formPhone: {
        phone: '',
        smsCode: '',
      },
      phoneRule: {
        phone: [
          {
            required: true,
            trigger: 'blur',
            validator: validatePhone,
          },
        ],
        smsCode: phoneCodeRule,
      },

      loading: false,
      redirect: undefined,
      tabKey: 1,
      timeOut: null,
      yzm: {},

      isLoading: false,
    }
  },
  watch: {
    $route: {
      handler(route) {
        this.redirect = (route.query && route.query.redirect) || '/'
      },
      immediate: true,
    },
  },
  created() {
    localStorage.clear()
    document.body.style.overflow = 'hidden'
  },
  beforeDestroy() {
    document.body.style.overflow = 'auto'
  },
  mounted() {
    this.captchaFn()
    // this.form.username = 'newzuhu1'
    // this.form.password = 'jy123456'
    // 后续微信登录逻辑
    // var obj = new WxLogin({
    //   self_redirect: true,
    //   id: 'login_container',
    //   appid: '',
    //   scope: '',
    //   redirect_uri: '',
    //   state: '',
    //   style: '',
    //   href: '',
    // })
    // console.log(this.$decimal)
    // console.log(this.$Regex('PhoneReg', '15064025789'))
  },
  methods: {
    // 监听登录
    loginSys(e) {
      if (e.key === 'Enter') {
        this.tabKey == 1 ? this.handleLogin() : this.phoneLogin()
      }
    },
    // 账号密码登录
    handleLogin() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true
          loginByPassword({
            ...this.form,
            captchaKey: this.yzm.key,
          })
            .then((res) => {
              if (res.code == 200) {
                if (res.data.isShowWindows == 1) {
                  this.captchaFn()
                  // 登陆后 右上角的切换使用
                  store.dispatch('user/setIsMerchant', 1)
                } else {
                  this.loginSuccess(res)
                }
                res.data.loginType = 'pwd'
                localStorage.setItem('loginBySms', JSON.stringify(res.data))
              } else {
                this.captchaFn()
              }
            })
            .finally(() => {
              this.loading = false
            })
        } else {
          return false
        }
      })
    },
    // 手机号登录
    phoneLogin() {
      this.$refs.formPhone.validate((valid) => {
        if (valid) {
          this.loading = true
          loginBySms(this.formPhone)
            .then((res) => {
              if (res.code == 200) {
                if (res.data.isShowWindows == 1) {
                  localStorage.setItem('merchantList', JSON.stringify(res.data.list))
                  store.dispatch('user/setIsMerchant', 2)
                } else {
                  this.loginSuccess(res)
                }
                res.data.loginType = 'phone'
                localStorage.setItem('loginBySms', JSON.stringify(res.data))
              }
            })
            .finally(() => {
              this.loading = false
            })
        } else {
          return false
        }
      })
    },
    // 登录成功回调
    loginSuccess(res) {
      localStorage.setItem('currentMchUid', res.data.mchStoreUid)
      localStorage.setItem('mchAdminUid', res.data.mchAdminUid)
      this.$ReachToken.init()
      store.dispatch('user/login', res.data)
      const routerPath = this.redirect === '/404' || this.redirect === '/401' ? '/' : this.redirect
      this.$router.push(routerPath)
    },
    // 获取验证码
    getVerificationCode() {
      this.$refs.formPhone.validateField('phone')
      if (this.formPhone.phone) {
        this.isLoading = true
        sendCodeApi({
          phone: this.formPhone.phone,
        })
          .then((res) => {
            if (res.code == 200) {
              this.$message({
                type: 'success',
                message: '验证码已发送，请注意查收！',
              })
              this.timeOutID = new timeOut(60)
              this.timeOutID.start((e) => {
                if (e.totalSeconds == 0) {
                  this.timeOut = null
                } else {
                  this.timeOut = e.totalSeconds + 's'
                }
              })
            }
          })
          .finally(() => {
            this.isLoading = false
          })
      } else {
        return false
      }
    },
    // 获取验证码
    captchaFn() {
      captcha().then((res) => {
        if (res.code == 200) {
          this.yzm = res.data
        }
      })
    },
    changeTabKey(val) {
      this.tabKey = val
      // this.$refs.formPhone&& this.$refs.formPhone.resetFields()
    },
  },
}
</script>

<style lang="scss" scoped>
.login-container {
  height: 100vh;
  background: url('~@/assets/login_images/background.png') center center fixed no-repeat;
  background-size: cover;
  display: flex;
  align-items: center;
  justify-content: center;
  .sub-title {
    font-size: $base-font-size-bigger;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    .key {
      color: $base-color-default;
    }
  }
  .login-main {
    // width: 730px;
    // width: 365px;
    height: 470px;
    background: #ffffff;
    box-shadow: 0px 4px 21px 0px rgba(159, 159, 159, 0.08);
    border-radius: 20px;
    display: flex;
    .wx-login {
      width: 297px;
      position: relative;
      margin-top: 123px;
      display: flex;
      align-items: center;
      flex-direction: column;
      text-align: center;
      flex-shrink: 0;
      &::after {
        content: '';
        display: block;
        position: absolute;
        right: 0;
        bottom: 78px;
        width: 1px;
        height: 240px;
        background: #f5f7fa;
      }

      #login_container {
        width: 168px;
        height: 168px;
        margin-top: 24px;
      }
      .wx-tip {
        margin-top: 12px;
        .tip {
          color: $base-color-default;
        }
      }
    }
  }

  // 手机号密码登录
  .phone-pwd {
    padding: 35px 62px 0 70px;
    box-sizing: border-box;
    .logo {
      width: 40px;
      height: 40px;
      margin-right: 10px;
    }
    & .sub-title {
      margin-top: 48px;
      margin-bottom: 24px;
    }
    &.phone-pwd-title {
      font-size: $base-font-size-max;
      font-weight: 600;
      color: #000;
    }
    .element-input {
      width: 308px;
    }

    .verification-code {
      width: 300px;
      height: 40px;
      background: #ffffff;
      border-radius: 6px;
      border: 1px solid #e8eaeb;
      padding-right: 6px;
      overflow: hidden;
      &-input {
        width: 150px;
        background: #fff;
        border: 0;
        caret-color: $base-font-color;
      }
      .refresh-image {
        height: 28px;
      }
      .line {
        width: 1px;
        height: 20px;
        background: #f5f7fa;
        margin: 0 10px;
      }
    }
    .login-btn {
      margin-top: 40px;
      width: 300px;
      &:hover {
        opacity: 0.9;
      }
    }
  }
}

::v-deep {
  .el-input {
    box-sizing: border-box;
    input {
      height: 40px;
      font-size: $base-font-size-default;
      line-height: 40px;
      color: $base-font-color;
      background: #fff;
      border: 0;
      caret-color: $base-font-color;
      border-radius: 6px;
      border: 1px solid #e8eaeb;
    }
  }
  .verification-code-input input {
    border: 0;
  }
}
</style>
