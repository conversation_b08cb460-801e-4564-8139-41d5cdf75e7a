<template>
  <div class="index">
    <el-row :gutter="10" type="flex" style="align-items: stretch;">
      <el-col :lg="10" :md="12" :sm="10" :xl="10" :xs="24">
        <el-card style="height: calc(100% - 20px);">
          <div slot="header" class="flex-b">
            <div class="header-top flex">
              <div class="label"></div>
              <div>操作指引</div>
              <span>根据您的使用场景按指引操作，快速开启您的生意</span>
            </div>
          </div>
          <div class="operate flex">
            <div class="operate-item flex-b" v-for="(item,index) in 5" :key="index">
              <el-image class="operate-image"></el-image>
              <div class="operate-name">
                <div>线上售卖</div>
                <div>快速配送</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :lg="14" :md="12" :sm="14" :xl="14" :xs="24">
        <el-card style="height: calc(100% - 20px);">
          <div slot="header" class="flex-b">
            <div class="header-top flex">
              <div class="label"></div>
              <div>快捷入口</div>
              <span>点击快速进入相应菜单</span>
            </div>
            <div class="header-set flex">
              <i class="iconfont icon-shezhi"></i>
              <span>自定义</span>
            </div>
          </div>
          <div class="quick flex">
            <div class="quick-item">
              <el-image src></el-image>
              <div>特权卡管理</div>
            </div>
            <div class="quick-item">
              <el-image src="@/assets/index/index-tianjia.png"></el-image>
              <div>添加</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-card>
      <div slot="header" class="flex-b">
        <div class="header-top flex">
          <div class="label"></div>
          <div>实时交易情况</div>
          <span>更新至2024年12月 12:00:10</span>
        </div>
        <div class="header-set flex">
          <i class="iconfont icon-shezhi"></i>
          <span>自定义</span>
        </div>
      </div>
      <div class="transaction flex_b">
        <el-row :gutter="20" type="flex" justify="end">
          <el-col :span="5" v-for="(item,index) in 5" :key="index">
            <div class="transaction-item">
              <div class="transaction-tips">
                <span>今日支付订单数</span>
                <el-tooltip class="item" effect="dark" content="Top Center 提示文字" placement="top">
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
              </div>
              <div class="transaction-num">
                <span>6,454</span>
                <span>笔</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <el-card>
      <div slot="header" class="flex-b">
        <div class="header-top flex">
          <div class="label"></div>
          <div>趋势图</div>
        </div>
        <div class="flex">
          <div class="chart-tab flex-c">
            <div>今日</div>
            <div class="key">本周</div>
            <div>本月</div>
            <div>全年</div>
          </div>
          <el-date-picker
            v-model="time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            class="picker"
          ></el-date-picker>
        </div>
      </div>
      <div>
        <el-row :gutter="20" style="align-item: stretch;">
          <el-col :span="16">
            <div class="chart-tab flex-c">
              <div class="key">订单量</div>
              <div>销售量</div>
              <div>客户数</div>
            </div>
            <VabChart :option="echartsOption" style="height: 400px"></VabChart>
          </el-col>
          <el-col :span="8">
            <div class="sale-title">门票销售额排行</div>
            <div>
              <div class="rank-item flex-b" v-for="(item,index) in 10" :key="index">
                <div class="flex">
                  <span :class="index < 3 ? 'key-' + index : '' ">{{index + 1}}</span>
                  <div class="line1">安提瓜和巴布达安提瓜和巴布达</div>
                </div>
                <div class="sale-num">5404</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <el-card>
      <div slot="header" class="flex-b">
        <div class="header-top flex">
          <div class="label"></div>
          <div>常见问题</div>
        </div>
      </div>
      <div>
        <div class="problem" v-for="(item,index) in 5" :key="index">
          <div class="problem-title">如何添加新品？</div>
          <div class="problem-content">段落示意：蚂蚁金服设计平台 design段落示意：蚂蚁金服设计平台 design段落示意：蚂蚁金服设计平台 design</div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import VabChart from '@/plugins/echarts'

export default {
  name: 'Index',
  components: {
    VabChart,
  },
  data() {
    return {
      activeNames: ['1', '2', '3', '4'],
      time: '',
      echartsOption: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
            axisTick: {
              alignWithLabel: true,
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
          },
        ],
        series: [
          {
            name: 'Direct',
            type: 'bar',
            barWidth: '24px',
            data: [10, 52, 200, 334, 390, 330, 220, 10, 52, 200, 334, 390, 330, 220],
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1, // 垂直渐变
                colorStops: [
                  { offset: 0, color: '#00AAFF' }, // 顶部颜色
                  { offset: 1, color: '#0071FE' }, // 底部颜色
                ],
              },
              borderRadius: [8, 8, 0, 0], // 顶部圆角（顺时针方向：左上、右上、右下、左下）
            },
          },
        ],
      },
    }
  },
  created() {
    this.fetchData()
  },
  beforeDestroy() {},
  mounted() {},
  methods: {
    handleChange(val) {},
    fetchData() {},
  },
}
</script>
<style lang="scss" scoped>
// 公共顶部
.header-top {
  .label {
    width: 2px;
    height: 14px;
    min-width: 2ox;
    margin-right: 8px;
    background-color: $base-color-default;
  }
  div + div {
    font-size: $base-font-size-big;
    color: rgba(0, 0, 0, 0.85);
    margin-right: 8px;
    min-width: 70px;
  }
  > span {
    font-weight: 400;
    font-size: 14px;
    color: #8d8d8d;
  }
}
.header-set {
  margin-left: auto;
  .iconfont {
    color: #6e6e7a;
    font-size: 14px;
    margin-right: 9px;
  }
  > span {
    font-weight: 400;
    font-size: 14px;
    color: #6e6e7a;
  }
}

// 操作指引
.operate {
  flex-wrap: wrap;
  gap: 20px;
  .operate-item {
    width: 134px;
    height: 50px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #f7f7f7;
    padding: 5px;
    box-sizing: border-box;
    .operate-image {
      width: 40px;
      height: 40px;
      margin-right: 8px;
    }
    .operate-name {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      font-size: 14px;
      color: #000000;
      > div + div {
        color: #8d8d8d;
      }
    }
  }
}
// 快捷入口
.quick {
  flex-wrap: wrap;
  .quick-item {
    margin-right: 27px;
    .operate-image {
      width: 40px;
      height: 40px;
      margin: 0 auto;
      display: block;
    }
    > div {
      font-weight: 400;
      font-size: 14px;
      color: #32363a;
      margin-top: 10px;
      text-align: center;
    }
  }
}
// 实时交易情况
.transaction {
  &-item {
    background: #f6f7fa;
    border-radius: 10px;
    text-align: center;
    padding: 26px 0;
  }
  &-tips {
    font-weight: 400;
    font-size: 14px;
    color: #8d8d8d;
    > span {
      margin-right: 5px;
    }
  }
  &-num {
    font-weight: 600;
    font-size: 26px;
    color: rgba(0, 0, 0, 0.85);
    margin-top: 8px;
    > span + span {
      font-weight: 400;
      font-size: 14px;
      color: rgba(141, 141, 141, 0.85);
      margin-left: 2px;
    }
  }
}

// 常见问题
.problem {
  border-bottom: 1px dashed #e9e9e9;
  padding: 20px 0;
  &-title {
    font-size: 16px;
    margin-bottom: 10px;
  }
  &-content {
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    line-height: 24px;
  }
}

// 图标
.chart-tab {
  width: 190px;
  height: 40px;
  background: #f6f7fa;
  border-radius: 8px;

  > div {
    text-align: center;
    width: 60px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    font-weight: 400;
    font-size: 14px;
    border-radius: 8px;
  }
  .key {
    background: #ffffff;
    color: $base-color-default;
  }
}

.picker {
  width: 256px;
  margin-left: 10px;
}

.sale-title {
  margin-bottom: 20px;
  font-weight: 500;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
}
.rank-item {
  & + & {
    margin-top: 18px;
  }
  span {
    display: block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 24px;
    text-align: center;
    line-height: 22px;
    color: #000;
    background: #f0f2f5;
    &.key-0 {
      background: #ff4d4f;
      color: #ffffff;
    }
    &.key-1 {
      background: #faad14;
      color: #ffffff;
    }
    &.key-2 {
      background: #52c41a;
      color: #ffffff;
    }
  }
  .line1 {
    flex: 0.8;
  }

  .sale-num {
    width: 120px;
  }
}
</style>
