<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-03-26 15:44:01
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-24 10:50:09
 * @FilePath: /qst-merchant-admin-2.0/src/views/shop/goodsManagement/goodsDetaile.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="goods-detial">
    <el-card style="min-height: 200px;" v-loading="!isShow">
      <el-tabs v-model="tabKeysIndex" @tab-click="handleClick">
        <el-tab-pane label="基础信息" name="1" :disabled="isDisabled">
          <basic-information
            @backList="backList"
            :goods_type="goods_type"
            v-if="tabKeysIndex == 1 && isShow"
          ></basic-information>
        </el-tab-pane>

        <el-tab-pane label="有效期和使用说明" name="2" :disabled="isDisabled" v-if="goods_type == 2">
          <expirationDate @backList="backList" v-if="tabKeysIndex == 2 && isShow"></expirationDate>
        </el-tab-pane>

        <el-tab-pane label="规格库存" name="3" :disabled="isDisabled">
          <specs v-if="tabKeysIndex == 3 && isShow"></specs>
        </el-tab-pane>

        <el-tab-pane label="商品详情" name="4" :disabled="isDisabled">
          <goodsDetailsTem @backList="backList" v-if="tabKeysIndex == 4&& isShow"></goodsDetailsTem>
        </el-tab-pane>

        <el-tab-pane label="卡片设置" name="5" :disabled="isDisabled" v-if="goods_type == 2">
          <cartSet @backList="backList" v-if="tabKeysIndex == 5 && isShow"></cartSet>
        </el-tab-pane>

        <el-tab-pane v-if="goods_type != 2" label="物流设置" name="6" :disabled="isDisabled">
          <logistics @backList="backList" v-if="tabKeysIndex == 6 && isShow"></logistics>
        </el-tab-pane>

        <el-tab-pane
          v-if="isApplyStores && !shop_id"
          label="适用门店"
          name="7"
          :disabled="isDisabled || !isStoresPage"
        >
          <usingStore v-if="tabKeysIndex == 7 && isShow"></usingStore>
        </el-tab-pane>
        <el-tab-pane v-if="isReject" label="审核" name="8" :disabled="isDisabled">
          <examine @backList="backList" v-if="tabKeysIndex == 8 && isShow"></examine>
        </el-tab-pane>
      </el-tabs>
      <div class="footer-zw"></div>
    </el-card>
  </div>
</template>

<script>
import basicInformation from './components/step/basicInformation.vue'
import specs from './components/step/specs.vue'
import goodsDetailsTem from './components/step/goodsDetailsTem.vue'
import logistics from './components/step/logistics.vue'
import usingStore from './components/step/usingStore.vue'
import expirationDate from './components/step/expirationDate.vue'
import cartSet from './components/step/cartSet.vue'
import { mapGetters } from 'vuex'
import store from '@/store'
import { goodsDetailsApi } from '@/api/shop/goodsManagement'
import { goodsStoreDetailsApi } from '@/api/shop/goodsStore'
import examine from './components/step/examine.vue'

export default {
  name: 'goodsDetaile',
  components: {
    basicInformation,
    specs,
    goodsDetailsTem,
    logistics,
    usingStore,
    expirationDate,
    cartSet,
    examine,
  },
  // provide 父组件向子组件传递数据

  computed: {
    ...mapGetters({
      tabKeys: 'goodsDetaile/getTabKeys',
      setRefreshShopNum: 'goodsDetaile/getRefreshShopNum',
      goodsId: 'goodsDetaile/getGoodsId',
      isApplyStores: 'goodsDetaile/getIsApplyStores',
      getShopId: 'goodsDetaile/getShopId',
    }),
    isDisabled() {
      return !this.goods_id
    },
    isReject() {
      return (
        (this.$route.query.pageType == 'headquarters' && this.shop_id > 0) ||
        (this.$route.query.pageType == 'shop' && this.goodsStatus == 3)
      )
    },
  },
  watch: {
    getShopId(newVal) {
      if (newVal && !this.shop_id) {
        this.shop_id = newVal
      }
    },
    tabKeys: {
      handler(newVal) {
        if (newVal) {
          this.tabKeysIndex = newVal + ''
        }
      },
      immediate: true,
      deep: true,
    },

    // 监听刷新门店商品数量
    setRefreshShopNum(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.initGoodsDetail()
        })
      }
    },

    goodsId(newVal) {
      if (newVal) {
        this.goods_id = newVal
      }
    },
  },
  provide() {
    return {
      goodsDetail: () => this.goodsDetail,
    }
  },
  data() {
    return {
      pageType: 'headquarters', // headquarters 总部门店 shop 门店
      tabKeysIndex: '2',
      saveType: 'add',
      goods_id: '',
      goodsDetail: {},
      goods_type: 1,
      isShow: false,
      isStoresPage: false,
      shop_id: null,

      // 是否审核中
      goodsStatus: 0,
    }
  },
  methods: {
    // 提示
    tip() {
      if (this.$route.query.step && this.$route.query.step > 0) {
        store.dispatch('goodsDetaile/setTabKeys', this.$route.query.step)
        this.$message({
          showClose: true,
          message: this.$route.query.tip,
          type: 'error',
        })
      }
    },
    // 切换
    handleClick(tab) {
      store.dispatch('goodsDetaile/setTabKeys', tab.name)
    },

    // 初始化商品详情
    initGoodsDetail(id) {
      this.isShow = false
      let params = {
        goods_id: id || this.goods_id, //商品ID
      }
      let http = goodsDetailsApi
      console.log(this.shop_id, id || this.goods_id)

      // ⚠️ 只有门店创建商品（第一步赋值shop_id）
      if (this.shop_id && this.pageType == 'shop') {
        params = {
          shop_id: this.shop_id,
          head_goods_id: id || this.goods_id,
        }
        http = goodsStoreDetailsApi
      }
      http(params).then((res) => {
        if (res.code == 200) {
          // 商品id
          this.goodsDetail = res.data || {}
          this.goods_id = res.data.goods_id || ''
          store.dispatch('goodsDetaile/setGoodsId', this.goods_id)

          // 审核状态
          this.goodsStatus = res.data.goods_status || 0

          // 店铺id （二次赋值）
          store.dispatch('goodsDetaile/setShopId', res.data.shop_id || '')
          this.shop_id = res.data.shop_id || ''

          // 是否为草稿状态 草稿状态编辑不做限制
          let type = this.$route.query.type
          let is_draft = res.data.is_draft == 'Y'
          type = is_draft && type == 'edit' ? 'add' : type
          store.dispatch('goodsDetaile/setSaveType', type)
          console.log(type)

          // 商品类型 1实物，2卡券
          this.goods_type = parseInt(res.data.goods_type) || 1
          this.isShow = true
          store.dispatch('goodsDetaile/setSpecsType', res.data.spec_type || 1)
          // 卡券商品可以设置门店
          if (this.goods_type == 2) {
            this.isStoresPage = true
            store.dispatch('goodsDetaile/setApplyStores', true)
            return
          }
          // 设置适用门店是否开启
          let delivery_rules = res.data.delivery_rules.delivery_methods || []
          let isApplyStores = delivery_rules.indexOf('pickup') > -1
          this.isStoresPage = isApplyStores
          store.dispatch('goodsDetaile/setApplyStores', isApplyStores)
        }
      })
    },

    // 返回
    backList() {
      this.$router.back()
    },
  },
  created() {
    store.dispatch('goodsDetaile/resetState')
    let goods_id = this.$route.query.id || ''
    this.pageType = this.$route.query.pageType
    this.tip()
    // 初始化商品详情
    if (goods_id) {
      // 如果有传入的shop_id 则认为是门店商品详情页面，否则为店铺总商品页面（包含门店提交审核的商品）
      // 重点 --- 虽然商品详情页面会获取shop_id 但是不一定是门店商品详情页面，因为门店商品详情页面会传入shop_id和goods_id
      if (this.$route.query.shop_id) {
        this.shop_id = this.$route.query.shop_id
        store.dispatch('goodsDetaile/setShopId', this.shop_id)
      }
      this.initGoodsDetail(goods_id)
    } else {
      // 新增商品
      this.saveType = 'add'
      store.dispatch('goodsDetaile/setSaveType', 'add')
      this.isShow = true
    }
  },
}
</script>

<style lang="scss" scoped>
::v-deep {
  .el-form-item__label {
    font-size: 14px;
    color: #303133;
    font-weight: 600 !important;
  }
}
.ml10 {
  margin-left: 10px;
}

.goods-detial ::v-deep {
}
</style>
