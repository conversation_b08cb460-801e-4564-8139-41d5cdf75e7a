<!--
 * @Author: shang<PERSON>i <EMAIL>
 * @Date: 2025-03-22 15:01:29
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-06-19 09:36:27
 * @FilePath: \qst-merchant-admin-2.0\src\views\shop\goodsManagement\index.vue
 * @Description: 商品管理页面
-->
<template>
  <div class="goods">
    <el-card>
      <el-alert type="warning" show-icon :closable="false" style="margin-bottom: 20px;">
        <template #title>
          尊敬的用户：为规范经营行为，防范因虚假宣传引发的法律风险及经济损失，请您编辑/上架商品时，严格遵守《中华人民共和国广告法》、《中华人民共和国消费者权益保护法》、《反不正当竞争法》等法律相关规定，合法合规经营。可参考：
          <agreement :title="'商品宣传管理规范'"></agreement>
        </template>
      </el-alert>
      <base-form
        ref="baseForm"
        :tableForm="tableForm"
        :formArray="formArray"
        @searchForm="searchForm"
      ></base-form>
    </el-card>
    <el-card>
      <template slot="header">
        <div class="flex scroll" ref="scroll">
          <div
            v-for="(item, index) in selectTabList"
            :class="tableForm.search_goods_status == item.value || (!tableForm.search_goods_status && index == 0) ? 'key' : ''"
            :key="index"
            @click="searchGoodsStatusFn(item.value)"
          >
            <el-tooltip class="item" effect="dark" placement="top" :content="item.tip">
              <span type="text">{{ item.label }}({{ item.count }})</span>
            </el-tooltip>
          </div>
        </div>
      </template>
      <div class="operate flex-b">
        <div>
          <el-button @click="goGoodsDetial('add')" type="primary" icon="el-icon-plus">新建商品</el-button>
          <!-- <el-button @click="openImportDialogFn">从其他平台导入</el-button> -->
          <!-- <el-button :disabled="selectListKey.length == 0">批量设置</el-button> -->
        </div>
        <div
          class="flex"
          v-if="tableForm.search_goods_status != 0 && tableForm.search_goods_status != 5"
        >
          <span>显示正常规格商品</span>
          <el-switch
            class="ml10"
            v-model="tableForm.is_all_sku_show"
            :active-value="'Y'"
            :inactive-value="'N'"
            active-color="#0071FE"
            inactive-color="#d9d9d9"
            @change="refreshTable"
          ></el-switch>
          <span v-if="tableForm.is_all_sku_show == 'Y'" class="ml10">是</span>
          <span v-else class="ml10">否</span>
          <el-tooltip
            class="item"
            effect="dark"
            placement="top"
            content="开启后将显示商品的所有规格，关闭后只显示对应状态的规格"
          >
            <img :src="imgOssPath + '20250619_sf_wenhao.png'" alt class="icon_wenhao ml10" />
          </el-tooltip>
        </div>
      </div>
      <base-table
        :tableColumn="tableColumn"
        :tableRequest="getGoodsListApi"
        :tableForm="tableForm"
        rowKey="good_id"
        ref="baseTable"
        :isSelect="true"
        @selectiKey="selectiKey"
        :treeProps="{ children: 'sku_list', hasChildren: 'hasChildren' }"
        :isScrollX="true"
        @scrollX="tableScrollX"
        v-if="!isLoading"
      >
        <template #expand="{ scope }">
          <!-- 65*(scope.row.sku_list.length + 1) + 'px' -->
          <div
            class="prive-table-relative"
            :style="{
                width: tableWidth,
                transform: 'translateX(' + tableLeft + ')',
              }"
          >
            <el-table
              :ref="'priveTable' + scope.$index"
              :data="scope.row.sku_list"
              class="prive-table"
              :style="{
                  width: tableWidth,
                }"
              border
              rowKey="sku_id"
            >
              <el-table-column prop="date" label="商品主图" width="80">
                <template #default="{ row }">
                  <el-image
                    v-if="row.image"
                    class="shop-image"
                    :src="row.image"
                    :preview-src-list="[row.image]"
                  ></el-image>
                  <div v-else class="shop-image-text">暂无图片</div>
                </template>
              </el-table-column>
              <el-table-column prop="spect" label="规格值">
                <template #default="{ row }">
                  <el-tag v-for="(value, key) in row.spec_format" :key="key">{{ key }}：{{ value }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="price" label="规格售价" width="80">
                <template #default="{ row }">
                  <div>{{ row.sale_price }}</div>
                </template>
              </el-table-column>

              <el-table-column prop="num" label="门店总规格库存" width="80">
                <template #default="{ row }">
                  <div class="flex">{{ row.shop_stock }}</div>
                </template>
              </el-table-column>

              <el-table-column prop="num" label="总部规格库存" width="80">
                <template #default="{ row }">
                  <div class="flex">
                    <div
                      class="price-icon price-success"
                      :class="{
                          'price-error': row.status == 3,
                          'price-warning': row.status == 4,
                          'price-success': row.status == 1,
                        }"
                    ></div>
                    {{ row.stock }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="on_stock" label="线上库存" width="80"></el-table-column>

              <el-table-column prop="state" label="状态" width="80">
                <template #default="{ row }">
                  <div>
                    <el-tag v-if="row.status == 3" type="danger">{{ row.status_name }}</el-tag>
                    <el-tag v-else-if="row.status == 4" type="warning">{{ row.status_name }}</el-tag>
                    <el-tag v-else-if="row.status == 1" type="success">{{ row.status_name }}</el-tag>
                    <el-tag v-else>{{ row.status_name }}</el-tag>
                  </div>
                </template>
              </el-table-column>

              <el-table-column prop="state" label="默认规格" width="80">
                <template #default="{ row, $index }">
                  <div class="relative_zzc">
                    <div class="zzc" @click="skuDefaultFn(row, $index, scope)"></div>
                    <el-radio :disabled="row.is_show == 'N'" :value="row.is_default" label="Y">默认</el-radio>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="220">
                <template #default="{ row, $index }">
                  <div class="flex">
                    <div class="flex relative_zzc">
                      <div class="zzc" @click.stop="goodsSkuGoodsFn(row, $index, scope)"></div>
                      <el-switch
                        class="ml10"
                        active-color="#0071FE"
                        inactive-color="#d9d9d9"
                        v-model="row.is_show"
                        :active-value="'Y'"
                        :inactive-value="'N'"
                      ></el-switch>
                      <span class="edit-show">{{ row.is_show == 'Y' ? '显示' : '隐藏' }}</span>
                    </div>

                    <div class="public-operate-btn">
                      <el-button
                        type="text"
                        v-if="isBtnArr(scope.row, '修改sku库存')"
                        @click="openStock(scope, row, $index)"
                      >修改库存</el-button>

                      <template v-if="isBtnArr(scope.row, '删除')">
                        <el-tooltip
                          v-if="row.is_show == 'Y'"
                          class="item"
                          effect="dark"
                          content="只有已下架的SKU才能删除"
                          placement="top"
                        >
                          <el-button type="text" disabled>删除</el-button>
                        </el-tooltip>

                        <el-popconfirm
                          v-else
                          placement="top"
                          confirm-button-text="删除"
                          cancel-button-text="取消"
                          icon="el-icon-info"
                          icon-color="red"
                          title="确定要删除规格 吗？"
                          @confirm="deleteSkuFn(scope, row)"
                        >
                          <el-button class="del-button" type="text" slot="reference">删除</el-button>
                        </el-popconfirm>
                      </template>

                      <template v-if="isBtnArr(scope.row, '恢复')">
                        <el-popconfirm
                          placement="top"
                          confirm-button-text="恢复"
                          cancel-button-text="取消"
                          icon="el-icon-info"
                          icon-color="red"
                          :title="'确认将商品' + scope.row.name + '下' + restoreSkuName(row) + '恢复？'"
                          @confirm="restoreSkuFn(scope.row, row)"
                        >
                          <el-button type="text" class="del-button" slot="reference">恢复</el-button>
                        </el-popconfirm>

                        <el-tooltip
                          class="item"
                          effect="dark"
                          content="点击【恢复】时，会将当前规格组合恢复；"
                          placement="top"
                        >
                          <el-button type="text" disabled>
                            <img
                              :src="imgOssPath + '20250619_sf_wenhao.png'"
                              alt
                              class="icon_wenhao"
                            />
                          </el-button>
                        </el-tooltip>
                      </template>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </template>
        <template #goods_type="{ scope }">
          <!-- 商品类型 -->
          <div v-for="item in goodsConfig.type_list" :key="item.value">
            <span v-if="scope.row.goods_type == item.value">{{ item.label }}</span>
          </div>
        </template>

        <template #total_shop_stock_head="{ scope }">
          <span>门店总库存</span>
          <el-tooltip
            class="item"
            effect="dark"
            content="各门店上架状态的库存总和，若该商品门店下架，对应商品库存会进行扣减"
            placement="top"
          >
            <img
              :src="imgOssPath + '20250619_sf_wenhao.png'"
              alt
              class="icon_wenhao"
              style="transform: translateY(2px);"
            />
          </el-tooltip>
        </template>

        <template #group_name="{ scope }">
          <!-- 商品分组 -->
          {{ scope.row.group_name.join(',') }}
          <!-- <span v-for="item in scope.row.group_name" :key="item">{{item}} &nbsp;</span> -->
        </template>

        <template #image="{ scope }">
          <el-image
            class="delfate-image"
            :src="scope.row.image"
            :preview-src-list="[scope.row.image]"
          ></el-image>
        </template>

        <template #total_stock="{ scope }">
          <div class="flex">
            <div
              v-if="scope.row.status"
              class="price-icon price-success"
              :class="{
                  'price-error': scope.row.status == 3,
                  'price-warning': scope.row.status == 4,
                  'price-success': scope.row.status == 1,
                }"
            ></div>
            {{ scope.row.total_stock }}
          </div>
        </template>

        <template #status_name="{ scope }">
          <!-- 已售空 || 预警 -->
          <div v-if="scope.row.status_name">
            <!-- <el-tag v-if="scope.row.status == 3 || scope.row.status == 4" type="danger">
                {{ scope.row.status_name }}
              </el-tag>
              <el-tag v-else type="success">{{ scope.row.status_name }}</el-tag>
            -->
            <el-tag v-if="scope.row.status == 3" type="danger">{{ scope.row.status_name }}</el-tag>
            <el-tag v-else-if="scope.row.status == 4" type="warning">{{ scope.row.status_name }}</el-tag>
            <el-tag v-else-if="scope.row.status == 1" type="success">{{ scope.row.status_name }}</el-tag>
            <el-tag v-else>{{ scope.row.status_name }}</el-tag>
          </div>
        </template>

        <template #is_show="{ scope }">
          <div class="flex relative_zzc">
            <div class="zzc" @click.stop="goodsShowFn(scope.row)"></div>
            <el-switch
              class="ml10"
              active-color="#0071FE"
              inactive-color="#d9d9d9"
              v-model="scope.row.is_show"
              :active-value="'Y'"
              :inactive-value="'N'"
            ></el-switch>
            <span>{{ scope.row.is_show == 'Y' ? '上' : '下' }}架</span>
          </div>
        </template>

        <template #operate="{ scope }">
          <div class="public-operate-btn">
            <el-button
              v-if="isBtnArr(scope.row, '查看详情')"
              size="mini"
              type="text"
              @click="goGoodsDetial('view', scope.row)"
            >查看</el-button>

            <el-button
              v-if="isBtnArr(scope.row, '编辑')"
              size="mini"
              type="text"
              @click="goGoodsDetial('edit', scope.row)"
            >编辑</el-button>

            <el-button
              v-if="isBtnArr(scope.row, '修改spu库存')"
              size="mini"
              type="text"
              @click="openSpuStock(scope)"
            >修改总部库存</el-button>

            <el-popconfirm
              v-if="isBtnArr(scope.row, '审核通过')"
              placement="top"
              confirm-button-text="通过"
              cancel-button-text="取消"
              icon="el-icon-info"
              icon-color="red"
              width="300px"
              :title="'确定要“' + scope.row.name + '”审核通过吗？'"
              @confirm="examine(scope.row, 1)"
            >
              <el-button type="text" class="del-button" slot="reference">审核通过</el-button>
            </el-popconfirm>

            <el-button
              v-if="isBtnArr(scope.row, '审核驳回')"
              size="mini"
              type="text"
              @click="examine(scope.row, 2)"
              class="del-button"
            >审核驳回</el-button>

            <template v-if="isBtnArr(scope.row, '删除')">
              <el-tooltip
                v-if="scope.row.is_show == 'Y'"
                class="item"
                effect="dark"
                content="只有下架的SPU才能删除"
                placement="top"
              >
                <el-button type="text" disabled>删除</el-button>
              </el-tooltip>

              <el-popconfirm
                v-else
                placement="top"
                confirm-button-text="删除"
                cancel-button-text="取消"
                icon="el-icon-info"
                icon-color="red"
                width="300px"
                :title="'确定要删除“' + scope.row.name + '”吗？'"
                @confirm="tableForm.search_goods_status == 5 ? deleteHideFn(scope.row) : deleteSpuFn(scope.row)"
              >
                <el-button type="text" class="del-button" slot="reference">删除</el-button>
              </el-popconfirm>
            </template>

            <template v-if="isBtnArr(scope.row, '恢复')">
              <el-popconfirm
                placement="top"
                confirm-button-text="恢复"
                cancel-button-text="取消"
                icon="el-icon-info"
                icon-color="red"
                :title="'确认将商品' + restoreSpuName(scope.row) + '恢复？'"
                @confirm="restoreSpuFn(scope.row)"
              >
                <el-button type="text" class="del-button" slot="reference">恢复</el-button>
              </el-popconfirm>

              <el-tooltip
                class="item"
                effect="dark"
                content="点击【恢复】时，会将该商品下所有回收站规格组合恢复，若仅想恢复某一规格，请在单独规格后操作恢复；"
                placement="top"
              >
                <el-button type="text" disabled>
                  <img
                    :src="imgOssPath + '20250619_sf_wenhao.png'"
                    alt
                    style="width: 12px; height: 12px; cursor: pointer; transform: translateY(2px);"
                  />
                </el-button>
              </el-tooltip>
            </template>
          </div>
        </template>
      </base-table>
    </el-card>
    <edit-stock ref="editStock" @updataStock="updataStock" @update="refreshTable"></edit-stock>
    <import-dialog ref="importDialog"></import-dialog>
    <edit-code @update="refreshTable" ref="editCode"></edit-code>
    <reject-model ref="rejectModel" @update="refreshTable"></reject-model>
  </div>
</template>

<script>
import editStock from './components/editStock.vue'
import importDialog from './components/importDialog.vue'
import editCode from './components/editCode.vue'
import agreement from '@/components/Agreement/agreement.vue'

import {
  getGoodsConfigApi,
  getGoodsListApi,
  getGoodsCensusApi,
  goodsShowApi,
  skuShowApi,
  skuDefaultApi,
  deleteGoodsSkuApi,
  deleteGoodsApi,
  restoreSkuApi,
  restoreGoodsApi,
  goodsGroupListApi,
  goodsCategorylistBySelectApi,
  reviewGoodsApi,
  deleteHideApi,
} from '@/api/shop/goodsManagement.js'
import rejectModel from './components/rejectModel.vue'

export default {
  name: 'goodsManagementIndex',
  components: {
    editStock,
    importDialog,
    editCode,
    agreement,
    rejectModel,
  },
  data() {
    return {
      btnObj: {
        // 销售中
        1: ['查看详情', '编辑', '上下架', '修改spu库存', '修改sku库存'],
        // 待上架
        2: ['查看详情', '编辑', '删除', '上下架', '修改spu库存', '修改sku库存'],
        // 已售空
        3: ['查看详情', '编辑', '上下架', '修改spu库存', '修改sku库存'],
        // 库存预警
        4: ['查看详情', '编辑', '上下架', '修改spu库存', '修改sku库存'],
        //  回收站
        5: ['查看详情', '删除', '恢复'],
        //  待审核
        6: ['查看详情', '审核通过', '审核驳回'],
        // 待提交 - 驳回
        7: ['查看详情', '删除'],
        // 草稿箱
        8: ['查看详情', '编辑', '删除', '修改spu库存', '修改sku库存'],
      },

      tableWidth: '100%',
      tableLeft: '10px',
      getGoodsListApi,

      value: false,
      // tab
      selectTabList: [],
      // 表单
      tableForm: {
        search_goods_status: '0',
        is_all_sku_show: '',
        search_goods_type: '',
        search_spec_type: '',
        search_cate_id: '',
        search_group_id: '',
      },
      // 表单key
      formArray: [
        {
          label: '商品编码',
          type: 'input',
          key: 'search_goods_code',
          placeholder: '请输入商品编码',
        },
        {
          label: '商品名称',
          type: 'input',
          key: 'search_name',
          placeholder: '请输入商品名称',
        },
        {
          label: '商品类型',
          type: 'select',
          key: 'search_goods_type',
          placeholder: '请选择商品类型',
          options: [],
        },

        {
          label: '规格类型',
          type: 'select',
          key: 'search_spec_type',
          placeholder: '请选择规格类型',
          options: [],
        },

        {
          label: '商品分类',
          type: 'cascader',
          key: 'search_cate_id',
          placeholder: '请选择商品分类',
          props: {
            label: 'name',
            value: 'cate_id',
            checkStrictly: true,
          },
          options: [],
        },

        {
          label: '商品分组',
          type: 'select',
          key: 'search_group_id',
          placeholder: '请选择商品分组',
          options: [],
          props: {
            label: 'name',
            value: 'id',
          },
        },
        {
          label: '创建时间',
          type: 'time',
          timeType: 'daterange',
          valueFormat: 'yyyy-MM-dd',
          key: 'time',
          timeKey: ['search_start_time', 'search_end_time'],
        },
      ],
      // 表格配置
      tableColumn: [
        {
          prop: 'expand',
          width: '55',
          isExpand: true,
        },
        {
          label: '商品主图',
          prop: 'image',
          type: 'customize',
          width: '100',
        },
        {
          label: '商品编码',
          prop: 'goods_code',
          width: '140',
        },
        {
          label: '商品名称',
          prop: 'name',
          width: '140',
        },
        {
          label: '商品类型',
          prop: 'goods_type',
          type: 'customize',
          width: '100',
        },
        {
          label: '商品分类',
          prop: 'cate_name',
        },
        {
          label: '商品售价',
          prop: 'sale_price',
        },
        {
          label: '商品来源',
          prop: 'goods_source_name',
        },
        {
          label: '商品分组',
          prop: 'group_name',
          type: 'customize',
        },
        {
          label: '总部库存',
          prop: 'total_stock',
        },
        {
          label: '门店总库存',
          prop: 'total_shop_stock',
          headType: 'total_shop_stock_head',
          width: 120,
        },
        {
          label: '线上库存',
          prop: 'total_on_stock',
        },
        {
          label: '商品状态',
          prop: 'status_name',
          type: 'customize',
        },
        {
          label: '创建时间',
          prop: 'created_at',
        },
        {
          label: '上下架状态',
          prop: 'is_show',
          type: 'customize',
          fixed: 'right',
        },
        {
          label: '操作',
          type: 'customize',
          prop: 'operate',
          fixed: 'right',
          width: 170,
        },
      ],
      selectListKey: [],
      search_goods_status: '0',

      goodsConfig: {},
      // sku删除
      skuDel: {},
      // 加载数据
      isLoading: true,
    }
  },
  computed: {
    // 按钮判断方法
    isBtnArr() {
      let _this = this
      return (row, key) => {
        let status = row.status
        let isKey = true

        if (key == '修改spu库存') {
          if (!(row.spec_type == 1 && row.is_unlimited_stock == 'N')) {
            isKey = false
          }
        }
        if (key == '修改sku库存') {
          isKey = row.is_unlimited_stock == 'N'
        }
        if (key == '编辑') {
          isKey = row.goods_source == 1
        }
        return _this.btnObj[status].includes(key) && isKey
      }
    },

    tableHeight(i) {
      let priveTable = this.$refs
    },
  },
  beforeRouteLeave(to, from, next) {
    if (to.path != '/goodsManagement/goodsDetaile') {
      this.$store.dispatch('tabsBar/delRoute', from)
    }
    next()
  },
  activated() {
    if (!this.isLoading) {
      this.refreshTable()
    }
  },
  created() {
    this.checkAccountShopFn()
  },
  methods: {
    skuRestoreName(row) {
      return row.is_show == 'Y' ? '上架' : '下架'
    },
    checkAccountShopFn() {
      if (this.isLoading) {
        // 初始化
        this.init()
        // 获取商品数量
        this.getGoodsCensus()

        // 获取商品分类列表
        this.getCategoryListFn()
        // 获取商品分组列表
        this.goodsGroupListFn()
      } else {
        this.refreshTable()
      }
    },
    init() {
      // 获取商品类型
      getGoodsConfigApi({
        is_search: 'Y',
      }).then((res) => {
        if (res.code == 200) {
          this.goodsConfig = res.data
          this.$set(this.formArray[2], 'options', [
            {
              label: '全部',
              value: '',
            },
            ...res.data.type_list,
          ])
          this.$set(this.formArray[3], 'options', [
            {
              label: '全部',
              value: '',
            },
            ...res.data.spec_list,
          ])
          this.$set(this.tableForm, 'is_all_sku_show', res.data.is_all_sku_show)
          this.isLoading = false
          // 获取表格宽度
          this.$nextTick(() => {
            this.tableWidth = this.$refs.baseTable?.$el.offsetWidth - 50 + 'px'
          })
        }
      })
    },

    // 获取商品分类
    getCategoryListFn(e) {
      goodsCategorylistBySelectApi({
        type: 'list',
      }).then((res) => {
        if (res.code == 200) {
          this.$set(this.formArray[4], 'options', [
            {
              name: '全部',
              cate_id: '',
            },
            ...res.data,
          ])
        }
      })
    },

    // 商品分组
    goodsGroupListFn(e) {
      goodsGroupListApi({}).then((res) => {
        if (res.code == 200) {
          this.$set(this.formArray[5], 'options', [
            {
              name: '全部',
              id: '',
            },
            ...res.data,
          ])
        }
      })
    },

    // 商品数量
    getGoodsCensus() {
      getGoodsCensusApi(this.tableForm).then((res) => {
        if (res.code == 200) {
          this.selectTabList = res.data
        }
      })
    },

    // 刷新表格
    refreshTable(isReachPage) {
      this.$refs.baseTable.tableRequestFn(isReachPage)
      this.getGoodsCensus()
    },
    // 表格滚动事件
    tableScrollX(e) {
      this.tableLeft = e
    },

    // 搜索对应状态的商品类白哦
    searchGoodsStatusFn(val) {
      this.$set(this.tableForm, 'search_goods_status', val)
      this.refreshTable(true)
    },

    // 打开导入弹窗
    openImportDialogFn() {
      this.$refs.importDialog.openImportDialog()
    },
    // 表单搜索事件
    searchForm(form) {
      console.log('form', form)
      if (Array.isArray(form.search_cate_id)) {
        form.search_cate_id = form.search_cate_id[form.search_cate_id.length - 1] || ''
      }
      this.tableForm = Object.assign({}, this.tableForm, form)
      this.getGoodsCensus()
    },

    selectiKey(e) {
      this.selectListKey = e
    },
    // 编辑商品 type : add edit view 查看详情 编辑商品 添加商品
    goGoodsDetial(type, row = {}) {
      this.$router.push({
        path: '/goodsManagement/goodsDetaile',
        query: {
          id: row.good_id || '',
          type,
          pageType: 'headquarters',
        },
      })
    },

    // 回收站 修改编码code
    setGoodsCode(type, spu, sku) {
      console.log('row', type, spu, sku)
      this.$refs.editCode.open(type, spu, sku)
    },

    // 删除商品 spu
    deleteSpuFn(row) {
      deleteGoodsApi({
        goods_id: row.good_id, //商品id
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success('删除成功')
          this.refreshTable()
        }
      })
    },
    // 回收站删除商品
    deleteHideFn(row) {
      deleteHideApi({
        goods_id: row.good_id, //商品id
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success('删除成功')
          this.refreshTable()
        }
      })
    },

    // 恢复 spu 商品规格
    restoreSpuFn(spu) {
      restoreGoodsApi({
        goods_id: spu.good_id, //商品id
      }).then((res) => {
        if (res.code == 200) {
          if (res.data.step > 0) {
            this.$router.push({
              path: '/goodsManagement/goodsDetaile',
              query: {
                id: spu.good_id || '',
                type: 'edit',
                pageType: 'headquarters',
                step: res.data.step,
                tip: res.data.tip,
              },
            })
          } else {
            this.$message.success('恢复成功')
            this.refreshTable()
          }
        }
      })
    },
    // 恢复 spu
    restoreSpuName(row) {
      let goodsName = ''
      if (row.sku_list) {
        row.sku_list.forEach((item) => {
          let specName = ''
          for (let key in item.spec_format) {
            specName += `${item.spec_format[key]} `
          }
          goodsName += `${row.name}下${specName};`
        })
      } else {
        goodsName = `${row.name};`
      }
      return goodsName
    },

    // 上下架 spu
    goodsShowFn(row) {
      let upTip = '该商品下所有规格商品均会下架；'
      let downTip = '该商品下所有已下架状态的规格商品均会上架；'
      this.$alert(row.is_show == 'Y' ? upTip : downTip, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          goodsShowApi({
            goods_id: row.good_id,
            is_show: row.is_show == 'Y' ? 'N' : 'Y',
          }).then((res) => {
            if (res.code == 200) {
              this.refreshTable()
            }
          })
        })
        .catch(() => {})
    },

    // sku 打开库存预警弹窗
    openStock(spu, sku, skuIndex) {
      this.$refs.editStock.open(spu, {
        row: sku,
        skuIndex,
      })
    },
    // spu 打开库存预警弹窗
    openSpuStock(spu) {
      this.$refs.editStock.openSpu(spu)
    },

    // 删除sku
    deleteSkuFn(spu, sku) {
      console.log(spu, sku)
      deleteGoodsSkuApi({
        goods_id: spu.row.good_id, //商品id
        sku_id: sku.sku_id, //商品sku ID
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success('删除成功')
          this.refreshTable()
        }
      })
    },
    // sku 修改库存弹窗
    updataStock({ num, spu, sku }) {
      this.$refs.baseTable.setTableData((_this) => {
        _this.$set(_this.tableData[spu.$index].sku_list[sku.skuIndex], 'stock', num)
        // this.getGoodsCensus()
        this.refreshTable()
      })
    },
    // sku 恢复 sku名称
    restoreSkuName(row) {
      let name = ''
      for (let key in row.spec_format) {
        name += `${row.spec_format[key]} `
      }
      console.log(row, '--------')
      return name
    },
    // 恢复 sku
    restoreSkuFn(spu, sku) {
      restoreSkuApi({
        goods_id: spu.good_id, //商品id
        sku_id: sku.sku_id, //商品sku ID
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success('恢复成功')
          this.refreshTable()
        }
      })
    },

    // sku 商品显示隐藏
    goodsSkuGoodsFn(row, index, scoped) {
      skuShowApi({
        goods_id: scoped.row.good_id,
        is_show: row.is_show == 'Y' ? 'N' : 'Y',
        sku_id: row.sku_id,
      }).then((res) => {
        if (res.code == 200) {
          this.$refs.baseTable.setTableData((_this) => {
            _this.$set(
              _this.tableData[scoped.$index].sku_list[index],
              'is_show',
              row.is_show == 'Y' ? 'N' : 'Y'
            )
          })
          this.refreshTable()
        }
      })
    },
    // sku 默认规格切换
    skuDefaultFn(row, index, scoped) {
      console.log(row, index, scoped)
      skuDefaultApi({
        goods_id: scoped.row.good_id,
        sku_id: row.sku_id,
      }).then((res) => {
        if (res.code == 200) {
          this.$refs.baseTable.setTableData((_this) => {
            let sku_list = _this.tableData[scoped.$index].sku_list
            let skuIndex = 0
            sku_list.map((item, index) => {
              if (item.is_default == 'Y') {
                skuIndex = index
              }
            })
            _this.$set(_this.tableData[scoped.$index].sku_list[skuIndex], 'is_default', 'N')
            _this.$set(_this.tableData[scoped.$index].sku_list[index], 'is_default', 'Y')
          })
        }
      })
    },
    // 审核
    examine(row, key) {
      if (key == 1) {
        reviewGoodsApi({
          goods_id: row.good_id,
          is_pass: 'Y',
        }).then((res) => {
          if (res.code == 200) {
            this.$message.success('审核成功')
            this.refreshTable()
          }
        })
      } else {
        this.$refs.rejectModel.open(row)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.ml10 {
  margin-left: 10px;
}
.select {
  cursor: pointer;
}
.scroll {
  overflow-x: auto;
  flex-shrink: 0;
  padding-bottom: 15px;
  & > div {
    flex-shrink: 0;
    white-space: nowrap;
    cursor: pointer;
  }
  & > div + div {
    margin-left: 50px;
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
  }
  .key {
    color: #0071fe;
    position: relative;
    &::after {
      content: '';
      display: block;
      width: 28px;
      height: 2px;
      background: #0071fe;
      position: absolute;
      left: 50%;
      bottom: -15px;
      transform: translateX(-50%);
    }
  }
}

.delfate-image {
  width: 50px;
  height: 50px;
}

// 第一列表格的样式
.goods ::v-deep {
  .el-card__header {
    padding-bottom: 0;
  }
  .el-table-column--selection .cell {
    padding-left: 10px;
  }

  th:first-child,
  tr.el-table__row td:first-child {
    transform: translateX(55px) !important;
  }

  th:nth-child(2),
  tr.el-table__row td:nth-child(2) {
    transform: translateX(-55px) !important;
  }
  // tr:not(.el-table__row) > td {
  //   padding-top: 0 !important;
  //   padding-bottom: 0 !important;
  // }
}
// 嵌套内的表格样式
.goods .prive-table ::v-deep {
  z-index: 100;
  th,
  tr.el-table__row td {
    transform: translateX(0) !important;
  }
}
.prive-table-relative {
  overflow: auto;
  position: relative;
  z-index: 9;
}
.prive-table {
  min-width: 800px;
}
// 规格table的样式
.shop-image {
  width: 40px !important;
  height: 40px !important;
}
.shop-image-text {
  height: 40px;
}
.user-state {
  margin: 0 6px;
  font-size: 12px;
}

// 价格警告图标样式
.price-success {
  background: #67c23a;
}
.price-icon {
  width: 6px;
  height: 6px;
  box-sizing: border-box;
  border-radius: 50%;
  margin-right: 5px;
}
.price-warning {
  background: #ffb546;
}
.price-error {
  background: #ff6a6a;
}

// 按钮遮罩层样式
.relative_zzc {
  position: relative;
  cursor: pointer;
  z-index: 0;
  .zzc {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    opacity: 0;
  }
}

.edit-show {
  font-size: 12px;
  margin: 2px 6px 0;
}

// status
// spu-删除
.del-button {
  margin-left: 4px !important;
  position: relative;
  padding: 0 0 0 5px;
  &::before {
    content: '';
    display: block;
    width: 1px;
    height: 8px;
    background: rgba(0, 0, 0, 0.08);
    box-shadow: inset 0px -1px 0px 0px #ebeef5;
    position: absolute;
    left: 0;
    top: 2px;
  }
}
.el-popconfirm ::v-deep {
  width: 300px !important;
}
</style>
