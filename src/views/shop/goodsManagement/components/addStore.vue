<template>
  <div>
    <el-dialog
      append-to-body
      :visible.sync="isStoreDialog"
      width="800px"
      @close="isStoreDialog = false"
      title="添加门店"
      :close-on-click-modal="false"
    >
      <base-form
        ref="baseForm"
        :tableForm="tableForm"
        :formArray="formArray"
        @searchForm="searchForm"
      ></base-form>

      <el-table
        ref="multipleTable"
        :data="tableData"
        tooltip-effect="dark"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        highlight-current-row
        :row-key="'id'"
      >
        <el-table-column reserve-selection type="selection" width="55"></el-table-column>
        <el-table-column prop="name" label="门店名称"></el-table-column>
        <el-table-column prop="manage_name" label="联系人"></el-table-column>
        <el-table-column prop="contact_phone" label="联系方式"></el-table-column>
        <el-table-column prop="address" label="门店地址"></el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="tableForm.page"
        :page-sizes="[10, 20, 30, 40, 50]"
        :page-size="tableForm.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
      <base-dialog-footer @cancel="isStoreDialog = false" @confirm="sumbitFn"></base-dialog-footer>
    </el-dialog>
  </div>
</template>

<script>
import { goodsPickupShopList } from '@/api/shop/goodsManagement'
export default {
  name: 'addStore',
  data() {
    return {
      isStoreDialog: false,
      tableForm: {
        limit: 10,
        page: 1,
      },
      formArray: [
        {
          label: '门店名称',
          type: 'input',
          key: 'name',
          placeholder: '请输入门店名称',
        },
      ],
      tableData: [],
      total: 0,
      // 选择
      multipleSelection: [],

      shops: [],
    }
  },
  methods: {
    // 打开弹窗时触发事件
    open(shops) {
      this.isStoreDialog = true
      this.shops = shops || []
      this.$nextTick(() => {
        this.$refs.multipleTable.clearSelection()
      })
      this.goodsPickupInit(true)
    },

    // 获取商品
    goodsPickupInit(isOnce) {
      goodsPickupShopList(this.tableForm).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.list || []
          this.total = res.data.pagination.totalCount || 0
          if (this.shops && this.shops.length > 0 && isOnce) {
            this.shops.map((item) => {
              this.$refs.multipleTable.toggleRowSelection(item, true)
            })
          }
        }
      })
    },
    // 分页事件处理函数
    handleSizeChange(val) {
      this.tableForm.limit = val
      this.goodsPickupInit()
    },
    // 分页事件处理函数
    handleCurrentChange(val) {
      this.tableForm.page = val
      this.goodsPickupInit()
    },

    searchForm(form) {
      console.log(form)
      this.tableForm.name = form.name
      this.tableForm.page = 1
      this.goodsPickupInit()
    },

    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    sumbitFn() {
      this.$emit('selectKey', this.multipleSelection)
      this.isStoreDialog = false
    },
  },
}
</script>