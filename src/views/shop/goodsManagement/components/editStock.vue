<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-04-01 09:20:06
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-24 09:26:15
 * @FilePath: /qst-merchant-admin-2.0/src/views/shop/goodsManagement/compontes/editStock.vue
 * @Description: 修改sku库存
-->
<template>
  <div class="edit-stock">
    <el-dialog title="修改SKU库存" :visible.sync="isStock" :close-on-click-modal="false">
      <div class="flex shop-box">
        <el-image class="shop-image" :src="sku.row.image"></el-image>
        <div>
          <div class="specs">{{spu.row.name}}</div>
          <div>
            <el-tag v-for="(key,value) in sku.row.spec_format" :key="key">{{value}}:{{key}}</el-tag>
          </div>
        </div>
      </div>
      <el-form :rules="formDataRule" :model="formData" ref="formData" label-width="100px">
        <el-form-item label="当前库存">
          <el-input v-model="sku.row.stock" disabled></el-input>
        </el-form-item>
        <el-form-item label="操作类型">
          <el-radio-group v-model="formData.operate_type">
            <el-radio :label="1">入库</el-radio>
            <el-radio :label="2">出库</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="新库存" prop="num">
          <div>
            <el-input
              type="number"
              @input="handleInput"
              :controls="false"
              v-model="formData.num"
              placeholder="请输入新库存"
            ></el-input>
            <!-- <el-input-number
              @input.native="handleChange"
              :controls="false"
              :precision="0"
              :min="0"
              v-model="formData.num"
              placeholder="请输入新库存"
            ></el-input-number>-->
            <div>操作后库存：{{stockNum}} 件</div>
          </div>
        </el-form-item>
      </el-form>

      <base-dialog-footer @confirm="confirm" @cancel="isStock=false"></base-dialog-footer>
    </el-dialog>
  </div>
</template>

<script>
import { skuStockApi } from '@/api/shop/goodsManagement'
import { skuStoreStockApi } from '@/api/shop/goodsStore'
export default {
  name: 'editStock',
  props: {
    isStore: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      formData: {
        num: undefined,
        operate_type: 1, //操作类型 1:入库，2：出库
      },
      formDataRule: {
        num: [
          { required: true, message: '请输入库存', trigger: 'blur' },
          // {
          //   required: true,
          //   validator: (rule, value, callback) => {
          //     if (value == this.sku.row.store) {
          //       callback(new Error('新库存不能和当前库存一样'))
          //       return false
          //     } else {
          //       callback()
          //     }
          //   },
          //   trigger: 'blur',
          // },
        ],
      },
      dialogVisible: false,

      spu: {
        row: {},
      },
      sku: {
        row: {},
      },
      isStock: false,
      type: '',
    }
  },
  computed: {
    stockNum() {
      let stock = parseInt(this.sku.row.stock)
      let num = parseInt(this.formData.num) || 0
      if (this.formData.operate_type == 1) {
        return num + stock
      } else {
        return stock - num
      }
    },
  },
  methods: {
    handleInput() {
      this.formData.num = this.formData.num.replace(/\D/g, '')
    },
    open(spu, sku) {
      console.log(spu, sku)
      this.spu = spu
      this.sku = sku
      this.formData.operate_type = 1
      this.isStock = true
      this.type = 'sku'
      this.formData.num = undefined
      this.$nextTick(() => {
        this.$refs.formData.clearValidate()
      })
    },
    openSpu(spu) {
      this.spu = spu
      this.sku = {
        row: {
          image: spu.row.image,
          sku_id: spu.row.sku_id,
          stock: spu.row.total_stock,
          spec_format: {},
        },
      }
      this.type = 'spu'
      this.formData.operate_type = 1
      this.isStock = true
      this.formData.num = undefined
      this.$nextTick(() => {
        this.$refs.formData.clearValidate()
      })
    },
    confirm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          let http = this.isStore ? skuStoreStockApi : skuStockApi
          http({
            shop_id: this.spu.row.shop_id, //所属门店id
            goods_id: this.spu.row.good_id, //商品id
            sku_id: this.sku.row.sku_id, //商品sku ID
            stock: this.formData.num + '', //sku库存
            operate_type: this.formData.operate_type, //操作类型 1:入库，2：出库
          }).then((res) => {
            if (res.code == 200) {
              this.$message.success('修改成功')
              this.isStock = false
              if (this.type == 'spu') {
                this.$emit('update')
              } else {
                this.$emit('updataStock', {
                  num: this.formData.num,
                  spu: this.spu,
                  sku: this.sku,
                })
              }

              this.formData.num = null
            }
          })
        } else {
          return false
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.shop-box {
  margin: 10px 0 28px 40px;
  .specs {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #303133;
    margin-bottom: 10px;
  }
  .shop-image {
    width: 50px;
    height: 50px;
    margin-right: 10px;
    border-radius: 4px;
  }
}
</style>
