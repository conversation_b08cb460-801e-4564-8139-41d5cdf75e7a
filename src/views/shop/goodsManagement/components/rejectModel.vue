<template>
  <div class="reject">
    <el-dialog title="驳回商品" :visible.sync="isReject" :close-on-click-modal="false">
      <el-form :model="rejectForm" ref="rejectForm" label-position="top" :rules="rule">
        <el-form-item label="驳回原因" prop="reason">
          <el-input type="textarea" v-model="rejectForm.reason" maxlength="200" show-word-limit></el-input>
        </el-form-item>
      </el-form>
      <div class="flex footer">
        <el-button @click="isReject = false">取消</el-button>
        <el-button type="danger" @click="rejectGoods">确认驳回</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { reviewGoodsApi } from '@/api/shop/goodsManagement.js'
export default {
  data() {
    return {
      isReject: false,
      rejectForm: {
        reason: '',
      },
      goodsId: '',
      rule: {
        reason: [{ required: true, message: '请输入驳回原因', trigger: 'blur' }],
      },
    }
  },
  methods: {
    open(row) {
      this.isReject = true
      this.goodsId = row.good_id
      this.rejectForm.reason = ''
      this.$nextTick(() => {
        this.$refs['rejectForm'].resetFields()
      })
    },

    rejectGoods() {
      this.$refs['rejectForm'].validate((valid) => {
        if (!valid) return false
        reviewGoodsApi({
          rejection_reason: this.rejectForm.reason,
          goods_id: this.goodsId,
          is_pass: 'N',
        }).then((res) => {
          if (res.code == 200) {
            this.$emit('update')
            this.isReject = false
          }
        })
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.footer {
  justify-content: flex-end;
  > button {
    margin-left: 20px;
  }
}
.reject ::v-deep {
  .el-dialog__body {
    padding: 10px 20px !important;
  }
}
</style>