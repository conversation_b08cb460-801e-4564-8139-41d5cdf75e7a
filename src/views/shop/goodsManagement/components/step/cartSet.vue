<template>
  <div>
    <el-form label-position="top" label-width="200px">
      <el-row :gutter="10">
        <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
          <el-form-item label="卡片封面" required>
            <el-radio-group v-model="shopInfo.cover_type" :disabled="isView">
              <el-radio :label="1">颜色</el-radio>
              <el-radio :label="2">图片</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="背景图片" v-if="shopInfo.cover_type == 2">
            <el-image style="width: 355px;height: 100px;" v-if="isView" :src="backgourndImage"></el-image>
            <el-upload
              v-else
              :show-file-list="false"
              class="upload_img"
              action="fakeaction"
              accept=".jpg, .png"
              :http-request="(e) => upLoadImgFn(e)"
            >
              <el-image
                style="width: 355px;height: 100px;"
                v-if="backgourndImage"
                :src="backgourndImage"
              ></el-image>
              <div class="cart-updata" v-else>
                <i class="el-icon-plus mtp"></i>
                <div class="el-upload__text">上传图片</div>
              </div>
            </el-upload>
            <div>建议尺寸：710x200像素</div>
          </el-form-item>
          <el-form-item label="背景颜色" v-if="shopInfo.cover_type == 1">
            <div class="flex color-block">
              <div
                class="color-block-div"
                :class="backgroundColor == item ? 'key' : ''"
                v-for="(item,index) in backgroundColorList"
                :key="index"
                @click="changeBackground(item)"
              >
                <div :style="'background:' + item"></div>
              </div>
            </div>
          </el-form-item>
        </el-col>

        <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
          <el-form-item label="预览效果" required>
            <div class="cart-show" :style="background">
              <div>商品名称展示</div>
              <div class="time-size">有效期：xxxx.xx.xx-xxxx.xx.xx</div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    {{isReject}}
    <footerBtn
      :is-btn-loading="isBtnLoading"
      :benText="isReject ? '下一步' : ''"
      @next="next"
      :num="5"
    ></footerBtn>
  </div>
</template>

<script>
import { upLoadImg } from '@/utils/uploadImage.js'
import { saveCardBackgroundApi } from '@/api/shop/goodsManagement'
import footerBtn from './footerBtn.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'cartSet',
  components: {
    footerBtn,
  },
  data() {
    return {
      shopInfo: {
        cover_type: 1,
      },
      backgourndImage: this.imgOssPath + '20250729_card_background.png',
      backgroundColor: '',
      backgroundColorList: [
        'linear-gradient( 270deg, #00AAFF 0%, #0071FE 100%)',
        'linear-gradient( 270deg, #FF7C4F 0%, #FF2727 100%)',
        'linear-gradient( 270deg, #FFB44F 0%, #FF7D27 100%)',
        'linear-gradient( 270deg, #85E854 0%, #67C23A 100%)',
      ],
      shopId: 0,
      goods_id: 0,
      isBtnLoading: false,
      goodsStatus: 0,
    }
  },
  computed: {
    ...mapGetters({
      save_type: 'goodsDetaile/getSaveType',
    }),
    isView() {
      return this.save_type == 'view'
    },
    background() {
      if (this.shopInfo.cover_type == 2) {
        return `background: url(${this.backgourndImage}) no-repeat 100% 100%;`
      } else {
        return `background: ${this.backgroundColor}`
      }
    },
    // 是否有审核页面
    isReject() {
      return (
        (this.$route.query.pageType == 'headquarters' && this.shopId > 0) ||
        (this.$route.query.pageType == 'shop' && this.goodsStatus == 3)
      )
    },
  },
  created() {
    this.backgroundColor = this.backgroundColorList[0]
    this.init()
  },
  inject: ['goodsDetail'],
  methods: {
    init() {
      let data = this.goodsDetail().card_setting
      this.goods_id = this.goodsDetail().goods_id
      this.shopId = this.goodsDetail().shop_id
      this.goodsStatus = this.goodsDetail().goods_status || 0

      this.shopInfo.cover_type = data.cover_type
      if (data.cover_type == 2) {
        this.backgourndImage = data.cover_value || this.imgOssPath + '20250729_card_background.png'
      } else {
        this.backgroundColor = data.cover_value
      }
      console.log()
    },
    // 图片上传回调
    upLoadImgFn(e) {
      upLoadImg(e.file).then((res) => {
        this.backgourndImage = res.data.url
      })
    },

    next(callback, key) {
      if (this.isBtnLoading) return
      this.isBtnLoading = true
      let params = {
        goods_id: this.goods_id,
        card_backgrounds: {
          cover_type: this.shopInfo.cover_type,
          cover_value: this.shopInfo.cover_type == 2 ? this.backgourndImage : this.backgroundColor,
        },
      }
      console.log(this.shopId, key != 'draft')
      params.is_release = this.shopId && key != 'draft' ? 'Y' : 'N'
      saveCardBackgroundApi(params).then((res) => {
        this.isBtnLoading = false
        if (res.code == 200) {
          callback()
        }
      })
    },

    changeBackground(item) {
      if (this.isView) {
        return
      }
      this.backgroundColor = item
    },
  },
}
</script>

<style lang="scss" scoped>
.upload_img {
}

// 背景图上传
.cart-updata {
  width: 355px;
  height: 120px;
  border: 1px dashed #dcdfe6;
  border-radius: 8px;
  .mtp {
    margin-top: 40px;
  }
  &:hover {
    border-color: #409eff;
  }
}

// 看破9i安
.cart-show {
  width: 355px;
  height: 100px;
  border-radius: 8px;
  background-size: 355px 120px !important;
  padding: 16px 0 20px 20px;
  box-sizing: border-box;

  font-size: 16px;
  color: #ffffff;
  line-height: 16px;
  .time-size {
    margin-top: 36px;
    font-weight: 400;
    font-size: 12px;
    color: #ffffff;
    line-height: 12px;
  }
}

// 色块
.color-block-div {
  border-radius: 4px;
  border: 1px solid #eaeaea;
  padding: 6px;
  cursor: pointer;
  & + & {
    margin-left: 8px;
  }
  &.key {
    border-color: #0071fe;
  }
}
.color-block-div > div {
  width: 68px;
  height: 22px;
}
</style>