<template>
  <div>
    <div class="flex-c footer-btn">
      <el-button @click="backFn">返回列表</el-button>
      <el-button class="ml10" v-if="is_draft && !isView" @click="next('draft')">保存草稿</el-button>
      <el-button class="ml10" v-if="num != 1" @click="up" type="primary">上一步</el-button>
      <el-button
        v-if="nextText"
        class="ml10"
        @click="next"
        type="primary"
        v-loading="isBtnLoading"
      >{{nextText}}</el-button>
    </div>
    <div style="height: 80px;"></div>
  </div>
</template>

<script>
import store from '@/store'
import { getStepNum } from './step.js'
import { mapGetters } from 'vuex'
export default {
  name: 'footerBtn',
  props: {
    isBtnLoading: {
      type: Boolean,
      default: false,
    },
    num: {
      type: Number,
      default: 0,
    },
    goodsType: {
      type: Number,
      default: 0,
    },
    benText: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      is_draft: false,
      goods_type: 1,
      shopId: 0,
      goodsStatus: 0,
    }
  },
  mounted() {
    this.init()
  },
  computed: {
    ...mapGetters({
      save_type: 'goodsDetaile/getSaveType',
      goods_id: 'goodsDetaile/getGoodsId',
    }),
    isView() {
      return this.save_type == 'view'
    },
    nextText() {
      let text = this.isView ? '下一步' : '保存, 下一步'
      if ((this.shopId > 0 && this.num == 5) || (this.shopId == 0 && this.num == 7)) {
        text = this.isView ? false : '发布'
      }
      text = this.benText || text
      return text
    },
    // 是否有审核页面 审核步骤单独判断
    isReject() {
      return (
        (this.$route.query.pageType == 'headquarters' && this.shopId > 0) ||
        (this.$route.query.pageType == 'shop' && this.goodsStatus == 3)
      )
    },
  },
  inject: ['goodsDetail'],
  methods: {
    init() {
      let data = this.goodsDetail()
      this.shopId = data.shop_id || ''
      this.goods_type = data.goods_type || 1
      this.is_draft = data.is_draft == 'Y'
      this.goodsStatus = data.goods_status || 0
    },
    // 回退
    backFn() {
      this.$router.go(-1)
    },
    // 上一步
    up() {
      getStepNum(this.num, this.goods_type, 'up', store)
    },

    // 下一步
    next(key) {
      if (this.goodsType > 0) {
        this.goods_type = this.goodsType
      }
      if (this.isView) {
        getStepNum(this.num, this.goods_type, 'next', store, this.isReject)
        return
      }
      // 规格/物流 单独页面处理
      if (this.num == 3 || this.num == 6) {
        this.$emit('next', key)
        return
      }

      let _this = this
      this.$emit(
        'next',
        function () {
          store.dispatch('goodsDetaile/setRefreshShopNum')
          if (key == 'draft') {
            _this.$message.success('保存成功')
            return
          }

          // 门店商品创建 或者 商品库创建 保存成功后 直接返回列表页面. -- 非驳回保存。驳回商品还有下一步
          if (
            !_this.isReject &&
            ((_this.shopId > 0 && _this.num == 5) || (_this.shopId == 0 && _this.num == 7))
          ) {
            _this.backFn()
            return
          }
          getStepNum(_this.num, _this.goods_type, 'next', store, _this.isReject)
        },
        key
      )
    },
  },
}
</script>

<style scoped lang="scss">
.footer-btn {
  position: fixed;
  width: calc(#{$base-right-content-width} - 42px);
  bottom: 0px;
  right: 20px;
  background: #fff;
  border-right: 1px solid #ebeef5;
  border-left: 1px solid #ebeef5;
  padding: 20px 0 20px;
  z-index: 4;
}
.footer-zw {
  height: 80px;
}
</style>