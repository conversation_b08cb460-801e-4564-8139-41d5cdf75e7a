<template>
  <div>
    <el-form label-position="top" label-width="200px">
      <el-form-item label="适用门店" required>
        <el-radio-group v-model="shop_rules.apply_shop_type" :disabled="isView">
          <el-radio :label="1">全部门店</el-radio>
          <el-radio :label="2">部分门店</el-radio>
          <el-radio :label="3">仅总部售卖</el-radio>
        </el-radio-group>
        <div>可选择将商品同步到哪些门店使用，选择'仅总部售卖'则商品不同步任何门店</div>
      </el-form-item>

      <el-form-item label="库存同步">
        <div>
          <el-switch
            active-value="Y"
            inactive-value="N"
            v-model="shop_rules.is_synchro_stock"
            :disabled="isView"
          ></el-switch>
          <div>
            <div>开启：填写的库存数量将同步至所有关联门店，即门店也具有相同数量的独立库存售卖；</div>
            <div>关闭：商品创建时，门店库存将自动设为0，门店可自行调整库存数量。</div>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="选择门店" required v-if="shop_rules.apply_shop_type == 2">
        <div class="flex-b">
          <div>门店列表</div>
          <el-button v-if="!isView" type="primary" icon="el-icon-plus" @click="addStoreAddress">添加门店</el-button>
        </div>
        <base-table
          :tableColumn="tableColumn"
          :tableRequest="goodsPickupShopList"
          :tableForm="tableForm"
          ref="baseTable"
          v-if="isTbale"
        >
          <template #operate="{scope}">
            <el-button v-if="!isView" type="text" @click="delFn(scope.row)">删除</el-button>
          </template>
        </base-table>
      </el-form-item>
    </el-form>

    <footerBtn @next="nextTick" :num="7" :isBtnLoading="isBtnLoading"></footerBtn>
    <addStore @selectKey="selectKey" ref="addStore"></addStore>
  </div>
</template>

<script>
import { goodsSaveFiveApi, goodsPickupShopList } from '@/api/shop/goodsManagement'
import addStore from '../addStore.vue'
import { mapGetters } from 'vuex'
import store from '@/store'
import footerBtn from './footerBtn.vue'
export default {
  name: 'usingStore',
  components: {
    addStore,
    footerBtn,
  },
  data() {
    return {
      goodsPickupShopList,
      tableForm: {
        shop_ids: [],
        name: '',
      },

      tableColumn: [
        {
          label: '门店名称',
          prop: 'name',
        },
        {
          label: '联系人',
          prop: 'manage_name',
        },
        {
          label: '联系方式',
          prop: 'contact_phone',
        },
        {
          label: '门店地址',
          prop: 'address',
        },
        {
          label: '操作',
          type: 'customize',
          prop: 'operate',
          width: 150,
        },
      ],
      shop_rules: {
        apply_shop_type: 1,
        is_synchro_stock: true,
      },
      rules: {
        apply_shop_type: [{ required: true, message: '请选择适用门店', trigger: 'change' }],
        is_synchro_stock: [{ required: true, message: '请选择库存同步', trigger: 'change' }],
      },
      isBtnLoading: false,
      isTbale: false,
    }
  },
  computed: {
    ...mapGetters({
      goods_id: 'goodsDetaile/getGoodsId',
      save_type: 'goodsDetaile/getSaveType',
    }),
    isView() {
      if (this.save_type == 'view') {
        this.tableColumn.splice(4, 1)
      }
      return this.save_type == 'view'
    },
  },
  inject: ['goodsDetail'],
  created() {
    this.init()
  },
  methods: {
    init() {
      let shop = this.goodsDetail()
      this.shop_rules = {
        apply_shop_type: parseInt(shop.delivery_rules.apply_shop_type),
        is_synchro_stock: shop.delivery_rules.is_synchro_stock,
      }
      this.tableForm.shop_ids = shop.delivery_rules.shop_ids || []
      this.isTbale = true
    },

    addStoreAddress() {
      goodsPickupShopList({
        shop_ids: this.tableForm.shop_ids,
        page: 1,
        limit: 10000,
      }).then((res) => {
        if (res.code == 200) {
          this.$refs.addStore.open(res.data.list || [])
        }
      })
    },
    // 下一步保存数据
    nextTick(callback, key) {
      if (this.shop_rules.apply_shop_type == 2 && this.tableForm.shop_ids.length < 1) {
        return this.$message.error('请选择门店')
      }
      this.isBtnLoading = true
      let params = {
        goods_id: this.goods_id,
        shop_rules: {
          apply_shop_type: this.shop_rules.apply_shop_type,
          is_synchro_stock: this.shop_rules.is_synchro_stock,
          shop_ids: this.tableForm.shop_ids,
        },
      }
      params.is_release = key != 'draft' ? 'Y' : 'N'

      goodsSaveFiveApi(params)
        .then((res) => {
          if (res.code == 200) {
            callback()
          }
        })
        .finally(() => {
          this.isBtnLoading = false
        })
    },

    // 选择门店回调
    selectKey(arr) {
      let shop_ids = arr.map((item) => item.id)
      this.tableForm.shop_ids = shop_ids
      this.$refs.baseTable.tableRequestFn(true)
    },

    delFn(row) {
      let ids = this.tableForm.shop_ids.filter((item) => item.id != row.id)
      this.tableForm.shop_ids = this.tableForm.shop_ids.filter((item) => item != row.id)
      console.log(ids, row.id, this.tableForm.shop_ids)
      this.$refs.baseTable.tableRequestFn(true)
    },
  },
}
</script>