<!--
 * @Author: shang<PERSON>i <EMAIL>
 * @Date: 2025-03-27 09:48:13
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-24 14:36:44
 * @FilePath: /qst-merchant-admin-2.0/src/views/shop/goodsManagement/compontes/specs.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="specs-tem">
    <el-form label-position="top" label-width="200px">
      <el-row :gutter="10">
        <el-col :lg="24" :md="24" :sm="24" :xl="24" :xs="24">
          <el-form-item label="规格类型" required>
            <el-radio-group v-model="specsType" :disabled="isView">
              <el-radio label="1">单规格</el-radio>
              <el-radio label="2">
                多规格
                <el-tooltip
                  v-if="goods_type == 2"
                  popper-class="maxWidth400"
                  class="maxWidth400"
                  effect="dark"
                  content="您当前选择的预约方式为在线预约，多规格商品的预约数量为共享使用，任一规格被预约后，该商品总可预约数量将同步扣减。若需独立设置可预约数量，请创建单独商品。"
                  placement="top"
                >
                  <img
                    :src="imgOssPath + '20250619_sf_wenhao.png'"
                    alt
                    class="icon_wenhao"
                    style="transform: translateY(2px);"
                  />
                </el-tooltip>
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <one-specs v-show="specsType == 1"></one-specs>
    <multi-specs v-show="specsType == 2"></multi-specs>
  </div>
</template>

<script>
import oneSpecs from './specs/oneSpecs.vue'
import multiSpecs from './specs/multiSpecs.vue'
import { mapGetters } from 'vuex'
export default {
  name: 'specs',
  components: {
    oneSpecs,
    multiSpecs,
  },

  computed: {
    ...mapGetters({
      type: 'goodsDetaile/getSpecsType',
      save_type: 'goodsDetaile/getSaveType',
    }),
    isView() {
      return this.save_type == 'view'
    },
  },
  inject: ['goodsDetail'],
  watch: {
    type: {
      immediate: true,
      handler(val) {
        this.specsType = val + ''
      },
    },
  },
  data() {
    return {
      specsType: '1',
      goods_type: 0,
    }
  },
  methods: {
    // 初始化商品详情
    initGoodsDetail() {
      let data = this.goodsDetail()
      this.goods_type = data.goods_type
    },
  },
  mounted() {
    this.initGoodsDetail()
  },
}
</script>

<style lang="scss" scoped>
.w100 {
  width: 100%;
  flex: 1;
}
</style>