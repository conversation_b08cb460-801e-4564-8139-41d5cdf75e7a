<template>
  <div>
    <div class="calendar">
      <el-dialog :close-on-click-modal="false" :visible.sync="isCalendar" title="预约日历">
        <div class="flex-b calendar-top">
          <div class="flex calendar-date" v-if="allDayList[allDayKey]">
            <span>{{allDayList[allDayKey].year}}年</span>
            <div class="option flex-b">
              <el-button @click="changeMonth('prev')" :disabled="allDayKey == 0" type="text">上个月</el-button>
              <span>{{allDayList[allDayKey].month}}月</span>
              <el-button @click="changeMonth('next')" :disabled="allDayKey == 11" type="text">下个月</el-button>
            </div>
          </div>
          <div class="flex-right" v-if="!isView">
            <el-button type="primary" @click="setDayBatch">批量设置</el-button>
            <div>点击某一天可设置该天可预约数量</div>
          </div>
        </div>
        <div class="ctable">
          <div class="th flex-a">
            <div v-for="item,index in thList" :key="index">{{item}}</div>
          </div>
          <div class="td flex" v-for="item,index in ctableList" :key="index">
            <div
              v-for="items,i in item"
              :key="i"
              class="td-son"
              :class="{
              'click': items.type == 1,
              'no-click': items.type != 1,
            }"
              @dblclick="setDay(items,index,i)"
            >
              <div
                class="ctable-num"
                :class="{
              'expiration': items.type == 2
            }"
              >{{items.day}}</div>

              <div v-if="items.isDel" class="ctable-del">禁用</div>
              <div v-if="items.type == 1" class="ctable-main">
                <div>可预约数量{{items.max_capacity}}</div>
                <div>已预约数量{{items.current_reserved}}</div>
              </div>
            </div>
          </div>
        </div>
      </el-dialog>
    </div>

    <set-prebook @confirmCalendar="confirmCalendar" @confirmDay="setDayCallback" ref="setPrebook"></set-prebook>
  </div>
</template>

<script>
import { getCardReservationApi } from '@/api/shop/goodsManagement'
import setPrebook from './setPrebook.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'calendar',
  components: {
    setPrebook,
  },
  props: {
    isView: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // 年月列表
      yearMonthList: [],
      allDayList: [],
      allDayKey: 0,
      isCalendar: false,
      thList: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
      // type 1 运行中   2 已结束
      ctableList: [],

      weeks: null,
      day: null,

      dayTitle: '', // 当天标题

      dayKeys: [0, 0],

      // 初始化数据
      cardReservaObj: {},
    }
  },
  computed: {
    ...mapGetters({
      goods_id: 'goodsDetaile/getGoodsId',
    }),
    isdUpMonth() {
      return this.allDayKey <= 0
    },
    isdDownMonth() {
      return this.allDayKey >= 11
    },
  },
  mounted() {
    // 获取当前日期
    this.yearMonthList = this.getDatesForYears()
    this.yearMonthList.map((item, index) => {
      this.initCalendar(item.year, item.month, index)
    })
    this.ctableList = this.allDayList[0].list
  },
  methods: {
    // 初始化日历
    open(res) {
      this.disableObj = res || {}
      this.isCalendar = true
      this.getCardInfo()
    },
    // 设置禁用
    delDay() {
      if (this.disableObj.dateType == 1) {
        let week = this.disableObj.week
        this.allDayList.forEach((item, index) => {
          item.list.forEach((dayItem, dayIndex) => {
            dayItem.map((items, i) => {
              if (items.type != 1) {
                return
              }
              if (week.indexOf(i) != -1) {
                this.$set(this.allDayList[index].list[dayIndex][i], 'isDel', true)
              }
            })
          })
        })
      } else {
        let dateObj = this.disableObj.dateObj
        this.allDayList.forEach((item, index) => {
          item.list.forEach((dayItem, dayIndex) => {
            dayItem.map((items, i) => {
              if (items.type != 1) {
                return
              }
              let key = dateObj?.[item.year]?.[item.month]?.[items.day]
              if (key) {
                this.$set(this.allDayList[index].list[dayIndex][i], 'isDel', true)
              }
            })
          })
        })
      }
      this.ctableList = this.allDayList[0].list
    },
    getDatesForYears() {
      const today = new Date()
      const maxday = new Date(today.getFullYear() + 1, today.getMonth(), today.getDate())
      const dates = []

      let date = new Date(today)
      // 循环，每个月的第一天
      while (date <= maxday) {
        date.setDate(1)
        dates.push({
          year: date.getFullYear(),
          month: date.getMonth() + 1,
        })
        date.setMonth(date.getMonth() + 1)
      }
      return dates
    },
    getCardInfo() {
      getCardReservationApi({
        goods_id: this.goods_id,
      }).then((res) => {
        if (res.code == 200) {
          if (res.data.length > 0 && JSON.stringify(this.cardReservaObj) == '{}') {
            this.cardReservaObj = {}
            // 将数据对象化
            res.data.map((item) => {
              item.days.map((day) => {
                if (this.cardReservaObj[item.year] && this.cardReservaObj[item.year][item.month]) {
                  this.cardReservaObj[item.year][item.month] = {
                    ...this.cardReservaObj[item.year][item.month],
                    [day.day]: day,
                  }
                } else if (this.cardReservaObj[item.year]) {
                  this.cardReservaObj[item.year][item.month] = {
                    [day.day]: day,
                  }
                } else {
                  this.cardReservaObj[item.year] = {
                    [item.month]: {
                      [day.day]: day,
                    },
                  }
                }
              })
            })
            this.yearMonthList.map((item, index) => {
              this.initCalendar(item.year, item.month, index)
            })
            this.ctableList = this.allDayList[0].list
            this.delDay()
          } else {
            this.delDay()
          }
        }
      })
    },

    // 初始化
    initCalendar(year = '', month = '', index) {
      // 获取当前日期
      let now
      // 获取今天是几号 (1-31)
      if (index == 0) {
        now = new Date()
        this.currentDate = now.getDate()
      } else {
        let outNow = new Date(year, month - 1)
        now = new Date(outNow.getFullYear(), outNow.getMonth(), 1)
      }
      let lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0)
      let daysInMonth = lastDayOfMonth.getDate()

      // 获取当月数据 -- 挂载当前数据
      let list = []
      for (let i = 1; i <= daysInMonth; i++) {
        let type = 1
        if (index == 0) {
          type = i < this.currentDate ? 2 : 1
        }
        list.push({
          type,
          day: i,
          max_capacity: this.cardReservaObj[year]?.[month]?.[i]?.max_capacity || 0,
          current_reserved: this.cardReservaObj[year]?.[month]?.[i]?.current_reserved || 0,
          isDel: false,
        })
      }
      this.arrTable(list, now, index, year, month)
    },
    // 将数组 转成2维数组
    arrTable(list, now, index, year, month) {
      // 获取一号是周几
      let firstDay = new Date(now.getFullYear(), now.getMonth(), 1)
      let firstDayOfWeek = firstDay.getDay()

      // 月前空数组
      list = this.addNull(firstDayOfWeek, list, 'unshift')
      let i = -1

      list = list.reduce((arr, item, index) => {
        index % 7 == 0 && arr.push([]) && (i += 1)
        arr[i].push(item)
        // 月末空数据
        if (index == list.length - 1) {
          arr[i] = this.addNull(7 - arr[i].length, arr[i], 'push')
        }
        return arr
      }, [])
      this.allDayList[index] = {
        year,
        month,
        list,
      }
    },

    // 添加空数据
    addNull(max, list, type) {
      for (let i = 0; i < max; i++) {
        list[type]({
          type: 3,
        })
      }
      return list
    },

    // 改个月
    changeMonth(type) {
      this.allDayKey =
        type == 'next' ? this.allDayKey + 1 : type == 'prev' ? this.allDayKey - 1 : this.allDayKey
      this.ctableList = this.allDayList[this.allDayKey].list
    },

    // 设置天
    setDay(item, index, i) {
      if (this.isView) {
        return
      }
      if (item.type != 1) {
        return
      }
      this.dayKeys = [index, i]
      let dayItem = this.allDayList[this.allDayKey]
      this.$refs.setPrebook.open('calendarDay', {
        title: '设置' + dayItem.year + '-' + dayItem.month + '-' + item.day + '库存',
        ...item,
      })
    },
    // 设置天回调
    setDayCallback(num) {
      this.$set(this.ctableList[this.dayKeys[0]][this.dayKeys[1]], 'max_capacity', num)
    },

    // 批量设置日期
    setDayBatch() {
      this.$refs.setPrebook.open('calendarBatch')
    },

    // 批量设置天
    confirmCalendar(e) {
      let { dateRange, maxCapacity } = e
      // 设置天
      if (dateRange.dateType == '2') {
        let dateObj = dateRange.dateObj
        this.allDayList.forEach((item, index) => {
          item.list.forEach((dayItem, dayIndex) => {
            dayItem.map((items, i) => {
              if (items.type != 1) {
                return
              }
              let key = dateObj?.[item.year]?.[item.month]?.[items.day]
              if (key && items.max_capacity == 0) {
                this.$set(this.allDayList[index].list[dayIndex][i], 'max_capacity', maxCapacity)
              }
            })
          })
        })
        this.ctableList = this.allDayList[this.allDayKey].list
      } else if (dateRange.dateType == '1') {
        // 设置周几
        let week = dateRange.week
        this.allDayList.forEach((item, index) => {
          item.list.forEach((dayItem, dayIndex) => {
            dayItem.map((items, i) => {
              if (items.type != 1 || index > 6) {
                return
              }
              if (week.indexOf(i) != -1 && items.max_capacity == 0) {
                this.$set(this.allDayList[index].list[dayIndex][i], 'max_capacity', maxCapacity)
              }
            })
          })
        })
      }
    },
    getData() {
      let list = []
      this.allDayList.forEach((item, index) => {
        item.list.forEach((dayItem, dayIndex) => {
          dayItem.map((items, i) => {
            if (items.max_capacity > 0 && items.type == 1) {
              list.push({
                day:
                  item.year +
                  '-' +
                  String(item.month).padStart(2, '0') +
                  '-' +
                  String(items.day).padStart(2, '0'),
                max_capacity: Number(items.max_capacity),
              })
            }
          })
        })
      })
      return list
    },
  },
}
</script>

<style>
.calendar .el-dialog {
  width: 880px !important;
}
</style>

<style lang="scss" scoped>
.calendar-top {
  align-items: flex-end;
  width: 840px;
}
.flex-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  > div {
    font-weight: 400;
    font-size: 14px;
    color: #999999;
    line-height: 32px;
  }
}

.calendar-date {
  font-size: 14px;
  color: #222222;
  line-height: 22px;
  font-weight: 600;
  .option {
    width: 220px;
    height: 32px;
    padding: 0 10px;
    line-height: 32px;
    border: 1px solid #dcdfe6;
    box-sizing: border-box;
    margin-left: 16px;
  }
}

// 日历样式
.ctable {
  margin-top: 20px;
  .th {
    width: 840px;
    height: 40px;
    background: #f5f7fa;
    box-shadow: inset 0px -1px 0px 0px #ebeef5;
  }

  .td-son {
    border-top: 1px solid #dcdfe6;
    border-left: 1px solid #dcdfe6;
    box-sizing: border-box;
  }
  .td-son:last-child {
    border-right: 1px solid #dcdfe6;
  }
  .td:last-child {
    border-bottom: 1px solid #dcdfe6;
  }

  .td-son {
    position: relative;
    width: 120px;
    height: 120px;
    box-sizing: border-box;
    &.click {
      cursor: pointer;
      &:hover {
        background: #e7f2ff;
      }
    }
    &.no-click {
      pointer-events: none;
    }
    .ctable-num {
      position: absolute;
      right: 11px;
      top: 11px;
      font-weight: 600;
      font-size: 16px;
      color: #303133;
      line-height: 16px;
      &.expiration {
        color: #c0c4cc;
      }
    }
    .ctable-del {
      position: absolute;
      left: 0px;
      top: 0px;
      font-size: 14px;
      background: #dcdfe6;
      border-radius: 0px 0px 8px 0px;
      font-weight: 400;
      font-size: 14px;
      color: #999999;
      line-height: 20px;
      text-align: left;
      padding: 0 10px;
    }
    .ctable-main {
      margin-top: 46px;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #999999;
      line-height: 30px;
      .price {
        font-weight: 400;
        font-size: 16px;
        color: #222222;
        line-height: 22px;
        font-style: normal;
      }
    }
  }
}
</style>