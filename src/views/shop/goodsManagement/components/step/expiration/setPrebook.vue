<!-- 预约数量设置 -->
<template>
  <div>
    <!-- 时段 批量设置可预约数量 -- 表格 -->
    <el-dialog :close-on-click-modal="false" :visible.sync="isTimeBatch" title="批量设置可预约数量">
      <div>
        <el-form :model="timeBatchForm" :rules="numRule" ref="timeBatchForm">
          <el-form-item prop="maxCapacity" label="可预约数量">
            <el-input
              placeholder="请输入"
              v-model.number="timeBatchForm.maxCapacity"
              @blur="(e)=>inputNumStep('timeBatchForm',  'maxCapacity', e,)"
            ></el-input>
            <div>将为所有时段设置相同的可预约数量</div>
          </el-form-item>
        </el-form>
        <base-dialog-footer @confirm="timeBatchConfirm" @cancel="isTimeBatch = false"></base-dialog-footer>
      </div>
    </el-dialog>

    <!-- 日历预约 -->
    <el-dialog :close-on-click-modal="false" :visible.sync="isDay" :title="dayTitle || '批量设置可预约数量'">
      <el-form :model="dayForm" :rules="numRule" ref="dayForm">
        <el-form-item prop="maxCapacity" label="可预约数量">
          <el-input
            placeholder="请输入"
            v-model.number="dayForm.maxCapacity"
            @blur="(e)=>inputNumStep('dayForm',  'maxCapacity', e,)"
          ></el-input>
          <div>当天已预约数量{{currentReserved}}</div>
        </el-form-item>
      </el-form>
      <base-dialog-footer @confirm="dayConfirm" @cancel="isDay = false"></base-dialog-footer>
    </el-dialog>

    <!-- 日历批量设置可预约数量 -->
    <el-dialog :close-on-click-modal="false" :visible.sync="isBatchSet" title="批量设置可预约数量">
      <el-form :model="batchForm" :rules="numRule" ref="batchForm">
        <!-- 批量设置条件 -->
        <el-form-item label="批量设置条件" required>
          <dateSet v-if="isBatchSet" ref="dateSet" :type="'batch'" :isDay="true"></dateSet>
        </el-form-item>

        <el-form-item prop="maxCapacity" label="可预约数量">
          <el-input
            placeholder="请输入"
            v-model.number="batchForm.maxCapacity"
            @blur="(e)=>inputNumStep('batchForm',  'maxCapacity', e,)"
          ></el-input>
        </el-form-item>
      </el-form>
      <base-dialog-footer @confirm="calendarPrebookConfirm" @cancel="isDay = false"></base-dialog-footer>
    </el-dialog>
  </div>
</template>

<script>
import dateSet from './dateSet.vue'
export default {
  name: 'setPrebook',
  components: {
    dateSet,
  },
  props: {},
  data() {
    return {
      // 单独
      isTimeBatch: false,
      timeBatchForm: { maxCapacity: '' },
      batchTableSetRule: {
        num: [
          { required: true, message: '请输入预约数量', trigger: 'blur' },
          {
            type: 'number',
            min: 1,
            trigger: 'blur',
          },
        ],
      },

      // start 批量设置
      numRule: {
        maxCapacity: [{ required: true, message: '请输入预约数量', trigger: 'blur' }],
      },
      // 日历预约
      isDay: false,
      dayForm: {
        maxCapacity: '',
      },
      dayTitle: '',
      currentReserved: 0,

      // 日历批量设置可预约数量
      isBatchSet: false,
      batchForm: {
        maxCapacity: '',
      },
      // 日期范围列表
    }
  },
  methods: {
    // 输入整数
    inputNumStep(form, code, num) {
      if (this[form][code] != '' || this[form][code] == 0) {
        console.log(isNaN(this[form][code]) || Number(this[form][code]) < 0)
        if (isNaN(this[form][code]) || Number(this[form][code]) < 0) {
          this.$message.error('请输入大于等于0的数字')
          this[form][code] = ''
        } else {
          this[form][code] = Number(this[form][code]).toFixed(num)
        }
      }
    },
    // 打开批量设置弹窗
    // type: calendarDay itemObj: 「标题」「当天已预约数量」
    open(type, itemObj) {
      console.log(type, itemObj)
      // 单
      if (type == 'calendarDay') {
        this.dayTitle = itemObj.title
        this.currentReserved = itemObj.current_reserved
        this.dayForm.maxCapacity = itemObj.max_capacity
        this.isDay = true
      } else if (type == 'calendarBatch') {
        this.isBatchSet = true
        this.batchForm.maxCapacity = ''
      } else if (type == 'timeBatch') {
        this.isTimeBatch = true
        this.timeBatchForm.maxCapacity = ''
      }
    },
    // 一天设置确认按钮事件
    dayConfirm() {
      let _this = this
      this.$refs.dayForm.validate((valid) => {
        if (valid) {
          _this.isDay = false
          _this.$emit('confirmDay', this.dayForm.maxCapacity)
        } else {
          return false
        }
      })
    },
    // 日历批量设置确认按钮事件
    calendarPrebookConfirm() {
      let _this = this
      this.$refs.batchForm.validate((valid) => {
        if (valid) {
          this.$refs.dateSet.getDate(true).then((res) => {
            _this.isBatchSet = false
            _this.$emit('confirmCalendar', {
              maxCapacity: _this.batchForm.maxCapacity,
              dateRange: res,
            })
          })
        } else {
          return false
        }
      })
    },
    timeBatchConfirm() {
      let _this = this
      this.$refs.timeBatchForm.validate((valid) => {
        if (valid) {
          _this.isTimeBatch = false
          _this.$emit('confirmTime', _this.timeBatchForm.maxCapacity)
        } else {
          return false
        }
      })
    },
  },
  watch: {},
}
</script>

<style lang="scss" scoped>
.add_reason {
  width: 100%;
  border-style: dashed;
}
</style>