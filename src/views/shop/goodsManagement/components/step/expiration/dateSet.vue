<template>
  <div>
    <el-radio-group v-model="form.disable_date_type" v-if="type == 'isDisable'" :disabled="isView">
      <el-radio :label="1">按星期禁用</el-radio>
      <el-radio :label="2" style="margin: 10px 0;">按日期周期禁用</el-radio>
    </el-radio-group>
    <el-radio-group v-model="form.disable_date_type" v-if="type == 'batch'" :disabled="isView">
      <el-radio :label="2">按日期范围（最多设置未来1年内的可预约数量）</el-radio>
      <el-radio :label="1" style="margin: 10px 0;">按星期（仅支持设置未来6个月内符合条件的可预约数量）</el-radio>
    </el-radio-group>
    <div v-if="form.disable_date_type == 1">
      <el-select class="w100" v-model="week" multiple placeholder="当选择有禁用">
        <el-option v-for="item in weekList" :key="item.key" :label="item.name" :value="item.key"></el-option>
      </el-select>
    </div>
    <div class="disable-week" v-if="form.disable_date_type == 2">
      <div v-for="(item,index) in disableTimeList" :key="index" class="mt10">
        <div class="flex">
          <el-date-picker
            :disabled="isView"
            class="w100"
            :value-format="'yyyy-MM-dd'"
            v-model="item.date"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
            @focus="delIndex = index"
            @blur="delIndex = -1"
          ></el-date-picker>
          <el-button
            v-if="!isView"
            class="del-disable"
            @click="deleteTime(index)"
            type="text"
            style
          >删除</el-button>
        </div>
        <div v-if="type == 'batch'" class="flex mt10">
          <span>快速设置</span>
          <div class="flex">
            <el-button type="text" @click="setTimeList('month', index)">至月底</el-button>
            <el-button type="text" @click="setTimeList('quarter', index)">至季度末</el-button>
            <el-button type="text" @click="setTimeList('year', index)">至年底</el-button>
            <el-button type="text" @click="setTimeList('nextYear', index)">至明年今日</el-button>
          </div>
        </div>
      </div>

      <el-button
        v-if="disableTimeList.length <= 4 && !isView"
        @click="addDelTime"
        icon="el-icon-plus"
        class="w100 bordel-dashed"
      >添加日期段（最多添加五个）</el-button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    type: {
      type: String,
      default: 'isDisable',
    },
    // 是否获取时间段的天数的合集
    isDay: {
      type: Boolean,
      default: false,
    },

    disableDateInit: {
      type: Object,
      default() {
        return {}
      },
    },
    isView: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      disableTimeList: [],
      form: {
        disable_date_type: 1,
      },
      weekList: [
        { key: 1, name: '星期一' },
        { key: 2, name: '星期二' },
        { key: 3, name: '星期三' },
        { key: 4, name: '星期四' },
        { key: 5, name: '星期五' },
        { key: 6, name: '星期六' },
        {
          key: 0,
          name: '星期日',
        },
      ],
      week: [],
      delIndex: -1,
      pickerOptions: {
        disabledDate: this.disabledDateMethod,
      },
    }
  },
  watch: {
    disableDateInit: {
      handler(val) {
        console.log(val, '-----=====')
        this.form.disable_date_type = val.disable_date_type || 1
        if (val.disable_date_type == 1) {
          this.week = val.disable_date || []
        } else {
          this.disableTimeList = val.disable_date || []
        }
      },
      immediate: true,
      deep: true,
    },
    week(val) {
      this.upDisable()
    },
    disableTimeList: {
      handler() {
        this.upDisable()
      },
      deep: true,
      immediate: true,
    },
    'form.disable_date_type'(val) {
      this.upDisable()
    },
  },
  methods: {
    // 禁用时间段
    disabledDateMethod(time) {
      // 禁用当天之前 禁用默认输出false。有值后选择禁用区间
      let isDel = time < new Date().getTime() - 86400000
      let clickTime = time.getTime()
      this.disableTimeList.map((item, index) => {
        if (item.date && item.date[0] && item.date[1] && index != this.delIndex) {
          isDel =
            isDel ||
            (clickTime + 86500000 >= new Date(item.date[0]).getTime() &&
              clickTime - 1000 < new Date(item.date[1]).getTime())
        }
      })
      console.log(isDel)
      return isDel
    },
    // 添加
    addDelTime() {
      this.disableTimeList.push({
        date: [],
      })
    },
    // 删除时间段
    deleteTime(index) {
      this.disableTimeList.splice(index, 1)
    },

    // 快速设置
    setTimeList(type, index = 0) {
      let time = []
      const today = new Date()
      const format = (date) => date.toISOString().split('T')[0]
      switch (type) {
        case 'month':
          const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0)
          time = [format(today), format(endOfMonth)]
          break
        case 'quarter':
          const endOfQuarter = new Date(
            today.getFullYear(),
            Math.floor((today.getMonth() + 3) / 3) * 3,
            0
          )
          time = [format(today), format(endOfQuarter)]
          break
        case 'year':
          const endOfYear = new Date(today.getFullYear() + 1, 0, 0)
          time = [format(today), format(endOfYear)]
          break
        // 至明年今日
        case 'nextYear':
          const endOfNextYear = new Date(today.getFullYear() + 1, today.getMonth(), today.getDate())
          time = [format(today), format(endOfNextYear)]
          break
        default:
          return []
      }
      this.disableTimeList[index].date = time
    },

    // 更新数据
    upDisable() {
      if (this.type != 'isDisable') {
        return
      }
      let disableTimeList = this.disableTimeList
        .filter((item) => item.date.length > 0 && item.date[0] && item.date[1])
        .map((item) => {
          return {
            ...item,
            start_time: item.date[0],
            end_time: item.date[1],
          }
        })
      console.log(this.week)
      this.$emit('upDisable', {
        dateType: this.form.disable_date_type,
        week: this.week,
        disableTimeList: disableTimeList || [],
      })
    },

    // 获取时间详细数据
    getDate(day) {
      return new Promise((resolve) => {
        let disableTimeList = this.disableTimeList
          .filter((item) => item.date.length > 0 && item.date[0] && item.date[1])
          .map((item) => {
            return {
              ...item,
              start_time: item.date[0],
              end_time: item.date[1],
            }
          })
        let data = {
          dateType: this.form.disable_date_type,
          week: this.week,
          disableTimeList,
          dateObj: [],
        }
        if (this.form.disable_date_type == 2 && day) {
          // 时间段合集的年月日数组
          let dayList = []
          disableTimeList.map((item) => {
            if (item.date && item.date[0] && item.date[1]) {
              const startDate = new Date(item.date[0])
              const endDate = new Date(item.date[1])
              for (let date = startDate; date <= endDate; date.setDate(date.getDate() + 1)) {
                dayList.push(new Date(date).toISOString().split('T')[0])
              }
            }
          })
          let time = [...new Set(dayList)]
          // 将去重后的数据按年月日对象归类，并存入time字段中
          let dateObj = {}
          time.forEach((item) => {
            let time = item.split('-')
            let year = time[0]
            let month = parseInt(time[1])
            let day = parseInt(time[2])
            if (!dateObj[year]) {
              dateObj[year] = {}
            }
            if (dateObj[year][month]) {
              dateObj[year][month][day] = item
            } else {
              dateObj[year][month] = { [day]: item }
            }
          })
          // 去重年月日数组，并存入time字段中
          data.dateObj = dateObj
        }
        resolve(data)
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.w100 {
  width: 100%;
}
// 禁用 功能
.disable-week > * + * {
  margin-top: 10px;
}
.del-disable {
  color: #ff2727;
  margin-left: 20px;
}
</style>