<template>
  <div>
    <el-form ref="form" label-position="top" label-width="200px" :model="form" :rules="rules">
      <el-form-item label="时段划分">
        <el-radio-group v-model="form.radio">
          <el-radio :label="1">自动划分</el-radio>
          <el-radio :label="2">
            自定义划分
            <el-tooltip
              class="item"
              effect="dark"
              content="请按时间先后顺序添加，后一时间段开始时间不可早于前一时间段结束时间，最多可添加48个时段"
              placement="top"
            >
              <img :src="imgOssPath + '20250619_sf_wenhao.png'" alt class="icon_wenhao" />
            </el-tooltip>
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-row :gutter="24" v-if="form.radio == 1">
        <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
          <el-form-item label="营业时间">
            <el-time-picker
              is-range
              format="HH:mm"
              arrow-control
              value-format="HH:mm"
              v-model="form.times"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              placeholder="选择时间范围"
            ></el-time-picker>
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
          <el-form-item label="时间间隔">
            <div class="flex">
              <el-input placeholder="请输入" @blur="inputNum" v-model.number="form.minute">
                <template slot="append">分钟</template>
              </el-input>
              <el-button @click="setTime" type="text" style="margin: 0 10px;">设置</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="营业时间" v-if="form.radio == 2">
        <div class="flex w100">
          <el-time-picker
            is-range
            arrow-control
            format="HH:mm"
            value-format="HH:mm"
            v-model="stepTime"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            placeholder="选择时间范围"
          ></el-time-picker>
          <el-button type="text" @click="addStep" style="margin: 0 10px;">添加</el-button>
        </div>
      </el-form-item>

      <el-form-item label="时段列表">
        <el-table :data="tableList" style="width: 100%" row-key="start_time">
          <el-table-column prop="date" label="时段" width="260">
            <template slot-scope="scope">{{scope.row.start_time + '-' + scope.row.end_time }}</template>
          </el-table-column>
          <el-table-column label>
            <template slot="header">
              <div class="flex-b">
                <span>可预约数量</span>
                <el-button size="mini" @click="bathAdd" type="primary">批量设置</el-button>
              </div>
            </template>
            <template slot-scope="scope">
              <el-input v-model.number="scope.row.max_capacity" placeholder="输入可预约的数量"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="160">
            <template slot-scope="scope">
              <el-button @click="delRon(scope.$index)" type="text">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          v-if="tableData.length > 0"
          :current-page="pageNo"
          :page-size="pageSize"
          @current-change="pageNo = $event"
          background
          layout="prev, pager, next"
          :total="tableData.length"
        ></el-pagination>
      </el-form-item>
    </el-form>
    <setPrebook @confirmTime="confirmTime" ref="setPrebook"></setPrebook>
  </div>
</template>

<script>
import setPrebook from './setPrebook.vue'
export default {
  components: {
    setPrebook,
  },
  props: {
    reservation_number_slot: {
      type: Array,
      default() {
        return []
      },
    },
  },
  watch: {
    reservation_number_slot: {
      immediate: true,
      deep: true,
      handler(val) {
        this.tableData = val
      },
    },
  },
  data() {
    return {
      form: {
        radio: 1,
        times: ['00:00', '23:59'],
        minute: '',
      },
      rules: {},
      tableData: [],
      pageSize: 10,
      pageNo: 1,
      stepTime: ['00:00', '23:59'],
    }
  },
  computed: {
    tableList() {
      // 根据pageSize和pageNo计算当前页的数据
      return this.tableData.slice((this.pageNo - 1) * this.pageSize, this.pageNo * this.pageSize)
    },
  },
  methods: {
    inputNum() {
      if (this.form.minute != '') {
        if (isNaN(this.form.minute) || Number(this.form.minute) < 10) {
          this.$message.error('请输入大于10的数字')
          this.form.minute = ''
        } else if (this.form.minute > 1440) {
          this.$message.error('请输入小于1440的数字')
          this.form.minute = ''
        } else {
          this.form.minute = Number(this.form.minute).toFixed(0)
        }
      }
    },
    // 添加时间段
    addStep() {
      if (this.stepTime.length != 2) {
        return this.$message.error('请选择时间段')
      }
      console.log(this.stepTime)
      let start_time = this.stepTime[0]
      let end_time = this.stepTime[1]
      let startMinute = Number(start_time.split(':')[0]) * 60 + Number(start_time.split(':')[1])
      let endMinute = Number(end_time.split(':')[0]) * 60 + Number(end_time.split(':')[1])

      if (endMinute - startMinute < 10) {
        return this.$message.error('时间段间隔不能小于10分钟')
      }

      if (
        (startMinute == '00:00' && this.tableData.length > 0) ||
        (this.tableData.length > 0 &&
          this.tableData[this.tableData.length - 1].endMinute >= startMinute)
      ) {
        return this.$message.error(
          '请按时间先后顺序添加，后一时间段开始时间不可早于前一时间段结束时间'
        )
      }
      this.tableData.push({
        start_time,
        end_time,
        endMinute,
        max_capacity: 0,
      })
      this.stepTime = [end_time, '23:59']
      console.log(this.stepTime)
    },
    // 设置时间间隔
    setTime() {
      if (this.form.minute == '' || this.form.times == null) {
        return this.$message.error('请输入时间间隔')
      }
      if (this.form.times.length != 2) {
        return this.$message.error('请选择营业时间')
      }
      let start_time = this.form.times[0]
      let end_time = this.form.times[1]
      let minute = Number(this.form.minute)

      // 根据开始时间和结束时间，循环计算出各个时间段
      let endMinute = Number(end_time.split(':')[0]) * 60 + Number(end_time.split(':')[1])
      let startMinute = Number(start_time.split(':')[0]) * 60 + Number(start_time.split(':')[1])

      if (
        this.tableData.length > 0 &&
        this.tableData[this.tableData.length - 1].end_time <= startMinute
      ) {
        return this.$message.error('时间段重复')
      }

      let arr = []
      while (startMinute < endMinute && startMinute + minute <= 1440) {
        let start_time = this.timeChange(startMinute)
        let end_time = this.timeChange(startMinute + minute)
        startMinute + minute == 1440 ? (end_time = '23:59') : end_time
        arr.push({
          start_time,
          end_time,
          max_capacity: 0,
        })
        startMinute += minute
      }
      this.tableData = arr
    },
    timeChange(start_time) {
      return `${String(Math.floor(start_time / 60)).padStart(2, '0')}:${String(
        start_time % 60
      ).padStart(2, '0')}`
    },

    // 删除时间段
    delRon(index) {
      this.tableData.splice(index, 1)
    },

    // 批量设置时间段
    bathAdd() {
      this.$refs.setPrebook.open('timeBatch')
    },

    confirmTime(max_capacity) {
      this.tableData.forEach((item, index) => {
        item.max_capacity = Number(max_capacity)
      })
    },

    // 获取数据
    getData() {
      return this.tableData
    },
  },
}
</script>


<style lang="scss" scoped>
.w100 {
  width: 100%;
}
::v-deep {
  .el-table thead,
  .el-table th.el-table__cell {
    background: #ebeef5;

    div {
      font-weight: 600;
      font-size: 14px;
      color: #303133 !important;
    }
  }
}
</style>