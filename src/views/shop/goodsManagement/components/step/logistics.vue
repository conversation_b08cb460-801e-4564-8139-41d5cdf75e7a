<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-03-26 15:47:49
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-28 16:26:46
 * @FilePath: /qst-merchant-admin-2.0/src/views/shop/goodsManagement/compontes/basicInformation.vue
 * @Description: 修改物流控制
-->
<template>
  <div class="logistics">
    <el-form
      ref="form"
      label-position="top"
      :model="form"
      label-width="200px"
      :rules="basicInfoRule"
    >
      <el-row :gutter="10">
        <el-col>
          <el-form-item label="配送方式" prop="delivery_methods">
            <el-checkbox-group
              v-model="form.delivery_methods"
              @change="methodsFn"
              :disabled="isView"
            >
              <el-checkbox label="express">快递发货</el-checkbox>
              <el-checkbox label="pickup">自提</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>

        <el-col v-if="isDelivery">
          <el-form-item label="运费设置" prop="shipping_fee_type">
            <el-radio-group v-model="form.shipping_fee_type" :disabled="isView">
              <el-radio label="free">包邮</el-radio>
              <el-radio label="fixed">固定运费</el-radio>
              <el-radio label="template">使用运费模版</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col v-if="form.shipping_fee_type == 'fixed' && isDelivery">
          <el-form-item label="固定运费" prop="fixed_fee">
            <div class="input-number">
              <el-input
                :disabled="isView"
                placeholder="请输入固定运费金额"
                v-model="form.fixed_fee"
                controls-position="right"
                :controls="false"
                prefix-icon="¥"
                @blur="inputFree($event, 'fixed_fee')"
              ></el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col v-if="form.shipping_fee_type == 'template' && isDelivery">
          <el-form-item label="运费模版" prop="delivery_id">
            <div class="flex-b">
              <el-select
                class="w100"
                v-model="form.delivery_id"
                placeholder="请选择"
                :disabled="isView"
              >
                <el-option
                  v-for="item in deliveryList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
              <el-button
                v-if="!isView"
                style="margin-left: 10px"
                @click="addFreight"
                type="primary"
                size="small"
              >新增运费模版</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <footerBtn
      :benText="isPickup ? '' : '发布'"
      :num="6"
      :isBtnLoading="isBtnLoading"
      @next="nextTick"
    ></footerBtn>

    <createFreightDialog
      :billing_methodList="billing_methodList"
      :datailTemplateList="datailTemplateList"
      :loadingEdit="false"
      ref="createFreight"
      @confirm="confirm"
    ></createFreightDialog>
  </div>
</template>

<script>
import { getdeliveryList, getdeliveryMethod } from '@/api/shop/goodsSet/freightTemplate'
import { mapGetters } from 'vuex'
import createFreightDialog from '../../../goodsSet/freightTemplate/createFreightDialog.vue'
import { goodsSaveFourthApi } from '@/api/shop/goodsManagement.js'
import store from '@/store'
import storeBasicInfoDialog from '@/views/enterpriseManagement/storeManagement/component/storeBasicInfo/storeBasicInfoDialog.vue'
import footerBtn from './footerBtn.vue'

export default {
  name: 'logistics',
  data() {
    return {
      form: {
        delivery_methods: [],
        shipping_fee_type: 'free',
        fixed_fee: '',
        delivery_id: undefined,
        local_id: [],
      },
      basicInfoRule: {
        delivery_methods: [{ required: true, message: '请选择配送方式', trigger: 'blur' }],
        shipping_fee_type: [{ required: true, message: '请选择运费设置', trigger: 'blur' }],
        fixed_fee: [
          { required: true, message: '请输入固定运费', trigger: 'blur' },
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value > 0) {
                callback()
              } else {
                return callback(new Error('输入金额必须大于0'))
              }
            },
            trigger: 'blur',
          },
        ],
        delivery_id: [{ required: true, message: '请选择运费模板', trigger: 'blur' }],
        local_id: [{ required: true, message: '请选择自提点', trigger: 'blur' }],
      },
      deliveryList: [],
      datailTemplateList: {},
      isBtnLoading: false,

      billing_methodList: [],
    }
  },
  components: {
    createFreightDialog,
    storeBasicInfoDialog,
    footerBtn,
  },
  computed: {
    isDelivery() {
      return this.form.delivery_methods.indexOf('express') != -1
    },
    isPickup() {
      return this.form.delivery_methods.indexOf('pickup') != -1
    },
    ...mapGetters({
      shop_id: 'goodsDetaile/getShopId',
      goods_id: 'goodsDetaile/getGoodsId',
      save_type: 'goodsDetaile/getSaveType',
    }),
    isView() {
      return this.save_type == 'view'
    },
  },
  watch: {
    shop_id: {
      handler(val) {
        if (val) {
          console.log('获取运费模版列表', val)
          this.getdeliveryList()
        }
      },
      deep: true,
      immediate: true,
    },
  },
  inject: ['goodsDetail'],
  mounted() {
    this.getBillmethods()
    this.getDetails()
  },
  methods: {
    getDetails() {
      let data = this.goodsDetail()
      this.form = {
        ...data.delivery_rules,
        delivery_id: data.delivery_rules.delivery_id > 0 ? data.delivery_rules.delivery_id : '',
      }
      this.form.local_id = this.form.local_id.map((item) => Number(item))
    },
    getBillmethods() {
      getdeliveryMethod().then((res) => {
        this.billing_methodList = res.data.map((item) => {
          return {
            label: item.label,
            value: item.value,
          }
        })
      })
    },
    addFreight() {
      this.datailTemplateList = {}
      this.$refs.createFreight.open({ shop_id: this.shop_id })
    },

    confirm(val) {
      this.getdeliveryList()
    },
    async getdeliveryList() {
      const res = await getdeliveryList({
        shop_id: this.shop_id,
        page: 1,
        limit: 100,
        status: 1,
      })
      if (res.code == 200) {
        this.deliveryList = res.data.list
      } else {
        this.deliveryList = []
        this.$message({
          type: 'error',
          message: res.msg,
        })
      }
    },

    // 商品保存
    nextTick(callback, key) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.isBtnLoading = true
          this.goodsSaveFourth(key)
        } else {
          return false
        }
      })
    },
    goodsSaveFourth(key) {
      let params = {
        delivery_rules: this.form,
        goods_id: this.goods_id,
      }
      params.is_release = key == 'draft' || this.isPickup ? 'N' : 'Y'
      goodsSaveFourthApi(params)
        .then((res) => {
          this.isBtnLoading = false
          if (res.code == 200) {
            console.log('this.isPickup', this.isPickup)
            if (this.isPickup) {
              store.dispatch('goodsDetaile/setTabKeys', '7')
              store.dispatch('goodsDetaile/setRefreshShopNum')
            } else {
              this.$message({
                type: 'success',
                message: '保存成功！',
              })
              this.$router.back()
            }
          }
        })
        .finally(() => {
          this.isBtnLoading = false
        })
    },
    inputFree(e, code) {
      let val = e.target.value
      if (isNaN(val) || val <= 0) {
        this.form[code] = ''
      } else {
        this.form[code] = Number(val).toFixed(2)
      }
    },
    // 修改配送方式
    methodsFn() {
      store.dispatch('goodsDetaile/setApplyStores', this.isPickup)
    },
  },
}
</script>

<style lang="scss" scoped>
.w100 {
  width: 100%;
  flex: 1;
}
.el-select-dropdown__item {
  height: auto;
}

.logistics ::v-deep {
  .el-input-number--small {
    width: 300px;
    text-align: left;
  }
  .el-input-number.is-without-controls .el-input__inner {
    text-align: left;
    padding-left: 20px;
  }
  .input-number {
    position: relative;
  }

  .input-number::after {
    content: '¥';
    position: absolute;
    top: 3px;
    left: 2px;
    width: 18px;
    line-height: 26px;
    text-align: center;
    background: #fff;
    color: #222222;
    box-sizing: border-box;
    z-index: 1;
    display: block;
  }
}
</style>
