<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-03-26 15:47:49
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-28 15:59:54
 * @FilePath: /qst-merchant-admin-2.0/src/views/shop/goodsManagement/compontes/basicInformation.vue
 * @Description: 商品基本信息
-->
<template>
  <div class="maxWidth" v-if="pageLoading">
    <el-form ref="form" label-position="top" label-width="200px" :model="form" :rules="rules">
      <el-form-item label="可用次数" v-if="card_type == 1" prop="use_num">
        <div class="flex">
          <el-input
            class="p100"
            placeholder="请输入"
            v-model.number="form.use_num"
            @blur="(e)=>inputNumStep(e, 'use_num', 0)"
            :disabled="isView"
          ></el-input>
          <div style="margin-left: 10px">次</div>
        </div>
      </el-form-item>

      <el-form-item label="生效开始时间" v-if="card_type != 3" prop="effective_start_type">
        <el-radio-group v-model="form.effective_start_type" :disabled="isView">
          <el-radio :label="1">付款完成卡券立即生效</el-radio>
          <el-radio :label="2">
            <span>付款完成</span>
            <div class="flex input-div">
              <el-input
                placeholder="请输入"
                class="p100"
                v-model.number="form.effective_start_time"
                @blur="(e)=>inputNumStep(e, 'effective_start_time', 0)"
                :disabled="isView"
              ></el-input>
              <el-select
                class="p80"
                v-model="form.effective_start_unit"
                placeholder
                :disabled="isView"
              >
                <el-option label="小时" :value="1"></el-option>
                <el-option label="天" :value="2"></el-option>
              </el-select>
            </div>

            <span>后卡券生效</span>
          </el-radio>
          <el-radio :label="3">首次核销后生效</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label v-if="card_type != 3" prop="validity_type">
        <template slot="label">有效期</template>
        <el-radio-group v-model="form.validity_type" :disabled="isView">
          <div class="flex">
            <el-radio :disabled="isView" :label="1" v-if="card_type == 1">生效时间起长期可用</el-radio>
            <el-radio :disabled="isView" :label="2">
              <span>生效时间起</span>
              <div class="flex input-div">
                <el-input
                  :disabled="isView"
                  placeholder="请输入"
                  class="p100"
                  v-model.number="form.validity_end_day"
                  @blur="(e)=>inputNumStep(e, 'validity_end_day', 0)"
                ></el-input>
              </div>
              <span>天内可用</span>
            </el-radio>
            <el-radio :label="3">
              付款完成后在
              <el-date-picker
                :disabled="isView"
                class="p270"
                v-model="validity_date"
                :value-format="'yyyy-MM-dd'"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
              <span>内可用</span>
            </el-radio>
          </div>
        </el-radio-group>
      </el-form-item>

      <el-form-item label v-if="card_type != 3" prop="limit_type">
        <template slot="label">
          核销频次
          <el-tooltip
            class="item"
            effect="dark"
            content="限制核销频次，例如设置为每日核销1次，则当日核销后，次日才可再次核销；"
            placement="top"
          >
            <img :src="imgOssPath + '20250619_sf_wenhao.png'" alt class="icon_wenhao" />
          </el-tooltip>
        </template>

        <el-radio-group v-model="form.limit_type">
          <div class="flex">
            <el-radio :disabled="isView" :label="1">无限制</el-radio>
            <el-radio :disabled="isView" :label="2">
              <el-select class="p100" v-model="form.limit_unit_type" placeholder :disabled="isView">
                <el-option label="每日" :value="1"></el-option>
                <el-option label="每周" :value="2"></el-option>
                <el-option label="每月" :value="3"></el-option>
                <el-option label="每季度" :value="4"></el-option>
                <el-option label="每年" :value="5"></el-option>
              </el-select>

              <div class="flex input-div">
                <span>允许核销</span>
                <el-input
                  :disabled="isView"
                  placeholder="请输入"
                  class="p100"
                  v-model.number="form.limit_num"
                  @blur="(e)=>inputNumStep(e, 'limit_num', 0)"
                ></el-input>
              </div>次
            </el-radio>
          </div>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="是否有禁用日期" required v-if="card_type != 3">
        <el-radio-group v-model="form.is_disable_date" :disabled="isView">
          <el-radio label="Y">有</el-radio>
          <el-radio label="N">无</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="禁用日期" required v-if="form.is_disable_date == 'Y'">
        <dateSet
          :isView="isView"
          :disableDateInit="disableDateInit"
          ref="dateSet"
          :type="'isDisable'"
          @upDisable="upDisable"
        ></dateSet>
      </el-form-item>

      <!-- 预约设置 start -->
      <el-form-item required label="预约设置">
        <el-radio-group v-model="form.reservation_type" :disabled="isView || card_type == 3">
          <el-radio :label="1">无需预约</el-radio>
          <el-radio :label="2">电话预约</el-radio>
          <el-radio :label="3">在线预约（需设置对应日期/时段库存）</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item
        prop="reservation_advance_time"
        v-if="form.reservation_type == 3 || form.reservation_type == 2"
      >
        <template slot="label">
          提前预约时间
          <el-tooltip class="item" effect="dark" content="买家需要提前多久预约" placement="top">
            <img :src="imgOssPath + '20250619_sf_wenhao.png'" alt class="icon_wenhao" />
          </el-tooltip>
        </template>

        <span>买家需提前</span>
        <div class="flex input-div">
          <el-input
            placeholder="请输入"
            class="p100"
            v-model.number="form.reservation_advance_time"
            @blur="(e)=>inputNumStep(e, 'reservation_advance_time', 0)"
            :disabled="isView"
          ></el-input>
          <el-select
            class="p80"
            v-model="form.reservation_advance_unit"
            placeholder
            :disabled="isView"
          >
            <el-option label="小时" :value="1"></el-option>
            <el-option label="天" :value="2"></el-option>
            <el-option label="分钟" :value="3"></el-option>
          </el-select>
        </div>
        <span>预约</span>
      </el-form-item>

      <!-- -- 在线预约 -- start -->
      <el-form-item label="取消预约" prop="reservation_cancel_type" v-if="form.reservation_type == 3 ">
        <el-radio-group v-model="form.reservation_cancel_type" :disabled="isView">
          <el-radio :label="1">不允许取消</el-radio>
          <el-radio :label="2">
            <span>服务开始</span>
            <div class="flex input-div">
              <el-input
                :disabled="isView"
                placeholder="请输入"
                class="p100"
                v-model.number="form.reservation_cancel_time"
                @blur="(e)=>inputNumStep(e, 'reservation_cancel_time', 0)"
              ></el-input>
              <el-select
                class="p80"
                v-model="form.reservation_cancel_unit"
                placeholder
                :disabled="isView"
              >
                <el-option label="小时" :value="1"></el-option>
                <el-option label="天" :value="2"></el-option>
              </el-select>
            </div>
            <span>内允许取消</span>
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="预约显示日期" prop="reservation_show_time" v-if="form.reservation_type == 3 ">
        <span>对用户展示</span>
        <div class="flex input-div">
          <el-input
            :disabled="isView"
            placeholder="请输入"
            class="p100"
            v-model.number="form.reservation_show_time"
            @blur="(e)=>inputNumStep(e, 'reservation_show_time', 0)"
          ></el-input>
          <el-select
            class="p80"
            v-model="form.reservation_show_unit"
            placeholder
            :disabled="isView"
          >
            <el-option label="天" :value="2"></el-option>
            <el-option label="月" :value="4"></el-option>
          </el-select>
        </div>
        <span>可预约日期</span>
        <el-button type="text">查看示例</el-button>
      </el-form-item>
      <el-form-item required v-if="form.reservation_type == 3 ">
        <template slot="label">
          预约数量设置维度
          <el-tooltip
            class="item"
            effect="dark"
            content="当前商品为多规格商品，预约数量为共享使用，任一规格被预约后，该商品总可预约数量将同步扣减。若需独立设置可预约数量，请创建单独商品。"
            placement="top"
          >
            <img :src="imgOssPath + '20250619_sf_wenhao.png'" alt class="icon_wenhao" />
          </el-tooltip>
        </template>

        <el-radio-group v-model="form.reservation_number_type">
          <el-radio :label="1" :disabled="isView">
            按时段
            <el-tooltip
              class="item"
              effect="dark"
              content="按时间段设置预约数量，非禁用日期内，每天所设置时段及预约数量相同；"
              placement="top"
            >
              <img :src="imgOssPath + '20250619_sf_wenhao.png'" alt class="icon_wenhao" />
            </el-tooltip>
          </el-radio>
          <el-radio :label="2" :disabled="isView">
            按日期
            <el-tooltip
              class="item"
              effect="dark"
              content="按日期设置预约数量，不分时段，支持不同日期设置不同预约数量；"
              placement="top"
            >
              <img :src="imgOssPath + '20250619_sf_wenhao.png'" alt class="icon_wenhao" />
            </el-tooltip>
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <time-period-list
        :isView="isView"
        :reservation_number_slot="reservation_number_slot"
        ref="timePeriod"
        v-if="form.reservation_number_type == 1 && form.reservation_type == 3 "
      ></time-period-list>

      <el-button
        v-if="form.reservation_number_type == 2 && form.reservation_type == 3 "
        @click="openCalendar"
      >预约日历</el-button>
      <calendar ref="calendar" :isView="isView"></calendar>

      <!-- 预约设置 end -->

      <el-form-item prop="reservation_phone" label="预约电话" v-if="form.reservation_type == 2">
        <el-input
          :disabled="isView"
          maxlength="11"
          v-model="form.reservation_phone"
          placeholder="请输入预约手机号"
        ></el-input>
      </el-form-item>

      <el-form-item :label="form.reservation_type == 3 ? '预约所需信息' : '下单所需信息'" required>
        <el-checkbox-group v-model="form.reservation_info" :disabled="isView">
          <el-checkbox disabled :label="1">姓名</el-checkbox>
          <el-checkbox disabled :label="2">手机号</el-checkbox>
          <el-checkbox :label="3">身份证号</el-checkbox>
          <el-checkbox :label="4">邮箱</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="售后设置" required>
        <el-radio-group v-model="form.refund_type" :disabled="isView">
          <el-radio :label="1">不支持买家申请退款</el-radio>
          <el-radio :label="2">支持买家申请退款</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="退款条件" v-if="form.refund_type == 2">
        <el-radio-group v-model="form.refund_condition" :disabled="isView">
          <el-radio :label="1">未核销卡券无论是否过期均支持退款</el-radio>
          <el-radio :label="2">
            <span>未核销卡券在过期前</span>
            <div class="flex input-div">
              <el-input
                :disabled="isView"
                placeholder="请输入"
                class="p100"
                v-model.number="form.refund_time"
                @blur="(e)=>inputNumStep(e, 'refund_time', 0)"
              ></el-input>
              <el-select class="p80" v-model="form.refund_unit_type" placeholder :disabled="isView">
                <el-option label="小时" :value="1"></el-option>
                <el-option label="天" :value="2"></el-option>
              </el-select>
            </div>
            <span>前支持退款</span>
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item v-if="form.validity_type != 1">
        <template slot="label">
          是否支持到期后自动退款
          <el-tooltip
            class="item"
            effect="dark"
            content="当选择过期自动退款时，买家购买的卡券，在到达有效期后仍没有核销使用时，自动退款给买家"
            placement="top"
          >
            <img :src="imgOssPath + '20250619_sf_wenhao.png'" alt class="icon_wenhao" />
          </el-tooltip>
        </template>
        <el-radio-group v-model="form.is_refund_auto" :disabled="isView">
          <el-radio :label="'Y'">是</el-radio>
          <el-radio :label="'N'">否</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="使用说明" prop="usage_instructions">
        <div class="instructions-border">
          <div v-for="item,index in instructionsList" :key="index">
            <div>
              <span class="instructions-title">{{item.title}}：</span>
              <span class="main">{{item.main}}</span>
            </div>
          </div>
          <el-input
            :disabled="isView"
            v-model="form.usage_instructions"
            type="textarea"
            class="w100"
            :rows="5"
            :maxLength="500"
            placeholder="请输入卡券适用须知，如退款规则、特殊限制"
          ></el-input>
        </div>
      </el-form-item>
    </el-form>
    <div @click="next">1312312</div>
    <footer-btn :num="2" @next="next" :isBtnLoading="isBtnLoading"></footer-btn>
  </div>
</template>


<script>
import calendar from './expiration/calendar.vue'
import timePeriodList from './expiration/timePeriodList.vue'
import store from '@/store'
import dateSet from './expiration/dateSet.vue'
import { inputNum, getStepNum } from './step.js'
import { saveCardSettingApi, saveCardReservationApi } from '@/api/shop/goodsManagement.js'
import { mapGetters } from 'vuex'
import footerBtn from './footerBtn.vue'
export default {
  name: 'expirationDate',
  components: {
    calendar,
    timePeriodList,
    dateSet,
    footerBtn,
  },
  data() {
    return {
      validity_date: [],
      rules: {
        use_num: [
          {
            required: true,
            message: '请输入使用数量',
            trigger: 'blur',
          },
        ],
        reservation_phone: [
          {
            required: true,
            message: '请输入预约电话',
            trigger: 'blur',
          },
          {
            pattern: /^1[3456789]\d{9}$/,
            message: '请输入正确的手机号',
            trigger: 'blur',
          },
        ],
        // 生效有效时间
        effective_start_type: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value == 2) {
                if (!this.form.effective_start_time) {
                  callback(new Error('请选择生效时间'))
                } else {
                  callback()
                }
              } else {
                callback()
              }
            },
          },
        ],
        // 有效期
        validity_type: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value == 3) {
                if (!this.validity_date || this.validity_date.length < 1) {
                  callback(new Error('请选择有效期'))
                } else {
                  callback()
                }
              } else if (value == 2) {
                if (!this.form.validity_end_day) {
                  callback(new Error('请输入天数'))
                } else {
                  callback()
                }
              } else {
                callback()
              }
            },
          },
        ],
        // 核销限制
        limit_type: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value == 2) {
                if (!this.form.limit_num) {
                  callback(new Error('请输入核销次数'))
                } else {
                  callback()
                }
              } else {
                callback()
              }
            },
          },
        ],

        // 取消预约
        reservation_cancel_type: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value == 2) {
                if (!this.form.reservation_cancel_time) {
                  callback(new Error('请输入取消预约时间'))
                } else {
                  callback()
                }
              } else {
                callback()
              }
            },
          },
        ],
        // 预约显示
        reservation_show_time: [{ required: true, message: '请输入预约显示时间', trigger: 'blur' }],
        reservation_advance_time: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error('请输入提前预约时间'))
              } else {
                callback()
              }
            },
          },
        ],
        usage_instructions: [{ required: true, message: '请输入使用说明', trigger: 'blur' }],
      },
      form: {
        // 可用次数
        use_num: 1,
        // 生效开始时间
        effective_start_type: 1,
        // 生效时间2 默认小时
        effective_start_unit: 1,
        // 生效时间2 时必填
        effective_start_time: 1,

        // 次卡默认1 周期卡默认3
        validity_type: 1,
        // 生效天
        validity_end_day: 1,

        // 核销限制
        limit_type: 1,
        // 核销类型
        limit_unit_type: 1,
        // 核销次数
        limit_num: 1,

        // 禁用日期
        is_disable_date: 'N',

        // 预约设置 「次卡」、「周期卡」1   单人电子票 3
        reservation_type: 1,
        reservation_info: [1, 2],

        // 售后设置
        refund_type: 2,
        // 退款条件
        refund_condition: 1,
        refund_unit_type: 1,
        refund_time: 1,

        // 过期自动退
        is_refund_auto: 'N',

        // 提前预约时间
        reservation_advance_time: '',
        // 提前的时间的单位
        reservation_advance_unit: 1,

        // 取消预约
        reservation_cancel_type: 1,
        reservation_cancel_unit: 1,
        reservation_cancel_time: '',

        // 预约显示
        reservation_show_unit: 4,
        reservation_show_time: 1,

        // 预约设置纬度
        reservation_number_type: 1,

        // 是否打开过日历
        isCalendar: false,

        usage_instructions: '',
      },

      // 卡券类型
      card_type: 1,

      radio: 1,
      value1: '123',
      value: 12,

      // 禁用日期回调数据
      disableData: {},

      // 按钮 加载状态
      isBtnLoading: false,
      // 页面 加载状态
      pageLoading: false,

      // 回显时间段
      reservation_number_slot: [],
      // 禁用日期初始化
      disableDateInit: {},
    }
  },

  inject: ['goodsDetail'],
  mounted() {
    this.init()
  },

  methods: {
    // 打开日历 -- > 禁用日期
    openCalendar() {
      this.isCalendar = true
      if (this.form.is_disable_date == 'Y') {
        this.$refs.dateSet.getDate(true).then((res) => {
          this.$refs.calendar.open(res)
        })
      } else {
        this.$refs.calendar.open()
      }
    },
    // 初始化
    init() {
      let data = this.goodsDetail()
      let cardSetting = data.card_setting || {}
      // 卡券类型 1 次卡  2 周期卡   3 单人电子票
      this.card_type = cardSetting.card_type || 1
      // 根据card_type 类型判断初始值
      this.form.validity_type = cardSetting.card_type == 1 ? 1 : cardSetting.card_type == 2 ? 3 : 0
      this.form.reservation_type = cardSetting.card_type == 3 ? 3 : 1
      if (cardSetting.is_save == 'Y') {
        this.form = cardSetting
        this.reservation_number_slot = cardSetting.reservation_number_slot || []

        this.disableDateInit = {
          disable_date_type: cardSetting.disable_date_type || 1,
          disable_date: cardSetting.disable_date || [],
        }

        if (this.form.validity_type == 3) {
          this.validity_date = [cardSetting.validity_start_time, cardSetting.validity_end_time]
        }
      }
      this.$nextTick(() => {
        this.pageLoading = true
      })
    },

    // 输入整数
    inputNumStep(e, code, num) {
      inputNum(this.form, code, num, this)
    },

    // -------- 有效期时间选择器 ---------
    // 禁用回调
    upDisable(e) {
      this.disableData = e
    },

    // 下一步
    next(callback) {
      let _this = this
      console.log(callback)
      this.$refs.form.validate((valid) => {
        console.log(valid)
        if (valid) {
          let params = {}
          // 有效期时间选择器返回的值
          if (_this.form.validity_type == 3 && this.validity_date.length > 0) {
            params.validity_start_time = this.validity_date[0]
            params.validity_end_time = this.validity_date[1]
          }

          // 禁用日期
          if (_this.form.is_disable_date == 'Y') {
            params.disable_date_type = _this.disableData.dateType
            if (params.disable_date_type == 1) {
              params.disable_date = _this.disableData.week
            } else if (params.disable_date_type == 2) {
              if (_this.disableData.disableTimeList.length == 0) {
                _this.$message.error('请选择禁用日期')
                return false
              }
              params.disable_date = _this.disableData.disableTimeList
            }
            if (params.disable_date.length == 0) {
              _this.$message.error('请选择禁用日期')
              return false
            }
          }

          // 预约设置
          if (_this.form.reservation_type == 3) {
            if (_this.form.reservation_number_type == 1) {
              params.reservation_number_slot = this.$refs.timePeriod.getData()
              if (params.reservation_number_slot.length == 0) {
                _this.$message.error('请选择时间段')
                return false
              }
            }
          }
          console.log(params)

          this.isBtnLoading = true
          saveCardSettingApi({
            card_settings: {
              ...this.form,
              ...params,
            },
            goods_id: this.goods_id,
          }).then((res) => {
            if (res.code == 200) {
              store.dispatch('goodsDetaile/setRefreshShopNum')
              // 如果没有打开过日历，或者是时间段预约，则不需要保存日历操作
              if (
                !this.isCalendar ||
                !(this.form.reservation_number_type == 2 && this.form.reservation_type == 3)
              ) {
                this.isBtnLoading = false
                callback()
              } else {
                this.saveCalendar(callback)
              }
            } else {
              this.isBtnLoading = false
            }
          })

          // getStepNum(2, _this.form.goods_type, 'next', store)
        } else {
          return false
        }
      })
    },

    // 保存日历
    saveCalendar(callback) {
      let data = this.$refs.calendar.getData()
      saveCardReservationApi({
        reservation_days: data,
        goods_id: this.goods_id,
      }).then((res) => {
        this.isBtnLoading = false
        if (res.code == 200) {
          callback()
        }
      })
    },
  },
  watch: {
    'form.reservation_type'() {
      if (this.form.reservation_type == 3 && this.pageLoading) {
        this.$refs.form.clearValidate('reservation_phone')
      }
    },
  },
  computed: {
    ...mapGetters({
      shop_id: 'goodsDetaile/getShopId',
      goods_id: 'goodsDetaile/getGoodsId',
      save_type: 'goodsDetaile/getSaveType',
    }),
    isView() {
      return this.save_type == 'view'
    },
    // 使用说明提示
    instructionsList() {
      // 生效时间
      let effective_text = ''
      let startText = this.form.effective_start_unit == 1 ? '小时' : '天'
      switch (this.form.effective_start_type) {
        case 1:
          effective_text = '付款完成卡券立即生效'
          break
        case 2:
          effective_text =
            '付款完成卡券' + this.form.effective_start_time + startText + '后卡券生效'
          break
        case 3:
          effective_text = '首次核销后生效'
          break
      }
      // 有效期
      let validity_text = ''
      switch (this.form.validity_type) {
        case 1:
          validity_text = '生效时间起长期可用'
          break
        case 2:
          validity_text = '生效时间起' + this.form.validity_end_day + '天内可用'
          break
        case 3:
          validity_text =
            '付款完成后在' +
            ((this.validity_date && this.validity_date[0]) || 'xxxx.xx.xx') +
            '~' +
            ((this.validity_date && this.validity_date[1]) || 'xxxx.xx.xx') +
            '内可用'
          break
      }
      // 频次
      let limit_text = ''
      let unTyle = { 1: '每日', 2: '每周', 3: '每月', 4: '每季度', 5: '每年' }
      switch (this.form.limit_type) {
        case 1:
          limit_text = '核销频次无限制'
          break
        case 2:
          limit_text =
            '核销频次限制每' +
            unTyle[this.form.limit_unit_type] +
            '仅可核销' +
            this.form.limit_num +
            '次'
          break
      }
      // 禁用日期
      let disable_text = ''
      switch (this.form.is_disable_date) {
        case 'N':
          disable_text = '所有日期均可用'
          break
        case 'Y':
          let { week, disableTimeList, dateType } = this.disableData
          if (dateType == 1) {
            if (!week || (week && week.length == 0)) {
              disable_text = '请完善禁用日期信息'
              break
            }
            var weeks = week.slice().sort(function (a, b) {
              return a - b
            })
            let weekObj = ['周天', '周一', '周二', '周三', '周四', '周五', '周六']
            disable_text = weeks.map((item) => weekObj[item]).join(',')
          } else {
            if (!disableTimeList || (disableTimeList && disableTimeList.length == 0)) {
              disable_text = '请完善禁用日期信息'
              break
            }
            disable_text = disableTimeList
              .map((item, index) => item.start_time + '至' + item.end_time)
              .join(',')
          }
          break
      }
      // 预约方式
      let reservation_text = ''
      let advance_time = this.form.reservation_advance_time
      if (advance_time) {
        switch (this.form.reservation_advance_unit) {
          case 1:
            advance_time = '需提前' + advance_time + '小时'
            break
          case 2:
            advance_time = '需提前' + advance_time + '天'
            break
          case 3:
            advance_time = '需提前' + advance_time + '月'
            break
        }
      } else {
        advance_time = ''
      }
      switch (this.form.reservation_type) {
        case 1:
          reservation_text = '无需提前预约'
          break
        case 2:
          reservation_text = advance_time + '电话预约，预约电话：' + this.form.reservation_phone
          break
        case 3:
          reservation_text = advance_time + '在线预约'
          break
      }

      let list = [
        {
          title: '生效时间',
          main: effective_text,
        },
        {
          title: '有效期',
          main: validity_text,
        },
        {
          title: '核销频次限制',
          main: limit_text,
        },
        {
          title: '禁用日期',
          main: disable_text,
        },
        {
          title: '预约方式',
          main: reservation_text,
        },
      ]
      return list
    },
  },
}
</script>

<style lang="scss" scoped>
.mt10 {
  margin-top: 10px;
}
// 付款完成样式
.w100 {
  width: 100%;
}
.p270 {
  width: 270px;
}
.p100 {
  margin-left: 0 8px;
  width: 100px;
}
.p80 {
  margin: 0 8px;
  width: 80px;
}
.maxWidth {
  max-width: 985px;
}

// input样式
.input-div {
  display: inline-flex;
  margin: 0 8px;
  > * + * {
    margin-left: 8px;
  }
}
.bordel-dashed {
  border-style: dashed;
}

// 使用说明
.instructions-title {
  font-weight: 600;
}

.instructions-border {
  border: 1px dashed #dcdfe6;
}
</style>
