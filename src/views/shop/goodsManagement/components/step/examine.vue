<!-- 审核 -->
<template>
  <div>
    <el-form :model="form" ref="form" :rules="rule">
      <el-form-item label="审核" prop="is_pass">
        <el-radio-group v-model="form.is_pass" :disabled="!isHeadquarters">
          <el-radio label="Y">审核通过</el-radio>
          <el-radio label="N">审核驳回</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="驳回理由" prop="rejection_reason" v-if="form.is_pass == 'N'">
        <el-input
          v-model="form.rejection_reason"
          :disabled="!isHeadquarters"
          type="textarea"
          placeholder="请输入驳回理由"
        ></el-input>
      </el-form-item>
    </el-form>

    <div>
      <div class="flex-c footer-btn">
        <el-button @click="backFn">返回列表</el-button>
        <el-button class="ml10" @click="up" type="primary">上一步</el-button>
        <el-button
          v-if="isHeadquarters"
          @click="rejectGoods"
          class="ml10"
          type="danger"
        >审核{{ form.is_pass == 'Y' ? '通过':'驳回'}}</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { reviewGoodsApi } from '@/api/shop/goodsManagement'
import { getStepNum } from './step.js'
import store from '@/store'
export default {
  name: 'examine',
  data() {
    return {
      rule: {
        is_pass: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
        rejection_reason: [{ required: true, message: '请输入驳回理由', trigger: 'blur' }],
      },
      form: {
        is_pass: 'Y',
        rejection_reason: '',
      },
      goodsId: '',
      goods_type: '',
      pageType: '', // headquarters
      goodsStatus: 0, // 审核状态 0 未审核 1 已审
    }
  },
  inject: ['goodsDetail'],
  mounted() {
    this.pageType = this.$route.query.type || ''
    this.init()
  },
  computed: {
    isHeadquarters() {
      return this.$route.query.pageType == 'headquarters' && this.goodsStatus == 1
    },
  },
  methods: {
    init() {
      let data = this.goodsDetail()
      this.goods_type = data.goods_type || 1
      this.goodsId = data.goods_id || ''
      this.form.rejection_reason = data.rejection_reason || ''
      this.goodsStatus = data.goods_status || ''
    },
    // 返回列表
    backFn() {
      this.$router.go(-1)
    },
    // 上一步
    up() {
      getStepNum(7, this.goods_type, 'up', store)
    },

    rejectGoods() {
      this.$refs['form'].validate((valid) => {
        if (!valid) return false
        reviewGoodsApi({
          rejection_reason: this.form.rejection_reason,
          goods_id: this.goodsId,
          is_pass: this.form.is_pass,
        }).then((res) => {
          if (res.code == 200) {
            if (this.form.is_pass == 'Y') {
              this.$message.success('审核通过成功')
            } else {
              this.$message.success('审核驳回成功')
            }
            this.$router.go(-1)
          }
        })
      })
    },
  },
}
</script>

<style scoped lang="scss">
.footer-btn {
  position: fixed;
  width: calc(#{$base-right-content-width} - 42px);
  bottom: 0px;
  right: 20px;
  background: #fff;
  border-right: 1px solid #ebeef5;
  border-left: 1px solid #ebeef5;
  padding: 20px 0 20px;
  z-index: 4;
}
.footer-zw {
  height: 80px;
}
</style>