<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-03-26 15:47:49
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-28 15:59:54
 * @FilePath: /qst-merchant-admin-2.0/src/views/shop/goodsManagement/compontes/basicInformation.vue
 * @Description: 商品基本信息
-->
<template>
  <div>
    <el-alert
      v-if="isEdit"
      class="alert-warning"
      title="温馨提示：您当前的修改操作仅适用于此后产生的新订单，系统将自动保留所有历史订单的商品快照信息，确保交易记录的完整性。"
      type="warning"
      show-icon
      :closable="false"
    ></el-alert>
    <el-form ref="form" label-position="top" label-width="200px" :model="form" :rules="rules">
      <el-form-item label="商品类型" required>
        <el-row :gutter="10">
          <div class="goods-type-tab">
            <el-col :lg="8" :md="8" :sm="8" :xl="8" :xs="8">
              <div
                class="goods-tab-item"
                :class="{ 
                  key: form.goods_type == 1,
                  'del-select': (isEdit || isView || isShop) && form.goods_type != 1
                 }"
                @click="selectGoodsType('1')"
              >
                <div>实物商品</div>
                <div>适用于需要物流配送的实体商品，如食品、服装等</div>
              </div>
            </el-col>
            <el-col :lg="8" :md="8" :sm="8" :xl="8" :xs="8">
              <div
                class="goods-tab-item"
                :class="{ 
                  key: form.goods_type == 2,
                  'del-select': (isEdit || isView) && form.goods_type != 2
                 }"
                @click="selectGoodsType('2')"
              >
                <div>卡券商品</div>
                <div>适用于优惠券、代金券、会员卡等可兑换的凭</div>
              </div>
            </el-col>
            <el-col :lg="8" :md="8" :sm="8" :xl="8" :xs="8">
              <div class="goods-tab-item del-select">
                <div>虚拟商品</div>
                <div>适用于虚拟内容、服务、数字商品等无实物的</div>
              </div>
            </el-col>
          </div>
        </el-row>
      </el-form-item>

      <el-form-item label="卡券类型" required v-if="form.goods_type == 2">
        <div class="flex m10">
          <el-radio v-model="form.card_type" :disabled="isEdit || isView" :label="1">&nbsp;</el-radio>
          <div class="card-title">
            <div>次卡</div>
            <div class="card-main">适用于按次数使用的卡券，如健身次卡、洗车次卡等</div>
          </div>
        </div>

        <div class="flex m10">
          <el-radio v-model="form.card_type" :disabled="isEdit || isView" :label="2">&nbsp;</el-radio>
          <div class="card-title">
            <div>周期卡</div>
            <div class="card-main">适用于按时间周期使用的卡券，如月卡、年卡等</div>
          </div>
        </div>

        <div class="flex m10">
          <el-radio v-model="form.card_type" :disabled="isEdit || isView" :label="3">&nbsp;</el-radio>
          <div class="card-title">
            <div>单人电子票</div>
            <div class="card-main">适用于指定日期使用的电子票券，如演出票、景点票等</div>
          </div>
        </div>
      </el-form-item>

      <el-row :gutter="10">
        <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12" v-if="isShop">
          <el-form-item label="所属店铺" props="shop_id">
            <StoreHeader
              :disabled="save_type == 'edit' || isView"
              :searchType="2"
              :storeId="form.shop_id"
              @changeStore="e => changeStore(e)"
              ref="storeHeader"
            ></StoreHeader>
          </el-form-item>
        </el-col>

        <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
          <el-form-item label="商品名称" prop="name">
            <el-input :disabled="isView" v-model="form.name" placeholder="请输入商品名称"></el-input>
          </el-form-item>
        </el-col>

        <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
          <el-form-item>
            <template slot="label">
              商品编码
              <el-tooltip class="item" effect="dark" content="商品的唯一编码标识" placement="top">
                <img
                  :src="imgOssPath + '20250619_sf_wenhao.png'"
                  alt
                  class="icon_wenhao"
                  style="transform: translateY(2px);"
                />
              </el-tooltip>
            </template>
            <el-input :disabled="isView" v-model="form.goods_code" placeholder="请输入商品编码"></el-input>
          </el-form-item>
        </el-col>

        <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
          <el-form-item label="商品分类" prop="cate_id">
            <div class="flex">
              <el-cascader
                v-model="form.cate_id"
                :props="{
                  label: 'name',
                  value: 'cate_id',
                  checkStrictly: true,
                }"
                :disabled="isView"
                :options="categoryList"
                placeholder="请选择商品分类"
                style="flex: 1"
              ></el-cascader>

              <el-button
                @click="addSelect('cate')"
                type="text"
                icon="el-icon-plus"
                style="margin-left: 20px"
                v-if="!isView && !isShop"
              >添加分类</el-button>
            </div>
          </el-form-item>
        </el-col>

        <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
          <el-form-item label="商品分组">
            <div class="flex">
              <el-select
                :disabled=" isView"
                v-model="form.group_ids"
                placeholder="请选择商品分组"
                multiple
                style="flex: 1"
              >
                <el-option
                  v-for="item in groupList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
              <el-button
                type="text"
                @click="addSelect('group')"
                icon="el-icon-plus"
                style="margin-left: 20px"
                v-if="!isView && !isShop"
              >添加分组</el-button>
            </div>
          </el-form-item>
        </el-col>

        <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
          <el-form-item>
            <template slot="label">
              基础销量
              <el-tooltip class="item" effect="dark" content="设置商品的初始销量数据" placement="top">
                <img
                  :src="imgOssPath + '20250619_sf_wenhao.png'"
                  alt
                  class="icon_wenhao"
                  style="transform: translateY(2px);"
                />
              </el-tooltip>
            </template>
            <el-input-number
              style="width: 100%"
              :precision="0"
              :controls="false"
              :min="0"
              v-model="form.sales"
              placeholder="请输入基础销量"
              :disabled="isView"
            ></el-input-number>
          </el-form-item>
        </el-col>

        <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
          <el-form-item>
            <template slot="label">
              自定义排序
              <el-tooltip class="item" effect="dark" content="数值越大越靠前，相同数值随机排序" placement="top">
                <img
                  :src="imgOssPath + '20250619_sf_wenhao.png'"
                  alt
                  class="icon_wenhao"
                  style="transform: translateY(2px);"
                />
              </el-tooltip>
            </template>
            <el-input-number
              style="width: 100%"
              :precision="0"
              :controls="false"
              :min="0"
              v-model="form.sort"
              placeholder="请输入排序数值(数值越小越靠前)"
              :disabled="isView"
            ></el-input-number>
          </el-form-item>
        </el-col>

        <el-col :lg="24" :md="24" :sm="24" :xl="24" :xs="24" v-if="form.goods_type == 2">
          <el-form-item>
            <template slot="label">
              服务项目
              <el-tooltip class="item" effect="dark" content="设置卡券包含的服务项目内容；" placement="top">
                <img
                  :src="imgOssPath + '20250619_sf_wenhao.png'"
                  alt
                  class="icon_wenhao"
                  style="transform: translateY(2px);"
                />
              </el-tooltip>
            </template>
            <div class="refund_reason_list">
              <div class="flex" v-for="(item, index) in serviceList" :key="index">
                <el-input :disabled="isView" placeholder="请输入服务项目" v-model="item.name"></el-input>
                <el-button
                  v-if="!isView"
                  class="refund_del"
                  @click="deleteRefund(index)"
                  type="text"
                  style
                >删除</el-button>
              </div>
            </div>
            <el-button
              v-if="!isView"
              icon="el-icon-plus"
              @click="addRefund"
              class="add_reason"
            >添加服务项目</el-button>
          </el-form-item>
        </el-col>

        <el-col :lg="24" :md="24" :sm="24" :xl="24" :xs="24">
          <el-form-item label="商品图片" prop="shopImageArr" v-if="isView">
            <div class="flex flex-image">
              <el-image
                class="shop-image"
                v-for="(item, index) in shopImageArr"
                :src="item.url"
                :key="index"
                :preview-src-list="previewSrcList"
              ></el-image>
            </div>
          </el-form-item>
          <el-form-item label="商品图片" prop="shopImageArr" v-else>
            <div class="flex flex-image">
              <!-- <VueDraggable
                v-if="shopImageArr.length > 0"
                class="flex-draggable"
                v-model="shopImageArr"
                group="items"
              >-->
              <PictureComponent
                class="image-draggable"
                :imgParams="previewSrcList"
                :imgViewer="previewSrcList"
                imgWidth="100px"
                imgHeight="100px"
                @deleteImg="deleteImg"
                @changePicSort="changePicSort"
              ></PictureComponent>
              <!-- </VueDraggable> -->
              <div class="up_data">
                <i class="el-icon-plus"></i>
                <div class="el-upload__text">上传图片</div>

                <el-upload
                  class="upload_img"
                  action="fakeaction"
                  :show-file-list="false"
                  multiple
                  :limit="10"
                  drag
                  accept=".jpg, .png"
                  :file-list="shopImageArr"
                  :on-exceed="limitExceedFn"
                  :on-change="handleChange"
                  :http-request="(e) => upLoadImgFn(e)"
                ></el-upload>
              </div>

              <el-button v-if="isShowUpImage" style="margin: 10px" @click="selectImage">从图库选择</el-button>
            </div>

            <div
              class="el-upload__tip"
            >支持jpg、png格式，单张图片大小不超过2MB，建议尺寸为800x800像素，第一张图片 将作为商品主图，可拖动图片调整顺序</div>
          </el-form-item>
        </el-col>

        <el-col :lg="24" :md="24" :sm="24" :xl="24" :xs="24">
          <el-form-item label="商品视频">
            <div class="flex">
              <video
                v-if="form.video"
                ref="videoPlayer"
                style
                width="200"
                height="200"
                controls
                class="video_player"
              >
                <source :src="form.video" type="video/mp4" />
              </video>
              <div class="up_data" v-if="!isView">
                <i class="el-icon-plus"></i>
                <div class="el-upload__text">上传视频</div>
                <el-upload
                  class="upload_img"
                  action="fakeaction"
                  :show-file-list="false"
                  drag
                  accept=".mp4, .mov"
                  :http-request="(e) => upLoadVideoFn(e)"
                ></el-upload>
              </div>
            </div>
            <div
              v-if="!isView"
              class="el-upload__tip"
            >支持mp4、mov格式，视频大小不超过50MB，建议时长不超过60秒，建议分辨率1280x720及以上</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <footerBtn
      :goodsType="Number(form.goods_type) "
      :num="1"
      :isBtnLoading="isBtnLoading"
      @next="next"
    ></footerBtn>

    <!-- 选择商品 -->
    <el-dialog
      title="从图库选择"
      :visible.sync="isSelectImage"
      width="1100px"
      destroy-on-close
      :close-on-click-modal="false"
    >
      <div>
        <getSelectUploadImages
          :limit="selectImages"
          :selectPicList="selectPicList"
          @getSelectPicList="getSelectPicList"
        />
      </div>
      <base-dialog-footer
        cancelText="清空选择"
        :confirmText="confirmTextPic"
        @confirm="saveSelectImage"
        @cancel="cancelSelectImage"
      ></base-dialog-footer>
    </el-dialog>
  </div>
</template>

<script>
import { goodsGroupListApi } from '@/api/shop/goodsSet/goodsGrouping'
import {
  goodsSaveFirstApi,
  checkGalleryPermissionApi,
  goodsCategorylistBySelectApi,
} from '@/api/shop/goodsManagement'
import { upLoadImg, upLoadVideo } from '@/utils/uploadImage.js'
import { VueDraggable } from 'vue-draggable-plus'
import addCate from '@/views/shop/goodsSet/goodsCate/components/addCate.vue'
import addGroup from '@/views/shop/goodsSet/goodsGrouping/components/addGroup.vue'
import store from '@/store'
import { mapGetters } from 'vuex'
import footerBtn from './footerBtn.vue'

export default {
  name: 'basicInformation',
  props: {
    goods_type: {
      type: Number,
      default: 1,
    },
  },
  components: {
    addCate,
    VueDraggable,
    addGroup,
    footerBtn,
  },

  data() {
    return {
      // 是否是草稿状态
      confirmTextPic: '确定选择(0张)',
      selectPicList: [], //清空选择的图片
      form: {
        goods_type: 1,
        shop_id: '',
        name: '',
        group_ids: [],
        cate_id: [],
        video: '',
        card_type: 1,
        sort: 100,
      },
      rules: {
        name: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
        shopImageArr: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (this.shopImageArr.length > 0) {
                callback()
              } else {
                return callback(new Error('请上传商品图片'))
              }
            },
            trigger: 'change',
          },
        ],
        cate_id: [{ required: true, message: '请选择商品分类', trigger: 'change' }],
        group_ids: [{ required: true, message: '请选择商品分组', trigger: 'change' }],
        shop_id: [{ required: true, message: '请选择门店', trigger: 'change' }],
      },

      // 分类列表
      categoryList: [],

      // 商品分组列表
      groupList: [],

      // 图片数组
      shopImageArr: [],
      shopSelectImageArr: [],
      isSelectImage: false,

      isShowUpImage: false,

      // 服务列表
      serviceList: [],

      // 按钮加载
      isBtnLoading: false,

      // 是否是门店创建商品
      isShop: false,
    }
  },

  computed: {
    ...mapGetters({
      save_type: 'goodsDetaile/getSaveType',
      goods_id: 'goodsDetaile/getGoodsId',
    }),
    isView() {
      return this.save_type == 'view'
    },
    isEdit() {
      return this.save_type == 'edit'
    },

    selectImages() {
      return 10 - this.shopImageArr.length
    },
    previewSrcList() {
      return this.shopImageArr.map((item) => item.url)
    },
  },

  inject: ['goodsDetail'],

  watch: {
    shopImageArr() {
      this.$refs.form.validateField('shopImageArr')
    },
  },
  mounted(options) {
    // 初始化分类列表
    this.initCategory()
    // 商品分组列表
    this.goodsGroupList()
    this.checkGalleryPermissionFn()
    this.initGoodsDetail()
  },
  methods: {
    // 验证上传权限
    checkGalleryPermissionFn() {
      checkGalleryPermissionApi().then((res) => {
        if (res.code == 200) {
          this.isShowUpImage = res.data.is_show
        }
      })
    },
    // 初始化商品详情
    initGoodsDetail() {
      console.log(this.$route.query, 'this.$router.query')
      this.isShop = this.$route.query.pageType == 'shop'
      if (this.isShop) {
        this.$set(this.form, 'goods_type', '2')
      }
      if (this.goodsDetail().goods_id == 0 || this.goodsDetail().goods_id == undefined) {
        return
      }
      let data = this.goodsDetail()
      this.form = {
        sales: data.sales,
        goods_type: Number(data.goods_type),
        name: data.name,
        group_ids: data.group_ids || [],
        video: data.video,
        goods_code: data.goods_code,
        cate_id: [data.cate_first_id, data.cate_second_id, data.cate_third_id].filter(
          (item) => !!item
        ),
        sort: data.sort,
      }

      this.shopImageArr = data.images || []
      this.shopImageArr = this.shopImageArr.map((item) => {
        return {
          name: item,
          url: item,
        }
      })

      this.shopImageArr = data.images.map((item) => {
        return {
          name: item,
          url: item,
        }
      })

      // 卡券
      if (data.goods_type == 2) {
        let cardSetting = data.card_setting
        this.$set(this.form, 'card_type', parseInt(cardSetting.card_type))
        this.serviceList = cardSetting.service_item.map((item) => {
          return {
            name: item,
          }
        })
      }
    },
    // 选择商品类型
    selectGoodsType(type) {
      if (this.isEdit || this.isView || this.isShop) {
        return
      }
      let _this = this
      this.$confirm(
        `切换商品类型后，原类型商品内容将被清空且无法恢复。请确认是否继续操作？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(() => {
          _this.$set(_this.form, 'goods_type', type)
        })
        .catch(() => {})
    },

    // // 选择门店回调
    changeStore(e) {
      this.form.shop_id = e
      // this.form.cate_id = []
      // this.form.group_ids = []
      // this.checkGalleryPermissionFn()
      // // 初始化分类列表
      // this.initCategory()
      // // 商品分组列表
      // this.goodsGroupList()
    },

    // 商品分组列表
    goodsGroupList() {
      goodsGroupListApi().then((res) => {
        if (res.code == 200) {
          this.groupList = res.data.list
        }
      })
    },

    // 添加分类 - 添加分组
    addSelect(e) {
      // if (this.form.shop_id == '' || this.form.shop_id == null) return this.$message('请先选择门店')
      if (e == 'cate') {
        this.$refs.cate.openCate('add', this.form.shop_id)
      } else {
        this.$refs.group.open('add', this.form.shop_id)
      }
    },
    // 添加分类回调
    addCateUpdate(e) {
      this.$set(this.form, 'cate_id', e)
      this.initCategory()
    },
    // 添加分组回调
    addGroupUpdate(e) {
      this.$set(this.form, 'group_ids', this.form.group_ids.concat(e))
      this.goodsGroupList()
    },

    // 分类
    initCategory() {
      goodsCategorylistBySelectApi({
        type: 'list',
      }).then((res) => {
        if (res.code == 200) {
          this.categoryList = res.data
        }
      })
    },

    // 选择图片
    selectImage() {
      if (this.shopImageArr.length == 10) {
        this.$message.error('最多上传十张图片')
        return false
      }
      this.isSelectImage = true
    },
    // 选择图片回调
    getSelectPicList(e) {
      console.log(e)
      this.shopSelectImageArr = e
      this.confirmTextPic = `确定选择(${this.shopSelectImageArr.length}张)`
    },
    // 保存选择图片回调
    saveSelectImage() {
      this.shopSelectImageArr.map((item) => {
        this.shopImageArr.push({
          name: item.name,
          url: item.image_url,
        })
      })
      console.log(this.shopImageArr)
      this.isSelectImage = false
      this.confirmTextPic = `确定选择(0张)`
    },
    //清空选择的图片
    cancelSelectImage() {
      this.selectPicList = []
      this.shopSelectImageArr = []
      this.confirmTextPic = `确定选择(0张)`
    },
    // 删除图片回调
    deleteImg(e) {
      this.shopImageArr = this.shopImageArr.filter((item, index) => {
        return index != e
      })
    },

    // 图片上传回调
    upLoadImgFn(e, key) {
      let type = e.file.type
      if (type != 'image/png' && type != 'image/jpg') {
        return this.$message.error('仅支持png,jpg格式，请选择正确格式文件重试！')
      }
      upLoadImg(e.file, key).then((res) => {
        this.shopImageArr.push({
          name: res.data.url,
          url: res.data.url,
        })
      })
    },

    // 图片上传限制回调
    limitExceedFn() {
      this.$message.error('最多上传十张图片')
    },
    handleChange(file, fileList) {
      this.shopImageArr = fileList.filter((item, index) => {
        return item.status == 'success'
      })
      console.log(fileList)
      // 动态校验（含初始文件）
    },

    // 视频上传回调
    upLoadVideoFn(e) {
      let type = e.file.type
      if (type != 'video/mp4' && type != 'video/mov') {
        return this.$message.error('仅支持mp4,mov格式，请选择正确格式文件重试！')
      }
      upLoadVideo(e.file)
        .then((res) => {
          if (res.code == 200) {
            this.form.video = ''
            this.$nextTick(() => {
              this.$set(this.form, 'video', res.data.video_url)
              this.$message.success('视频上传成功')
            })
            console.log(res.data.video_url)
          }
        })
        .catch((err) => {
          this.form.video = ''
        })
    },

    // 视频上传限制回调
    limitExceedVideoFn() {
      this.$message.error('最多上传一个视频')
    },
    // 下一步
    next(callbackFn) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.isBtnLoading = true
          let params = {
            ...this.form,
            images: this.shopImageArr.map((item) => item.url),
            cate_id: this.form.cate_id[this.form.cate_id.length - 1],
          }
          if (this.save_type == 'edit' || this.goods_id) {
            params.goods_id = this.goods_id
          }
          if (this.form.goods_type == 2) {
            params.service_item = this.serviceList.map((item) => item.name)
          }
          goodsSaveFirstApi(params).then((res) => {
            this.isBtnLoading = false
            if (res.code == 200) {
              // ⚠️ 只有门店创建商品时才可以赋值。 现需求租户无权更改门店商品操作 如果需求更改 请另穿值判断
              if (params.shop_id) {
                store.dispatch('goodsDetaile/setShopId', params.shop_id || '')
              }
              store.dispatch('goodsDetaile/setGoodsId', res.data.goods_id)
              callbackFn()
            }
          })
        } else {
          return false
        }
      })
    },
    //移动后图片
    changePicSort(data) {
      let aa = []
      data.forEach((iitem) => {
        this.shopImageArr.forEach((item) => {
          if (item.url == iitem) {
            aa.push(item)
          }
        })
      })
      this.shopImageArr = [].concat(aa)
    },

    // 添加服务
    addRefund() {
      this.serviceList.push({
        name: '',
      })
    },

    deleteRefund(index) {
      this.serviceList.splice(index, 1)
    },
  },
}
</script>

<style lang="scss" scoped>
.goods-tab-item {
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #dcdfe6;
  text-align: center;
  padding: 24px 0;
  font-size: 12px;
  color: #999999;
  cursor: pointer;
  & > div:first-child {
    font-size: 14px;
    color: #222222;
    line-height: 20px;
    font-weight: 600;
  }
  &.key {
    border-color: #0071fe;
    position: relative;
    &::after {
      content: '';
      position: absolute;
      width: 32px;
      height: 32px;
      right: 0;
      top: 0;
      background: url('~@/assets/goods/goods_select.png') no-repeat;
      background-size: 32px 32px;
    }
  }
}
.goods-tab-item.del-select {
  border-color: #f6f8f9;
  background: #f6f8f9;
  & > div {
    color: #c0c4cc;
  }
}
.flex-image {
  flex-wrap: wrap;
  align-items: flex-end;
  .shop-image {
    width: 100px;
    height: 100px;
    margin-right: 10px;
    border-radius: 6px;
    margin-bottom: 10px;
  }
  .image-draggable {
    display: inline-flex;
    flex-wrap: wrap;
    margin-right: 10px;
  }
}

// 上传图片样式
.up_data {
  position: relative;
  width: 100px;
  height: 100px;
  background-color: #fff;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  box-sizing: border-box;
  text-align: center;
  overflow: hidden;
  .el-icon-plus {
    margin: 20px 10px 0;
  }
  .upload_img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
  }
}
// 上传提示文字样式
.el-upload__tip {
  font-size: 12px;
  color: #606266;
  margin-top: 7px;
}
// 视频播放样式
.video_player {
  margin-right: 10px;
}

.flex-draggable {
  display: inline-flex;
}

::v-deep {
  .el-input-number.is-without-controls .el-input__inner {
    text-align: left;
  }
}

.m10 {
  margin-top: 10px;
}
.card-title {
  line-height: 24px;
  margin-left: -35px;
}
.card-main {
  font-weight: 400;
  font-size: 12px;
  color: #909399;
  line-height: 12px;
}

.add_reason {
  width: 100%;
  border-style: dashed;
}

.refund_reason_list {
  > div {
    margin-bottom: 10px;
  }
  .refund_del {
    color: #ff2727;
    margin-left: 20px;
  }
}

.alert-warning {
  border: 1px solid #ffeabf;
}
</style>
