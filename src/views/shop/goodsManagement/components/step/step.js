// 获取第几步  num数量 goods_type 商品类型 type 下一页还是上一步

export let getStepNum = (num, goods_type, type, store, isReject) => {
  num = parseInt(num)
  let step
  if (type == 'next') {
    switch (num) {
      case 1: step = (goods_type == 1 ? 3 : goods_type == 2 ? 2 : 2); break;
      case 2: step = 3; break;
      case 3: step = 4; break
      case 4: step = (goods_type == 1 ? 6 : goods_type == 2 ? 5 : 6); break;
      case 5: step = isReject ? 8 : 5; break;
      case 6: step = 7; break;
    }
  }
  if (type == 'up') {
    switch (num) {
      case 2: step = 1; break;
      case 3: step = (goods_type == 1 ? 1 : goods_type == 2 ? 2 : 1); break;
      case 4: step = 3; break;
      case 5: step = 4; break;
      case 6: step = (goods_type == 1 ? 4 : goods_type == 2 ? 5 : 4); break;
      case 7: step = (goods_type == 1 ? 6 : goods_type == 2 ? 5 : 6); break;
      case 8: step = 7; break;
    }
  }
  console.log('step', step, isReject)
  store.dispatch('goodsDetaile/setTabKeys', step + '')
}


// 数量 请输入大于0的数字
export let inputNum = (data, code, num, that) => {
  console.log(Number(data[code]), data[code] != '')
  if (data[code] != '' || data[code] == 0) {
    if (isNaN(data[code]) || Number(data[code]) <= 0) {
      that.$message.error('请输入大于0的数字')
      data[code] = ''
    } else {
      data[code] = Number(data[code]).toFixed(num)
    }
  }
}


