<template>
  <div>
    <el-dialog
      class="batch-specs"
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="isBatch"
      title="批量设置"
    >
      <el-form
        v-if="isBatch"
        label-position="top"
        ref="batchForm"
        label-width="200px"
        :model="batchForm"
        :rules="batchFormRules"
      >
        <div class="flex step">
          <div class="num">1</div>
          <div>选择所需设置规格</div>
        </div>
        <el-form-item
          :label="item.specsName"
          :prop="`specsValue${index}`"
          v-for="(item, index) in saveSpecsList"
          :key="index"
        >
          <el-select
            class="w100"
            v-model="batchForm[`specsValue${index}`]"
            placeholder="请选择"
            multiple
          >
            <el-option
              v-for="items in item.specsValueBatch"
              :key="items"
              :label="items"
              :value="items"
            ></el-option>
          </el-select>

          <div v-if="index == (saveSpecsList.length - 1)">
            已选择
            <span>{{goodsNum}}</span>个规格组合
          </div>
        </el-form-item>

        <el-form-item>
          <template #label>
            <div class="flex step">
              <div class="num">2</div>
              <div>选择所需设置字段</div>
            </div>
          </template>

          <el-select class="w100" v-model="selectKey" placeholder="请选择" multiple>
            <el-option
              v-for="items in speciesList"
              :key="items.key"
              :label="items.name"
              :value="items.key"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <div class="flex step">
            <div class="num">3</div>
            <div>填写字段内容</div>
          </div>
          <div v-if="selectKey == 0" class="no-data w100">
            <img :src="imgOssPath + '20250729_no_set.png'" alt />
            <div>请先在步骤2中选择需要设置的字段</div>
          </div>
          <multiSpecsBatchSet ref="multiSpecsBatchSet" v-else :selectKeyObj="selectKeyObj"></multiSpecsBatchSet>
        </el-form-item>
      </el-form>
      <base-dialog-footer @confirm="confirm" @cancel="isBatch = false"></base-dialog-footer>
    </el-dialog>
  </div>
</template>
<script>
import multiSpecsBatchSet from './multiSpecsBatchSet.vue'
export default {
  name: 'batchSpecs',
  components: {
    multiSpecsBatchSet,
  },
  props: {
    // 是否无限库存
    is_unlimited_stock: {
      type: Boolean,
      default: false,
    },
    // 商品类型 1:普通商品，2:套餐商品，3:组合商品
    goods_type: {
      type: Number,
      default: 1,
    },
    // 批量设置表单
    saveSpecsList: {
      type: Array,
      default: () => [],
    },

    goodsList: {
      type: Array,
      default: () => [],
    },
  },
  created() {},
  computed: {
    selectKeyObj() {
      let obj = {}
      this.selectKey.forEach((item) => {
        obj[item] = true
      })
      return obj
    },

    goodsNum() {
      let num = 0
      this.goodsList.filter((item, index) => {
        let isOk = []
        this.saveSpecsList.map((items, indexs) => {
          this.batchForm[`specsValue${indexs}`].length > 0 &&
            isOk.push(this.batchForm[`specsValue${indexs}`].includes(item[`specsValue${indexs}`]))
        })
        if (isOk.length > 0 && isOk.every((item) => item == true)) num++
      })
      return num
    },
  },
  mounted() {
    this.pageType = this.$route.query.pageType
    if (this.pageType == 'headquarters') {
      this.speciesList[2].name = '总部库存'
    } else if (this.pageType == 'shop') {
      this.speciesList[2].name = '门店库存'
    }
  },
  data() {
    // headquarters: 总部商品，shop:门店商品
    return {
      // 批量设置表单
      batchForm: {},
      batchFormRules: {},
      batchFormRulesKeys: [
        {
          validator: (rule, value, callback) => {
            let valuse = Object.values(this.batchForm).some((item) => item.length > 0)
            if (valuse) {
              callback()
            } else {
              callback(new Error('请至少选择一个规格值'))
            }
          },
          trigger: 'change',
        },
      ],

      selectKey: [],
      isBatch: false,
      speciesList: [
        {
          name: '成本价',
          key: 'cost_price',
          type: [1, 2, 'is_unlimited_stock'],
        },
        {
          name: '销售价',
          key: 'sale_price',
          type: [1, 2, 'is_unlimited_stock'],
        },
        {
          name: '总部库存',
          key: 'stock',
          type: [1, 2],
        },
        {
          name: '库存预警值',
          key: 'warning_stock',
          type: [1, 2],
        },
        {
          name: '重量（kg）',
          key: 'weight',
          type: [1, 'is_unlimited_stock'],
        },
        {
          name: '体积（cm³）',
          key: 'volume',
          type: [1, 'is_unlimited_stock'],
        },
        {
          name: '规格图片',
          key: 'image',
          type: [1, 2, 'is_unlimited_stock'],
        },
      ],
      pageType: '',
    }
  },
  methods: {
    opetnBatch() {
      this.selectKey = []
      this.saveSpecsList.forEach((item, index) => {
        this.$set(this.batchForm, `specsValue${index}`, [])
        this.$set(this.batchFormRules, `specsValue${index}`, this.batchFormRulesKeys)
      })
      this.speciesList = this.speciesList.filter((item) => {
        let isUnlimited = this.is_unlimited_stock ? item.type.includes('is_unlimited_stock') : true
        console.log(item.type.includes(this.goods_type), isUnlimited)
        return item.type.includes(this.goods_type) && isUnlimited
      })
      this.isBatch = true
    },

    confirm() {
      this.$refs.batchForm.validate((valid) => {
        if (valid) {
          if (this.selectKey.length == 0) {
            this.$message.error('请选择需要设置的字段')
            return false
          }
          let obj = this.$refs.multiSpecsBatchSet.confirm()
          this.isBatch = false
          this.$emit('confirm', obj, this.batchForm)
        } else {
          return false
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.step .num {
  width: 24px;
  height: 24px;
  background: #0071fe;
  border-radius: 80px;
  line-height: 24px;
  text-align: center;
  margin-right: 8px;
  color: #fff;
}
.no-data {
  text-align: center;
  font-weight: 400;
  font-size: 14px;
  color: #74777a;
  line-height: 20px;
  text-align: center;
  font-style: normal;
  img {
    width: 150px;
    height: 150px;
    margin-bottom: 10px;
  }
}
::v-deep {
  .batch-specs .el-form--label-top .el-form-item__label {
    padding: 0px !important;
  }
  .batch-specs .el-form {
    padding-right: 0 !important;
  }
}
</style>