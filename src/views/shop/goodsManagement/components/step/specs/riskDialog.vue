<template>
  <div>
    <el-dialog append-to-body :visible.sync="isRisk" :close-on-click-modal="false">
      <template #title>
        <div class="error-dialog-tip">
          <i class="el-icon-warning"></i>
          风险提示
        </div>
      </template>
      <el-alert
        title="当前商品中存在「成本价」大于「销售价」的情况，继续执行此操作可能会导致商品亏损，请确认是否继续？"
        type="error"
        :closable="false"
      ></el-alert>
      <base-dialog-footer
        cancelText="返回修改"
        confirmText="确认修改"
        @cancel="isRisk = false"
        @confirm="confirm"
      ></base-dialog-footer>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'CancelOrderDialog',
  data() {
    return {
      isRisk: false,
    }
  },
  methods: {
    open() {
      this.isRisk = true
    },
    confirm() {
      this.isRisk = false
      this.$emit('confirm')
    },
  },
}
</script>

<style lang="scss" scoped>
.error-dialog-tip .el-icon-warning {
  color: #f52f3e;
}
</style>