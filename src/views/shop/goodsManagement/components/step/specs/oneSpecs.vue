<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-03-27 09:48:13
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-24 15:47:09
 * @FilePath: /qst-merchant-admin-2.0/src/views/shop/goodsManagement/compontes/specs.vue
 * @Description: 单规格
-->
<template>
  <div class="specs-tem">
    <el-form ref="form" :model="form" label-position="top" label-width="200px" :rules="rules">
      <el-row :gutter="10">
        <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
          <el-form-item label="成本价">
            <div class="input-number-tem">
              <el-input
                class="w100 input-number"
                :precision="2"
                v-model="form.cost_price"
                placeholder="请输入成本价"
                :disabled="isView"
                prefix-icon="¥"
                @blur="inputFree0($event, 'cost_price')"
              ></el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
          <el-form-item label="销售价" prop="sale_price">
            <div class="input-number-tem">
              <el-input
                class="w100 input-number"
                :min="0"
                :precision="2"
                v-model="form.sale_price"
                placeholder="请输入销售价"
                :disabled="isView"
                prefix-icon="¥"
                @blur="inputFree0($event, 'sale_price')"
                :controls="false"
              ></el-input>
            </div>
          </el-form-item>
        </el-col>

        <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12" v-if="!shop_id">
          <el-form-item label="是否无限库存" required>
            <el-select
              class="w100"
              v-model="is_unlimited_stock"
              placeholder="请选择"
              :disabled="isView"
            >
              <el-option :key="1" label="是" :value="'Y'"></el-option>
              <el-option :key="2" label="否" :value="'N'"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <template v-if="viewStock">
          <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
            <el-form-item label="总部库存" prop="stock" v-if="is_unlimited_stock == 'N'">
              <el-input-number
                :disabled="isView"
                class="w100"
                :min="0"
                :precision="0"
                v-model="form.stock"
                placeholder="请输入库存仅支持大于等于0的整数"
                :controls="false"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
            <el-form-item label="门店总库存" prop="shop_stock" v-if="is_unlimited_stock == 'N'">
              <el-input-number
                :disabled="isView"
                class="w100"
                :min="0"
                :precision="0"
                v-model="form.shop_stock"
                placeholder="请输入库存仅支持大于等于0的整数"
                :controls="false"
              ></el-input-number>
            </el-form-item>
          </el-col>
        </template>

        <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
          <el-form-item
            :label=" isShop ? '门店总库存' : '总部库存'"
            prop="stock"
            v-if="is_unlimited_stock == 'N'"
          >
            <el-input-number
              :disabled="isView"
              class="w100"
              :min="0"
              :precision="0"
              v-model="form.stock"
              placeholder="请输入库存仅支持大于等于0的整数"
              :controls="false"
            ></el-input-number>
          </el-form-item>
        </el-col>

        <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
          <el-form-item label="是否显示剩余库存" required>
            <template #label>
              <span>是否显示剩余库存</span>
              <el-button type="text" class="ml20">查看示例</el-button>
            </template>
            <el-select class="w100" v-model="is_show_stock" placeholder="请选择" :disabled="isView">
              <el-option :key="1" label="是" :value="'Y'"></el-option>
              <el-option :key="2" label="否" :value="'N'"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12" v-if="is_unlimited_stock == 'N'">
          <el-form-item label="库存预警值">
            <el-input
              class="w100"
              :min="0"
              :precision="0"
              v-model="form.warning_stock"
              placeholder="请输入库存预警值"
              :disabled="isView"
              @blur="inputFree2($event, 'warning_stock')"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12" v-if="goods_type != 2">
          <el-form-item label="重量（kg）">
            <el-input
              class="w100"
              :min="0"
              :precision="3"
              v-model="form.weight"
              placeholder="请输入重量"
              :disabled="isView"
              @blur="inputFree1($event, 'weight')"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12" v-if="goods_type != 2">
          <el-form-item label="体积（cm³）">
            <el-input
              class="w100"
              :min="0"
              :precision="0"
              v-model="form.volume"
              placeholder="请输入体积"
              :disabled="isView"
              @blur="inputFree2($event, 'volume')"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
          <el-form-item label="商品条码">
            <span slot="label">
              商品条码
              <el-tooltip effect="dark" placement="top" content="商品的条形码，用于扫码识别">
                <img
                  :src="imgOssPath + '20250619_sf_wenhao.png'"
                  alt
                  class="icon_wenhao"
                  style="transform: translateY(2px);"
                />
              </el-tooltip>
            </span>
            <el-input :disabled="isView" v-model="form.barcode" placeholder="请输入商品条码"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <footer-btn :num="3" :isBtnLoading="isBtnLoading" @next="next"></footer-btn>

    <riskDialog @confirm="riskSumbit" ref="riskDialog"></riskDialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { goodsSaveSecondApi } from '@/api/shop/goodsManagement.js'
import store from '@/store'
import { getStepNum } from '../step.js'
import riskDialog from './riskDialog.vue'
import footerBtn from '../footerBtn.vue'

export default {
  name: 'specs',
  components: {
    riskDialog,
    footerBtn,
  },
  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  inject: ['goodsDetail'],
  computed: {
    ...mapGetters({
      save_type: 'goodsDetaile/getSaveType',
      goods_id: 'goodsDetaile/getGoodsId',
      shop_id: 'goodsDetaile/getShopId',
    }),
    isView() {
      return this.save_type == 'view'
    },

    // 总部库存查看
    viewStock() {
      console.log(this.$route.query.pageType, this.shop_id)
      // 查看 总部查看 店铺商品 非无限库存
      return (
        this.save_type == 'view' &&
        this.$route.query.pageType == 'headquarters' &&
        this.shop_id > 0 &&
        this.is_unlimited_stock == 'N'
      )
    },
    // 门店查看/创建时的
    isShop() {
      return this.$route.query.pageType == 'shop'
    },
  },
  mounted() {
    this.initGoodsDetail()
  },
  data() {
    return {
      is_draft: false,
      isBtnLoading: false,
      // 是否需要校验风险
      isRiskAuth: false,
      isJump: '',
      rules: {
        sale_price: [
          { required: true, message: '请输入销售价', trigger: 'blur' },
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value >= 0) {
                callback()
              } else {
                return callback(new Error('请输入大于等于0的数字'))
              }
            },
            trigger: 'blur',
          },
        ],
        stock: [{ required: true, message: '请输入库存仅支持大于等于0的整数', trigger: 'blur' }],
      },
      is_unlimited_stock: 'N', //是否无限库存
      form: {
        cost_price: undefined, //sku成本价
        sale_price: undefined, //sku销售价
        stock: undefined, //sku库存
        warning_stock: undefined, //sku预警库存
        weight: undefined, //sku重量
        volume: undefined, //sku体积
        is_default: 'Y', //是否默认规格
        barcode: undefined, //sku条形码多个逗号分割
      },
      is_show_stock: 'Y', //是否显示库存
      goods_type: '',
    }
  },
  methods: {
    // 获取商品详情信息
    initGoodsDetail() {
      let data = this.goodsDetail()
      this.is_draft = data.is_draft == 'Y'
      this.goods_type = data.goods_type
      if (data.spec_type == '1' && data.goods_skus.length > 0) {
        this.form = data.goods_skus[0] || {}
        this.is_unlimited_stock = data.is_unlimited_stock
        this.is_show_stock = data.is_show_stock
      }
    },
    // 风险确认
    riskSumbit() {
      this.isRiskAuth = true
      this.next(this.isJump)
    },
    // 下一步
    next(isJump) {
      this.isJump = isJump
      this.isBtnLoading = true
      this.$refs.form.validate((valid) => {
        if (valid) {
          let form = {}
          Object.keys(this.form).forEach((key) => {
            form[key] = this.form[key] >= 0 ? this.form[key] : ''
          })
          form.barcode = this.form['barcode']
          console.log(this.form)
          if (!this.isRiskAuth) {
            if (Number(form.cost_price) > Number(form.sale_price)) {
              this.$refs.riskDialog.open()
              this.isBtnLoading = false
              return false
            }
          }
          goodsSaveSecondApi({
            goods_id: this.goods_id,
            spec_type: '1',
            is_unlimited_stock: this.is_unlimited_stock,
            is_show_stock: this.is_show_stock,
            goods_skus: [
              {
                ...form,
                is_default: 'Y',
              },
            ],
          })
            .then((res) => {
              this.isBtnLoading = false
              this.isRiskAuth = false
              if (res.code === 200) {
                store.dispatch('goodsDetaile/setRefreshShopNum')
                if (isJump == 'draft') {
                  this.$message.success('保存成功')
                  return false
                }
                getStepNum(3, this.form.goods_type, 'next', store)
              }
            })
            .catch(() => {})
        } else {
          this.isBtnLoading = false
          return false
        }
      })
    },

    // 单规格输入限制
    inputFree0(e, code) {
      let val = e.target.value
      if (isNaN(val) || val < 0 || val == '') {
        this.form[code] = ''
      } else {
        this.form[code] = Number(val).toFixed(2)
      }
    },
    inputFree(e, code) {
      let val = e.target.value
      if (val == '') {
        this.form[code] = ''
      } else if (isNaN(val) || val <= 0) {
        this.$message.error('请输入大于0的数字')
        this.form[code] = ''
      } else {
        this.form[code] = Number(val).toFixed(2)
      }
    },
    inputFree1(e, code) {
      let val = e.target.value
      if (val == '') {
        this.form[code] = ''
      } else if (isNaN(val) || val <= 0) {
        this.$message.error('请输入大于0的数字')
        this.form[code] = ''
      } else {
        this.form[code] = Number(val).toFixed(3)
      }
    },
    inputFree2(e, code) {
      let val = e.target.value
      if (val == '') {
        this.form[code] = ''
      } else if (isNaN(val) || val < 1) {
        this.$message.error('输入的数字必须大于0且为整数')
        this.form[code] = ''
      } else {
        this.form[code] = Number(val).toFixed(0)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.w100 {
  width: 100%;
}
.ml20 {
  display: inline-block;
  margin-left: 20px;
}
::v-deep {
  .el-form-item__content input {
    text-align: left;
  }
}
.input-number-tem ::v-deep {
  .el-input-number--small {
    width: 110px;
  }
  .el-input-number.is-controls-right .el-input__inner {
    padding-left: 22px;
    padding-right: 36px;
    width: 110px;
    box-sizing: border-box;
    text-align: left !important;
  }
  .input-number::after {
    content: '¥';
    position: absolute;
    top: 1px;
    left: 2px;
    width: 18px;
    line-height: 30px;
    text-align: center;
    background: #fff;
    color: #222222;
    box-sizing: border-box;
    z-index: 1;
    display: block;
  }
}
</style>
