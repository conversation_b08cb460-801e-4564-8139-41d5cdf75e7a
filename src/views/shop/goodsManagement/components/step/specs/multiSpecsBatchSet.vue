<!--
 * @Author: shang<PERSON>i <EMAIL>
 * @Date: 2025-03-27 09:48:13
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-28 16:05:30
 * @FilePath:
 * @Description: 多规格批量设置弹窗
-->

<template>
  <div class="multiSpecsBatchSet">
    <el-form :model="batchForm" ref="batchFormRef">
      <el-form-item label="成本价" v-if="selectKeyObj.cost_price">
        <div class="input-number-tem">
          <el-input
            class="w100 input-number"
            v-model="batchForm.cost_price"
            :min="0"
            :precision="2"
            placeholder="请输入成本价"
            controls-position="right"
            prefix-icon="¥"
            @blur="inputFree($event, 'cost_price', 2)"
          ></el-input>
        </div>
      </el-form-item>

      <el-form-item label="销售价" v-if="selectKeyObj.sale_price">
        <div class="input-number-tem">
          <el-input
            class="w100 input-number"
            v-model="batchForm.sale_price"
            :min="0"
            :precision="2"
            placeholder="请输入销售价"
            controls-position="right"
            prefix-icon="¥"
            @blur="inputFree0($event, 'sale_price', 2)"
          ></el-input>
        </div>
      </el-form-item>

      <el-form-item label="总部库存" v-if="selectKeyObj.stock">
        <el-input-number
          class="w100"
          v-model="batchForm.stock"
          :min="0"
          :precision="0"
          placeholder="请输入库存仅支持大于等于0的整数"
          :controls="false"
        ></el-input-number>
      </el-form-item>

      <el-form-item label="库存预警值" v-if="selectKeyObj.warning_stock">
        <el-input
          class="w100"
          v-model="batchForm.warning_stock"
          :precision="0"
          placeholder="请输入库存预警值"
          controls-position="right"
          :controls="false"
          @blur="inputFree($event, 'warning_stock', 0)"
        ></el-input>
      </el-form-item>

      <el-form-item label="重量（kg）" v-if="selectKeyObj.weight">
        <el-input
          class="w100"
          v-model="batchForm.weight"
          :min="0"
          :precision="3"
          placeholder="请输入商品重量"
          controls-position="right"
          @blur="inputFree($event, 'weight', 3)"
        ></el-input>
      </el-form-item>

      <el-form-item label="体积（cm³）" v-if="selectKeyObj.volume">
        <el-input
          class="w100"
          v-model="batchForm.volume"
          :precision="0"
          placeholder="请输入商品体积"
          controls-position="right"
          :controls="false"
          @blur="inputFree($event, 'volume', 0)"
        ></el-input>
      </el-form-item>

      <el-form-item label="规格图片" v-if="selectKeyObj.image">
        <div class="specs-item">
          <el-image v-if="batchForm.image" class="shop-image" :src="batchForm.image"></el-image>
          <div class="up_data" v-else>
            <i class="el-icon-plus"></i>
            <div class="el-upload__text">上传图片</div>
          </div>
          <el-upload
            class="upload_img"
            action="fakeaction"
            :show-file-list="false"
            drag
            accept=".jpg, .png"
            :http-request="(e) => upLoadImg(e, 'image')"
          ></el-upload>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { upLoadImg } from '@/utils/uploadImage.js'
import multiSpecsItem from './multiSpecsItem.vue'
export default {
  name: 'specs',
  components: {
    multiSpecsItem,
  },
  props: {
    selectKeyObj: {
      type: Object,
      default: () => {
        return {
          cost_price: true,
          sale_price: true,
          stock: true,
          warning_stock: true,
          weight: true,
          volume: true,
          image: true,
          sku_id: true,
        }
      },
    },
  },
  watch: {},
  data() {
    return {
      // 是否显示批量设置弹窗
      isBatchSet: false,
      // 规格模版设置
      batchForm: {},
    }
  },
  methods: {
    // 初始化数据 打开弹窗
    open() {
      this.batchForm = {}
      this.isBatchSet = true
    },
    // 上传
    upLoadImg(e, key) {
      let _this = this
      upLoadImg(e.file, key).then((res) => {
        _this.$set(_this.batchForm, key, res.data.url)
      })
    },

    // 确认按钮
    confirm() {
      let flag = true
      Object.values(this.batchForm).forEach((item) => {
        if (!(item === null || item === undefined)) {
          flag = false
        }
      })
      if (flag) {
        this.$message.error('请填写信息')
        return
      }
      this.isBatchSet = false
      let obj = {}
      Object.keys(this.batchForm).forEach((key) => {
        if (this.batchForm[key] || this.batchForm[key] === 0) {
          obj[key] = this.batchForm[key]
        }
      })
      return obj
      console.log('批量', obj)
      this.$emit('confirm', obj)
    },
    inputFree0(e, code, num) {
      console.log(11)
      let val = e.target.value
      if (isNaN(val) || val < 0) {
        // if (val < 0) {
        this.$message.error('请输入大于等于0的数字')
        this.batchForm[code] = ''
      } else {
        this.batchForm[code] = Number(val).toFixed(num)
      }
      // }
    },
    inputFree(e, code, num) {
      let val = e.target.value
      // if (val != '') {
      if (isNaN(val) || val <= 0) {
        this.$message.error('请输入大于0的数字')
        this.batchForm[code] = ''
      } else {
        this.batchForm[code] = Number(val).toFixed(num)
      }
      // }
    },
  },
  mounted() {},
}
</script>

<style lang="scss" scoped>
.w100 {
  width: 100%;
  flex: 1;
}

.el-icon-warning {
  color: #ffad32;
  font-size: 18px;
  margin-right: 5px;
}

.el-icon-plus {
  padding-top: 30px;
}

// 上传图片
.specs-item {
  width: 100px;
  height: 100px;
  border: 1px dashed #e9e9eb;
  text-align: center;
  margin-top: 36px;
  overflow: hidden;
  // padding-top: 20px;
  box-sizing: border-box;
  position: relative;
  .upload_img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
  }
  .el-upload__text {
    margin-top: -10px;
  }
}
.multiSpecsBatchSet ::v-deep {
  .el-form {
    padding-right: 0 !important;
  }
}
::v-deep {
  .el-form-item__content input {
    text-align: left;
  }
}
.input-number-tem ::v-deep {
  .el-input-number--small {
    width: 110px;
  }
  .el-input-number.is-controls-right .el-input__inner {
    padding-left: 22px;
    padding-right: 36px;
    width: 110px;
    box-sizing: border-box;
    text-align: left !important;
  }
  .input-number::after {
    content: '¥';
    position: absolute;
    top: 1px;
    left: 2px;
    width: 18px;
    line-height: 30px;
    text-align: center;
    background: #fff;
    color: #222222;
    box-sizing: border-box;
    z-index: 1;
    display: block;
  }
}
</style>
