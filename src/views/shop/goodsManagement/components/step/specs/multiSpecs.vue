<!--
 * @Author: shang<PERSON>i <EMAIL>
 * @Date: 2025-03-27 09:48:13
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-26 15:08:31
 * @FilePath:
 * @Description: 多规格 
  因为要用到规格模版 所以要单独写一个form  
  因为要和规格组共用一套数据 所以要单独再写一个组件 multiSpecsItem
  form表单各自验证 需要分开验证  就分开写
-->

<template>
  <div class="specs-tem">
    <el-form
      label-position="top"
      ref="tem"
      label-width="200px"
      :model="temForm"
      :rules="temFormRules"
    >
      <el-row :gutter="10">
        <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12" v-if="!shop_id">
          <el-form-item label="是否无限库存" required :disabled="isView">
            <el-select class="w100" v-model="temForm.is_unlimited_stock" placeholder="请选择">
              <el-option :key="'Y'" label="是" :value="'Y'"></el-option>
              <el-option :key="'N'" label="否" :value="'N'"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
          <el-form-item>
            <template #label>
              <div class="flex-b w100">
                <span>是否显示剩余库存</span>
                <el-button type="text">查看示例</el-button>
              </div>
            </template>
            <el-select
              class="w100"
              v-model="temForm.is_show_stock"
              placeholder="请选择"
              :disabled="isView"
            >
              <el-option :key="'Y'" label="是" :value="'Y'"></el-option>
              <el-option :key="'N'" label="否" :value="'N'"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :lg="24" :md="24" :sm="24" :xl="24" :xs="24">
          <el-form-item label="规格模版">
            <div class="flex">
              <el-select
                class="w100"
                v-model="temForm.temKey"
                @change="specsChange"
                placeholder="请选择规格模版"
                :disabled="isView"
              >
                <el-option
                  v-for="item in selectSpecsList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
              <el-button
                style="margin-left: 10px"
                @click="specsModelAdd"
                icon="el-icon-plus"
                v-if="isAdd && !shop_id"
              >添加模版</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <multi-specs-item
      :ref="'specs' + index"
      v-for="(item, index) in specsList"
      :index="index"
      :key="index"
      :info="item"
      @delSpecs="delSpecs"
      :length="specsList.length"
      :shop_id="shop_id"
    ></multi-specs-item>

    <div
      class="flex add-model"
      :class="{
        'add-model-active': isEdit,
      }"
      v-if="!isView"
    >
      <el-button @click="addSpecs" icon="el-icon-plus" class="add-boder">添加规格组</el-button>
      <div>
        <el-button style="width: 100%" @click="saveSpecs" type="primary" icon="el-icon-plus">保存规格组</el-button>
        <!-- <div>啊睡了多久啊算了大量圣诞节啦是的啊睡了多久啊算了大量圣诞节啦是的</div> -->
      </div>
    </div>
    <div v-if="!isView">
      <div class="form-title w100">批量设置</div>
      <el-button type="primary" @click="openBatchSet">批量设置</el-button>
    </div>

    <el-table :data="tableData" style="width: 100%" v-loading="isTableLoading" ref="table">
      <el-table-column label="默认选中规格" fixed="left">
        <template slot-scope="scope">
          <div class="flex">
            <el-radio
              v-model="defaultIndex"
              :disabled="scope.row.is_show == 'N' || isView"
              :label="scope.$index"
            >{{ '' }}</el-radio>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        prop="date"
        :label="item.specsName"
        v-for="(item, index) in saveSpecsList"
        :key="index"
        fixed="left"
      >
        <template slot-scope="scope">{{ scope.row['specsValue' + index] }}</template>
      </el-table-column>
      <el-table-column v-if="saveSpecsList.length == 0" label="规格组合"></el-table-column>
      <el-table-column label="图片" width="100px">
        <template slot-scope="scope">
          <div class="specs-item">
            <div>
              <div v-if="!scope.row.image && isView"></div>
              <el-image v-else-if="scope.row.image " class="shop-image" :src="scope.row.image"></el-image>
              <div class="up_data" v-else>
                <i class="el-icon-plus"></i>
                <div class="el-upload__text">上传</div>
              </div>
              <el-upload
                v-if="!isView"
                class="upload_img"
                action="fakeaction"
                :show-file-list="false"
                drag
                accept=".jpg, .png"
                :http-request="(e) => upLoadImg(e, scope.$index)"
              ></el-upload>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="成本价" width="140px">
        <template slot-scope="scope">
          <div class="input-number-tem">
            <el-input
              :disabled="isView"
              class="input-number"
              placeholder="请输入"
              :precision="2"
              v-model="scope.row.cost_price"
              @blur="inputFree(scope.row, 'cost_price', 2)"
              :controls="false"
              prefix-icon="¥"
            ></el-input>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="销售价" width="140px">
        <template slot="header">
          销售价
          <span class="red">*</span>
        </template>
        <template slot-scope="scope">
          <div class="input-number-tem">
            <el-input
              :disabled="isView"
              class="input-number"
              :precision="2"
              placeholder="请输入"
              v-model="scope.row.sale_price"
              @blur="inputFree0(scope.row, 'sale_price', 2)"
              prefix-icon="¥"
              :controls="false"
            ></el-input>
          </div>
        </template>
      </el-table-column>

      <!-- ‼️ 门店详情的stock为门店的库存  总部接口查询时stock为总部的库存 总部查询门店商品详情时(stock为总部的) -->
      <el-table-column width="100px" v-if="viewStock">
        <template slot="header">
          总部库存
          <span class="red">*</span>
        </template>
        <template slot-scope="scope">
          <el-input-number
            :disabled="isView"
            style="width: 80px"
            :precision="0"
            :min="0"
            v-model="scope.row.stock"
            :controls="false"
          ></el-input-number>
        </template>
      </el-table-column>

      <el-table-column width="100px" v-if="viewStock">
        <template slot="header">
          门店总库存
          <span class="red">*</span>
        </template>
        <template slot-scope="scope">
          <el-input-number
            :disabled="isView"
            style="width: 80px"
            :precision="0"
            :min="0"
            v-model="scope.row.shop_stock"
            :controls="false"
          ></el-input-number>
        </template>
      </el-table-column>

      <el-table-column width="100px" v-else-if="temForm.is_unlimited_stock == 'N'">
        <template slot="header">
          {{isShop ? '门店库存': '总部库存'}}
          <span class="red">*</span>
        </template>
        <template slot-scope="scope">
          <el-input-number
            :disabled="isView"
            style="width: 80px"
            placeholder="请输入"
            :precision="0"
            :min="0"
            v-model="scope.row.stock"
            :controls="false"
          ></el-input-number>
        </template>
      </el-table-column>

      <el-table-column label="库存预警值" width="110px" v-if="temForm.is_unlimited_stock == 'N'">
        <template slot-scope="scope">
          <el-input
            :disabled="isView"
            style="width: 80px"
            placeholder="请输入"
            :precision="0"
            v-model="scope.row.warning_stock"
            @blur="inputFree(scope.row, 'warning_stock', 0)"
            :controls="false"
          ></el-input>
        </template>
      </el-table-column>

      <el-table-column label="重量（kg）" width="100px" v-if="goodsType != 2">
        <template slot-scope="scope">
          <el-input
            style="width: 80px"
            placeholder="请输入"
            v-model="scope.row.weight"
            @blur="inputFree(scope.row, 'weight', 3)"
            :controls="false"
            :precision="3"
          ></el-input>
        </template>
      </el-table-column>

      <el-table-column label="体积（cm³）" width="100px" v-if="goodsType != 2">
        <template slot-scope="scope">
          <el-input
            :disabled="isView"
            style="width: 80px"
            placeholder="请输入"
            v-model="scope.row.volume"
            @blur="inputFree(scope.row, 'volume', 0)"
            :precision="0"
            :controls="false"
          ></el-input>
        </template>
      </el-table-column>

      <el-table-column width="180px">
        <template slot="header" slot-scope="scope">
          <span type="text">商品条码</span>
          <el-tooltip class="item" effect="dark" placement="top" content="商品的条形码，用于扫码识别">
            <img
              :src="imgOssPath + '20250619_sf_wenhao.png'"
              alt
              class="icon_wenhao"
              style="transform: translateY(2px);margin-left: 4px;"
            />
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <el-input
            :disabled="isView"
            style="width: 160px"
            placeholder="请输入"
            v-model="scope.row.barcode"
            :precision="0"
            :controls="false"
          ></el-input>
        </template>
      </el-table-column>

      <el-table-column v-if="!isView" label="操作" width="100px" fixed="right">
        <template slot-scope="scope">
          <el-button type="danger" @click="delTable(scope.$index)" icon="el-icon-delete">删除</el-button>
          <div class="flex" v-if="isEdit" style="margin-top: 4px">
            <el-switch
              active-value="Y"
              :disabled="scope.row.is_default == 'Y'"
              inactive-value="N"
              v-model="scope.row.is_show"
            ></el-switch>
            <span style="margin-left: 4px">{{ scope.row.is_show == 'Y' ? '显示' : '隐藏' }}</span>
          </div>
        </template>
      </el-table-column>

      <template slot="empty">
        <base-tabel-empty />
      </template>
    </el-table>

    <footer-btn :num="3" @next="next" :isBtnLoading="isBtnLoading"></footer-btn>

    <riskDialog ref="riskDialog" @confirm="riskSumbit"></riskDialog>

    <batchSpecs
      :is_unlimited_stock="temForm.is_unlimited_stock == 'Y'"
      :goods_type="goodsType"
      :saveSpecsList="saveSpecsList"
      :goodsList="tableData"
      ref="batchSpecs"
      @confirm="batchSet"
    ></batchSpecs>

    <createSpersTemplate ref="reateSpers" @confirm="getGoodsSpecListFn"></createSpersTemplate>
  </div>
</template>

<script>
import multiSpecsItem from './multiSpecsItem.vue'
import batchSpecs from './batchSpecs.vue'
import { getGoodsSpecList } from '@/api/shop/goodsSet/goodsSpec'
import createSpersTemplate from '../../../../goodsSet/specsTemplate/createSpersTemplate.vue'
import { upLoadImg } from '@/utils/uploadImage.js'
import { goodsSaveSecondApi } from '@/api/shop/goodsManagement.js'
import store from '@/store'
import { mapGetters } from 'vuex'
import { getStepNum } from '../step.js'
import riskDialog from './riskDialog.vue'
import footerBtn from '../footerBtn.vue'

export default {
  name: 'specs',
  components: {
    multiSpecsItem,
    createSpersTemplate,
    riskDialog,
    batchSpecs,
    footerBtn,
  },

  props: {
    info: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    ...mapGetters({
      goods_id: 'goodsDetaile/getGoodsId',
      save_type: 'goodsDetaile/getSaveType',
      save_init_type: 'goodsDetaile/getInitSpecsType',
      shop_id: 'goodsDetaile/getShopId',
    }),
    isView() {
      return this.save_type == 'view'
    },
    // 总部库存查看
    viewStock() {
      // 查看 总部查看 店铺商品 非无限库存
      return (
        this.save_type == 'view' &&
        this.$route.query.pageType == 'headquarters' &&
        this.shop_id > 0 &&
        this.temForm.is_unlimited_stock == 'N'
      )
    },
    // 门店查看/创建时的
    isShop() {
      return this.$route.query.pageType == 'shop'
    },
  },

  data() {
    return {
      // 风险确认
      isRiskAuth: false,
      isJump: false,

      // 规格模版设置
      temForm: {
        temKey: '',
        is_unlimited_stock: 'N', //是否无限库存
        is_show_stock: 'Y', //是否显示库存
      },
      temFormRules: {
        temKey: [
          {
            required: true,
            message: '请选择规格模版',
            trigger: 'change',
          },
        ],
      },

      // 规格列表 初始化
      specsList: [],

      // 编辑时参数
      // specsList: [
      //   {
      //     specsName: '颜色',
      //     id: 1,
      //     spec_values: [
      //       { id: 1, name: '黑色', isEdit: false, defaultNmae: '黑色', spec_id: '1:1' },
      //       { id: 2, name: '蓝色', isEdit: false, defaultNmae: '蓝色', spec_id: '1:2' },
      //     ],
      //     specsValue: [],
      //   },
      // ],

      // 保存后的列表
      saveSpecsList: [],
      // saveSpecsList: [
      //   {
      //     specsName: '颜色',
      //     id: 1,
      //     spec_values: [ // 编辑商品详情返回
      //       { id: 1, name: '黑色', isEdit: false, defaultNmae: '黑色', spec_id: '1:1' },
      //       { id: 2, name: '蓝色', isEdit: false, defaultNmae: '蓝色', spec_id: '1:2' },
      //     ],
      //     specsValue: [], // 新增可编辑
      //     specsValueBatch: ['黑色', '蓝色'],
      //   },
      // ],

      tableData: [],
      isTableLoading: false,

      // 筛选规格列表
      selectSpecsList: [],

      defaultIndex: 0,

      isBtnLoading: false,
      isEdit: false,
      isAdd: false,

      goodsType: 2,
    }
  },

  inject: ['goodsDetail'],
  mounted() {
    this.isBtnLoading = false
    this.getGoodsSpecListFn()
    this.initGoodsDetail()
  },
  watch: {
    saveSpecsList: {
      deep: true,
      immediate: true,
      handler(val) {
        if (this.$refs.table) {
          this.$refs.table.doLayout()
        }
      },
    },
  },
  methods: {
    // 初始化商品详情
    initGoodsDetail() {
      let data = this.goodsDetail()
      this.goodsType = data.goods_type
      // 存贮类型是2 不是查看就是编辑 不是2 就是新增
      if (data.spec_type == '2') {
        this.isEdit = this.save_type != 'view'
      } else {
        this.isAdd = this.save_type != 'view'
      }
      if (data.spec_type != '2') {
        return
      }

      this.temForm.is_unlimited_stock = data.is_unlimited_stock
      this.temForm.is_show_stock = data.is_show_stock
      // 初始化规格列表 spec_values为编辑数据
      let spec_list = data.spec_list

      spec_list.map((item, index) => {
        const propName = `specsValue${index}` // 动态生成属性名
        this.specsList.push({
          specsName: '' + item.name,
          id: item.id,
          spec_values: item.spec_values.map((items) => {
            return {
              ...items,
              isEdit: false,
              defaultNmae: '' + items.name,
            }
          }),
          specsValue: [],
        })
      })

      // 用户批量添加功能
      this.saveSpecsList = this.specsList.map((item) => {
        return {
          ...item,
          specsValueBatch: item.spec_values.map((item) => item.name),
        }
      })

      // 初始化表格数据
      let goods_skus = data.goods_skus
      goods_skus = goods_skus.map((item, i) => {
        spec_list.map((items, indexs) => {
          item[`specsValue${indexs}`] = item.spec_format[items.name]
        })
        if (item.is_default == 'Y') {
          this.defaultIndex = i
        }
      })
      console.log(data.goods_skus)
      this.tableData = data.goods_skus
    },

    // 选择默认
    selectDefaultFn(e) {
      this.$set(this.tableData[e], 'is_default', e)
      console.log(this.tableData)
    },
    // 获取规格列表
    getGoodsSpecListFn() {
      getGoodsSpecList({
        page: '1',
        limit: 100,
      }).then((res) => {
        this.selectSpecsList = res.data.list
      })
    },

    // 规格模型添加
    specsModelAdd() {
      if (this.isEdit) {
        return
      }
      this.$refs.reateSpers.open()
    },

    // 下拉规格中选择规格值
    specsChange(e) {
      let spescItem = this.selectSpecsList.find((item) => item.id === e)
      let specsList = []
      spescItem.spec_list.map((item) => {
        console.log(item)
        let specsName = item.split('：')[0]
        let specsValue = item.split('：')[1].split('、')
        specsList.push({
          specsName,
          specsValue,
        })
      })
      this.specsList = specsList
    },

    delSpecs(i) {
      this.specsList.splice(i, 1)
    },
    // 打开批量设置
    openBatchSet() {
      if (this.saveSpecsList.length === 0) {
        this.$message.error('请先添加并保存规格组！')
        return false
      }
      this.$refs.batchSpecs.opetnBatch()
    },
    // 批量设置
    batchSet(e, batchForm) {
      this.tableData.map((item, i) => {
        // 过滤设置值
        let specsKeys = Object.keys(batchForm).filter((item) => batchForm[item].length > 0)
        // 每个规格值对应
        let isSet = specsKeys.every((items) => {
          console.log(batchForm[items].indexOf(item[items]))
          return batchForm[items].indexOf(item[items]) > -1
        })
        console.log(isSet)
        if (isSet) {
          this.$set(this.tableData, i, { ...item, ...e })
        }
      })
    },

    // 添加规格组 -- 动态添加表单验证规则
    addSpecs() {
      this.specsList.push({
        specsName: '',
        specsValue: [],
      })
    },
    // 保存规格组
    async saveSpecs() {
      this.isTableLoading = true
      let isSpecs = await this.specsRefs()
      if (!isSpecs) {
        this.isTableLoading = false
        this.$message.error('请先完善规格组！')
        return false
      }
      let tableData = []
      if (this.isAdd) {
        // 生成笛卡尔积，并初始化表格数据
        tableData = await this.generateCartesianProduct(this.saveSpecsList)
        tableData = tableData.map((item) => {
          return {
            image: '',
            cost_price: undefined,
            sale_price: undefined,
            stock: undefined,
            warning_stock: undefined,
            weight: undefined,
            volume: undefined,
            is_show: 'Y',
            ...item,
          }
        })
      } else if (this.isEdit) {
        // 生成表格后 需要对比新旧规格组，并生成笛卡尔积，最后合并表格数据  --  根绝spec_id
        tableData = await this.editGenerateCartesianProduct(this.saveSpecsList)
        console.log(tableData)
        let newShop = [],
          oldShop = []
        tableData.map((item) => {
          let specItem = this.tableData.filter((items) => items.spec_value_items == item.spec_id)
          if (specItem.length > 0) {
            oldShop.push({ ...specItem[0], ...item })
          } else {
            newShop.push({
              image: '',
              cost_price: undefined,
              sale_price: undefined,
              stock: undefined,
              warning_stock: undefined,
              weight: undefined,
              volume: undefined,
              is_show: 'N',
              ...item,
            })
          }
        })
        tableData = [...oldShop, ...newShop]
      }
      this.tableData = tableData
      this.isTableLoading = false
      return
    },
    // 规格组表单验证并赋值 -- 返回Promise对象，用于判断是否保存成功
    specsRefs() {
      return new Promise((resolve, reject) => {
        if (this.specsList.length === 0 && this.isAdd) {
          resolve(false)
        }
        let _this = this

        // 初始化保存列表，用于提交数据时使用
        this.saveSpecsList = []

        // 遍历规格组表单，并保存数据到saveSpecsList中
        let specsRefsList = []
        this.specsList.map((item, i) => {
          specsRefsList.push(this.$refs['specs' + i][0].save())
        })
        Promise.all(specsRefsList)
          .then((res) => {
            console.log('0----------+', res)
            res.map((item, index) => {
              const propName = `specsValue${index}` // 动态生成属性名

              // 编辑时需要考虑规格组是否发生变化，如果发生变化则需要重新生成笛卡尔积
              // 编辑时使用  spec_values 属于编辑

              if (_this.specsList[index] && item.spec_values) {
                _this.$set(_this.specsList, index, item)
                _this.$set(_this.saveSpecsList, index, {
                  ...item,
                  specsValueBatch: [
                    ...item.specsValue,
                    ...item.spec_values.map((items) => items.name),
                  ],
                })
                // console.log(JSON.stringify(_this.specsList))
                // console.log(JSON.stringify(_this.saveSpecsList))
              } else {
                // 创建时使用
                _this.saveSpecsList.push({})
                _this.$set(_this.specsList[index], 'specsName', item.specsName)
                _this.$set(_this.specsList[index], 'specsValue', item.specsValue)
                _this.$set(_this.specsList[index], 'specsValueBatch', item.specsValue)
                _this.$set(_this.saveSpecsList[index], 'specsName', item.specsName)
                _this.$set(_this.saveSpecsList[index], 'specsValue', item.specsValue)
                _this.$set(_this.saveSpecsList[index], 'specsValueBatch', item.specsValue)
              }
              if (index == this.specsList.length - 1) {
                console.log('1----------+', _this.saveSpecsList)
                resolve(true)
              }
            })
          })
          .catch((err) => {
            console.log('err:', err)
            resolve(false)
          })
      })
    },
    // 笛卡尔积算法
    generateCartesianProduct(specsList) {
      return specsList.reduce(
        (combinations, currentSpec, index) => {
          const propName = `specsValue${index}` // 动态生成属性名
          let specsValue = currentSpec.specsValue
          if (specsList.length === 1) {
            return specsValue.map((value) => ({
              [propName]: value,
            }))
          } else {
            return combinations.flatMap((combo) =>
              specsValue.map((value) => {
                return {
                  ...combo,
                  [propName]: value,
                }
              })
            )
          }
        },
        [{}]
      )
    },

    // 编辑商品时笛卡尔积算法
    editGenerateCartesianProduct(specsList) {
      console.log('specsList:', specsList)
      return specsList.reduce(
        (combinations, currentSpec, index) => {
          const propName = `specsValue${index}` // 动态生成属性名
          let spec_values = currentSpec.spec_values || []
          let specsEditList = [
            ...spec_values,
            ...currentSpec.specsValue.map((item) => {
              return {
                name: item,
                spec_id: item.id || false, // 2025 7 29 修改 需求明哥：编辑时可增加规格 id:false。重置商品
              }
            }),
          ]
          console.log('specsEditList:', specsEditList)
          if (specsList.length === 1) {
            return specsEditList.map((items) => ({
              [propName]: items.name,
              spec_id: items.spec_id,
            }))
          } else {
            return combinations.flatMap((combo) =>
              specsEditList.map((items) => {
                return {
                  ...combo,
                  [propName]: items.name,
                  spec_id: combo.spec_id ? `${combo.spec_id};${items.spec_id}` : items.spec_id,
                }
              })
            )
          }
        },
        [{}]
      )
    },

    delTable(index) {
      this.tableData.splice(index, 1)
      if (this.defaultIndex > this.tableData.length - 1) {
        this.defaultIndex = this.tableData.length - 1
      }
    },

    // 上传
    upLoadImg(e, i) {
      upLoadImg(e.file).then((res) => {
        if (res.code == 200) {
          this.$set(this.tableData[i], 'image', res.data.url)
        }
      })
    },
    // 风险确认
    riskSumbit() {
      this.isRiskAuth = true
      this.next(this.isJump)
    },
    // 下一步
    next(paramsKey) {
      this.isJump = paramsKey
      if (this.isView) {
        getStepNum(3, this.goodsType, 'next', store)
        return
      }
      if (this.tableData.length === 0) {
        return this.$message.error('请先添加规格！')
      }
      let isPriceRisk = false
      let errorTip = false
      this.tableData.map((item) => {
        Object.keys(item).map((key) => {
          if ((key === 'sale_price' || key === 'stock') && !errorTip) {
            if (item[key] === '' || typeof item[key] == 'undefined') {
              errorTip = '请完善所有规格的售价和库存！'
            }
            return
          }
        })
        isPriceRisk = isPriceRisk ? isPriceRisk : Number(item.cost_price) > Number(item.sale_price)
      })

      if (errorTip) {
        this.$message.error(errorTip)
        return false
      }
      console.log('0-0-')
      // 价格风险确认
      if (isPriceRisk && !this.isRiskAuth) {
        this.$refs.riskDialog.open()
        return false
      }

      if (this.isBtnLoading) {
        return
      }
      this.isBtnLoading = true

      // 规格列表
      let spec_list = this.saveSpecsList.map((item) => {
        let sonItem = item.specsValue.map((item) => {
          return {
            id: '',
            name: item,
          }
        })
        let spec_values = item.spec_values ? [...item.spec_values, ...sonItem] : sonItem
        return {
          id: item.id || '0',
          name: item.specsName,
          spec_values,
        }
      })
      console.log(this.saveSpecsList)

      // 规格值列表
      let goods_skus = this.tableData.map((item, i) => {
        let spec_format = {}
        this.saveSpecsList.map((items, index) => {
          spec_format[items.specsName] = item[`specsValue${index}`]
        })
        console.log(spec_format)
        return {
          ...item,
          sku_id: item.sku_id || '0',
          spec_format,
          is_default: this.defaultIndex == i ? 'Y' : 'N',
        }
      })
      let params = {
        goods_id: this.goods_id,
        is_show_stock: this.temForm.is_show_stock,
        is_unlimited_stock: this.temForm.is_unlimited_stock,
        spec_type: 2,
        spec_list,
        goods_skus,
      }

      goodsSaveSecondApi(params)
        .then((res) => {
          if (res.code == 200) {
            this.isRiskAuth = false
            store.dispatch('goodsDetaile/setRefreshShopNum')
            if (paramsKey == 'draft') {
              this.$message.success('保存成功')
              return
            }
            getStepNum(3, this.goodsType, 'next', store)
          } else {
            this.isBtnLoading = false
          }
        })
        .catch(() => {
          this.isBtnLoading = false
        })
      // this.$emit('next', this.tableData, this.saveSpecsList)
    },
    inputFree0(data, code, num) {
      if (data[code] != '') {
        if (isNaN(data[code]) || data[code] < 0) {
          this.$message.error('请输入大于等于0的数字')
          data[code] = ''
        } else {
          data[code] = Number(data[code]).toFixed(num)
        }
      }
    },
    inputFree(data, code, num) {
      if (data[code] != '') {
        if (isNaN(data[code]) || data[code] <= 0) {
          this.$message.error('请输入大于0的数字')
          data[code] = ''
        } else {
          data[code] = Number(data[code]).toFixed(num)
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.w100 {
  width: 100%;
  flex: 1;
}

.red {
  color: red;
}

.form-title {
  font-size: 14px;
  color: #303133;
  font-weight: 600;
  margin: 30px 0 10px;
}

.add-model {
  display: flex;
  width: 100%;
  align-items: flex-start;
  > * {
    width: 50%;
  }
  .add-boder {
    margin-right: 30px;
    border: 1px dashed #dcdfe6;
  }
}
.add-model-active > * {
  width: 100%;
}

// 上传图片
.specs-item {
  width: 80px;
  height: 80px;
  border: 1px dashed #e9e9eb;
  position: relative;
  text-align: center;
  box-sizing: border-box;
  padding-top: 10px;
  .upload_img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
  }
}
.input-number-tem ::v-deep {
  .el-input-number--small {
    width: 110px;
  }
  .el-input-number.is-controls-right .el-input__inner {
    padding-left: 22px;
    padding-right: 36px;
    width: 110px;
    box-sizing: border-box;
    text-align: left !important;
  }
  .input-number::after {
    content: '¥';
    position: absolute;
    top: 1px;
    left: 2px;
    width: 18px;
    line-height: 30px;
    text-align: center;
    background: #fff;
    color: #222222;
    box-sizing: border-box;
    z-index: 1;
    display: block;
  }
}

.specs-tem ::v-deep {
  .el-table th.el-table__cell {
    background: #f5f7fa;
    box-shadow: inset 0px -1px 0px 0px #ebeef5, inset 0px -1px 0px 0px #ebeef5;
  }

  .el-pagination.is-background .btn-prev,
  .el-pagination.is-background .btn-next,
  .el-pagination.is-background .el-pager li {
    background-color: #fff;
    border: 1px solid #dcdfe6;
    color: #606266;
  }

  .el-pagination.is-background .btn-prev:disabled,
  .el-pagination.is-background .btn-next:disabled {
    color: #c0c4cc;
  }

  .el-form-item__label {
    width: 100%;
  }
}

.specs-label ::v-deep {
  .el-form-item__label {
    padding-bottom: 8px;
    line-height: 14px;
    margin-top: 4px;
  }
}

.el-icon-warning {
  color: #ffad32;
  font-size: 18px;
  margin-right: 5px;
}
</style>
