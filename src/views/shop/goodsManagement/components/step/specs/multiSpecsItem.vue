<template>
  <div class="specs-tem">
    <el-form
      label-position="top"
      ref="specs"
      label-width="200px"
      :model="specsForm"
      :rules="specsRules"
    >
      <el-row :gutter="10">
        <el-col :lg="24" :md="24" :sm="24" :xl="24" :xs="24">
          <div class="flex-b specs-title">
            <div>规格组{{index + 1}}</div>
            <el-button type="text" v-if="isAdd" @click="delSpecs">删除</el-button>
          </div>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
          <el-form-item label="规格组名称" class="specs-label" prop="specsName">
            <el-input
              :disabled="isView"
              v-model="specsForm.specsName"
              placeholder="请输入规格组名称，如颜色、尺寸等"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="12" :sm="12" :xl="12" :xs="12">
          <el-form-item label="规格值" class="specs-label" prop="specsValue">
            <el-select
              class="w100"
              v-model="specsForm.specsValue"
              multiple
              filterable
              allow-create
              default-first-option
              placeholder="请输入规格值，如红色、蓝色等"
              :disabled="isView"
            ></el-select>
          </el-form-item>
          <div class="flex edit_spec_flex">
            <div class="edit_specs" v-for="(item,index) in specsForm.spec_values" :key="item.id">
              <div v-if="!item.isEdit" class="option">
                <el-button @click="editSpecs(item, index)" type="text">{{item.name}}</el-button>
                <i v-if="!isView" @click="delSpecsOnce(index)" class="el-icon-circle-close"></i>
              </div>

              <el-input v-else type="text" v-model="item.defaultNmae">
                <div @click="saveSpecs(item, index)" slot="append" class="save_specs">保存</div>
              </el-input>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-form>
    <el-divider></el-divider>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'specs',
  props: {
    info: {
      type: Object,
      default: () => {},
    },
    index: {
      type: Number,
      default: 0,
    },
    // 规格数量
    length: {
      type: Number,
      default: 0,
    },
  },
  inject: ['goodsDetail'],
  computed: {
    ...mapGetters({
      save_type: 'goodsDetaile/getSaveType',
      save_init_type: 'goodsDetaile/getInitSpecsType',
    }),
    isView() {
      return this.save_type === 'view'
    },
    // isAdd() {
    //   return (this.save_type == 'add' || this.save_init_type == '') && this.length != 1
    // },
    // isEdit() {
    //   return this.save_init_type == '2' && this.save_type == 'edit'
    // },
  },
  watch: {
    info: {
      deep: true,
      handler(val) {
        this.specsForm = JSON.parse(JSON.stringify(val))
      },
      immediate: true,
    },
  },
  mounted() {
    let data = this.goodsDetail()
    // 存贮类型是2 不是查看就是编辑 不是2 就是新增
    if (data.spec_type == '2') {
      this.isEdit = this.save_type != 'view'
    } else {
      this.isAdd = this.save_type != 'view'
    }
  },
  data() {
    return {
      specsForm: {
        specsName: '',
        specsValue: '',
        spec_values: [],
      },
      specsList: [],
      // 规格验证
      specsRules: {
        specsName: [
          {
            required: true,
            message: '请输入规格组名称，如颜色、尺寸等',
            trigger: 'blur',
          },
        ],
        specsValue: [
          {
            required: true,
            validator: (rule, value, callback) => {
              console.log(1)
              if (value.length == 0 && !this.specsForm.id) {
                callback(new Error('请输入规格值，如红色、蓝色等'))
              } else {
                callback()
              }
            },
            trigger: 'blur',
          },
        ],
      },
      isAdd: false,
      isEdit: false,
    }
  },
  methods: {
    // 编辑规格值
    editSpecs(item, index) {
      if (this.isView) return
      this.$set(this.specsForm.spec_values, index, {
        ...item,
        isEdit: !item.isEdit,
      })
    },
    // 保存规格值
    saveSpecs(item, index) {
      this.$set(this.specsForm.spec_values, index, {
        ...item,
        isEdit: !item.isEdit,
        name: item.defaultNmae,
      })
    },
    // 添加规格值
    delSpecsOnce(index) {
      console.log(index)
      this.specsForm.spec_values.splice(index, 1)
      console.log(this.specsForm.spec_values)
    },
    // 删除规格组
    delSpecs() {
      this.$emit('delSpecs', this.index)
    },

    // 下一步
    save() {
      return new Promise((resolve, reject) => {
        this.$refs.specs.validate((valid) => {
          if (valid) {
            if (this.specsForm.spec_values && this.specsForm.spec_values.length > 0) {
              resolve({
                ...this.specsForm,
                spec_values:
                  this.specsForm.spec_values.length > 0
                    ? this.specsForm.spec_values.map((item) => {
                        console.log(this.specsForm.id + ':' + item.id)
                        return {
                          ...item,
                          spec_id: this.specsForm.id + ':' + item.id,
                        }
                      })
                    : [],
              })
            } else {
              resolve(this.specsForm)
            }
          } else {
            reject(false)
          }
        })
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.option {
  position: relative;
  .el-icon-circle-close {
    cursor: pointer;
    font-size: 16px;
    color: #f56c6c;
  }
}
.w100 {
  width: 100%;
  flex: 1;
}
::v-deep .el-divider {
  background-color: #f5f7fa;
}
.specs-tem ::v-deep {
  .el-table th.el-table__cell {
    background: #f5f7fa;
    box-shadow: inset 0px -1px 0px 0px #ebeef5, inset 0px -1px 0px 0px #ebeef5;
  }

  .el-pagination.is-background .btn-prev,
  .el-pagination.is-background .btn-next,
  .el-pagination.is-background .el-pager li {
    background-color: #fff;
    border: 1px solid #dcdfe6;
    color: #606266;
  }

  .el-pagination.is-background .btn-prev:disabled,
  .el-pagination.is-background .btn-next:disabled {
    color: #c0c4cc;
  }
}

.specs-label ::v-deep {
  .el-form-item__label {
    padding-bottom: 8px;
    line-height: 14px;
    margin-top: 4px;
  }
}

.specs-title {
  margin-bottom: 10px;
}
.edit_spec_flex {
  display: flex;
  flex-wrap: wrap;
}
.edit_specs {
  margin-right: 16px;
}
.save_specs {
  cursor: pointer;
}
</style>