<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-03-31 09:04:33
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-28 19:20:07
 * @FilePath: /qst-merchant-admin-2.0/src/views/shop/goodsManagement/compontes/goodsDetailsTem.vue
 * @Description: 富文本功能
-->
<template>
  <div class="goods-details">
    <el-form label-position="top">
      <el-row :gutter="10" type="flex">
        <el-col class="goods-left">
          <el-form-item label="商品详情">
            <tinymce
              id="myedit"
              ref="editor"
              :value="content"
              :disabled="isView"
              @input="tinymceInput"
            />
          </el-form-item>
        </el-col>
        <el-col class="goods-right">
          <el-form-item label="预览效果">
            <div class="goods-preview">
              <div v-html="content"></div>
              <div class="zzc"></div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <footer-btn :num="4" :isBtnLoading="isBtnLoading" @next="next" />
  </div>
</template>

<script>
import tinymce from '@/components/tinymce/tinymce'
import { goodsSaveThirdApi } from '@/api/shop/goodsManagement.js'
import store from '@/store'
import { mapGetters } from 'vuex'
import footerBtn from './footerBtn.vue'

export default {
  name: 'goodsDetailsTem',
  components: {
    tinymce,
    footerBtn,
  },
  props: {
    info: {
      type: String,
      default: '',
    },
  },

  inject: ['goodsDetail'],
  watch: {
    goodsDetail: {
      handler() {
        this.initGoodsDetail()
      },
      immediate: true,
      deep: true,
    },
  },

  computed: {
    ...mapGetters({
      goods_id: 'goodsDetaile/getGoodsId',
      save_type: 'goodsDetaile/getSaveType',
    }),
    isView() {
      return this.save_type == 'view'
    },
  },

  data() {
    return {
      content: '',
      disabled: false,
      isFullContent: false,
      isBtnLoading: false,
    }
  },
  methods: {
    initGoodsDetail() {
      let data = this.goodsDetail()
      this.content = data.content || ''
    },
    // 富文本内容变化时触发
    tinymceInput(e) {
      this.content = e
    },

    // 下一步按钮
    next(callback) {
      if (this.isBtnLoading) {
        return
      }
      this.isBtnLoading = true
      goodsSaveThirdApi({
        goods_id: this.goods_id, //商品id
        content: this.content, //商品详情
      })
        .then((res) => {
          this.isBtnLoading = false
          if (res.code == 200) {
            callback()
          }
        })
        .catch(() => {
          this.isBtnLoading = false
        })
    },
  },
}
</script>

<style lang="scss" scoped>
#myedit,
.goods-left {
  width: 500px;
}
.goods-right {
  margin-left: 10px;
}
.goods-preview {
  width: 375px;
  border: 1px solid #ccc;
  min-height: 700px;
  border-radius: 5px;
  padding: 10px;
  box-sizing: border-box;
  word-wrap: break-word;
  line-height: 22px;
  color: #000;
  font-size: 16px;
  position: relative;
  height: 800px;
  overflow-y: auto;
}

.goods-preview ::v-deep {
  p {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell,
      'Open Sans', 'Helvetica Neue', sans-serif;
  }
  img {
    width: 100%;
  }
}
</style>
