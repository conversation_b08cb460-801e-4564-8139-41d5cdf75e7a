<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-04-29 15:59:40
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-24 09:26:06
 * @FilePath: /qst-merchant-admin-2.0/src/views/shop/goodsManagement/components/batchSettings.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <el-dialog :title="title" :visible.sync="isBatchSettings" :close-on-click-modal="false"></el-dialog>
  </div>
</template>

<script>
export default {
  name: 'dialog',
  props: {},
  data() {
    return {
      title: '批量设置',
      isBatchSettings: false,
    }
  },
  methods: {
    openDialog() {
      this.isBatchSettings = true
    },
  },
}
</script>