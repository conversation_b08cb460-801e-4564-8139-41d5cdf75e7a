<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-04-07 14:23:49
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-24 09:26:20
 * @FilePath: /qst-merchant-admin-2.0/src/views/shop/goodsManagement/components/importDialog.vue
 * @Description: 导入弹窗
-->
<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="isImportDialog"
      destroy-on-close
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form" :rules="formRules">
        <el-form-item label="商品链接" prop="shopLink">
          <el-input v-model="form.shopLink" placeholder="请输入内容" type="textarea"></el-input>
        </el-form-item>
      </el-form>
      <base-dialog-footer @confirm="confirm" @cancel="isImportDialog=false"></base-dialog-footer>
    </el-dialog>
  </div>
</template>

<script>
import BaseDialogFooter from '../../../../components/Public/BaseDialogFooter.vue'
export default {
  components: { BaseDialogFooter },
  name: 'importDialog',
  data() {
    return {
      isImportDialog: false,
      title: '从其他平台导入商品',
      form: {
        shopLink: '',
      },
      formRules: {
        shopLink: [
          {
            required: true,
            message: '请输入商品链接',
            trigger: 'blur',
          },
        ],
      },
    }
  },
  methods: {
    // 打开导入弹窗
    openImportDialog() {
      this.isImportDialog = true
    },
    // 确认导入
    confirm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          console.log(this.form)
        } else {
          return false
        }
      })
    },
  },
}
</script>