<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-04-29 16:31:36
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-28 14:37:04
 * @FilePath: /qst-merchant-admin-2.0/src/views/shop/goodsManagement/components/editCode.vue
 * @Description: 商品code编码编辑
-->
<template>
  <div>
    <el-dialog
      append-to-body
      :visible.sync="isCodeDialog"
      width="300"
      @close="isCodeDialog = false"
      :title="'编辑商品' + editTitle"
      :close-on-click-modal="false"
    >
      <el-form ref="codeForm" :model="codeForm" :rules="rules">
        <el-form-item :label="'商品' + editTitle" prop="barcode">
          <el-input v-model="codeForm.barcode" :placeholder="'请输入商品' + editTitle"></el-input>
        </el-form-item>
      </el-form>
      <base-dialog-footer @cancel="isCodeDialog = false" @confirm="sumbitFn"></base-dialog-footer>
    </el-dialog>
  </div>
</template>

<script>
import {
  updateDeleteGoodsCodeApi,
  updateDeleteGoodsSkuCodeApi,
} from '@/api/shop/goodsManagement.js'
export default {
  name: 'CodeDialog',
  props: {},
  data() {
    return {
      isCodeDialog: false,
      sku: {},
      spu: {},
      type: '', // sku spu
      codeForm: {
        barcode: '',
      },
      rules: {
        barcode: [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (value != '') {
                callback()
              } else {
                return callback(new Error('请输入商品' + this.editTitle))
              }
            },
          },
        ],
      },
      editTitle: '',
    }
  },
  methods: {
    open(type, spu, sku) {
      this.type = type
      this.spu = spu
      this.sku = sku
      if (type === 'spu') {
        console.log(spu)
        this.editTitle = '编码'
        this.codeForm.barcode = spu.row.goods_code
      } else {
        this.editTitle = '条码'
        this.codeForm.barcode = sku.barcode
      }

      this.isCodeDialog = true
      this.$nextTick(() => {
        this.$refs.codeForm.clearValidate()
      })
    },
    sumbitFn() {
      this.$refs.codeForm.validate((valid) => {
        if (valid) {
          if (this.type === 'spu') {
            updateDeleteGoodsCodeApi({
              shop_id: this.spu.row.shop_id, //所属门店id
              goods_id: this.spu.row.good_id, //商品id
              goods_code: this.codeForm.barcode, //sku条形码
            }).then((res) => {
              if (res.code == 200) {
                this.$message.success('修改成功!')
                this.$emit('update')
              }
            })
          } else if (this.type === 'sku') {
            updateDeleteGoodsSkuCodeApi({
              shop_id: this.spu.row.shop_id, //所属门店id
              goods_id: this.spu.row.good_id, //商品id
              sku_id: this.sku.sku_id, //商品规格ID
              barcode: this.codeForm.barcode, //sku条形码
            }).then((res) => {
              if (res.code == 200) {
                this.$message.success('修改成功!')
                this.$emit('update')
              }
            })
          }
          this.isCodeDialog = false
        }
      })
    },
  },
}
</script>