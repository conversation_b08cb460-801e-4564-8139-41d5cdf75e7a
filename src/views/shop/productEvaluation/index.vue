<!--
 * @Author: liqian <EMAIL>
 * @Email: <EMAIL>
 * @Date: 2025-07-14 17:14:43
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-08-04 11:43:20
 * @FilePath: \qst-merchant-admin-2.0\src\views\shop\productEvaluation\index.vue
 * @Description: 
-->
<template>
  <div>
    <el-card class="store-model">
      <base-form :tableForm="tableForm" :formArray="formArray" @searchForm="searchForm"></base-form>
      <div class="operate">
        <el-button
          :disabled="!getSelectTableLsit.length"
          :class="{ primary: getSelectTableLsit.length }"
          @click="batchApproval($event, false)"
        >
          批量通过
        </el-button>
        <el-button
          :disabled="!getSelectTableLsit.length"
          :class="{ warnsty: getSelectTableLsit.length }"
          @click="batchRejection($event, false)"
        >
          批量驳回
        </el-button>
        <el-button :disabled="!getSelectTableLsit.length" @click="handelBatchReply($event, false)">
          批量回复
        </el-button>
      </div>
      <div class="tipsty" style="margin-top: 20px" v-if="is_open_comment == 'N'">
        <BaseContentTip
          tipColor="#FFFBE6"
          tiptitle=""
          tip="暂未开启评价，开启后下单客户才可进行评价！"
        >
          <div class="jumpTip" @click="jumpSetOrder"><span>点击跳转开启</span></div>
        </BaseContentTip>
      </div>

      <base-table
        v-else
        :tableColumn="tableColumn"
        :tableRequest="getEvaluatedApi"
        :tableForm="tableForm"
        ref="tableRef"
        :isSelect="true"
        @selectiKey="selectTableData"
      >
        <template #expand="{ scope }">
          <el-checkbox :disabled="scope.row.approved_status == 'approved'"></el-checkbox>
        </template>
        <template #is_anonymous="{ scope }">
          <el-tag v-if="scope.row.is_anonymous == 'Y'" type="success">是</el-tag>
          <el-tag v-if="scope.row.is_anonymous == 'N'" type="warning">否</el-tag>
        </template>
        <template #rating="{ scope }">
          <span v-if="['5.0', '4.5', '4.0'].includes(scope.row.rating)" style="color: #0071fe">
            {{ scope.row.rating }}
          </span>
          <span v-if="['3.5', '3.0'].includes(scope.row.rating)" style="color: #f6a70e">
            {{ scope.row.rating }}
          </span>
          <span
            v-if="['2.5', '2.0', '1.5', '1.0'].includes(scope.row.rating)"
            style="color: #ff2727"
          >
            {{ scope.row.rating }}
          </span>
        </template>
        <template #operate="{ scope }">
          <div class="public-operate-btn">
            <el-button
              size="mini"
              type="text"
              v-if="['pending'].includes(scope.row.approved_status)"
              @click="batchApproval(scope.row, true)"
            >
              通过
            </el-button>
            <el-button
              style="color: #ff2727"
              size="mini"
              type="text"
              v-if="['pending', 'approved'].includes(scope.row.approved_status)"
              @click="batchRejection(scope.row, true)"
            >
              驳回
            </el-button>
            <el-button
              size="mini"
              type="text"
              v-if="scope.$index != 0 && ['approved'].includes(scope.row.approved_status)"
              @click="handelTopup(scope.row)"
            >
              置顶
            </el-button>
            <el-button
              size="mini"
              type="text"
              v-if="scope.$index == 0 && ['approved'].includes(scope.row.approved_status)"
              @click="cancelTopup(scope.row)"
            >
              取消置顶
            </el-button>
            <el-button
              size="mini"
              type="text"
              v-if="['pending', 'approved'].includes(scope.row.approved_status)"
              @click="handelBatchReply(scope.row, true)"
            >
              回复
            </el-button>
            <el-button
              size="mini"
              type="text"
              v-if="['pending', 'rejected'].includes(scope.row.approved_status)"
              @click="handelDelete(scope.row)"
            >
              删除
            </el-button>
          </div>
        </template>
      </base-table>
    </el-card>
    <!-- 订单备注 -->
    <el-dialog
      append-to-body
      :visible.sync="showReplyDialog"
      :close-on-click-modal="false"
      title="回复评论"
      width="500px"
    >
      <el-form
        ref="basicInfo"
        :rules="basicInfoRule"
        :model="form"
        label-width="80px"
        label-position="top"
      >
        <el-form-item label="回复内容" prop="name">
          <el-input
            type="textarea"
            show-word-limit
            maxlength="100"
            v-model="form.name"
            placeholder="请输入回复内容"
          ></el-input>
        </el-form-item>
      </el-form>
      <base-dialog-footer @cancel="showReplyDialog = false" @confirm="confirm"></base-dialog-footer>
    </el-dialog>
  </div>
</template>

<script>
  import {
    getEvaluatedApi,
    goodsListBySelect,
    delEvaluatedApi,
    setToggleTop,
    setReplyApi,
    setApproveApi,
    setdisapproveApi,
  } from '@/api/shop/productEvaluation'
  import { getBaseSettingApi } from '@/api/shop/basicSettings.js'

  export default {
    name: 'productEvaluation',
    components: {},
    data() {
      return {
        form: {
          reply_content: '',
        },
        basicInfoRule: {
          name: [{ required: true, message: '请输入回复内容', trigger: 'blur' }],
        },
        flag: false,
        formArray: [
          {
            label: '评价时间',
            type: 'time',
            timeType: 'daterange',
            valueFormat: 'yyyy-MM-dd',
            key: 'time',
            timeKey: ['start_time', 'end_time'],
          },
          {
            label: '回复状态',
            type: 'select',
            key: 'reply_status',
            placeholder: '请选择回复状态',
            options: [
              {
                label: '未回复',
                value: 'pending',
              },
              {
                label: '已回复',
                value: 'replied',
              },
            ],
          },
          {
            label: '审核状态',
            type: 'select',
            key: 'approved_status',
            placeholder: '请选择审核状态',
            options: [
              {
                label: '待审核',
                value: 'pending',
              },
              {
                label: '已通过',
                value: 'approved',
              },
              {
                label: '已驳回',
                value: 'rejected',
              },
            ],
          },
          {
            label: '商品名称',
            type: 'select',
            key: 'goods_id',
            placeholder: '请选择商品名称',
            options: [],
          },
          {
            label: '商品规格',
            type: 'input',
            key: 'sku_id',
            placeholder: '请输入商品规格',
          },
          {
            label: '是否匿名',
            type: 'select',
            key: 'is_anonymous',
            placeholder: '请选择是否匿名',
            options: [
              {
                label: '否',
                value: 'N',
              },
              {
                label: '是',
                value: 'Y',
              },
            ],
          },
          {
            label: '评分',
            type: 'select',
            key: 'rating',
            placeholder: '请选择评分',
            options: [
              {
                label: '1',
                value: '1',
              },
              {
                label: '1.5',
                value: '1.5',
              },
              {
                label: '2',
                value: '2',
              },
              {
                label: '2.5',
                value: '2.5',
              },
              {
                label: '3',
                value: '3',
              },
              {
                label: '3.5',
                value: '3.5',
              },
              {
                label: '4',
                value: '4',
              },
              {
                label: '4.5',
                value: '4.5',
              },
              {
                label: '5',
                value: '5',
              },
            ],
          },
        ],

        tableColumn: [
          {
            prop: 'expand',
            width: '55',
          },
          {
            label: '商品信息',
            prop: 'name',
            width: '150px',
          },
          {
            label: '规格',
            prop: 'spec_value_items',
          },
          {
            label: '用户账号',
            prop: 'username',
            type: 'customize',
          },
          {
            label: '是否匿名',
            prop: 'is_anonymous',
          },
          {
            label: '评分',
            prop: 'rating',
          },
          {
            label: '评价内容',
            prop: 'content',
          },
          {
            label: '评价时间',
            prop: 'created_at',
          },
          {
            label: '审核状态',
            prop: 'approved_status',
          },
          {
            label: '回复内容',
            prop: 'reply_content',
          },
          {
            label: '回复人',
            prop: 'reply_id',
          },
          {
            label: '操作',
            prop: 'operate',
            type: 'customize',
            width: '150px',
          },
        ],
        getEvaluatedApi,
        delEvaluatedApi,
        setToggleTop,
        tableForm: {},
        rowIndexList: {},
        showReplyDialog: false,
        getSelectTableLsit: [],
        is_open_comment: 'N', //开启评价功能
      }
    },
    created() {
      this.getGoodsList()
      this.getSetDetail()
    },
    methods: {
      //商品名称下拉
      getGoodsList() {
        goodsListBySelect().then((res) => {
          console.log(res)
          if (res.code == 200) {
            this.formArray[3].options = [{}, ...res.data.list]
          }
        })
      },
      getSetDetail() {
        getBaseSettingApi().then((res) => {
          if (res.code == 200) {
            this.is_open_comment = res.data.is_open_comment
          }
        })
      },
      // 表单搜索事件
      searchForm(form) {
        this.tableForm = Object.assign({}, this.tableForm, form)
      },
      // 删除
      handelDelete(row) {
        let jsonList = {
          content: '确定删除评价么？删除操作不可恢复，请谨慎操作',
          api: delEvaluatedApi,
          tip: '删除',
        }
        this.confirmContent(row, jsonList)
      },

      //批量通过,通过
      batchApproval(row, flag) {
        this.rowIndexList = row
        this.flag = flag
        if (!this.flag) {
          if (!this.getSelectTableLsit.length) {
            this.$message.error('请选择数据')
            return false
          }
        }
        let paramsData = []
        this.getSelectTableLsit.forEach((item) => {
          paramsData.push(item.sub_order_no)
        })
        let params = {
          comment_id: this.flag ? [this.rowIndexList.sub_order_no] : paramsData,
          ...this.form,
        }
        this.$confirm(`审核通过后，评价将在商品评价中展示，确定通过审核么？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            setApproveApi(params).then((res) => {
              if (res.code == 200) {
                this.$message.success(`通过成功`)
                this.$refs.tableRef.tableRequestFn()
              }
            })
          })
          .catch((action) => {})
      },
      //批量驳回
      batchRejection(row, flag) {
        this.rowIndexList = row
        this.flag = flag
        if (!this.flag) {
          if (!this.getSelectTableLsit.length) {
            this.$message.error('请选择数据')
            return false
          }
        }
        let paramsData = []
        this.getSelectTableLsit.forEach((item) => {
          paramsData.push(item.sub_order_no)
        })
        let params = {
          comment_id: this.flag ? [this.rowIndexList.sub_order_no] : paramsData,
          ...this.form,
        }
        this.$confirm(
          `确定驳回评价么？驳回后评价将不在商品评价中展示，该动作不会同步给买家，可放心操作！`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
          .then(() => {
            setdisapproveApi(params).then((res) => {
              if (res.code == 200) {
                this.$message.success(`驳回成功`)
                this.$refs.tableRef.tableRequestFn()
              }
            })
          })
          .catch((action) => {})
      },

      //回复，批量回复
      handelBatchReply(row, flag) {
        this.rowIndexList = row
        this.flag = flag
        if (!this.flag) {
          this.remarksTitle = '批量回复'
          if (!this.getSelectTableLsit.length) {
            this.$message.error('请选择数据')
            return false
          }
        }
        this.showReplyDialog = true
        this.$nextTick(() => {
          this.$refs['basicInfo'].resetFields()
        })
      },
      // 置顶
      handelTopup(row) {
        let jsonList = {
          content: '确定置顶评价么？置顶后，该评价将展示在商品评价列表的第一条',
          api: setToggleTop,
          tip: '置顶',
        }
        this.confirmContent(row, jsonList)
      },
      // 取消置顶
      cancelTopup(row) {
        let jsonList = {
          content:
            '确定取消么？取消后，此评价将恢复按发布时间倒序排列，不再固定在商品评价列表的第一条',
          api: setToggleTop,
          tip: '取消置顶',
        }
        this.confirmContent(row, jsonList)
      },
      // 通过
      handelApproval(row) {
        let jsonList = {
          content: '审核通过后，评价将在商品评价中展示，确定通过审核么？',
          api: setApproveApi,
          tip: '通过',
        }
        this.confirmContent(row, jsonList)
      },
      // 驳回
      handelRejection(row) {
        let jsonList = {
          content:
            '确定驳回评价么？驳回后评价将不在商品评价中展示，该动作不会同步给买家，可放心操作！',
          api: setdisapproveApi,
          tip: '驳回',
        }
        this.confirmContent(row, jsonList)
      },
      //勾选表格数据
      selectTableData(data) {
        this.getSelectTableLsit = data
      },
      //添加回复
      confirm() {
        this.$refs.basicInfo.validate((valid) => {
          if (valid) {
            let paramsData = []
            this.getSelectTableLsit.forEach((item) => {
              paramsData.push(item.sub_order_no)
            })
            let params = {
              comment_id: this.flag ? [this.rowIndexList.sub_order_no] : paramsData,
              ...this.form,
            }
            setReplyApi(params).then((res) => {
              if (res.code == 200) {
                this.$message.success('回复成功')
                this.cancel()
                this.refreshTable()
              }
            })
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
      //提示弹框
      confirmContent(row, config) {
        this.$confirm(`${config.content}`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            config.api({ template_id: row.id }).then((res) => {
              if (res.code == 200) {
                this.$message.success(`${config.tip}`)
                this.$refs.tableRef.tableRequestFn()
              }
            })
          })
          .catch((action) => {})
      },
      //点击跳转开启
      jumpSetOrder() {
        this.$router.push({
          path: '/basicSettings/index',
          query: {},
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .operate {
    margin-top: 20px;
    .el-button {
      .primary {
        background: #0071fe;
        font-family: PingFangSC, PingFang SC;
        color: #ffffff;
      }
      .warnsty {
        background: #fff5f5;
        border: 1px solid #ff2727;
        font-family: PingFangSC, PingFang SC;
        color: #ff2727;
      }
    }
  }
  ::v-deep {
    .tipsty {
      .n_tips {
        border: 1px solid #ffeabf;
        align-items: center;
        .tip_name1 {
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          font-size: 14px;
          color: #222222;
          line-height: 14px;
        }
        .jumpTip {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #0071fe;
          line-height: 14px;
          text-decoration-line: underline;
          cursor: pointer;
        }
      }
    }
  }
</style>
