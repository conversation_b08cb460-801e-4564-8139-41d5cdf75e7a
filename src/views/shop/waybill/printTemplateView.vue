<template>
  <div>
    <el-dialog
      destroy-on-close
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="dialogVisible"
      :width=" printWidth + 15  + 'mm'"
      title="预览"
    >
      <div>
        <PrintView
          v-if="dialogVisible"
          :printType="printType"
          :width="printWidth + 5 + 'mm'"
          :height="printHeight+ 5 +  'mm'"
          :priveView="content"
        ></PrintView>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import PrintView from '@/components/PrintView/index.vue'
export default {
  name: 'viewPrint',
  components: {
    PrintView,
  },
  data() {
    return {
      printType: 0,
      printWidth: 0,
      printHeight: 0,
      content: '',
      dialogVisible: false,
    }
  },
  methods: {
    open({ content, printWidth, printHeight, printType }) {
      this.printType = printType
      this.content = content
      this.printWidth = printWidth
      this.printHeight = printHeight
      this.dialogVisible = true
    },
  },
}
</script>