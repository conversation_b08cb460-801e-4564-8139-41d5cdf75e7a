// 新增承运人组件
<template>
  <div>
    <el-dialog
      append-to-body
      :visible.sync="isCarrier"
      :title=" (type == 'add' ? '新增' : '编辑') + '承运商'"
      :close-on-click-modal="false"
    >
      <el-form :model="carrierFrom" label-position="top" :rules="rules" ref="carrierFrom">
        <el-form-item label="承运商名称" prop="name">
          <el-input v-model="carrierFrom.name" placeholder="请输入承运商名称"></el-input>
        </el-form-item>
        <el-form-item label="所属物流公司" prop="express_id">
          <el-select
            class="w100"
            v-model="carrierFrom.express_id"
            placeholder="请选择所属物流公司"
            @change="expressChange"
          >
            <el-option
              v-for="item in companyList"
              :key="item.express_id"
              :label="item.express_name"
              :value="item.express_id"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          v-for="item in expressCompany"
          :key="item.field_no"
          :label="item.field_name"
          :prop="item.field_no"
        >
          <el-input
            v-model="carrierFrom[item.field_no]"
            :placeholder="item.field_desc"
            v-if="item.input_type=='input'"
          ></el-input>
          <el-select
            class="w100"
            v-model="carrierFrom[item.field_no]"
            :placeholder="item.field_desc"
            v-if="item.input_type=='select'"
          >
            <el-option
              v-for="items in item.options"
              :key="items.value"
              :label="items.label"
              :value="items.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="打印模板" prop="template_id">
          <div class="flex">
            <el-select
              :disabled="!carrierFrom.express_id"
              class="w100"
              v-model="carrierFrom.template_id"
              placeholder="请选择打印模板"
            >
              <el-option
                v-for="items in templateList"
                :key="items.value"
                :label="items.label"
                :value="items.value"
              ></el-option>
            </el-select>
            <el-button
              :disabled="!carrierFrom.express_id"
              style="margin-left: 20px;"
              @click="addPrint"
              type="primary"
              size="mini"
            >添加打印模板</el-button>
          </div>
        </el-form-item>
        <el-form-item label="默认发货地址" prop="address_id">
          <el-select class="w100" v-model="carrierFrom.address_id" placeholder="请选择默认发货地址">
            <el-option
              v-for="item in mchaddressList"
              :key="item.id"
              :label="item.address"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>

        <base-dialog-footer @confirm="save" @cancel="isCarrier = false"></base-dialog-footer>
      </el-form>
    </el-dialog>

    <add-print-template ref="addTemplate" type="carrierAdd" @success="getTemSelect"></add-print-template>
  </div>
</template>

<script>
import { expressCompanyListApi, expressCompanyOptionApi } from '@/api/common.js'
import { shipmentAddressList } from '@/api/shop/order.js'
import { merchantTemplateSelectApi, createCarrier, carrierDetailApi } from '@/api/shop/waybill.js'
import addPrintTemplate from '../../printTemplate/components/addPrintTemplate.vue'
export default {
  name: 'addCarrier',
  props: {
    type: String,
    default: 'add',
  },
  components: {
    addPrintTemplate,
  },
  data() {
    return {
      isCarrier: false,
      carrierFrom: {
        name: '',
        express_id: '',
        template_id: '',
        address_id: '',
      },
      rules: {
        name: [{ required: true, message: '请输入承运商名称', trigger: 'blur' }],
        express_id: [{ required: true, message: '请选择所属物流公司', trigger: 'change' }],
        template_id: [{ required: true, message: '请选择打印模板', trigger: 'change' }],
        address_id: [{ required: true, message: '请选择默认发货地址', trigger: 'change' }],
      },
      companyList: [],
      expressCompany: [],
      // 打印模版列表数据
      templateList: [],
      // 获取发货地址
      mchaddressList: [],
    }
  },
  created() {
    this.getSystem()
    this.getmchaddressList()
  },
  methods: {
    open(id) {
      if (this.type == 'add') {
        this.expressCompany = []

        this.carrierFrom = {
          name: '',
          express_id: '',
          template_id: '',
          address_id: '',
        }
        this.$nextTick(() => {
          this.$refs['carrierFrom'].clearValidate()
        })
      } else {
        this.id = id
        carrierDetailApi({
          carrier_id: id,
        }).then((res) => {
          if (res.code == 200) {
            let data = res.data
            this.carrierFrom = {
              name: data.name,
              express_id: data.express_id,
              template_id: data.template_id,
              address_id: data.address_id,
            }
            let field_config = {}
            data.field_config.map((item) => {
              field_config[item.field_id] = item.field_val
            })
            this.expressChange('init', field_config)
          }
        })
      }

      this.isCarrier = true
    },

    // 添加打印模板
    addPrint() {
      if (this.carrierFrom.express_id) {
        this.$refs.addTemplate.open({
          type: 1,
          express_id: this.carrierFrom.express_id,
        })
      } else {
        this.$message.warning('请先选择所属物流公司')
        return false
      }
    },

    // 获取物流公司信息
    getSystem() {
      expressCompanyListApi({
        is_online: 'Y',
      }).then((res) => {
        if (res.code == 200) {
          this.companyList = res.data
        }
      })
    },

    //发货地址列表
    getmchaddressList() {
      shipmentAddressList().then((res) => {
        if (res.code == 200) {
          this.mchaddressList = res.data
        }
      })
    },
    // 删除物流公司下拉列表选择完成的数据展示
    delExpressCompany() {
      if (this.expressCompany.length == 0) return
      this.expressCompany.map((item) => {
        this.$delete(this.carrierFrom, item.field_no)
        this.$delete(this.rules, item.field_no)
      })
    },

    // 物流公司选择 isOnce, field_config 是否初次加载字段配置数据 field_config 初次加载的字段配置数据
    expressChange(isOnce, field_config) {
      expressCompanyOptionApi({
        express_id: this.carrierFrom.express_id || '',
      }).then((res) => {
        if (res.code == 200) {
          this.delExpressCompany()
          res.data.map((item) => {
            this.$set(this.carrierFrom, item.field_no, isOnce == 'init' ? field_config[item.express_option_id] : '')
            this.$set(this.rules, item.field_no, [{ required: item.is_required == 'Y', message: item.field_desc, trigger: 'blur' }])
          })
          this.$nextTick(() => {
            this.$refs.carrierFrom.clearValidate()
          })
          this.expressCompany = res.data
        }
      })
      this.getTemSelect(isOnce)
    },

    // 获取打印模板列表数据
    getTemSelect(isOnce) {
      merchantTemplateSelectApi({
        type: 1,
        express_id: this.carrierFrom.express_id || '',
      }).then((res) => {
        if (res.code == 200) {
          this.templateList = res.data || []
          if (isOnce == 'init') return
          this.$set(this.carrierFrom, 'template_id', '')
          this.$refs.carrierFrom.clearValidate('template_id')
        }
      })
    },

    save() {
      this.$refs.carrierFrom.validate((valid) => {
        if (valid) {
          this.createCarrierFn()
        } else {
          return false
        }
      })
    },

    createCarrierFn() {
      let params = this.carrierFrom
      if (this.id) {
        params.carrier_id = this.id
      }
      if (this.expressCompany.length > 0) {
        let field_config = []
        this.expressCompany.map((item) => {
          field_config.push({
            field_id: item.express_option_id,
            field_val: this.carrierFrom[item.field_no],
          })
        })
        params.field_config = field_config
      }
      createCarrier(params).then((res) => {
        if (res.code == 200) {
          this.$message.success(this.type == 'add' ? '添加成功' : '修改成功')
          this.$emit('success', this.carrierFrom)
          this.isCarrier = false
        } else {
          return false
        }
      })
    },
  },
}
</script>