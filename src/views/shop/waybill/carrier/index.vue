<template>
  <div class="carrier">
    <el-card>
      <el-button @click="openCarrier" type="primary" icon="el-icon-plus">新增承运商</el-button>

      <div class="carrier-main" v-for="(item,index) in carrierList" :key="index">
        <div class="flex carrier-header">
          <img class="carrier-header-image" :src="imgOssPath + '20250619_sf_cyicon.png'" alt />
          <div>{{item.express_name}}</div>
        </div>
        <div class="carrier-m">
          <el-row :gutter="24">
            <div v-for="it in item.list" :key="it.carrier_id">
              <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
                <div class="carrier-item">
                  <div class="flex-b i-title">
                    <div>{{it.name}}</div>
                    <div class="flex">
                      <i
                        @click="editCarrier(it.carrier_id)"
                        class="carrier-item-icon el-icon-edit-outline"
                      ></i>
                      <i @click="del(it.carrier_id)" class="carrier-item-icon el-icon-delete"></i>
                    </div>
                  </div>
                  <div class="i-main">
                    <div
                      v-for="i in it.field_config"
                      :key="i.field_id"
                    >{{i.field_name}}：{{i.field_val}}</div>
                    <div>
                      打印模板：{{it.template_name}}
                      <span
                        style="color: #0071FE; cursor: pointer;"
                        @click="openPriveDiage(it)"
                      >预览</span>
                    </div>
                    <div class="flex-b">
                      <div class="line2">默认发货地址：{{it.address}}</div>
                      <div class="switch-zzc" @click="setForbiddenShow(it)">
                        <div class="zzc"></div>
                        <el-switch v-model="it.is_forbidden" active-value="N" inactive-value="Y"></el-switch>
                      </div>
                    </div>
                  </div>
                </div>
              </el-col>
            </div>
          </el-row>
        </div>
      </div>
    </el-card>
    <add-carrier @success="getCarrierList" :type="carrierType" ref="addCarrier"></add-carrier>
    <print-template-view ref="printTemplateView"></print-template-view>
  </div>
</template>

<script>
import addCarrier from './components/addCarrier.vue'
import {
  carrierListApi,
  deleteCarrierApi,
  getTemplateDetail,
  isForbidden,
} from '@/api/shop/waybill.js'
import printTemplateView from '../printTemplateView.vue'

export default {
  name: 'carrier',
  data() {
    return {
      carrierType: 'add',
      carrierList: [],
      previewVisible: false,
      previewImage: '',
    }
  },
  components: {
    addCarrier,
    printTemplateView,
  },
  created() {
    this.getCarrierList()
  },
  methods: {
    getCarrierList() {
      carrierListApi().then((res) => {
        if (res.code == 200) {
          this.carrierList = res.data
        }
      })
    },
    openCarrier() {
      this.carrierType = 'add'
      this.$nextTick(() => {
        this.$refs.addCarrier.open()
      })
    },

    editCarrier(id) {
      this.carrierType = 'edit'
      this.$nextTick(() => {
        this.$refs.addCarrier.open(id)
      })
    },

    // 预览
    openPriveDiage(it) {
      console.log(it.template_id)
      if (it.template_id) {
        getTemplateDetail({
          print_template_id: it.template_id,
        }).then((res) => {
          if (res.code == 200) {
            let conetnt = JSON.parse(res.data.content)
            if (conetnt.panels.length > 0) {
              this.$refs.printTemplateView.open({
                printType: 1,
                content: conetnt,
                printWidth: conetnt.panels[0].width,
                printHeight: conetnt.panels[0].height,
              })
            }
          }
        })
      }
    },

    // 删除承运商
    del(id) {
      this.$confirm('是否删除该承运商？删除后将无法恢复。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          deleteCarrierApi({ carrier_id: id }).then((res) => {
            if (res.code == 200) {
              this.$message({
                type: 'success',
                message: '删除成功!',
              })
              this.getCarrierList()
            }
          })
        })
        .catch(() => {})
    },

    setForbidden() {
      this.$confirm('是否删除该承运商？删除后将无法恢复。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          deleteCarrierApi({ carrier_id: id }).then((res) => {
            if (res.code == 200) {
              this.$message({
                type: 'success',
                message: '删除成功!',
              })
              this.getCarrierList()
            }
          })
        })
        .catch(() => {})
    },

    // 承运商设置是否禁用
    setForbiddenShow(item) {
      this.$confirm('是否' + (item.is_forbidden == 'N' ? '禁用' : '开启') + '该承运商？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          isForbidden({
            carrier_id: item.carrier_id,
            is_forbidden: item.is_forbidden == 'Y' ? 'N' : 'Y',
          }).then((res) => {
            if (res.code == 200) {
              this.$message({
                type: 'success',
                message: '设置成功!',
              })
              this.getCarrierList()
            }
          })
        })
        .catch(() => {})
    },
  },
}
</script>

<style lang="scss" scoped>
.carrier-main {
  margin-top: 36px;
  .carrier-item {
    border-radius: 8px;
    border: 1px solid #e9e9eb;
    margin-top: 20px;
    padding: 0 20px;
    box-sizing: border-box;
    .carrier-item-icon {
      font-size: 16px;
      cursor: pointer;
      &:last-child {
        margin-left: 24px;
      }
    }
  }
}
.carrier-header {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 16px;
  font-weight: 600;
  &-image {
    width: 18px;
    height: 18px;
    margin-right: 8px;
  }
}
.i-title {
  height: 40px;
  font-size: 14px;
  line-height: 14px;
  border-bottom: 1px solid #f4f4f5;
  font-weight: 600;
}
.i-main {
  padding: 18px 0 24px;
  font-weight: 400;
  font-size: 14px;
  color: #6e6e7a;
  line-height: 14px;
  > div + div {
    margin-top: 12px;
  }
  .flex-b {
    align-items: flex-start;
  }
  .line2 {
    height: 28px;
  }
}
// 预览
.preview-image {
  object-fit: cover;
  display: block;
  margin: 0 auto;
}

.switch-zzc {
  position: relative;
  .zzc {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    cursor: pointer;
  }
}
</style>