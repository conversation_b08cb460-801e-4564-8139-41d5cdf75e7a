<template>
  <div class="print-dessign">
    <el-card v-loading="isSaveLoading">
      <el-button size="small" icon="el-icon-back" @click="backFn">返回</el-button>
      <el-button size="small" type="primary" @click="saveTemplate" icon="el-icon-s-management">保存</el-button>
      <div class="scroll-width">
        <div class="table-flex flex">
          <el-card class="card-left">
            <template #header>
              <div>字段列表</div>
            </template>
            <div class="table-scroll">
              <div v-for="item in selectList" :key="item.alias">
                <div class="card-title">{{item.name}}</div>
                <div class="checkbox-item" v-for="items in item.children" :key="items.alias">
                  <el-checkbox v-model="form[items.alias]">{{items.name}}</el-checkbox>
                </div>
              </div>
              <el-input
                type="textarea"
                v-model="remake"
                :maxlength="50"
                placeholder="请输入备注信息（50字以内）"
              ></el-input>
            </div>
          </el-card>
          <el-card class="card-right">
            <template #header>
              <div>预览</div>
            </template>
            <div class="print-scroll">
              <PrintView
                :isOneListener="false"
                :printType="3"
                :width="printWidth + 'mm'"
                :height="printHeight + 'mm'"
                :priveView="content"
              ></PrintView>
            </div>
          </el-card>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getTemplateFields, getTemplateDetail, configureTemplateFields } from '@/api/shop/waybill'
// import { json_data } from './components/print.js'
import PrintView from '@/components/PrintView/index.vue'

export default {
  name: 'printTemplateDesign',
  components: {
    PrintView,
  },
  data() {
    return {
      id: '',

      isSaveLoading: false,
      selectList: [],
      hiprintTemplate: null,
      printElements: [],
      form: {},
      isShow: false,
      remake: '',

      content: '',

      printWidth: 241.3,
      printHeight: 139.7,
      ptWidth: 0,
    }
  },

  watch: {
    form: {
      handler(val) {
        this.changeFrom()
      },
      deep: true,
    },
    remake: {
      handler(val) {
        this.changeFrom()
      },
    },
  },
  mounted() {
    this.id = this.$route.query.id || ''
    this.getTemplate()
    this.setWidth(this.printWidth)
  },
  methods: {
    changeFrom() {
      let table = {
        product_code: this.form.product_code,
        product_name: this.form.product_name,
        product_barcode: this.form.product_barcode,
        product_quantity: this.form.product_quantity,
        product_spec: this.form.product_spec,
        product_price: this.form.product_price,
        product_discounted_unit_price: this.form.product_discounted_unit_price,
        product_total_price: this.form.product_total_price,
        product_discounted_price: this.form.product_discounted_price,
      }
      this.printElements = window.PRINT_JSON_DATA.init(
        this.form,
        table,
        this.remake,
        this.printWidth
      )
      let paperNumberFormat = `${this.form.current_page ? '第paperNo页' : ''}${
        this.form.current_page && this.form.total_pages ? ',' : ''
      }${this.form.total_pages ? '共paperCount页' : ''}`
      let width =
        this.form.total_pages && this.form.current_page
          ? 40
          : this.form.total_pages || this.form.current_page
          ? 30
          : 0
      this.content = {
        panels: [
          {
            orient: 2,
            height: this.printHeight,
            width: this.printWidth,
            topOffset: 10,
            paperFooter: 340,
            paperHeader: 30,
            printElements: this.printElements,
            paperNumberLeft: this.ptWidth / 2 - width,
            paperNumberTop: 350,
            paperNumberFormat,
            paperNumberContinue: false,
            paperNumberToggleInEven: false,
          },
        ],
      }
    },

    setWidth(printWidth) {
      let proportion = 0.35 // 比例
      // mm => pt
      this.ptWidth = parseInt(printWidth / proportion) + 20
    },

    // 初始化模板
    getTemplate() {
      getTemplateFields({
        print_template_id: this.id,
      }).then((res) => {
        if (res.code == 200) {
          this.selectList = res.data
          let form = {}
          this.selectList.forEach((item) => {
            item.children.forEach((items) => {
              form[items.alias] = false
            })
          })
          this.form = form
          this.initPrint()
        }
      })
    },

    // 初始化打印模板设计器
    initPrint() {
      getTemplateDetail({
        print_template_id: this.id,
      }).then((res) => {
        if (res.code == 200) {
          let templateSet = res.data.configure_ids
          this.remake = res.data.remake
          if (templateSet.length > 0) {
            this.selectList.map((item) =>
              item.children.map((items) => {
                this.$set(
                  this.form,
                  items.alias,
                  templateSet.some((i) => items.sys_conf_id == i)
                )
              })
            )
          }
        }
      })
    },

    // 保存模板
    async saveTemplate() {
      this.isSaveLoading = true
      let fields = []
      this.selectList.forEach((item) => {
        item.children.forEach((items) => {
          this.form[items.alias] ? fields.push(items.sys_conf_id) : ''
        })
      })
      console.log(this.content)
      configureTemplateFields({
        print_template_id: this.id,
        fields: fields,
        content: JSON.stringify(this.content),
        remake: this.remake,
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success('保存成功')
          this.backFn()
        }
        this.isSaveLoading = false
      })
    },

    backFn() {
      this.$router.go(-1)
    },
  },
}
</script>

<style lang="scss" scoped>
.scroll-width {
  overflow-x: auto;
}
.table-flex {
  margin-top: 20px;
  min-width: 1220px;
  .card-left {
    width: 300px;
    margin-right: 20px;
  }
  .table-scroll {
    height: calc(100vh - 280px);
    overflow: auto;
  }
  .card-right {
    flex: 1;
    position: relative;
    height: calc(100vh - 218px);
    min-width: 780px;
    .card-zzc {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      z-index: 10;
    }
  }
}

.card-title {
  margin: 20px 0;
  font-size: 14px;
  color: #222222;
  line-height: 14px;
  font-weight: Medium;
}
.checkbox-item + .checkbox-item {
  margin-top: 8px;
}
.print-dessign .table-flex ::v-deep {
  .el-card__body {
    padding: 0 0 20px 20px;
  }
  .card-right .el-card__body {
    padding: 0;
  }
  .el-card__header {
    border-radius: 10px 10px 0px 0px;
    padding: 12px 20px;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 10px 10px 0px 0px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #222222;
    line-height: 16px;
  }
}
</style>>
