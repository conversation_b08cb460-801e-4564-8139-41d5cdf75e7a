<template>
  <div>
    <el-dialog
      :close-on-click-modal="false"
      :title="( type == 'add' || type == 'carrierAdd' ? '添加' : '编辑') +  '打印模版'"
      :visible.sync="isPrint"
      width="750px"
    >
      <div>
        <el-form :model="form" label-position="top" :rules="rules" ref="form">
          <el-form-item label="模版名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入模版名称"></el-input>
          </el-form-item>
          <el-form-item label="模版类型" prop="type">
            <el-select
              class="w100"
              :disabled="isDisabled"
              v-model="form.type"
              placeholder="请选择模版类型"
              @change="getSystem"
            >
              <el-option
                v-for="item in typeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="适用物流公司"
            v-if="form.type == 1"
            :prop="form.type == 1 ? 'express_id' : ''"
          >
            <el-select
              :disabled="isDisabled"
              class="w100"
              v-model="form.express_id"
              placeholder="请选择适用物流公司"
              @change="getSystem"
            >
              <el-option
                v-for="item in companyList"
                :key="item.express_id"
                :label="item.express_name"
                :value="item.express_id"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="选择母版" v-if="mubanList.length > 0" prop="system_template_id">
            <div class="flex select">
              <div v-for="item in mubanList" :key="item.system_template_id" class="select-i">
                <div
                  @click.stop="changeTempate(item.system_template_id)"
                  class="select-item"
                  :class="form.system_template_id == item.system_template_id ? 'key' : ''"
                >
                  <!-- :src="item.src" :preview-src-list="[item.src]" -->
                  <div class="select-item-image">
                    <print-view
                      class="print-view-image"
                      :style="'transform: scale(' + item.zoom + ');margin-left: ' + item.leftWidth + 'px'"
                      :printType="form.type"
                      :width="item.printWidth + 'mm'"
                      :height="item.printHeight+ 'mm'"
                      :priveView="item.content"
                    ></print-view>
                    <div @click.stop="viewPrint(item)" class="view-print">
                      <i slot="suffix" class="iconfont icon-zhengyan1"></i>
                      <span>查看</span>
                    </div>
                    <div class="zzc"></div>
                  </div>

                  <div class="size">
                    <div>{{item.name}}</div>
                    <div>{{item.size_text}}</div>
                  </div>
                </div>
                <div class="size-tip">{{item.remark}}</div>
              </div>
            </div>
          </el-form-item>
        </el-form>
        <base-dialog-footer @confirm="save" @cancel="isPrint = false"></base-dialog-footer>
      </div>
    </el-dialog>

    <print-template-view ref="printTemplateView"></print-template-view>
  </div>
</template>

<script>
import {
  createTemplate,
  filterTemplateListApi,
  systemTemplateSelectApi,
  updateTemplate,
} from '@/api/shop/waybill'
import { expressCompanyListApi } from '@/api/common.js'
import PrintView from '@/components/PrintView/index.vue'
import printTemplateView from '../../printTemplateView.vue'

export default {
  components: {
    PrintView,
    printTemplateView,
  },
  data() {
    return {
      isPrint: false,
      form: {
        name: '',
        type: '',
        express_id: '',
        template_id: '',
        system_template_id: '',
      },
      rules: {
        name: [{ required: true, message: '请输入模版名称', trigger: 'blur' }],
        type: [{ required: true, message: '请选择模版类型', trigger: 'change' }],
        express_id: [{ required: true, message: '请选择适用物流公司', trigger: 'change' }],
        system_template_id: [{ required: true, message: '请选择母版', trigger: 'change' }],
      },

      // 模版类型
      typeList: [],
      // 物流公司
      companyList: [],
      // 物流公司下拉列表选择完成的数据展示
      expressCompany: [],

      // 母版
      mubanList: [],

      // 缩放比例
      zoom: 1,
    }
  },
  props: {
    type: {
      type: String,
      default: 'add',
      // carrierAdd 从承运商处添加打印模版 不可修改模版类型和物流公司
    },
  },
  computed: {
    isDisabled() {
      return this.type == 'carrierAdd' || (this.type != 'add' && this.type != 'carrierAdd')
    },
  },
  created() {},
  methods: {
    // 切换模版
    changeTempate(id) {
      this.$set(this.form, 'system_template_id', id)
    },
    // 预览模版
    viewPrint(row) {
      console.log(row)
      if (!row.content) return
      let conetnt = row.content
      if (conetnt.panels.length > 0) {
        this.$refs.printTemplateView.open({
          printType: this.form.type,
          content: conetnt,
          printWidth: conetnt.panels[0].width,
          printHeight: conetnt.panels[0].height,
        })
      }
    },
    init() {
      // 初始化筛选内容
      filterTemplateListApi().then((res) => {
        if (res.code == 200) {
          this.typeList = res.data.type
        }
      })
      // 初始化物流公司下拉列表
      expressCompanyListApi({
        is_online: 'Y',
      }).then((res) => {
        if (res.code == 200) {
          this.companyList = res.data
        }
      })
    },

    // 获取系统模版列表
    getSystem() {
      if (this.form.type == '' || (this.form.type == 1 && this.form.express_id == '')) {
        this.mubanList = []
        return
      }
      systemTemplateSelectApi({
        type: this.form.type,
        express_id: this.form.express_id || '',
      }).then((res) => {
        if (res.code == 200) {
          // 处理数据，将content转为json格式，方便后续使用，例如缩放比例等
          res.data = res.data.map((item) => {
            if (item.content) {
              item.content = JSON.parse(item.content)
              console.log(item.content)
              if (item.content.panels.length > 0) {
                item.printWidth = item.content.panels[0].width
                item.printHeight = item.content.panels[0].height
                let maxWidth =
                  item.printWidth > item.printHeight ? item.printWidth : item.printHeight
                // 将尺寸转为像素，方便后续缩放比例的计算 mm => px 96dpi => 1in = 96px
                let conversionWidth = Number((maxWidth / 25.4) * 96).toFixed(2)
                // 计算缩放比例 使图片宽度为120px 方便后续居中显示，并保持宽高比不变
                item.zoom = (120 / conversionWidth).toFixed(2)
                // 计算左侧偏移量，使图片居中显示
                let minwidth = Number((item.printWidth / 25.4) * 96)
                console.log(minwidth, item.zoom)
                console.log(minwidth)

                item.leftWidth = (200 - minwidth * item.zoom) / 2
              }
            }
            return item
          })
          this.mubanList = res.data
          console.log(this.mubanList)
          if (res.data.length == 1) {
            this.$set(this.form, 'system_template_id', res.data[0].system_template_id)
          }
        }
      })
    },

    open(row) {
      this.mubanList = []
      if (this.type == 'edit') {
        this.form = row
        this.getSystem()
        // 编辑时获取数据
      } else if (this.type == 'carrierAdd') {
        this.form = {
          name: '',
          type: row.type,
          express_id: row.express_id,
          template_id: '',
        }
        this.getSystem()
      } else {
        this.form = { name: '', type: '', express_id: '', template_id: '' }
      }
      this.$nextTick(() => {
        this.$refs.form.clearValidate()
      })
      this.init()
      this.isPrint = true
    },
    save() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.confirm()
        } else {
          return false
        }
      })
    },
    confirm() {
      let api = this.type == 'edit' ? updateTemplate : createTemplate
      api(this.form).then((res) => {
        if (res.code == 200) {
          this.$emit('success')
          this.isPrint = false
        }
      })
    },
  },
  mounted() {},
}
</script>

<style lang="scss" scoped>
.select {
  flex-wrap: wrap;
  align-items: flex-start;
  .select-i {
    margin-right: 10px;
    margin-top: 10px;
  }
  .select-item {
    border-radius: 8px;
    border: 2px solid #e3e7ed;
    width: 220px;
    padding: 10px 10px 0;
    font-size: 14px;
    color: #303133;
    text-align: center;
    box-sizing: border-box;
    line-height: 20px;
    cursor: pointer;
    .select-item-image {
      width: 200px;
      height: 120px;
      overflow: hidden;
      position: relative;

      .view-print {
        position: absolute;
        bottom: 8px;
        right: 8px;
        width: 54px;
        height: 20px;
        border-radius: 10px;
        z-index: 11;
        background: rgba(0, 0, 0, 0.6);
        cursor: pointer;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
      }

      .print-view-image {
        transform-origin: top left;
      }

      .zzc {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 10;
        color: #333;
        cursor: pointer;
      }
    }
    &.key {
      border-radius: 8px;
      border: 2px solid #0071fe;
      position: relative;
      &::after {
        content: '';
        display: block;
        position: absolute;
        width: 32px;
        height: 32px;
        background: url('~@/assets/goods/goods_select.png') no-repeat;
        background-size: 32px 32px;
        right: 0;
        top: 0;
      }
    }
    > div.size {
      padding: 6px 0 4px;
      font-size: 14px;
      color: #303133;
      font-weight: Medium;
    }
  }
  .select-item:nth-child(3n) {
    margin-right: 0px;
  }
  .size-tip {
    margin: 10px auto 0;
    font-weight: 400;
    font-size: 14px;
    color: #999999;
    line-height: 20px;
    text-align: center;
    width: 210px;
  }
}
</style>