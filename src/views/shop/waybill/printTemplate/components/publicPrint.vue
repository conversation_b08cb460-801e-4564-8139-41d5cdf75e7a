<template>
  <div class="pad20">
    <div class="company_name" v-if="form.company_name">{company_name}出库单</div>
    <div class="prive_div">
      <div>
        <span v-if="form.receiver">收件人：{receiver}</span>
      </div>
      <div>
        <span v-if="form.order_time">下单时间：{order_time}</span>
      </div>
      <div>
        <span v-if="form.order_number">订单号：{order_number}</span>
      </div>
      <div>
        <span v-if="form.receiver_phone">联系电话：{receiver_phone}</span>
      </div>
      <div v-if="form.receiver_address">收货地址：{receiver_address}</div>
    </div>
    <el-table border :span-method="arraySpanMethod" :data="tableDataDialog" v-if="isTable">
      <el-table-column width="60" label="序号">
        <template slot-scope="scope">
          <div
            class="flex totle-right"
            v-if="scope.$index == 1 && (form.total_quantity || form.order_total_price || form.order_actual_payment )"
          >
            总计：
            <span v-if="form.total_quantity">数量：{total_quantity}</span>
            <span v-if="form.order_total_price">销售总金额：{order_total_price}</span>
            <span v-if="form.order_actual_payment">实付金额：{order_actual_payment}</span>
          </div>
          <div v-if="scope.$index != 1">1</div>
        </template>
      </el-table-column>
      <el-table-column v-if="form.product_code" prop="product_code" label="商品编码"></el-table-column>
      <el-table-column v-if="form.product_name" prop="product_name" label="商品名称"></el-table-column>
      <el-table-column v-if="form.product_barcode" prop="product_barcode" label="商品条码"></el-table-column>
      <el-table-column v-if="form.product_quantity" prop="product_quantity" label="数量"></el-table-column>
      <el-table-column v-if="form.product_spec" prop="product_spec" label="规格"></el-table-column>
      <el-table-column v-if="form.product_price" prop="product_price" label="销售单价"></el-table-column>
      <el-table-column
        v-if="form.product_discounted_unit_price"
        prop="product_discounted_unit_price"
        label="优惠后单价"
      ></el-table-column>

      <el-table-column
        v-if="form.product_total_price"
        prop="product_total_price"
        width="120"
        label="销售总金额"
      ></el-table-column>

      <el-table-column
        v-if="form.product_discounted_price"
        prop="product_discounted_price"
        width="120"
        label="优惠后商品总金额"
      ></el-table-column>
    </el-table>
    <div class="table_bottom_info">
      <div>
        <span v-if="form.buyer_message">买家留言:{buyer_message}</span>
      </div>
      <div>
        <span v-if="form.seller_memo">卖家备注:{seller_memo}</span>
      </div>
      <div>
        <span v-if="form.note">备注:{{remake || '{note}'}}</span>
      </div>
      <div>
        <span v-if="form.printer">打印人:{printer}</span>
      </div>
      <div style="width: 100%;" v-if="form.current_page || form.total_pages">
        <span v-if="form.current_page">第paperNo页</span>
        <span v-if="form.current_page && form.total_pages">,</span>
        <span v-if="form.total_pages">共totalPage页</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'publicPrint',
  props: {
    remake: {
      type: String,
      default: '',
    },
    form: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      tableDataDialog: [],
      arraySpanMethod({ row, column, rowIndex, columnIndex }) {
        console.log(rowIndex)
        if (rowIndex == 0) return
        return [1, 9]
      },
      isTable: false,
    }
  },
  computed: {
    prive_div_zwf() {
      let form = this.form
      return form.receiver && form.order_time && form.order_number && form.receiver_phone && form.receiver_address
    },
  },
  watch: {
    form: {
      handler(val) {
        let tableData = {
          product_code: 'product_code',
          product_name: 'product_name',
          product_barcode: 'product_barcode',
          product_quantity: 'product_quantity',
          product_spec: 'product_spec',
          product_price: 'product_price',
          product_total_price: 'product_total_price',
          product_discounted_unit_price: 'product_discounted_unit_price',
          product_discounted_price: 'product_discounted_price',
        }
        if (val.total_quantity || val.order_total_price || val.order_actual_payment) {
          this.tableDataDialog = [tableData, {}]
        } else {
          this.tableDataDialog = [tableData]
        }
        if (
          val.product_code ||
          val.product_name ||
          val.product_barcode ||
          val.product_quantity ||
          val.product_spec ||
          val.product_price ||
          val.product_total_price ||
          val.product_discounted_unit_price ||
          val.product_discounted_price
        ) {
          this.isTable = true
        }
      },
      deep: true,
      immediate: true,
    },
  },
}
</script>


<style lang="scss" scoped>
.pad20 {
  padding: 20px;
}
.totle-right {
  width: 100%;
  justify-content: flex-end;
}

.company_name {
  text-align: center;
  margin-bottom: 10px;
  font-weight: 600;
}
.prive_div {
  display: flex;
  flex-wrap: wrap;
  > div {
    width: 33%;
    flex-shrink: 0;
    line-height: 30px;
  }

  > div:nth-child(3n-1) {
    text-align: center;
  }

  > div:nth-child(3n) {
    text-align: right;
  }
}
.table_bottom_info {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  > div {
    width: 50%;
    flex-shrink: 0;
    line-height: 30px;
  }
  > div:nth-child(2n) {
    text-align: right;
  }
  > div:last-child {
    text-align: center;
  }
}
</style>