<template>
  <div>
    <el-card>
      <base-form
        ref="baseForm"
        :tableForm="tableForm"
        :formArray="formArray"
        @searchForm="searchForm"
      ></base-form>
      <div>
        <el-button
          @click="addTemplate"
          style="margin-top: 20px;"
          type="primary"
          icon="el-icon-plus"
        >添加模版</el-button>
      </div>
      <base-table
        :tableColumn="tableColumn"
        :tableRequest="printTemplateList"
        :tableForm="tableForm"
        ref="baseTable"
      >
        <!-- 状态 -->
        <template #is_forbidden="{ scope }">
          <div class="flex reative">
            <div class="reative_zzc" @click="changeForbidden(scope.row)"></div>
            <el-switch v-model="scope.row.is_forbidden" :active-value="'N'" :inactive-value="'Y'"></el-switch>
          </div>
        </template>

        <!-- 操作 -->
        <template #operate="{ scope }">
          <div class="public-operate-btn">
            <el-button size="mini" type="text" @click="viewPrint(scope.row)">预览</el-button>
            <el-button size="mini" type="text" @click="editPrint(scope.row)">编辑</el-button>
            <el-button
              size="mini"
              type="text"
              v-if="scope.row.type == 3"
              @click="setPrintDesign(scope.row)"
            >设计</el-button>
            <el-popconfirm
              placement="top"
              confirm-button-text="删除"
              cancel-button-text="取消"
              icon="el-icon-info"
              icon-color="red"
              title="是否删除该模版吗？"
              @confirm="delPrint(scope.row)"
            >
              <el-button class="del-button" size="mini" type="text" slot="reference">删除</el-button>
            </el-popconfirm>
          </div>
        </template>
      </base-table>
    </el-card>

    <add-print-template @success="reloadTable" :type="printType" ref="addPrintTemplate"></add-print-template>

    <print-template-view ref="printTemplateView"></print-template-view>
  </div>
</template>

<script>
import {
  printTemplateList,
  filterTemplateListApi,
  deleteTemplate,
  templateStatusApi,
} from '@/api/shop/waybill'
import { expressCompanyListApi } from '@/api/common.js'
import addPrintTemplate from './components/addPrintTemplate'
import printTemplateView from '../printTemplateView.vue'
export default {
  name: 'printTemplate',
  components: {
    addPrintTemplate,
    printTemplateView,
  },
  data() {
    return {
      printTemplateList,
      // 表单key
      formArray: [
        {
          label: '模版名称',
          type: 'input',
          key: 'name',
          placeholder: '请输入模版名称',
        },
        {
          label: '模版类型',
          type: 'select',
          key: 'type',
          placeholder: '请选择模版类型',
          options: [],
        },
        {
          label: '物流公司',
          type: 'select',
          key: 'express_id',
          placeholder: '请选择物流公司',
          options: [],
        },

        {
          label: '状态',
          type: 'select',
          key: 'is_forbidden',
          placeholder: '请选择状态',
          options: [],
        },
      ],
      tableForm: {
        is_forbidden: 'N',
      },
      tableColumn: [
        {
          label: '模版名称',
          prop: 'name',
        },
        {
          label: '模版类型',
          prop: 'type_text',
        },
        {
          label: '适用物流公司',
          prop: 'express_name',
        },
        {
          label: '纸张尺寸',
          prop: 'size',
          width: '180',
        },
        {
          label: '状态',
          prop: 'is_forbidden',
          type: 'customize',
          width: '100',
        },
        {
          label: '操作',
          prop: 'operate',
          type: 'customize',
          width: '200',
        },
      ],
      printType: 'add',
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      // 初始化筛选内容
      filterTemplateListApi().then((res) => {
        if (res.code == 200) {
          this.formArray[1].options = res.data.type
          this.formArray[3].options = res.data.is_forbidden
        }
      })
      // 初始化物流公司下拉列表
      expressCompanyListApi().then((res) => {
        if (res.code == 200) {
          let data = res.data.map((item) => {
            return {
              label: item.express_name,
              value: item.express_id,
            }
          })
          console.log(data)
          this.formArray[2].options = data
        }
      })
    },
    // 搜索表单
    searchForm(form) {
      this.tableForm = Object.assign({}, this.tableForm, form)
    },
    // 添加模版
    addTemplate() {
      this.printType = 'add'
      this.$nextTick(() => {
        this.$refs.addPrintTemplate.open()
      })
    },
    // 重新加载表格
    reloadTable() {
      this.$refs.baseTable.tableRequestFn(true)
    },

    // 编辑模版
    editPrint(row) {
      console.log(row)
      this.printType = 'edit'
      this.$nextTick(() => {
        this.$refs.addPrintTemplate.open(row)
      })
    },

    // 删除模版
    delPrint(row) {
      deleteTemplate({
        print_template_id: row.print_template_id,
      }).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: '删除成功!',
          })
          this.reloadTable()
        }
      })
    },

    // 预览模版
    viewPrint(row) {
      if (!row.content) return
      let conetnt = JSON.parse(row.content)

      if (conetnt.panels.length > 0) {
        this.$refs.printTemplateView.open({
          printType: row.type,
          content: conetnt,
          printWidth: conetnt.panels[0].width,
          printHeight: conetnt.panels[0].height,
        })
      }
    },

    // 设计模版
    setPrintDesign(row) {
      this.$router.push({
        path: '/waybill/printDesign',
        query: {
          id: row.print_template_id,
        },
      })
    },

    // 创建
    changeForbidden(row) {
      console.log(row)
      templateStatusApi({
        print_template_id: row.print_template_id, //模板ID
        is_forbidden: row.is_forbidden == 'Y' ? 'N' : 'Y', //Y 停用 N 启用
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success('操作成功')
          this.reloadTable()
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.del-button {
  margin-left: 4px !important;
  position: relative;
  padding: 0 0 0 5px;
  &::before {
    content: '';
    display: block;
    width: 1px;
    height: 8px;
    background: rgba(0, 0, 0, 0.08);
    box-shadow: inset 0px -1px 0px 0px #ebeef5;
    position: absolute;
    left: 0;
    top: 2px;
  }
}
.reative {
  position: relative;
}
.reative_zzc {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 10;
  cursor: pointer;
}
</style>