<template>
  <el-dialog
    append-to-body
    :visible.sync="showDetailDialog"
    :close-on-click-modal="false"
    title="预约详情"
    width="800px"
  >
    <div class="content" v-if="orderDetailData">
      <el-descriptions
        title="预约信息"
        :column="2"
        :labelStyle="label_Style"
        :contentStyle="content_style"
      >
        <el-descriptions-item label="预约单号">
          <div v-if="orderDetailData">
            {{ orderDetailData.reservation_order_no }}
            <span
              @click="copysuborderno(orderDetailData.reservation_order_no, 'order_no')"
              style="cursor: pointer; margin-left: 5px"
            >
              <i class="el-icon-copy-document"></i>
            </span>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <div v-if="orderDetailData">
            {{ orderDetailData.status }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="预约日期">
          {{ orderDetailData && orderDetailData.reserve_date }}
        </el-descriptions-item>
        <el-descriptions-item label="预约时段">
          {{ orderDetailData && orderDetailData.reserve_time_slot }}
        </el-descriptions-item>
        <el-descriptions-item label="预约门店">
          {{ orderDetailData && orderDetailData.shop && orderDetailData.shop.name }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ orderDetailData && orderDetailData.created_at }}
        </el-descriptions-item>
        <el-descriptions-item label="备注">
          {{ orderDetailData && orderDetailData.remark }}
        </el-descriptions-item>
      </el-descriptions>
      <el-divider></el-divider>
      <el-descriptions title="商品信息"></el-descriptions>
      <el-table
        :data="orderDetailData.order_goods_info && orderDetailData.order_goods_info"
        :header-cell-style="{ background: '#F5F7FA' }"
        style="width: 100%"
      >
        <el-table-column prop="info" label="商品信息" width="300">
          <template slot-scope="scope">
            <div class="flex">
              <el-image
                style="width: 50px; height: 50px"
                :src="scope.row.product_image"
                :preview-src-list="[scope.row.product_image]"
              ></el-image>
              <div class="saleInfo">
                <span :title="scope.row.sku_name" class="saleInfotitle">
                  {{ scope.row.sku_name }}
                </span>
                <span :title="scope.row.spec_value_items" class="saleInfotitle1">
                  规格：{{ scope.row.spec_value_items }}*{{ scope.row.quantity }}
                </span>
                <span class="saleInfoprice">支付单价:¥{{ scope.row.price }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="销售单价" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.price">¥{{ scope.row.price }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="total_price" label="销售总价">
          <template slot-scope="scope">
            <span v-if="scope.row.total_price">¥{{ scope.row.total_price }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="reduce_amount" label="优惠金额">
          <template slot-scope="scope">
            <span v-if="scope.row.reduce_amount">¥{{ scope.row.reduce_amount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="settle_price" label="实收金额">
          <template slot-scope="scope">
            <span class="settle_price" v-if="scope.row.settle_price">
              ¥{{ scope.row.settle_price }}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <el-divider></el-divider>
      <el-descriptions
        title="使用人信息"
        :column="2"
        :labelStyle="label_Style"
        :contentStyle="content_style"
      >
        <div v-if="orderDetailData.user_info">
          <div v-for="item in orderDetailData.user_info">
            <el-descriptions-item label="姓名">
              {{ item.name }}
            </el-descriptions-item>
            <el-descriptions-item label="电话">
              {{ item.phone }}
            </el-descriptions-item>
            <el-descriptions-item label="邮箱">
              {{ item.email }}
            </el-descriptions-item>
          </div>
        </div>
      </el-descriptions>
    </div>
  </el-dialog>
</template>

<script>
  import { getarrindex } from '@/utils/index'
  import { orderReservationDetailApi } from '@/api/shop/order.js'
  export default {
    name: 'cardreservationDetail',
    components: {},
    data() {
      return {
        content_style: {
          'font-size': '14px',
          color: '#8C8C8C ',
        },
        label_Style: {
          'font-size': '14px',
          color: '#8C8C8C ',
        },
        showDetailDialog: false,
        tableData: [],
        orderDetailData: {},
        shipping_method: '', //express(快递),pickup(自提)
      }
    },
    methods: {
      open(data) {
        this.getOrderDetailfn(data)
        this.$nextTick(() => {
          this.showDetailDialog = true
        })
      },
      getOrderDetailfn(data) {
        orderReservationDetailApi({ id: data.sub_order_no }).then((res) => {
          if (res.code == 200) {
            this.orderDetailData = res.data
            this.shipping_method = res.data.shipping_method
          }
        })
      },

      //复制订单编号
      async copysuborderno(data, code = 'sub_order_no') {
        // 创建临时输入框来复制文本
        const textarea = document.createElement('textarea')
        textarea.value = data[code]
        document.body.appendChild(textarea)
        textarea.select()

        try {
          document.execCommand('copy')
          this.$message.success('订单编号已复制到剪贴板')
        } catch (err) {
          this.$message.error('复制失败，请手动复制')
        }

        document.body.removeChild(textarea)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .content {
    padding: 15px;
    .el-descriptions {
      margin-top: 10px;
    }
  }
  .stepcontent {
    justify-content: space-around;
    .steps:last-child .stepline {
      display: none;
    }
    .stepdiv {
      text-align: center;
      width: 100%;
      .stepName {
        margin-bottom: 5px;
        &.active {
          color: #0071fe;
        }
      }
      .stepline {
        border-top: 1px dashed #d7d7d7;
        width: 70px;
        position: absolute;
        left: 96%;
        top: 8px;
        &.cancel {
          left: 81%;
        }
      }
    }
  }
  .saleInfo {
    display: flex;
    flex-direction: column;
    margin-left: 10px;
    .saleInfotitle {
      display: inline-block;
      white-space: nowrap;
      width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .saleInfotitle1 {
      display: inline-block;
      white-space: nowrap;
      width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 12px;
    }
    .saleInfoprice {
      color: #ff5500;
      font-size: 12px;
    }
    span {
      height: 20px;
    }
  }
  .payContent {
    margin: 10px;
    font-size: 16px;
    justify-content: flex-end;
    .contentBottom {
      margin-right: 20px;
      .title {
        font-size: 14px;
        color: #999999;
        text-align: left;
      }
      .pay1 {
        font-size: 13px;
        font-weight: 400;
        color: #222222;
      }
      .pay2 {
        font-size: 13px;
        color: #ff5500;
      }
    }
  }
  .payInfo {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    font-size: 16px;
    .money {
      font-weight: 500;
      color: #222222;
    }
    span:last-child {
      color: #ff5500;
      font-weight: 600;
    }
  }
  ::v-deep {
    // 表格头部样式
    .el-table th.el-table__cell {
      background: #f5f7fa;
      box-shadow: inset 0px -1px 0px 0px #ebeef5, inset 0px -1px 0px 0px #ebeef5;
    }
    th.el-table__cell {
      background-color: #f5f7fa;
    }
    th.el-table__cell .cell {
      color: #303133;
      font-weight: 600;
    }
  }
  .settle_price {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #222222;
    line-height: 20px;
  }
</style>
