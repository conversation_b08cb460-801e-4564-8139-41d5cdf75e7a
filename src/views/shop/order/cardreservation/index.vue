<!--
 * @Author: liqian <EMAIL>
 * @Email: <EMAIL>
 * @Date: 2025-07-14 17:17:05
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-08-04 11:01:13
 * @FilePath: \qst-merchant-admin-2.0\src\views\shop\order\cardreservation\index.vue
 * @Description: 
-->
<template>
  <div class="goods">
    <el-card>
      <base-form
        ref="baseForm"
        :tableForm="tableForm"
        :formArray="formArray"
        @searchForm="searchForm"
      ></base-form>
    </el-card>
    <el-card>
      <template slot="header">
        <div class="flex scroll" ref="scroll">
          <div
            v-for="(item, index) in selectTabList"
            :class="
              tableForm.search_order_status === `${item.id}` ||
              (tableForm.search_order_status != '' && !tableForm.search_order_status && index == 0)
                ? 'key'
                : ''
            "
            :key="index"
            @click="searchGoodsStatusFn(item.id)"
          >
            <el-tooltip
              :disabled="item.id === '' || item.id == 4"
              class="item"
              effect="dark"
              placement="top"
              :content="item.tip"
            >
              <span class="titlename" type="text">{{ item.name }}({{ item.count }})</span>
            </el-tooltip>
          </div>
        </div>
      </template>

      <base-table
        :tableColumn="tableColumn"
        :tableRequest="orderReservationsApi"
        :tableForm="tableForm"
        rowKey="good_id"
        ref="baseTable"
        :isSelect="true"
        :isInitCallback="true"
        v-if="!isLoading"
        @scrollX="tableScrollX"
        @initCallback="tableCallback"
        @selectiKey="selectTableData"
      >
        <template #sub_order_no="{ scope }">
          <!-- 手机号 -->
          <div>
            {{ scope.row.sub_order_no }}
            <span @click="copysuborderno(scope.row)" style="cursor: pointer; margin-left: 5px">
              <i class="el-icon-copy-document"></i>
            </span>
          </div>
        </template>
        <template #phone="{ scope }">
          <!-- 手机号 -->
          <div @click="showPhoneContent(scope.row, 'phone')">{{ scope.row.phone }}</div>
        </template>
        <template #address="{ scope }">
          <!-- 地址 -->
          <div @click="showPhoneContent(scope.row, 'address')">{{ scope.row.address }}</div>
        </template>

        <template #is_refund="{ scope }">
          <span>{{ scope.row.is_refund == 'Y' ? '是' : '否' }}</span>
        </template>

        <template #pay_amount="{ scope }">
          <!-- 商品类型 -->
          <div>¥{{ scope.row.pay_amount }}</div>
        </template>
        <template #express_name="{ scope }">
          <span class="statusSty">{{ scope.row.express_name && scope.row.express_name[0] }}</span>
        </template>
        <template #operate="{ scope }">
          <!-- 3-已完成 -->
          <div class="public-operate-btn" v-if="scope.row.order_status == 3">
            <el-button size="mini" type="text" @click="lookOrderDetial(scope.row)">
              查看详情
            </el-button>
            <el-button size="mini" type="text" @click="goodsOrderRemarks(scope.row)">
              添加备注
            </el-button>
          </div>
          <!-- 核销 -->
          <div class="public-operate-btn" v-if="scope.row.order_status == 8">
            <el-button size="mini" type="text" @click="lookOrderDetial(scope.row)">
              查看详情
            </el-button>
            <el-button size="mini" type="text" @click="goodsHxDetial(scope.row)">核销</el-button>
            <el-button size="mini" type="text" @click="goodsOrderRemarks(scope.row)">
              添加备注
            </el-button>
          </div>
          <div class="public-operate-btn" v-if="scope.row.order_status == 4">
            <el-button size="mini" type="text" @click="lookOrderDetial(scope.row)">
              查看详情
            </el-button>
            <el-button size="mini" type="text" @click="lookDelete(scope.row)">删除</el-button>
          </div>
        </template>
      </base-table>
    </el-card>
    <el-dialog :title="showTitle" :visible.sync="dialogVisible" :close-on-click-modal="false">
      <div class="view-phone">{{ modelPhone }}</div>
    </el-dialog>

    <!-- 订单备注 -->
    <el-dialog
      append-to-body
      :visible.sync="showRemarksOrder"
      :close-on-click-modal="false"
      title="添加备注"
      width="550px"
    >
      <el-form ref="basicInfo" :model="form" label-width="80px" label-position="top">
        <el-form-item>
          <template slot="label">
            <span class="textTitle">商家内部备注</span>
          </template>
          <el-input
            type="textarea"
            show-word-limit
            maxlength="100"
            v-model="form.remark"
            placeholder="请输入商家内部备注信息"
          ></el-input>
        </el-form-item>
      </el-form>
      <base-dialog-footer
        @cancel="showRemarksOrder = false"
        @confirm="confirmRemarks"
      ></base-dialog-footer>
    </el-dialog>
    <!-- 订单详情 -->
    <cardreservationDetail :itemList="rowIndexList" ref="orderDetailDialog"></cardreservationDetail>

    <!-- 核销确认 1无码核销  2 扫码核销-->
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="showHXconfirm"
      width="480px"
    >
      <template slot="title">
        <div class="flex">
          <img class="tip_icon" :src="imgPath" alt />
          <span class="title">核销确认</span>
        </div>
      </template>

      <el-form ref="basicInfo" :rules="rules" :model="form" label-width="80px" label-position="top">
        <el-form-item prop="hxcode">
          <template slot="label">
            <span class="codevalue">请输入/扫描核销码完成订单核销</span>
          </template>
          <el-input style="width: 100%" v-model="form.hxcode" placeholder="请输入核销码"></el-input>
        </el-form-item>
      </el-form>
      <base-dialog-footer
        confirmText="确认"
        @cancel="showHXconfirm = false"
        @confirm="confirmHXBtn"
      ></base-dialog-footer>
    </el-dialog>
  </div>
</template>

<script>
  import cardreservationDetail from './components/cardreservationDetail.vue'
  import {
    getSearchConfig,
    getOrderHideFiled,
    orderReservationsApi,
    orderReservationAddRemarkApi,
    orderReservationVerifyApi,
    orderReservationDeleteApi,
  } from '@/api/shop/order.js'

  export default {
    name: 'cardreservation',
    components: {
      cardreservationDetail,
    },
    data() {
      return {
        imgPath: require('../../../../assets/warn_20250626.png'),
        tableWidth: '100%',
        tableLeft: '10px',
        orderReservationsApi,
        value: false,
        showRemarksOrder: false,
        dialogVisible: false,
        showHXconfirm: false,
        // tab
        modelPhone: '',
        showTitle: '',
        selectTabList: [],
        rowIndexList: [],
        form: {
          remark: '',
          hxcode: '',
        },
        rules: {
          hxcode: [{ required: true, message: '请输入核销码', trigger: 'blur' }],
        },
        // 表单
        tableForm: {
          search_order_status: '',
        },
        order_status: ['待支付', '已支付', '已发货', '已完成', '已取消'], //订单状态0-待支付 1-已支付 2-已发货 3-已完成 4-已取消
        // 表单key
        formArray: [
          {
            label: '预约单号',
            type: 'input',
            key: 'reservation_order_no',
            placeholder: '请输入预约单号',
          },
          {
            label: '使用人姓名',
            type: 'input',
            key: 'username',
            placeholder: '请输入使用人姓名',
          },
          {
            label: '使用人电话',
            type: 'input',
            key: 'phone',
            placeholder: '请输入使用人电话',
          },
          {
            label: '预约门店',
            type: 'store',
            key: 'shop_id',
            placeholder: '请选择预约门店',
            options: [],
            callback: (e) => {
              this.tableForm = Object.assign(
                {},
                { search_order_status: this.tableForm.search_order_status, search_shop_id: e }
              )
            },
          },
          {
            label: '状态',
            type: 'select',
            key: 'status',
            placeholder: '请选择状态',
            options: [],
          },

          {
            label: '预约时间',
            type: 'time',
            key: 'time',
            timeKey: ['start_time', 'end_time'],
          },
        ],
        // 表格配置
        tableColumn: [
          {
            prop: 'expand',
            width: '55',
          },
          {
            label: '预约单号',
            prop: 'reservation_order_no',
            type: 'customize',
            width: '160',
          },
          {
            label: '客户信息',
            prop: 'username',
            width: '80',
          },
          {
            label: '预约商品',
            prop: 'goods_name',
          },
          {
            label: '预约时间',
            prop: 'reserve_date',
            width: '200px',
          },
          {
            label: '门店',
            type: 'customize',
            prop: 'shop_name',
            width: 200,
          },

          {
            label: '状态',
            prop: 'status',
          },
          {
            label: '创建时间',
            prop: 'created_at',
            width: '200px',
          },
          {
            label: '操作',
            type: 'customize',
            prop: 'operate',
            fixed: 'right',
            width: 250,
          },
        ],
        search_order_status: '',
        goodsConfig: {},
        getSelectTableLsit: [], //选择的数据

        // 加载数据
        isLoading: true,
      }
    },
    computed: {
      tableHeight(i) {
        let priveTable = this.$refs
        console.log('priveTable', priveTable)
      },
    },
    created() {
      // 初始化
      this.init()
    },
    methods: {
      init() {
        // 获取商品类型
        getSearchConfig({
          is_search: 'Y',
        }).then((res) => {
          if (res.code == 200) {
            this.goodsConfig = res.data
            this.$set(this.formArray[4], 'options', [...res.data.shipping_list])
            this.isLoading = false
            // 获取表格宽度
            this.$nextTick(() => {
              this.tableWidth = this.$refs.baseTable?.$el.offsetWidth - 50 + 'px'
            })
          }
        })
      },

      // 刷新表格
      refreshTable() {
        this.$refs.baseTable.tableRequestFn()
        this.$refs.baseTable.clearSelection()
        this.tableCallback()
      },
      tableCallback() {
        this.selectTabList = this.$refs.baseTable.TableDataAll.census
      },
      //勾选表格数据
      selectTableData(data) {
        this.getSelectTableLsit = data
      },
      // 表格滚动事件
      tableScrollX(e) {
        this.tableLeft = e
      },

      // 搜索对应状态的商品类白哦
      searchGoodsStatusFn(val) {
        this.$refs.baseTable.clearSelection()
        this.$set(this.tableForm, 'search_order_status', `${val}`)
        this.refreshTable()
      },

      // 查看详情
      lookOrderDetial(row) {
        this.rowIndexList = row
        this.$refs.orderDetailDialog.open(this.rowIndexList)
      },
      // 删除
      lookDelete(row) {
        this.$confirm('是否删除该预约单号?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            orderReservationDeleteApi({ id: row.id }).then((res) => {
              if (res.code == 200) {
                this.$message.success('删除成功')
                this.$refs.tableRef.tableRequestFn()
              }
            })
          })
          .catch((action) => {})
      },
      //订单备注
      goodsOrderRemarks(row) {
        this.rowIndexList = row
        this.showRemarksOrder = true
        this.$set(this.form, 'remark', row.remark)
      },
      //核销
      goodsHxDetial(row) {
        this.rowIndexList = row
        this.showHXconfirm = true
      },

      // 核销确认
      confirmHXBtn() {
        this.$refs.basicInfo.validate((valid) => {
          if (valid) {
            this.verifyOrderFnApi()
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
      //核销订单
      verifyOrderFnApi(sub_order_no = '') {
        let params = {
          code: this.hxcode,
          sub_order_no: sub_order_no,
        }
        orderReservationVerifyApi(params).then((res) => {
          if (res.code == 200) {
            this.$message.success('核销成功')
            this.showHXconfirm = false
            this.refreshTable()
          }
        })
      },
      //添加备注
      confirmRemarks(flag) {
        this.$refs.basicInfo.validate((valid) => {
          if (valid) {
            let params = {
              id: this.rowIndexList.id,
              ...this.form,
            }
            orderReservationAddRemarkApi(params).then((res) => {
              if (res.code == 200) {
                this.$message.success('添加备注成功')
                this.showRemarksOrder = false
                this.refreshTable()
              }
            })
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },

      // 表单搜索事件
      searchForm(form, isResetFields) {
        this.tableForm = Object.assign({}, this.tableForm, form)
      },
      showPhoneContent(data, code) {
        if (code == 'phone') {
          this.showTitle = '手机号'
        }
        if (code == 'address') {
          this.showTitle = '收货地址'
        }
        let params = {
          shop_id: data.shop_id,
          sub_order_no: data.sub_order_no, //子订单号
          filed_type: code, //查询字段 phone手机号 address 地址
        }
        getOrderHideFiled(params).then((res) => {
          if (res.code === 200) {
            this.dialogVisible = true
            this.modelPhone = res.data[code]
          }
        })
      },
      //复制订单编号
      async copysuborderno(data) {
        try {
          await navigator.clipboard.writeText(data.sub_order_no)
          this.$message.success('复制成功！')
        } catch (err) {
          this.$message.error('复制失败，请手动复制')
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .ml10 {
    margin-left: 10px;
  }
  .select {
    cursor: pointer;
  }
  .operate {
    &.btns {
      .el-button {
        background: #f5f7fa;
        color: #a8abb2;
        border-color: #dcdfe6;
      }
      .el-button:hover {
        border-color: #dcdfe6;
      }
      .el-button:focus {
        border-color: #dcdfe6;
      }
    }
  }

  .el-image {
    img {
      width: 80px;
    }
  }
  .saleInfo {
    display: flex;
    flex-direction: column;
    margin-left: 10px;
    .saleInfotitle {
      display: inline-block;
      white-space: nowrap;
      width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .saleInfotitle1 {
      display: inline-block;
      white-space: nowrap;
      width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 12px;
    }
    .saleInfoprice {
      color: #ff5500;
      font-size: 12px;
    }
    span {
      height: 20px;
    }
  }
  .scroll {
    overflow-x: auto;
    flex-shrink: 0;
    padding-bottom: 15px;
    & > div {
      flex-shrink: 0;
      white-space: nowrap;
      cursor: pointer;
    }
    & > div + div {
      margin-left: 50px;
      font-weight: 400;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
    }
    .key {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #0071fe;
      position: relative;
      &::after {
        content: '';
        display: block;
        width: 28px;
        height: 2px;
        background: #0071fe;
        position: absolute;
        left: 50%;
        bottom: -15px;
        transform: translateX(-50%);
      }
    }
  }
  // 第一列表格的样式
  .goods ::v-deep {
    .el-card__header {
      padding-bottom: 0;
    }
    .el-table-column--selection .cell {
      padding-left: 10px;
    }

    th:first-child,
    tr.el-table__row td:first-child {
      transform: translateX(55px) !important;
    }

    th:nth-child(2),
    tr.el-table__row td:nth-child(2) {
      transform: translateX(-55px) !important;
    }
    // tr:not(.el-table__row) > td {
    //   padding-top: 0 !important;
    //   padding-bottom: 0 !important;
    // }
  }

  .view-phone {
    text-align: center;
  }
  .textTitle {
    font-size: 14px;
    color: #303133;
    font-weight: 600;
    margin-right: 4px;
  }
  // .codevalueno {
  //   font-family: PingFangSC, PingFang SC;
  //   font-weight: 400;
  //   font-size: 14px;
  //   color: #606266;
  //   line-height: 14px;
  //   text-align: center;
  //   margin-bottom: 10px;
  // }
  // .hxcodeMain {
  //   .el-form {
  //     .el-form-item {
  //       text-align: center;
  //     }
  //   }
  // }
  .codevalue {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #222222;
  }

  .showImg {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    border: 1px solid #ededed;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }
</style>
