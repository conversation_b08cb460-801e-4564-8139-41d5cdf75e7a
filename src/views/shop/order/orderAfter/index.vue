<template>
  <div>
    <base-account-shop @checkAccountShopFn="checkAccountShopFn"></base-account-shop>

    <div class="after-order-list" v-if="isShowPage">
      <!-- 搜索区 -->
      <el-card>
        <base-form
          ref="baseForm"
          :tableForm="tableForm"
          :formArray="formArray"
          @searchForm="searchForm"
        ></base-form>
      </el-card>
      <el-card>
        <div style="margin: 16px 0; display: flex; align-items: center">
          <el-button type="primary" @click="batchApprove">批量通过</el-button>
        </div>
        <!-- 表格区 -->
        <selectable-base-table
          :isSelect="true"
          rowKey="id"
          :tableColumn="tableColumn"
          :tableRequest="getTableData"
          :tableForm="tableForm"
          ref="tableRef"
          @selectiKey="handleSelectionChange"
          :selectable="isRowSelectable"
          @scrollX="tableScrollX"
        >
          <!-- 商品信息插槽 -->
          <template #productInfo="{ scope }">
            <div style="display: flex; align-items: center">
              <img
                :src="scope.row.product_image"
                alt
                style="width: 40px; height: 40px; object-fit: cover; margin-right: 8px"
              />
              <div>
                <div>{{ scope.row.sku_name }}</div>
                <div style="color: #999; font-size: 12px">{{ scope.row.spec_name }}</div>
                <div style="color: #999; font-size: 12px">数量 {{ scope.row.quantity }}</div>
              </div>
            </div>
          </template>
          <!-- 状态插槽 -->
          <template #status="{ scope }">
            <el-tag
              :type="statusMap[scope.row.status] ? statusMap[scope.row.status].type : ''"
              disable-transitions
            >{{ statusMap[scope.row.status] ? statusMap[scope.row.status].label : '未知状态' }}</el-tag>
          </template>
          <!-- 操作插槽 -->
          <template #operate="{ scope }">
            <template v-if="scope.row.status === 1">
              <el-button type="text" size="mini" @click="approve(scope.row)">通过</el-button>
              <el-button
                type="text"
                size="mini"
                style="color: #f56c6c"
                @click="reject(scope.row)"
              >拒绝</el-button>
            </template>
            <el-button type="text" size="mini" @click="viewDetail(scope.row)">查看详情</el-button>
          </template>
        </selectable-base-table>
      </el-card>

      <!-- 拒绝弹窗 -->
      <el-dialog
        :visible.sync="rejectDialogVisible"
        width="700px"
        append-to-body
        :lock-scroll="false"
      >
        <div slot="title" class="dialog-title-with-icon">
          <i class="el-icon-question" style="color: #ffba53; margin-right: 8px; font-size: 25px"></i>
          <span style="color: #303133; font-size: 18px; font-weight: bold">确认拒绝?</span>
        </div>

        <div style="margin-bottom: 16px">
          <el-alert
            title="建议您与买家协商后，再确定是否拒绝。如拒绝后，买家可修改售后申请，重新发起售后"
            type="warning"
            show-icon
            class="himalaya-alert"
          />
        </div>
        <el-form
          :model="rejectForm"
          :rules="rejectFormRules"
          ref="rejectFormRef"
          label-width="120px"
          label-position="top"
        >
          <el-form-item label="拒绝说明" prop="reason">
            <el-input
              type="textarea"
              v-model="rejectForm.reason"
              placeholder="请输入你的拒绝说明，填写完成后将直接展示给消费者"
              rows="2"
            />
          </el-form-item>
          <el-form-item
            label="是否修改退款金额"
            prop="isRefund"
            v-if="currentRow && currentRow.refund_type == '1'"
          >
            <el-radio-group v-model="rejectForm.isRefund">
              <el-radio label="yes">是</el-radio>
              <el-radio label="no">否，直接拒绝</el-radio>
              <div class="radio-desc">如需修改退款金额，请修改完成后再确认，买家将收到拒绝通知并自动退款</div>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="修改退款金额"
            prop="refundAmount"
            v-if="currentRow && currentRow.refund_type == '1' && rejectForm.isRefund === 'yes'"
          >
            <el-input
              type="number"
              v-model.number="rejectForm.refundAmount"
              placeholder="请输入您建议的退款金额"
              step="1"
            />
            <div class="amount-tip">
              <span style="color: #8c8c8c">申请退款金额</span>
              <span>{{ currentRow ? currentRow.refundAmount : '¥0.00' }}</span>
            </div>
          </el-form-item>
          <el-form-item
            label="运费退款金额"
            prop="freightAmount"
            v-if="currentRow && currentRow.refund_type == '1' && rejectForm.isRefund === 'yes'"
          >
            <div style="display: flex; align-items: flex-start; gap: 8px">
              <el-input
                type="number"
                v-model.number="rejectForm.freightAmount"
                placeholder="请输入您建议的运费退款金额"
                step="1"
                style="flex: 1"
              />
              <span
                @click="openFreightCalculateDialog"
                class="freight-calculate-link"
                :class="{ loading: freightCalculateLoading }"
              >运费差额计算</span>
            </div>
            <div class="amount-tip">
              <span style="color: #8c8c8c">本单运费退款金额</span>
              <span>{{ currentRow ? currentRow.freightAmount || '¥0.00' : '¥0.00' }}</span>
            </div>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer-center">
          <el-button @click="rejectDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleRejectConfirm">拒绝</el-button>
        </div>
      </el-dialog>

      <!-- 通过弹窗 -->
      <el-dialog
        :visible.sync="approveDialogVisible"
        width="500px"
        style="margin-top: 15vh"
        custom-class="approve-dialog"
        append-to-body
        :lock-scroll="false"
      >
        <div slot="title" class="dialog-title-with-icon">
          <i class="el-icon-question" style="color: #ffba53; margin-right: 8px; font-size: 25px"></i>
          <span style="color: #303133; font-size: 18px; font-weight: bold">确认审核通过?</span>
        </div>
        <div style="padding: 16px 0; color: #606266; font-size: 14px; line-height: 1.5">
          <span v-if="approveRow">
            退款金额：{{ approveData.actual_refund_amount }}（包含{{
            approveData.shipping_refund_amount
            }}运费），通过后将进入退款/换货处理流程，确认退款？
          </span>
        </div>
        <div class="dialog-footer-center">
          <el-button @click="handleApproveEdit">退款金额不对，去修改</el-button>
          <el-button type="primary" @click="handleApproveConfirm">确认</el-button>
        </div>
      </el-dialog>

      <!-- 运费差额计算弹窗 -->
      <el-dialog
        :visible.sync="freightCalculateDialogVisible"
        width="500px"
        style="margin-top: 15vh"
        custom-class="freight-calculate-dialog"
        append-to-body
        :lock-scroll="false"
      >
        <div slot="title" class="dialog-title-with-icon">
          <i class="el-icon-question" style="color: #ffba53; margin-right: 8px; font-size: 25px"></i>
          <span style="color: #303133; font-size: 18px; font-weight: bold">运费差额计算结果</span>
        </div>
        <div v-loading="freightCalculateLoading" style="min-height: 40px">
          <div style="margin-bottom: 8px">
            <div style="margin-bottom: 6px; font-size: 14px; color: #606266; line-height: 1.3">
              <div>原订单运费：¥{{ freightCalculateData.order_shipping_fee }}</div>
            </div>
            <div style="margin-bottom: 6px; font-size: 14px; color: #606266; line-height: 1.3">
              <span>
                剩余商品所需运费：¥{{ freightCalculateData.surplus_order_shipping_fee }}
                (去除已完成、待退款的售后商品及本次售后商品)
              </span>
            </div>
            <div style="margin-bottom: 6px; font-size: 14px; color: #606266; line-height: 1.3">
              <span>运费差额：¥{{ freightCalculateData.diff_shipping_fee }}</span>
              <i
                class="el-icon-copy-document copy-icon"
                @click="copyToClipboard(freightCalculateData.diff_shipping_fee, '运费差额')"
                title="复制金额"
              ></i>
              <span>(订单调整运费增加¥{{ freightCalculateData.price_adjust }}, 根据情况决定是否退回)</span>
            </div>
          </div>

          <div style="color: #f56c6c; font-size: 14px; line-height: 1.3">
            <span style="color: #f56c6c; font-weight: bold">提示：</span>
            <span>请手动输入项目重量，以及运费差额，以免产生出错</span>
          </div>
        </div>

        <div slot="footer" class="dialog-footer-center">
          <el-button @click="freightCalculateDialogVisible = false">取消</el-button>
          <!--        <el-button type="primary" @click="handleFreightCalculateConfirm">按差额退费</el-button>-->
        </div>
      </el-dialog>

      <!-- 订单详情弹窗 -->
      <el-dialog
        :visible.sync="detailDialogVisible"
        width="800px"
        title="订单详情"
        custom-class="order-detail-dialog"
        append-to-body
        :lock-scroll="false"
      >
        <div class="order-detail-content">
          <!-- 订单状态进度条 -->
          <div class="order-progress">
            <div class="horizontal-steps" v-if="detailData && detailData.process_nodes">
              <div
                v-for="(node, idx) in detailData.process_nodes"
                :key="idx"
                class="step-item"
                :class="getStepClass(node.status)"
              >
                <div class="step-circle">
                  <i
                    v-if="node.status === 'finish' || node.status === 'end'"
                    class="el-icon-check step-check"
                  ></i>
                  <span v-else class="step-number">{{ idx + 1 }}</span>
                </div>
                <div class="step-content">
                  <div class="step-title">{{ node.name }}</div>
                  <div class="step-description">{{ node.msg }}</div>
                </div>
                <div
                  v-if="idx < detailData.process_nodes.length - 1"
                  class="step-line"
                  :class="getLineClass(node.status)"
                ></div>
              </div>
            </div>
          </div>

          <!-- 基本信息展示 -->
          <div class="basic-info">
            <div class="info-grid-basic">
              <div class="info-item">
                <span class="label">售后单号：</span>
                <span class="value">{{ detailData?.refund_order_no }}</span>
              </div>
              <div class="info-item">
                <span class="label">售后状态：</span>
                <span
                  class="value status"
                  :class="getStatusClass(detailData?.status)"
                >{{ getStatusText(detailData?.status) }}</span>
              </div>
              <div class="info-item">
                <span class="label">申请时间：</span>
                <span class="value">{{ detailData?.created_at }}</span>
              </div>
              <div class="info-item">
                <span class="label">售后类型：</span>
                <span class="value">{{ getAfterTypeText(detailData?.refund_type_name) }}</span>
              </div>
              <div class="info-item">
                <span class="label">买家寄回运单号：</span>
                <span
                  v-if="detailData?.receive_order_no"
                  class="value logistics-link"
                  @click="handleViewLogistics"
                  :title="detailData.receive_order_no"
                >{{ detailData.receive_order_no }}</span>
                <span v-else class="value">-</span>
              </div>
              <!-- 申请退款金额：仅退款/退货退款时展示 -->
              <div
                class="info-item"
                v-if="
                  detailData && (detailData.refund_type == '1' || detailData.refund_type == '2')
                "
              >
                <span class="label">申请退款金额：</span>
                <span class="value amount">
                  {{ detailData?.apply_amount }}
                  <span
                    v-if="
                      detailData?.random_discount_amount && detailData.random_discount_amount > 0
                    "
                    style="color: #8c8c8c"
                  >（已扣除随机立减{{ detailData.random_discount_amount }}元）</span>
                </span>
              </div>
              <!-- 运费补偿金额：仅退款/退货退款时展示 -->
              <div
                class="info-item"
                v-if="
                  detailData && (detailData.refund_type == '1' || detailData.refund_type == '2')
                "
              >
                <span class="label">运费补偿金额：</span>
                <span class="value amount">{{ detailData?.shipping_refund_amount }}</span>
                <span
                  @click="openFreightCalculateDialog"
                  class="freight-calculate-link"
                  :class="{ loading: freightCalculateLoading }"
                >运费差额计算</span>
              </div>
              <!-- 换新订单号：换货且该字段有数据时展示 -->
              <div
                class="info-item"
                v-if="detailData && detailData.refund_type == '3' && detailData.new_sub_order_no"
              >
                <span class="label">换新订单号：</span>
                <span
                  class="value logistics-link"
                  @click="handleViewNewOrder"
                  :title="detailData.new_sub_order_no"
                >{{ detailData.new_sub_order_no }}</span>
              </div>
              <div class="info-item full-width">
                <span class="label">申请原因：</span>
                <span class="value">{{ detailData?.reason }}</span>
              </div>
              <div class="info-item">
                <span class="label">问题描述：</span>
                <span class="value">{{ detailData?.problem }}</span>
                <span
                  v-if="detailData?.voucher_images && detailData.voucher_images.length > 0"
                  class="view-evidence-link"
                  @click="previewImage(detailData.voucher_images[0], 0, detailData.voucher_images)"
                  title="点击查看凭证图片"
                >查看凭证</span>
              </div>
              <!-- 退货地址：退货退款/换货时展示 -->
              <div
                class="info-item"
                v-if="
                  detailData && (detailData.refund_type == '2' || detailData.refund_type == '3')
                "
              >
                <span
                  class="view-evidence-link"
                  @click="handleViewReturnAddress"
                  title="点击查看退货地址"
                >退货地址</span>
              </div>
              <!-- 实际退款金额：仅退款/退货退款，且审核完成时展示 -->
              <div
                class="info-item"
                v-if="
                  detailData &&
                  (detailData.refund_type == '1' || detailData.refund_type == '2') &&
                  isAuditCompleted(detailData.status)
                "
              >
                <span class="label">实际退款金额：</span>
                <span class="value amount">{{ getActualRefundAmount() }}</span>
              </div>
            </div>
          </div>

          <!-- 操作按钮区域 -->
          <div v-if="detailData && detailData.status == '1'" class="action-buttons-section">
            <el-button class="reject-button" @click="handleReject(detailData)">拒绝申请</el-button>
            <el-button type="primary" @click="handleApprove(detailData)">通过申请</el-button>
          </div>

          <!-- 商家收货阶段按钮区域 (退货退款和换货) -->
          <div
            v-if="
              detailData &&
              (detailData.refund_type == '2' || detailData.refund_type == '3') &&
              detailData.status == '4'
            "
            class="action-buttons-section"
          >
            <el-button class="reject-button" @click="handleRejectReceive(detailData)">拒绝收货</el-button>
            <el-button type="primary" @click="handleConfirmReceive(detailData)">确认收货</el-button>
          </div>

          <!-- Element UI Tabs标签页 -->
          <el-tabs v-model="activeTab">
            <el-tab-pane label="订单信息" name="order">
              <div class="section-title1">订单基础信息</div>
              <div class="info-grid-three">
                <div class="info-item">
                  <span class="label">订单编号：</span>
                  <span class="value">
                    {{ detailData?.orderInfo?.sub_order_no }}
                    <el-button
                      type="text"
                      size="mini"
                      @click="copyOrderNo(detailData?.orderInfo?.sub_order_no)"
                      style="color: #409eff; margin-left: 8px; padding: 0; font-size: 12px"
                    >复制</el-button>
                  </span>
                </div>
                <div class="info-item">
                  <span class="label">店铺名称：</span>
                  <span class="value">{{ detailData?.orderInfo?.shop_name || '无' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">售后前订单状态：</span>
                  <span class="value">{{ detailData?.orderInfo?.apply_order_status || '无' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">下单时间：</span>
                  <span class="value">{{ detailData?.orderInfo?.created_at || '无' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">下单人账号：</span>
                  <span class="value">{{ detailData?.orderInfo?.member_name || '无' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">下单人电话：</span>
                  <span class="value">{{ detailData?.orderInfo?.member_phone || '无' }}</span>
                </div>
                <div class="info-item full-width">
                  <span class="label">买家留言：</span>
                  <span class="value">{{ detailData?.orderInfo?.remark || '无' }}</span>
                </div>
                <div class="info-item full-width">
                  <span class="label">卖家内部备注：</span>
                  <span class="value">{{ detailData?.orderInfo?.merchant_remark || '无' }}</span>
                </div>
                <div class="info-item full-width">
                  <span class="label">用户可见备注：</span>
                  <span class="value">{{ detailData?.orderInfo?.customer_remark || '无' }}</span>
                </div>
              </div>

              <div class="section-title">支付信息</div>
              <div class="info-grid-three">
                <div class="info-item">
                  <span class="label">商品销售总价：</span>
                  <span class="value">¥{{ detailData?.orderInfo?.payInfo?.goods_amount || '无' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">优惠金额：</span>
                  <span class="value">¥{{ detailData?.orderInfo?.payInfo?.discount_amount || '无' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">运费：</span>
                  <span class="value">¥{{ detailData?.orderInfo?.payInfo?.shipping_fee || '无' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">实收支付金额：</span>
                  <span class="value">{{ detailData?.orderInfo?.payInfo?.order_amount || '无' }}</span>
                </div>
              </div>

              <div class="section-title">配送信息</div>
              <div class="info-grid-three">
                <div class="info-item">
                  <span class="label">物流公司：</span>
                  <span class="value">{{ detailData?.orderInfo?.shippingInfo?.carrier_name || '无' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">运单号：</span>
                  <span
                    class="value"
                  >{{ detailData?.orderInfo?.shippingInfo?.tracking_number || '无' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">收货人：</span>
                  <span class="value">{{ detailData?.orderInfo?.shippingInfo?.consigner || '无' }}</span>
                </div>
                <div class="info-item full-width">
                  <span class="label">收货人电话：</span>
                  <span class="value">{{ detailData?.orderInfo?.shippingInfo?.phone || '无' }}</span>
                </div>
                <div class="info-item full-width">
                  <span class="label">收货地址：</span>
                  <span class="value">{{ detailData?.orderInfo?.shippingInfo?.address || '无' }}</span>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="商品信息" name="product">
              <div class="product-table">
                <table class="product-info-table">
                  <thead>
                    <tr>
                      <th>商品信息</th>
                      <th>商品名称</th>
                      <th>规格</th>
                      <th>单价</th>
                      <th>数量</th>
                      <th>实际支付单价</th>
                      <th>售后数量</th>
                      <th>商品退款金额</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>
                        <img
                          :src="
                            detailData?.goodsInfo?.product_image || 'https://via.placeholder.com/60'
                          "
                          alt="商品图片"
                          class="table-product-image"
                        />
                      </td>
                      <td>{{ detailData?.goodsInfo?.sku_name || '-' }}</td>
                      <td>{{ detailData?.goodsInfo?.spec_name || '-' }}</td>
                      <td>¥{{ detailData?.goodsInfo?.origin_price || '-' }}</td>
                      <td>{{ detailData?.goodsInfo?.quantity || '-' }}</td>
                      <td>¥{{ detailData?.goodsInfo?.apply_amount || '-' }}</td>
                      <td>{{ detailData?.goodsInfo?.refund_quantity || '-' }}</td>
                      <td>¥{{ detailData?.goodsInfo?.goods_amount || '-' }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </el-tab-pane>

            <el-tab-pane label="订单记录" name="record">
              <div class="record-table" v-loading="orderRecordLoading">
                <table class="record-info-table">
                  <thead>
                    <tr>
                      <th>订单/售后单号</th>
                      <th>操作记录</th>
                      <th>操作人</th>
                      <th>操作时间</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(item, idx) in orderRecordList" :key="idx">
                      <td>{{ item.refund_order_no || item.sub_order_no }}</td>
                      <td>
                        {{ item.change_message }}
                        <div v-if="item.extend_message && item.extend_message.length">
                          <div v-for="(ext, extIdx) in item.extend_message" :key="extIdx">
                            <template v-if="ext.type === 'text'">
                              <span>{{ ext.name }}：</span>
                              <span>{{ ext.val }}</span>
                            </template>
                            <template v-else-if="ext.type === 'img'">
                              <span>{{ ext.name }}：</span>
                              <span>
                                <img
                                  v-for="(img, imgIdx) in ext.val"
                                  :key="imgIdx"
                                  :src="img"
                                  alt="凭证"
                                  class="record-evidence-image"
                                  @click="previewImage(img, imgIdx, ext.val)"
                                  title="点击查看大图"
                                />
                              </span>
                            </template>
                          </div>
                        </div>
                      </td>
                      <td>{{ item.reviewer }}</td>
                      <td>{{ item.created_at }}</td>
                    </tr>
                    <tr v-if="!orderRecordLoading && orderRecordList.length === 0">
                      <td colspan="4" style="text-align: center; color: #999">暂无记录</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>

        <div slot="footer" class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </div>
      </el-dialog>

      <!-- 退货地址选择弹窗 -->
      <el-dialog
        :visible.sync="showAddressDialog"
        width="500px"
        style="margin-top: 15vh"
        custom-class="showAddress-dialog"
        append-to-body
        :lock-scroll="false"
      >
        <div slot="title">
          <i class="el-icon-question" style="color: #ffba53; margin-right: 8px; font-size: 25px"></i>
          <span style="color: #303133; font-size: 18px; font-weight: bold">当前未选择退货地址，请补充</span>
        </div>
        <div style="margin-bottom: 16px">
          选择退货地址
          <el-select v-model="selectedAddressId" placeholder="请选择" style="width: 78%">
            <el-option
              v-for="item in addressList"
              :key="item.id"
              :label="item.address"
              :value="item.id"
            />
          </el-select>
        </div>
        <div style="margin-bottom: 8px; font-size: 14px; color: #606266">
          若不想每次都选择，可在地址管理中将多余的退货地址删除
          <br />
          <br />
          <a @click="handleGoAddressManage" style="color: #409eff; cursor: pointer">点击跳转地址管理</a>
        </div>
        <template #footer>
          <div class="dialog-footer-center">
            <el-button @click="showAddressDialog = false">取消</el-button>
            <el-button type="primary" @click="handleAddressDialogOk">确认</el-button>
          </div>
          <div style="text-align: center; margin-top: 16px; font-size: 14px">
            无需买家寄退？
            <a
              @click="handleChangeToRefundOnly"
              style="color: #409eff; cursor: pointer"
            >点击修改为仅退款</a>
          </div>
        </template>
      </el-dialog>

      <!-- 无退货地址弹窗 -->
      <el-dialog
        :visible.sync="showNoAddressDialog"
        width="500px"
        style="margin-top: 15vh"
        custom-class="showAddress-dialog"
        append-to-body
        :lock-scroll="false"
      >
        <div slot="title">
          <i class="el-icon-question" style="color: #ffba53; margin-right: 8px; font-size: 25px"></i>
          <span style="color: #303133; font-size: 18px; font-weight: bold">系统未查到退货地址</span>
        </div>
        <div style="margin-bottom: 16px; margin-top: 25px">
          请先到地址管理菜单中配置退货地址后重试。
          <a href="/address-manage" target="_blank">点击跳转地址管理</a>
        </div>
        <template #footer>
          <div class="dialog-footer-center">
            <el-button @click="showNoAddressDialog = false">取消</el-button>
            <el-button type="primary" @click="showNoAddressDialog = false">确认</el-button>
          </div>
          <div style="text-align: center; margin-top: 16px; font-size: 14px">
            无需买家寄退？
            <a
              @click="handleChangeToRefundOnly"
              style="color: #409eff; cursor: pointer"
            >点击修改为仅退款</a>
          </div>
        </template>
      </el-dialog>

      <!-- 二次确认弹窗 -->
      <el-dialog
        :visible.sync="showSecondConfirmDialog"
        width="500px"
        style="margin-top: 15vh"
        custom-class="showAddress-dialog"
        append-to-body
        :lock-scroll="false"
      >
        <div slot="title">
          <i class="el-icon-warning" style="color: #ffba53; font-size: 22px; margin-right: 8px"></i>
          <span style="color: #303133; font-size: 18px; font-weight: bold">确认审核通过？</span>
        </div>
        <div style="margin-bottom: 16px">
          <span style="font-size: 14px; font-weight: bold">收货信息为：</span>
          <span style="font-size: 14px; line-height: 1.5">
            {{ selectedAddressObj?.manage_name }}，{{ selectedAddressObj?.contact_phone }}，{{
            selectedAddressObj?.address
            }}
          </span>
          <br />
          <br />
          <span
            v-if="approveRow && approveRow.refund_type == 2"
            style="color: #303133; font-size: 16px; font-weight: bold"
          >通过后将要求买家按上述地址寄回商品，进入退货退款流程</span>
          <span
            v-if="approveRow && approveRow.refund_type == 3"
            style="color: #303133; font-size: 16px; font-weight: bold"
          >通过后将进入换货流程</span>
        </div>
        <div slot="footer" class="dialog-footer-center">
          <el-button @click="showSecondConfirmDialog = false">取消</el-button>
          <el-button type="primary" @click="handleSecondConfirmOk">确认</el-button>
        </div>
      </el-dialog>

      <!-- 无需买家退货直接退款弹窗 -->
      <el-dialog
        :visible.sync="noReturnRefundDialogVisible"
        width="500px"
        style="margin-top: 15vh"
        custom-class="approve-dialog"
      >
        <div slot="title" class="dialog-title-with-icon">
          <i class="el-icon-warning" style="color: #ffba53; margin-right: 8px; font-size: 25px"></i>
          <span style="color: #303133; font-size: 18px; font-weight: bold">确认无需买家退货直接退款？</span>
        </div>
        <div style="padding: 16px 0; color: #606266; font-size: 14px; line-height: 1.5">
          退款金额：¥{{ approveData.actual_refund_amount }}（包含¥{{
          approveData.shipping_refund_amount
          }}运费），通过后将进入退款处理流程，确认退款？
        </div>
        <div class="dialog-footer-center">
          <el-button @click="noReturnRefundDialogVisible = false">取消</el-button>
          <el-button @click="handleChangeToRefundOnlyAndModifyAmount">变更为仅退款, 修改金额</el-button>
          <el-button type="primary" @click="handleNoReturnRefundConfirm">确认</el-button>
        </div>
      </el-dialog>

      <!-- 确认收货弹窗 -->
      <el-dialog
        :visible.sync="confirmReceiveDialogVisible"
        width="500px"
        style="margin-top: 15vh"
        custom-class="approve-dialog"
      >
        <div slot="title" class="dialog-title-with-icon">
          <i class="el-icon-question" style="color: #ffba53; margin-right: 8px; font-size: 25px"></i>
          <span style="color: #303133; font-size: 18px; font-weight: bold">确认收货</span>
        </div>
        <div style="padding: 16px 0; color: #606266; font-size: 14px; line-height: 1.5">
          <span
            v-if="confirmReceiveRow && confirmReceiveRow.refund_type == '2'"
          >确认已收到买家寄回的商品，收货后将进入退款处理流程，确认收货？</span>
          <span
            v-if="confirmReceiveRow && confirmReceiveRow.refund_type == '3'"
          >确认已收到买家寄回的商品，收货后将进入换货处理流程，确认收货？</span>
        </div>
        <div class="dialog-footer-center">
          <el-button @click="confirmReceiveDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmReceiveOk">确认</el-button>
        </div>
      </el-dialog>

      <!-- 图片预览弹窗 -->
      <el-dialog
        :visible.sync="imagePreviewVisible"
        width="70%"
        custom-class="image-preview-dialog"
        :show-close="false"
        center
      >
        <div slot="title" class="image-preview-header">
          <span>图片预览 ({{ currentImageIndex + 1 }} / {{ previewImages.length }})</span>
          <div class="image-preview-controls">
            <el-button
              v-if="previewImages.length > 1"
              @click="prevImage"
              :disabled="currentImageIndex === 0"
              icon="el-icon-arrow-left"
              size="small"
              circle
            ></el-button>
            <el-button
              v-if="previewImages.length > 1"
              @click="nextImage"
              :disabled="currentImageIndex === previewImages.length - 1"
              icon="el-icon-arrow-right"
              size="small"
              circle
            ></el-button>
            <el-button
              @click="imagePreviewVisible = false"
              icon="el-icon-close"
              size="small"
              circle
            ></el-button>
          </div>
        </div>
        <div class="image-preview-content">
          <img
            :src="currentPreviewImage"
            alt="预览图片"
            class="preview-image"
            @load="onImageLoad"
            @error="onImageError"
          />
        </div>
      </el-dialog>

      <!-- 退货地址弹窗 -->
      <el-dialog
        :visible.sync="returnAddressDialogVisible"
        width="500px"
        style="margin-top: 15vh"
        custom-class="return-address-dialog"
        :show-close="true"
      >
        <div slot="title" style="display: none"></div>
        <div class="return-address-content">
          <div class="address-info">
            <div class="address-label">退货地址为：</div>
            <div class="address-detail" v-if="selectedReturnAddress">
              {{ selectedReturnAddress.manage_name }}，{{ selectedReturnAddress.phone }}，{{
              selectedReturnAddress.address
              }}
            </div>
          </div>
        </div>
        <div slot="footer" class="dialog-footer-center">
          <el-button @click="returnAddressDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleModifyAddress">修改地址</el-button>
        </div>
      </el-dialog>

      <!-- 系统未检索到退货地址弹窗 -->
      <el-dialog
        :visible.sync="noReturnAddressDialogVisible"
        width="500px"
        style="margin-top: 15vh"
        custom-class="no-return-address-dialog"
        :show-close="true"
      >
        <div slot="title" style="display: none"></div>
        <div class="no-address-content">
          <div slot="title" class="dialog-title-with-icon">
            <i class="el-icon-question" style="color: #ffba53; margin-right: 8px; font-size: 25px"></i>
            <span style="color: #303133; font-size: 18px; font-weight: bold">系统未检索到退货地址</span>
          </div>
          <div
            style="
              padding-top: 40px;
              color: #606266;
              font-size: 14px;
              line-height: 1.5;
              text-align: center;
            "
          >
            <span>请先到地址管理菜单中配置退货地址后重试！</span>
            <span class="modify-link" @click="handleGoToAddressManagement">点击进入地址管理</span>
          </div>
        </div>
        <div slot="footer" class="dialog-footer-center">
          <el-button @click="noReturnAddressDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="noReturnAddressDialogVisible = false">确认</el-button>
        </div>
      </el-dialog>

      <!-- 批量操作退货地址选择弹窗 -->
      <el-dialog
        :visible.sync="showBatchAddressDialog"
        width="500px"
        style="margin-top: 15vh"
        custom-class="showAddress-dialog"
        append-to-body
        :lock-scroll="false"
      >
        <div slot="title">
          <i class="el-icon-question" style="color: #ffba53; margin-right: 8px; font-size: 25px"></i>
          <span style="color: #303133; font-size: 18px; font-weight: bold">当前未选择退货地址，请补充</span>
        </div>
        <div style="margin-bottom: 16px">
          选择退货地址
          <el-select v-model="batchSelectedAddressId" placeholder="请选择" style="width: 78%">
            <el-option
              v-for="item in batchAddressList"
              :key="item.id"
              :label="item.address"
              :value="item.id"
            />
          </el-select>
        </div>
        <div style="margin-bottom: 8px; font-size: 14px; color: #606266">
          若不想每次都选择，可在地址管理中将多余的退货地址删除
          <br />
          <br />
          <a @click="handleGoAddressManage" style="color: #409eff; cursor: pointer">点击跳转地址管理</a>
        </div>
        <template #footer>
          <div class="dialog-footer-center">
            <el-button @click="showBatchAddressDialog = false">取消</el-button>
            <el-button type="primary" @click="handleBatchAddressDialogOk">确认</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 物流信息弹窗 -->
      <el-dialog
        :visible.sync="logisticsDialogVisible"
        width="600px"
        title="物流信息"
        custom-class="logistics-dialog"
        :close-on-click-modal="false"
        append-to-body
        :lock-scroll="false"
      >
        <div class="logistics-content" v-if="logisticsData">
          <!-- 基本信息 -->
          <div class="logistics-basic-info">
            <div class="info-row">
              <span class="label">运单号：</span>
              <span class="value">{{ logisticsData.tracking_number || '-' }}</span>
            </div>
            <div class="info-row">
              <span class="label">物流公司：</span>
              <span class="value">{{ logisticsData.express_name || '-' }}</span>
            </div>
            <div class="info-row">
              <span class="label">物流状态：</span>
              <span class="value">{{ logisticsData.logistics?.exp_status_name || '-' }}</span>
            </div>
          </div>

          <!-- 物流轨迹 -->
          <div
            class="logistics-timeline"
            v-if="logisticsData.logistics?.exp_list && logisticsData.logistics.exp_list.length > 0"
          >
            <div class="timeline-title">物流轨迹</div>
            <el-timeline>
              <el-timeline-item
                v-for="(item, index) in logisticsData.logistics.exp_list"
                :key="index"
                :timestamp="item.time"
                placement="top"
              >
                <div class="timeline-content">
                  <div class="timeline-context">{{ item.context }}</div>
                  <div class="timeline-location" v-if="item.area_name">
                    <span class="location-label">地点：</span>
                    <span>{{ item.area_name }}</span>
                  </div>
                  <div class="timeline-status" v-if="item.status">
                    <span class="status-label">状态：</span>
                    <span>{{ item.status }}</span>
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>

          <!-- 无物流信息提示 -->
          <div v-else class="no-logistics-info">
            <i class="el-icon-info" style="color: #909399; margin-right: 8px"></i>
            <span>暂无物流轨迹信息</span>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-else-if="logisticsLoading" class="logistics-loading">
          <i class="el-icon-loading"></i>
          <span>正在加载物流信息...</span>
        </div>

        <!-- 错误状态 -->
        <div v-else class="logistics-error">
          <i class="el-icon-warning" style="color: #f56c6c; margin-right: 8px"></i>
          <span>获取物流信息失败</span>
        </div>

        <div slot="footer" class="dialog-footer">
          <el-button @click="logisticsDialogVisible = false">关闭</el-button>
        </div>
      </el-dialog>

      <!-- 订单详情组件 -->
      <order-detail ref="orderDetailDialog"></order-detail>
    </div>
  </div>
</template>

<script>
import SelectableBaseTable from '@/components/Public/SelectableBaseTable.vue'
import OrderDetail from '../orderList/components/orderDetail.vue'
import BaseForm from '@/components/Public/BaseForm.vue'
import {
  calculateFreightDifferenceApi,
  refundList,
  refundDetails,
  refundRecords,
  refundGetSearchConfig,
  refundCalculateRefundAndFreight,
  refundShipmentAddressList,
  refundChangeReturnRefundToRefundOnly,
  refundApproveAfterSale,
  refundRejectAfterSale,
  refundConfirmReturnGoodsAndReceive,
  refundBatchApproveAfterSale,
  refundLogistics,
} from '@/api/shop/order.js'
import { checkAccountShop } from '@/api/common.js'

export default {
  name: 'AfterOrderList',
  components: {
    SelectableBaseTable,
    OrderDetail,
    BaseForm,
  },
  data() {
    return {
      // 表单数据
      tableForm: {
        search_refund_order_no: '',
        search_sub_order_no: '',
        search_refund_type: '',
        search_status: '',
        search_phone: '',
        search_member_name: '',
        search_member_phone: '',
      },
      // 表单配置
      formArray: [
        {
          label: '售后单号',
          type: 'input',
          key: 'search_refund_order_no',
          placeholder: '请输入售后单号',
        },
        {
          label: '关联订单号',
          type: 'input',
          key: 'search_sub_order_no',
          placeholder: '请输入关联订单号',
        },
        {
          label: '售后类型',
          type: 'select',
          key: 'search_refund_type',
          placeholder: '全部',
          options: [],
        },
        {
          label: '处理状态',
          type: 'select',
          key: 'search_status',
          placeholder: '全部',
          options: [],
        },
        {
          label: '收件人电话',
          type: 'input',
          key: 'search_phone',
          placeholder: '请输入收件人电话',
        },
        {
          label: '下单人账号',
          type: 'input',
          key: 'search_member_name',
          placeholder: '请输入下单人账号',
        },
        {
          label: '下单人电话',
          type: 'input',
          key: 'search_member_phone',
          placeholder: '请输入下单人电话',
        },
      ],
      tableColumn: [
        { label: '售后单号', prop: 'refund_order_no' },
        { label: '关联订单号', prop: 'sub_order_no' },
        { label: '商品信息', prop: 'productInfo', type: 'customize', width: 200 },
        { label: '收件人电话', prop: 'receiver_phone', width: 120 },
        { label: '下单人账号（ID）', prop: 'member_name', width: 140 },
        { label: '下单人电话', prop: 'member_phone', width: 120 },
        {
          label: '售后类型',
          prop: 'refund_type',
          width: 100,
          type: 'template',
          stateType: 'select',
          stateObj: { 1: '仅退款', 2: '退货退款', 3: '换货' },
        },
        { label: '申请原因', prop: 'reason' },
        { label: '状态', prop: 'status', type: 'customize', width: 100 },
        { label: '退款金额', prop: 'actual_refund_amount', width: 100 },
        { label: '申请时间', prop: 'created_at', width: 160 },
        { label: '操作', prop: 'operate', type: 'customize', width: 250, fixed: 'right' },
      ],
      statusMap: {
        1: { label: '待审核', type: 'warning' },
        2: { label: '待买家同意', type: 'warning' },
        3: { label: '待买家退回', type: 'warning' },
        4: { label: '待商家收货', type: 'primary' },
        5: { label: '商家换新', type: 'primary' },
        6: { label: '待系统退款', type: 'primary' },
        7: { label: '商家拒绝', type: 'danger' },
        8: { label: '关闭', type: 'info' },
        100: { label: '完成', type: 'success' },
      },

      // 拒绝弹窗相关
      rejectDialogVisible: false,
      rejectForm: {
        reason: '',
        isRefund: 'yes',
        refundAmount: '',
        freightAmount: '',
      },
      rejectFormRules: {
        reason: [{ required: true, message: '请输入拒绝说明', trigger: 'blur' }],
        isRefund: [{ required: true, message: '请选择是否按设退款金额', trigger: 'change' }],
        refundAmount: [
          { required: true, message: '请输入退款金额', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,2})?$/, message: '退款金额必须为数字', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value !== '' && value !== null && value !== undefined) {
                if (parseFloat(value) <= 0) {
                  callback(new Error('退款金额必须大于0'))
                } else {
                  callback()
                }
              } else {
                callback()
              }
            },
            trigger: 'blur',
          },
        ],
        freightAmount: [
          { required: true, message: '请输入运费退款金额', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,2})?$/, message: '运费退款金额必须为数字', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value !== '' && value !== null && value !== undefined) {
                if (parseFloat(value) < 0) {
                  callback(new Error('运费退款金额不能小于0'))
                } else {
                  callback()
                }
              } else {
                callback()
              }
            },
            trigger: 'blur',
          },
        ],
      },
      currentRow: null,
      approveDialogVisible: false,
      approveRow: null,
      // 无需买家退货直接退款弹窗
      noReturnRefundDialogVisible: false,
      // 通过弹窗数据
      approveData: {
        actual_refund_amount: '', //实际退款金额（商品金额+预计运费）
        apply_amount: '', //用户申请金额
        goods_amount: '', //实际退款的商品金额
        shipping_refund_amount: '', //预计退的运费
      },
      // 运费差额计算弹窗相关
      freightCalculateDialogVisible: false,
      freightCalculateLoading: false, // 计算加载状态
      freightCalculateData: {
        order_shipping_fee: '', // 原订单运费
        surplus_order_shipping_fee: '', // 剩余商品运费
        diff_shipping_fee: '', // 运费差额,
        price_adjust: '', // 改价运费
      },
      // 订单详情弹窗相关
      detailDialogVisible: false,
      detailData: null,
      activeTab: 'order', // 当前激活的卡片
      orderRecordList: [], // 订单记录数据
      orderRecordLoading: false, // 订单记录加载状态
      showAddressDialog: false, // 选择退货地址弹窗
      showNoAddressDialog: false, // 无退货地址弹窗
      showSecondConfirmDialog: false, // 二次确认弹窗
      addressList: [], // 退货地址列表
      selectedAddressId: null, // 选中的退货地址id
      selectedAddressObj: null, // 选中的退货地址对象
      confirmReceiveDialogVisible: false, // 确认收货弹窗
      confirmReceiveRow: null, // 确认收货的行数据
      // 图片预览相关
      imagePreviewVisible: false, // 图片预览弹窗
      previewImages: [], // 预览图片数组
      currentImageIndex: 0, // 当前预览图片索引
      // 退货地址相关
      returnAddressDialogVisible: false, // 退货地址弹窗
      noReturnAddressDialogVisible: false, // 无退货地址弹窗
      selectedReturnAddress: null, // 选中的退货地址
      // 物流信息相关
      logisticsDialogVisible: false, // 物流信息弹窗
      logisticsData: null, // 物流信息数据
      logisticsLoading: false, // 物流信息加载状态

      // 批量操作相关
      selectedRows: [], // 选中的行数据
      batchAddressList: [], // 批量操作地址列表
      batchSelectedAddressId: null, // 批量操作选中的地址ID
      showBatchAddressDialog: false, // 批量操作地址选择弹窗

      // 表格滚动相关
      tableWidth: '100%',
      tableLeft: '10px',

      isShowPage: false,
    }
  },
  computed: {
    // 当前预览的图片
    currentPreviewImage() {
      return this.previewImages[this.currentImageIndex] || ''
    },
  },
  created() {
    // this.refundGetSearchConfig()
    // Добавляем watcher на activeTab
    this.$watch(
      () => this.activeTab,
      (val) => {
        if (val === 'record' && this.detailData) {
          this.fetchOrderRecords()
        }
      }
    )
  },

  methods: {
    checkAccountShopFn(isShowPage) {
      this.isShowPage = isShowPage
      if (this.isShowPage) {
        this.refundGetSearchConfig()
      }
    },
    // 表单搜索事件
    searchForm(form, isResetFields) {
      this.tableForm = Object.assign({}, this.tableForm, form)
      this.$refs.tableRef.tableRequestFn()
    },
    async getTableData(params) {
      const apiParams = {
        page: params.page,
        limit: params.limit,
        ...this.tableForm,
      }
      try {
        const response = await refundList(apiParams)
        if (response.code === 200) {
          const { list, pagination } = response.data
          const transformedList = list.map((item) => ({
            id: item.refund_order_no,
            refund_order_no: item.refund_order_no,
            sub_order_no: item.sub_order_no,
            product_image: item.product_image,
            sku_name: item.sku_name,
            spec_name: item.spec_name,
            quantity: item.quantity,
            receiver_phone: item.receiver_phone,
            member_name: item.member_name,
            member_phone: item.member_phone,
            refund_type: String(item.refund_type),
            reason: item.reason,
            status: item.status,
            apply_amount: `¥${item.apply_amount}`,
            created_at: item.created_at,
            ...item,
          }))
          return {
            code: 200,
            data: {
              list: transformedList,
              pagination: { totalCount: pagination.totalCount },
            },
          }
        } else {
          // 全局拦截器已经处理了错误提示
          return { code: response.code, data: { list: [], pagination: { totalCount: 0 } } }
        }
      } catch (error) {
        console.error('获取售后列表失败', error)
        // 全局拦截器已经处理了错误提示，这里不再重复显示
        return { code: 500, data: { list: [], pagination: { totalCount: 0 } } }
      }
    },
    async approve(row) {
      this.approveRow = row
      if (row.refund_type == 1) {
        // 仅退款，原有逻辑
        this.approveDialogVisible = true
        const res = await refundCalculateRefundAndFreight({
          refund_order_no: row.refund_order_no,
        })
        if (res.code === 200 && res.data) {
          this.approveData = res.data
        } else {
          // 全局拦截器已经处理了错误提示
          return
        }
        return
      }
      if (row.refund_type == 2) {
        // 退货退款
        const res = await refundShipmentAddressList({ type: 3 })
        if (res.code === 200) {
          if (!res.data || res.data.length === 0) {
            // 无退货地址
            this.showNoAddressDialog = true
          } else {
            // 有退货地址
            this.addressList = res.data
            this.selectedAddressId = res.data[0]?.id || null
            this.selectedAddressObj = res.data[0] || null
            this.showAddressDialog = true
          }
        } else {
          // 全局拦截器已经处理了错误提示
        }
        return
      }
      if (row.refund_type == 3) {
        // 换货，流程和退货退款一样
        const res = await refundShipmentAddressList({ type: 3 })
        if (res.code === 200) {
          if (!res.data || res.data.length === 0) {
            // 无退货地址
            this.showNoAddressDialog = true
          } else {
            // 有退货地址
            this.addressList = res.data
            this.selectedAddressId = res.data[0]?.id || null
            this.selectedAddressObj = res.data[0] || null
            this.showAddressDialog = true
          }
        } else {
          // 全局拦截器已经处理了错误提示
        }
        return
      }
    },
    reject(row) {
      this.rejectDialogVisible = true
      this.rejectForm = {
        reason: '',
        isRefund: 'yes',
        refundAmount: '',
        freightAmount: '',
      }
      this.currentRow = row
    },
    async handleRejectConfirm() {
      this.$refs.rejectFormRef.validate(async (valid) => {
        if (valid) {
          try {
            // 构建接口参数
            const params = {
              refund_order_no: this.currentRow.refund_order_no,
              refund_type: this.currentRow.refund_type,
              mch_remark: this.rejectForm.reason,
              // 仅退款类型根据用户选择，退货退款和换货直接传N
              is_amount_modified:
                this.currentRow.refund_type == '1' && this.rejectForm.isRefund === 'yes'
                  ? 'Y'
                  : 'N',
            }

            // 根据不同类型添加金额参数
            if (this.currentRow.refund_type == '1' && this.rejectForm.isRefund === 'yes') {
              // 仅退款类型且修改金额时，传入用户输入的值，为空时默认传0
              params.goods_amount = this.rejectForm.refundAmount || 0
              params.shipping_refund_amount = this.rejectForm.freightAmount || 0
            } else {
              // 其他情况（退货退款、换货、或仅退款不修改金额）默认传0
              params.goods_amount = 0
              params.shipping_refund_amount = 0
            }

            const res = await refundRejectAfterSale(params, { isNofilter: true })

            if (res.code === 200) {
              this.$message.success('已拒绝：' + this.currentRow.refund_order_no)
              this.rejectDialogVisible = false
              // 刷新列表
              this.$refs.tableRef.tableRequestFn()
            }
          } catch (error) {
            console.error('拒绝售后失败', error)
          }
        }
      })
    },
    async viewDetail(row) {
      // 售后详情接口对接，获取详情数据，直接赋值不做字段适配
      this.currentRow = row
      try {
        const res = await refundDetails({ refund_order_no: row.refund_order_no })
        if (res.code === 200 && res.data) {
          this.detailData = res.data // 直接赋值，页面字段全部用接口字段名
        } else {
          // 全局拦截器已经处理了错误提示
          return
        }
      } catch (error) {
        // 全局拦截器已经处理了错误提示，这里不再重复显示
        return
      }
      this.activeTab = 'order' // 重置到第一个卡片
      this.detailDialogVisible = true
    },

    // 下拉框数据
    async refundGetSearchConfig() {
      try {
        const res = await refundGetSearchConfig()

        // 设置formArray的options
        this.$set(this.formArray[2], 'options', [...res.data.refund_type_list])
        this.$set(this.formArray[3], 'options', [...res.data.refund_status_list])
      } catch (error) {
        // 全局拦截器已经处理了错误提示，这里不再重复显示
        return
      }
    },

    // 判断行是否可选择（只有待审核状态的订单可以被选择）
    isRowSelectable(row) {
      return row.status === 1 // 只有待审核状态可以选择
    },

    // 表格选择事件
    handleSelectionChange(selectedRows) {
      this.selectedRows = selectedRows
    },

    // 表格滚动事件
    tableScrollX(e) {
      this.tableLeft = e
    },

    // 批量通过
    async batchApprove() {
      if (!this.selectedRows || this.selectedRows.length === 0) {
        this.$message.warning('请先选择要操作的订单')
        return
      }

      // 检查选中的订单中是否包含换货(3)和退货退款(2)类型
      const hasReturnOrExchange = this.selectedRows.some(
        (row) => row.refund_type == 2 || row.refund_type == 3
      )

      if (hasReturnOrExchange) {
        // 需要选择地址，先获取地址列表
        try {
          const res = await refundShipmentAddressList({ type: 3 })
          if (res.code === 200) {
            if (!res.data || res.data.length === 0) {
              this.$message.error('系统未查到退货地址，请先配置退货地址')
              return
            } else {
              // 有退货地址，显示地址选择弹窗
              this.batchAddressList = res.data
              this.batchSelectedAddressId = res.data[0]?.id || null
              this.showBatchAddressDialog = true
            }
          } else {
            // 全局拦截器已经处理了错误提示
            return
          }
        } catch (error) {
          console.error('获取退货地址失败', error)
          return
        }
      } else {
        // 全部是仅退款类型，直接调用批量审核接口
        this.handleBatchApproveConfirm()
      }
    },

    // 批量地址选择弹窗确认
    async handleBatchAddressDialogOk() {
      if (!this.batchSelectedAddressId) {
        this.$message.warning('请选择退货地址')
        return
      }

      this.showBatchAddressDialog = false
      this.handleBatchApproveConfirm()
    },

    // 执行批量审核通过
    async handleBatchApproveConfirm() {
      try {
        const refundOrderNos = this.selectedRows.map((row) => row.refund_order_no)

        const params = {
          refund_order_nos: refundOrderNos,
        }

        // 如果有选择地址，添加地址ID
        if (this.batchSelectedAddressId) {
          params.address_id = this.batchSelectedAddressId
        }

        const res = await refundBatchApproveAfterSale(params)

        if (res.code === 200) {
          this.$message.success(`批量通过成功，共处理 ${refundOrderNos.length} 个订单`)
          // 清空选择
          this.selectedRows = []
          this.$refs.tableRef.clearSelection()
          // 刷新列表
          this.$refs.tableRef.tableRequestFn()
        } else {
          // 全局拦截器已经处理了错误提示
        }
      } catch (error) {
        console.error('批量审核通过失败', error)
        // 全局拦截器已经处理了错误提示，这里不再重复显示
      }
    },
    async handleApproveConfirm() {
      try {
        // 仅退款类型，不需要地址ID
        const params = {
          refund_order_no: this.approveRow.refund_order_no,
        }

        const res = await refundApproveAfterSale(params)

        if (res.code === 200) {
          this.$message.success('已通过：' + this.approveRow.refund_order_no)
          this.approveDialogVisible = false
          // 刷新列表
          this.$refs.tableRef.tableRequestFn()
        } else {
          // 全局拦截器已经处理了错误提示
        }
      } catch (error) {
        console.error('审核通过失败', error)
        // 全局拦截器已经处理了错误提示，这里不再重复显示
        return
      }
    },
    handleApproveEdit() {
      this.approveDialogVisible = false
      this.reject(this.approveRow)
    },
    // 打开运费差额计算弹窗并获取数据
    async openFreightCalculateDialog() {
      // 重置数据
      this.freightCalculateData = {
        order_shipping_fee: '',
        surplus_order_shipping_fee: '',
        diff_shipping_fee: '',
        price_adjust: '',
      }

      this.freightCalculateLoading = true
      this.freightCalculateDialogVisible = true

      try {
        const params = {
          // 售后单号
          refund_order_no: this.currentRow?.refund_order_no,
        }

        // 调用后端接口获取运费差额计算结果
        const response = await calculateFreightDifferenceApi(params)

        if (response.code === 200) {
          this.freightCalculateData = response.data
        }
      } catch (error) {
        console.error('获取运费差额计算结果失败:', error)
      } finally {
        this.freightCalculateLoading = false
      }
    },
    // 确认运费差额计算
    handleFreightCalculateConfirm() {
      if (!this.freightCalculateData.difference) {
        this.$message.warning('运费差额数据异常，请重新获取')
        return
      }
      // 将计算结果设置到运费退款金额
      this.rejectForm.freightAmount = parseFloat(this.freightCalculateData.difference)
      this.freightCalculateDialogVisible = false
      this.$message.success('运费差额计算完成，已自动填入运费退款金额')
    },

    // 详情弹窗相关方法
    getStatusText(status) {
      return this.statusMap[status]?.label || status
    },
    getAfterTypeText(type) {
      const typeMap = {
        1: '仅退款',
        2: '退货退款',
        3: '换货',
      }
      return typeMap[type] || type
    },
    getStatusClass(status) {
      return this.statusMap[status]?.type || ''
    },
    // 判断是否为审核完成状态
    isAuditCompleted(status) {
      // 审核完成的状态包括：待买家同意(2)、待买家退回(3)、待商家收货(4)、商家换新(5)、待系统退款(6)、完成(100)
      const completedStatuses = ['2', '3', '4', '5', '6', '100']
      return completedStatuses.includes(String(status))
    },
    // 获取实际退款金额
    getActualRefundAmount() {
      if (!this.detailData) return ''

      // 如果有实际退款金额字段，直接返回
      if (this.detailData.actual_refund_amount) {
        return this.detailData.actual_refund_amount
      }

      // 否则计算：申请退款金额 + 运费补偿金额
      const applyAmount = parseFloat(this.detailData.apply_amount || 0)
      const shippingAmount = parseFloat(this.detailData.shipping_refund_amount || 0)
      const total = applyAmount + shippingAmount

      return `¥${total.toFixed(2)}`
    },
    // 复制金额到剪贴板
    async copyToClipboard(amount, label) {
      if (!amount) {
        this.$message.warning('金额为空，无法复制')
        return
      }

      try {
        // 提取纯数字金额（去除¥符号等）
        const numericAmount = amount.toString().replace(/[¥,]/g, '')

        // 使用现代浏览器的 Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
          await navigator.clipboard.writeText(numericAmount)
        } else {
          // 降级方案：使用传统的 document.execCommand
          const textArea = document.createElement('textarea')
          textArea.value = numericAmount
          textArea.style.position = 'fixed'
          textArea.style.left = '-999999px'
          textArea.style.top = '-999999px'
          document.body.appendChild(textArea)
          textArea.focus()
          textArea.select()
          document.execCommand('copy')
          document.body.removeChild(textArea)
        }

        this.$message.success(`${label}已复制到剪贴板：${numericAmount}`)
      } catch (error) {
        console.error('复制失败:', error)
        this.$message.error('复制失败，请手动复制')
      }
    },
    // 图片预览相关方法
    previewImage(imageUrl, index, imageArray) {
      this.previewImages = imageArray || [imageUrl]
      this.currentImageIndex = index || 0
      this.imagePreviewVisible = true
    },
    // 上一张图片
    prevImage() {
      if (this.currentImageIndex > 0) {
        this.currentImageIndex--
      }
    },
    // 下一张图片
    nextImage() {
      if (this.currentImageIndex < this.previewImages.length - 1) {
        this.currentImageIndex++
      }
    },
    // 图片加载成功
    onImageLoad() {
      // 可以在这里添加图片加载成功的处理逻辑
    },
    // 图片加载失败
    onImageError() {
      this.$message.error('图片加载失败')
    },
    // 查看退货地址
    async handleViewReturnAddress() {
      try {
        const res = await refundShipmentAddressList({ type: 3 })
        if (res.code === 200) {
          if (!res.data || res.data.length === 0) {
            // 无退货地址，显示系统未检索到退货地址弹窗
            this.noReturnAddressDialogVisible = true
          } else {
            // 有退货地址，显示第一个地址信息
            this.selectedReturnAddress = res.data[0]
            this.returnAddressDialogVisible = true
          }
        } else {
          // 全局拦截器已经处理了错误提示
        }
      } catch (error) {
        console.error('获取退货地址失败', error)
        // 全局拦截器已经处理了错误提示，这里不再重复显示
      }
    },
    // 修改地址
    handleModifyAddress() {
      this.returnAddressDialogVisible = false
      // 这里可以跳转到地址管理页面或者其他处理逻辑
      this.$message.info('跳转到地址管理页面')
    },
    // 跳转到地址管理
    handleGoToAddressManagement() {
      this.noReturnAddressDialogVisible = false
      // 这里可以跳转到地址管理页面
      this.$message.info('跳转到地址管理页面')
    },
    // 查看物流信息
    async handleViewLogistics() {
      if (!this.detailData || !this.detailData.refund_order_no) {
        this.$message.warning('售后单号不存在')
        return
      }

      this.logisticsDialogVisible = true
      this.logisticsLoading = true
      this.logisticsData = null

      try {
        const res = await refundLogistics({
          refund_order_no: this.detailData.refund_order_no,
        })

        if (res.code === 200) {
          this.logisticsData = res.data
        } else {
          this.$message.error('获取物流信息失败')
        }
      } catch (error) {
        console.error('获取物流信息失败', error)
        this.$message.error('获取物流信息失败')
      } finally {
        this.logisticsLoading = false
      }
    },

    // 查看换新订单详情
    handleViewNewOrder() {
      if (!this.detailData || !this.detailData.new_sub_order_no) {
        this.$message.warning('换新订单号不存在')
        return
      }

      // 调用订单详情组件，传入换新订单号
      try {
        this.$refs.orderDetailDialog.open({
          sub_order_no: this.detailData.new_sub_order_no,
        })

        // 简单的位置调整
        this.$nextTick(() => {
          setTimeout(() => {
            this.adjustOrderDetailDialogPosition()
          }, 300)
        })
      } catch (error) {
        console.error('调用订单详情组件失败:', error)
        this.$message.error('打开订单详情失败')
      }
    },

    // 调整订单详情弹窗位置 - 简化版本
    adjustOrderDetailDialogPosition() {
      try {
        // 查找最后一个弹窗（通常是刚打开的订单详情弹窗）
        const dialogs = document.querySelectorAll('.el-dialog')
        if (dialogs.length > 0) {
          const lastDialog = dialogs[dialogs.length - 1]
          // 简单设置位置，避免复杂操作
          lastDialog.style.marginTop = '5vh'
          lastDialog.style.maxHeight = '90vh'

          // 设置内容区域滚动
          const body = lastDialog.querySelector('.el-dialog__body')
          if (body) {
            body.style.maxHeight = '70vh'
            body.style.overflowY = 'auto'
          }
        }
      } catch (error) {
        // 静默处理错误，不影响页面功能
      }
    },

    handleApprove(data) {
      this.detailDialogVisible = false
      this.approve(data)
    },
    handleReject(data) {
      this.detailDialogVisible = false
      this.reject(data)
    },
    async fetchOrderRecords() {
      if (!this.detailData || !this.detailData.refund_order_no) return
      this.orderRecordLoading = true
      try {
        const res = await refundRecords({
          refund_order_no: this.detailData.refund_order_no,
          page: 1,
          limit: 100,
        })
        if (res.code === 200) {
          this.orderRecordList = res.data && res.data.list ? res.data.list : []
        } else {
          this.orderRecordList = []
          // 全局拦截器已经处理了错误提示
        }
      } catch (e) {
        this.orderRecordList = []
        // 全局拦截器已经处理了错误提示，这里不再重复显示
        return
      } finally {
        this.orderRecordLoading = false
      }
    },
    // 地址选择弹窗确认
    handleAddressDialogOk() {
      const addr = this.addressList.find((a) => a.id === this.selectedAddressId)
      this.selectedAddressObj = addr
      this.showAddressDialog = false
      this.showSecondConfirmDialog = true
    },
    // 二次确认弹窗确认
    async handleSecondConfirmOk() {
      // 关闭二次确认弹窗
      this.showSecondConfirmDialog = false

      try {
        // 直接调用审核通过接口
        const params = {
          refund_order_no: this.approveRow.refund_order_no,
          address_id: this.selectedAddressObj?.id,
        }

        const res = await refundApproveAfterSale(params)

        if (res.code === 200) {
          this.$message.success('已通过：' + this.approveRow.refund_order_no)
          // 刷新列表
          this.$refs.tableRef.tableRequestFn()
        } else {
          // 全局拦截器已经处理了错误提示
        }
      } catch (error) {
        console.error('审核通过失败', error)
        // 全局拦截器已经处理了错误提示，这里不再重复显示
        return
      }
    },

    // 无需买家退货直接退款确认
    async handleNoReturnRefundConfirm() {
      try {
        // 调用接口修改售后类型为仅退款
        const res = await refundChangeReturnRefundToRefundOnly({
          refund_order_no: this.approveRow.refund_order_no,
        })

        if (res.code === 200) {
          this.$message.success('已变更为仅退款：' + this.approveRow.refund_order_no)
          // 关闭弹窗
          this.noReturnRefundDialogVisible = false
          // 刷新列表
          this.$refs.tableRef.tableRequestFn()
        } else {
          // 全局拦截器已经处理了错误提示
        }
      } catch (error) {
        console.error('修改售后类型失败', error)
        // 全局拦截器已经处理了错误提示，这里不再重复显示
        return
      }
    },
    handleGoAddressManage() {
      this.$message.info('待实现：跳转地址管理')
    },
    async handleChangeToRefundOnly() {
      // 关闭地址选择弹窗
      this.showAddressDialog = false

      // 获取退款金额数据
      const res = await refundCalculateRefundAndFreight({
        refund_order_no: this.approveRow.refund_order_no,
      })
      if (res.code === 200 && res.data) {
        this.approveData = res.data
        // 弹出无需买家退货直接退款弹窗
        this.noReturnRefundDialogVisible = true
      } else {
        // 全局拦截器已经处理了错误提示
        return
      }
    },

    // 变更为仅退款，修改金额
    async handleChangeToRefundOnlyAndModifyAmount() {
      try {
        // 先调用接口修改售后类型为仅退款
        const res = await refundChangeReturnRefundToRefundOnly({
          refund_order_no: this.approveRow.refund_order_no,
        })

        if (res.code === 200) {
          this.$message.success('已变更为仅退款')
          // 更新售后类型为仅退款
          this.approveRow.refund_type = '1'
          // 关闭当前弹窗
          this.noReturnRefundDialogVisible = false
          // 弹出拒绝弹窗
          this.reject(this.approveRow)
        } else {
          // 全局拦截器已经处理了错误提示
        }
      } catch (error) {
        console.error('修改售后类型失败', error)
        // 全局拦截器已经处理了错误提示，这里不再重复显示
        return
      }
    },
    // 获取步骤样式类
    getStepClass(status) {
      switch (status) {
        case 'finish':
        case 'end':
          return 'step-finished'
        case 'process':
        case 'selected':
          return 'step-current'
        case 'pending':
        case 'wait':
        default:
          return 'step-waiting'
      }
    },

    // 确认收货
    handleConfirmReceive(row) {
      this.confirmReceiveRow = row
      this.confirmReceiveDialogVisible = true
    },

    // 拒绝收货
    async handleRejectReceive(row) {
      try {
        // 调用拒绝售后接口，只传必要参数
        const res = await refundRejectAfterSale(
          {
            refund_order_no: row.refund_order_no,
            refund_type: row.refund_type,
            mch_remark: '',
            is_amount_modified: 'N',
            goods_amount: 0,
            shipping_refund_amount: 0,
          },
          { isNofilter: true }
        )

        if (res.code === 200) {
          this.$message.success('已拒绝收货：' + row.refund_order_no)
          this.detailDialogVisible = false
          // 刷新列表
          this.$refs.tableRef.tableRequestFn()
        } else {
          // 全局拦截器已经处理了错误提示
        }
      } catch (error) {
        console.error('拒绝收货失败', error)
        // 全局拦截器已经处理了错误提示，这里不再重复显示
        return
      }
    },

    // 确认收货弹窗确认
    async handleConfirmReceiveOk() {
      try {
        const res = await refundConfirmReturnGoodsAndReceive({
          refund_order_no: this.confirmReceiveRow.refund_order_no,
        })

        if (res.code === 200) {
          this.$message.success('已确认收货：' + this.confirmReceiveRow.refund_order_no)
          this.confirmReceiveDialogVisible = false
          this.detailDialogVisible = false
          // 刷新列表
          this.$refs.tableRef.tableRequestFn()
        } else {
          // 全局拦截器已经处理了错误提示
        }
      } catch (error) {
        console.error('确认收货失败', error)
        // 全局拦截器已经处理了错误提示，这里不再重复显示
        return
      }
    },
    // 获取连接线样式类
    getLineClass(status) {
      switch (status) {
        case 'finish':
        case 'end':
          return 'line-finished'
        case 'process':
        case 'selected':
          return 'line-current'
        case 'pending':
        case 'wait':
        default:
          return 'line-waiting'
      }
    },
    // 复制订单编号
    copyOrderNo(orderNo) {
      if (!orderNo) {
        this.$message.warning('订单编号为空')
        return
      }

      // 创建临时输入框来复制文本
      const textarea = document.createElement('textarea')
      textarea.value = orderNo
      document.body.appendChild(textarea)
      textarea.select()

      try {
        document.execCommand('copy')
        this.$message.success('订单编号已复制到剪贴板')
      } catch (err) {
        this.$message.error('复制失败，请手动复制')
      }

      document.body.removeChild(textarea)
    },
  },
}
</script>

<style scoped>
/* 单选框描述文字样式 */
.radio-desc {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
  margin: 4px 0 8px 0px;
}

/* 金额提示样式 */
.amount-tip {
  font-size: 14px;
  color: #0071fe;
  margin-top: 4px;
}

/* 拒绝弹窗按钮居中 */
.dialog-footer-center {
  text-align: center;
  padding-top: 10px;
}

.dialog-footer-center .el-button {
  margin: 0 8px;
  min-width: 80px;
}

/* 弹窗标题图标样式 */
.dialog-title-with-icon {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}

/* 运费差额计算超链接样式 */
.freight-calculate-link {
  color: #409eff;
  font-size: 12px;
  cursor: pointer;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.3s;
  display: inline-block;
}

.freight-calculate-link:hover {
  color: #66b1ff;
  border-bottom-color: #66b1ff;
}

.freight-calculate-link.loading {
  color: #c0c4cc;
  cursor: not-allowed;
}

/* 复制图标样式 */
.copy-icon {
  margin-left: 8px;
  color: #409eff;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.copy-icon:hover {
  color: #66b1ff;
  transform: scale(1.1);
}

/* 查看凭证链接样式 */
.view-evidence-link {
  color: #409eff;
  font-size: 14px;
  cursor: pointer;
  text-decoration: none;
  margin-left: 8px;
  border-bottom: 1px solid transparent;
  transition: all 0.3s;
}

.view-evidence-link:hover {
  color: #66b1ff;
  border-bottom-color: #66b1ff;
}

/* 退货地址弹窗样式 */
.return-address-content {
  padding: 20px 0;
}

.address-info {
  margin-bottom: 0;
}

.address-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 20px;
}

.address-detail {
  font-size: 14px;
  color: #303133;
  line-height: 1.5;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 6px;
  border-left: 3px solid #409eff;
}

.process-info {
  text-align: center;
}

.process-title {
  font-size: 14px;
  color: #606266;
}

/* 无退货地址弹窗样式 */
.no-address-content {
  text-align: center;
  padding: 20px 0;
}

.warning-icon {
  margin-bottom: 16px;
}

.warning-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 12px;
}

.warning-desc {
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

.modify-link {
  color: #409eff;
  cursor: pointer;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.3s;
}

.modify-link:hover {
  color: #66b1ff;
  border-bottom-color: #66b1ff;
}

/* 订单记录中的凭证图片样式 */
.record-evidence-image {
  width: 32px;
  height: 32px;
  object-fit: cover;
  border-radius: 4px;
  margin-right: 4px;
  vertical-align: middle;
  cursor: pointer;
  border: 1px solid transparent;
  transition: all 0.3s;
}

.record-evidence-image:hover {
  border-color: #409eff;
  transform: scale(1.1);
}

/* 订单详情弹窗样式 */
.order-detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.order-progress {
  padding: 16px;
  background: #fff;
  border-radius: 8px;
}

/* 基本信息样式 */
.basic-info {
  padding: 16px;
  background: #fff;
  border-radius: 8px;
}

.info-grid-basic {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px 24px;
  margin-bottom: 0;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row .label {
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
  flex-shrink: 0;
}

.info-row .value {
  color: #303133;
  margin-right: 24px;
}

.info-row .value.status {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.info-row .value.status.warning {
  background: #fdf6ec;
  color: #e6a23c;
}

.info-row .value.status.success {
  background: #f0f9ff;
  color: #67c23a;
}

.info-row .value.status.danger {
  background: #fef0f0;
  color: #f56c6c;
}

.info-row .value.amount {
  color: #f56c6c;
  font-weight: 600;
}

/* Element UI Tabs 样式 */
.el-tabs--card > .el-tabs__header {
  margin: 0 0 20px 0;
}

.el-tabs--card > .el-tabs__header .el-tabs__nav {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.el-tabs--card > .el-tabs__header .el-tabs__item {
  border-left: 1px solid #e4e7ed;
  border-top: none;
  border-bottom: none;
  border-right: none;
}

.el-tabs--card > .el-tabs__header .el-tabs__item:first-child {
  border-left: none;
}

.el-tabs__content {
  padding: 0;
}

/* 订单信息样式 */
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}
.section-title1 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px 24px;
  margin-bottom: 24px;
}

.info-grid-three {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px 24px;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  font-size: 14px;
  line-height: 1.5;
  min-height: 20px;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-item .label {
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
  flex-shrink: 0;
}

.info-item .value {
  color: #303133;
  word-break: break-all;
  flex: 1;
}

/* 商品信息表格样式 */
.product-table {
  width: 100%;
}

.product-info-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.product-info-table th,
.product-info-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e4e7ed;
  font-size: 14px;
}

.product-info-table th {
  background: #f5f7fa;
  color: #606266;
  font-weight: 600;
  border-right: 1px solid #e4e7ed;
}

.product-info-table td {
  color: #303133;
  border-right: 1px solid #e4e7ed;
}

.product-info-table th:last-child,
.product-info-table td:last-child {
  border-right: none;
}

.product-info-table tbody tr:last-child td {
  border-bottom: none;
}

.table-product-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 6px;
}

/* 订单记录表格样式 */
.record-table {
  width: 100%;
}

.record-info-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.record-info-table th,
.record-info-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e4e7ed;
  font-size: 14px;
}

.record-info-table th {
  background: #f5f7fa;
  color: #606266;
  font-weight: 600;
  border-right: 1px solid #e4e7ed;
}

.record-info-table td {
  color: #303133;
  border-right: 1px solid #e4e7ed;
}

.record-info-table th:last-child,
.record-info-table td:last-child {
  border-right: none;
}

.record-info-table tbody tr:last-child td {
  border-bottom: none;
}

.dialog-footer {
  text-align: center;
}

/* 操作按钮区域样式 */
.action-buttons-section {
  background: #ffffff;
  border-radius: 8px;
  text-align: center;
}

.action-buttons-section .el-button {
  margin: 0 8px;
  min-width: 100px;
}

/* 拒绝按钮自定义样式 */
.action-buttons-section .reject-button {
  background-color: #ffffff !important;
  border-color: #f56c6c !important;
  color: #f56c6c !important;
}

.action-buttons-section .reject-button:hover {
  background-color: #fef0f0 !important;
  border-color: #f56c6c !important;
  color: #f56c6c !important;
}

.action-buttons-section .reject-button:focus {
  background-color: #ffffff !important;
  border-color: #f56c6c !important;
  color: #f56c6c !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .info-grid-three,
  .info-grid-basic {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .info-grid,
  .info-grid-three,
  .info-grid-basic {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .info-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .info-row .label {
    margin-bottom: 4px;
  }

  .info-row .value {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .tab-content {
    padding: 16px;
  }
}

/* 订单详情弹窗样式 */
.order-detail-dialog {
  border-radius: 16px !important;
  margin-top: 5vh !important;
}

.order-detail-dialog .el-dialog__header {
  padding: 20px 24px 0 24px;
}

/* 横向步骤条样式 */
.order-progress {
  padding: 20px 0;
}

.horizontal-steps {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  position: relative;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  text-align: center;
}

.step-circle {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  position: relative;
  z-index: 2;
}

.step-number {
  font-size: 14px;
  font-weight: 500;
}

.step-check {
  font-size: 16px;
  font-weight: bold;
}

.step-content {
  max-width: 120px;
}

.step-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
  line-height: 1.2;
}

.step-description {
  font-size: 12px;
  line-height: 1.3;
  color: #909399;
}

.step-line {
  position: absolute;
  top: 16px;
  left: 50%;
  right: -50%;
  height: 2px;
  z-index: 1;
}

/* 已完成状态 */
.step-finished .step-circle {
  background-color: #0071fe;
  border: 2px solid #0071fe;
}

.step-finished .step-number,
.step-finished .step-check {
  color: #fff;
}

.step-finished .step-title {
  color: #222222;
}

.line-finished {
  background-color: #0071fe;
}

/* 当前状态 */
.step-current .step-circle {
  background-color: #0071fe;
  border: 2px solid #0071fe;
}

.step-current .step-number {
  color: #fff;
}

.step-current .step-title {
  color: #222222;
}

.line-current {
  background-color: #e4e7ed;
}

/* 等待状态 */
.step-waiting .step-circle {
  background-color: #fff;
  border: 2px solid #e4e7ed;
}

.step-waiting .step-number {
  color: #c0c4cc;
}

.step-waiting .step-title {
  color: #c0c4cc;
}

.line-waiting {
  background-color: #e4e7ed;
}

/* 图片预览弹窗样式 */
.image-preview-dialog {
  border-radius: 16px !important;
}

.image-preview-dialog .el-dialog__body {
  padding: 0 !important;
}

.image-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}

.image-preview-controls {
  display: flex;
  gap: 8px;
}

.image-preview-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  max-height: 60vh;
  background-color: #f5f5f5;
  padding: 20px;
  overflow: hidden;
}

.preview-image {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 物流链接样式 */
.logistics-link {
  color: #409eff !important;
  cursor: pointer;
  text-decoration: none;
}

.logistics-link:hover {
  color: #66b1ff !important;
  text-decoration: underline;
}

/* 物流信息弹窗样式 */
.logistics-dialog {
  border-radius: 16px !important;
}

.logistics-dialog .el-dialog__body {
  max-height: 60vh !important;
  overflow-y: auto !important;
  padding: 20px !important;
}

.logistics-content {
  padding: 0;
}

.logistics-basic-info {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.logistics-basic-info .info-row {
  display: flex;
  margin-bottom: 8px;
}

.logistics-basic-info .info-row:last-child {
  margin-bottom: 0;
}

.logistics-basic-info .label {
  font-weight: 500;
  color: #606266;
  width: 80px;
  flex-shrink: 0;
}

.logistics-basic-info .value {
  color: #303133;
  flex: 1;
}

.logistics-timeline {
  margin-top: 20px;
}

.timeline-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
  position: sticky;
  top: 0;
  background-color: #fff;
  z-index: 1;
}

.timeline-content {
  padding-left: 8px;
  max-height: 40vh;
  overflow-y: auto;
}

.timeline-context {
  font-size: 14px;
  color: #303133;
  line-height: 1.5;
  margin-bottom: 4px;
}

.timeline-location,
.timeline-status {
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.location-label,
.status-label {
  font-weight: 500;
}

.logistics-loading,
.logistics-error,
.no-logistics-info {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.logistics-loading i {
  font-size: 24px;
  margin-right: 8px;
}

.logistics-error {
  color: #f56c6c;
}

.no-logistics-info {
  color: #909399;
}
</style>

<style scoped lang="scss">
.after-order-list ::v-deep {
  /* 正常生效 */
  /* 运费差额计算弹窗圆角样式 */
  .freight-calculate-dialog {
    border-radius: 16px !important;
  }

  /* 隐藏弹窗分割线 */
  .freight-calculate-dialog .el-dialog__body {
    border-top: none !important;
    padding: 5px 50px;
  }

  .freight-calculate-dialog .el-dialog__footer {
    border-top: none !important;
  }

  /* 审核通过弹窗样式 */
  .approve-dialog {
    border-radius: 16px !important;
  }

  .approve-dialog .el-dialog__header {
    border-bottom: none !important;
  }

  .approve-dialog .el-dialog__footer {
    border-top: none !important;
  }

  .approve-dialog .el-dialog__body {
    border-top: none !important;
    padding: 0 50px 20px 44px;
  }

  /* 审核通过退货退款弹窗样式 */
  .showAddress-dialog {
    border-radius: 16px !important;
  }

  .showAddress-dialog .el-dialog__header {
    border-bottom: none !important;
  }

  .showAddress-dialog .el-dialog__footer {
    border-top: none !important;
    padding-top: 0;
  }

  .showAddress-dialog .el-dialog__body {
    border-top: none !important;
    padding: 0 50px 0 44px;
  }

  /* 自定义警告框样式 - 黑色文字 */
  .himalaya-alert .el-alert__title {
    color: #1a1a1a;
  }
  .el-alert--warning.is-light {
    color: #ffba53;
    background-color: #fffbe6;
  }

  /* 退货地址弹窗样式 */
  .return-address-dialog {
    border-radius: 16px !important;
  }

  .return-address-dialog .el-dialog__header {
    display: none !important;
  }

  .return-address-dialog .el-dialog__footer {
    border-top: none !important;
  }

  .return-address-dialog .el-dialog__body {
    border-top: none !important;
    padding: 0 50px 0 44px;
  }

  /* 无退货地址弹窗样式 */
  .no-return-address-dialog {
    border-radius: 16px !important;
  }

  .no-return-address-dialog .el-dialog__body {
    border-top: none !important;
    padding: 0 50px 0 44px;
  }

  .no-return-address-dialog .el-dialog__header {
    display: none !important;
  }

  .no-return-address-dialog .el-dialog__footer {
    border-top: none !important;
  }

  /* 防止弹窗打开时页面抖动 */
  .v-modal {
    padding-right: 0 !important;
  }

  body.el-popup-parent--hidden {
    padding-right: 0 !important;
    overflow: visible !important;
  }

  /* 强制移除ElementUI弹窗的滚动条补偿 */
  .el-popup-parent--hidden {
    padding-right: 0 !important;
  }

  /* 防止弹窗遮罩层导致的抖动 */
  .el-dialog__wrapper {
    overflow: visible !important;
  }

  /* 全局防抖动设置 */
  html {
    overflow-y: scroll !important;
  }

  /* 强制保持滚动条始终显示 */
  body {
    overflow-y: scroll !important;
  }

  /* 防止ElementUI弹窗修改body样式 */
  body.el-popup-parent--hidden {
    overflow: visible !important;
    padding-right: 0 !important;
    width: auto !important;
  }

  /* 订单详情弹窗居中样式 - 简单安全的CSS方案 */
  .el-dialog[aria-label='订单详情'] {
    margin-top: 5vh !important;
    margin-bottom: 5vh !important;
    max-height: 90vh !important;
  }

  .el-dialog[aria-label='订单详情'] .el-dialog__body {
    max-height: 80vh !important;
    overflow-y: auto !important;
  }

  /* 如果上面的选择器不生效，使用更通用的方式 */
  .el-dialog__wrapper .el-dialog {
    margin-top: 8vh !important;
  }

  /* 确保我们自己页面的弹窗保持原有位置 */
  .order-detail-dialog,
  .approve-dialog,
  .freight-calculate-dialog,
  .showAddress-dialog,
  .logistics-dialog,
  .image-preview-dialog,
  .return-address-dialog,
  .no-return-address-dialog {
    margin-top: 15vh !important;
  }

  .el-table-column--selection .cell {
    padding-left: 10px;
    padding-right: 10px;
  }
}
.no-tip {
  position: absolute;
  font-size: 25px;
  z-index: 200;
  background: #fff;
  width: 100%;
  height: 100%;
  text-align: center;
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;
}
</style>
