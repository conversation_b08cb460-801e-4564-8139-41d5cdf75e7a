<template>
  <div>
    <el-card class="store-model">
      <el-input
        style="width: 48%"
        @keyup.enter.native="keyDownEnter"
        v-model="searchInputValue"
        type="text"
        :placeholder="placeholder"
      >
        <el-button @click="searchValue" class="searchValue" slot="append">搜索</el-button>
      </el-input>
      <!-- 当前自提点 -->
      <div class="content">
        <div class="contentTop flex-b">
          <span class="contentTopLeft">
            当前自提点：{{ curOrderPicket ? curOrderPicket.name : '无' }}
          </span>
          <div>
            <el-button @click="changeAddress" size="big" type="text">切换自提点</el-button>
            <el-button @click="LookOrderRecords" size="big" type="text">查看核销记录</el-button>
          </div>
        </div>
        <div class="contentBottom">
          <div class="item">
            <span>联系人：</span>
            <span>{{ curOrderPicket ? curOrderPicket.manage_name : '无' }}</span>
          </div>
          <div class="item">
            <span>联系电话：</span>
            <span>{{ curOrderPicket ? curOrderPicket.phone : '无' }}</span>
          </div>
          <div class="item">
            <span>地址：</span>
            <span>{{ curOrderPicket ? curOrderPicket.address : '无' }}</span>
          </div>
        </div>
      </div>
      <!-- 核销列表 -->
      <div class="content_HX">
        <el-row :gutter="24">
          <el-col :xs="12" :sm="24" :md="24" :lg="24" :xl="24">
            <div class="contentCenter" v-for="(item, index) in batchVerifyRecordList">
              <div class="contentCenterTop">
                <span class="sort">0{{ index + 1 }}</span>
                <span class="delete" @click="deleteOrderList(item, index)">
                  <i class="el-icon-delete"></i>
                </span>
              </div>
              <div class="contentCenterBottom">
                <div class="item">
                  <span>订单号：{{ item.sub_order_no }}</span>
                  <span @click="lookItemDetail(item)" class="detail">查看详情</span>
                </div>
                <div class="item">
                  <span>核销码：{{ item.code }}</span>
                </div>
                <div class="item">
                  <span>下单时间：{{ item.created_at }}</span>
                </div>
                <div class="item">
                  <span>订单实付金额：￥{{ item.order_amount }}</span>
                </div>
                <div class="item">
                  <span>下单人：{{ item.username }}</span>
                </div>
                <div class="item">
                  <span>联系电话：{{ item.phone }}</span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-button v-if="batchVerifyRecordList.length" @click="batchVerifyFn" type="primary">
        确认核销{{ batchVerifyRecordList.length }}个订单
      </el-button>
    </el-card>

    <!-- 核销确认 1无码核销  2 扫码核销-->
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="showHXconfirm"
      width="480px"
    >
      <template slot="title">
        <div class="flex">
          <img class="tip_icon" v-if="pickup_verification_type == 0" :src="imgPath" alt />
          <span class="title">核销确认</span>
        </div>
      </template>
      <div v-if="this.pickup_verification_type == 0">
        <div class="codevalueno">确认核销此订单吗？</div>
        <div class="codevalueno">请仔细核对联系人信息，避免冒领或者错误核销造成损失</div>
      </div>
      <div v-if="this.pickup_verification_type == 1">
        <div class="codevalue">请输入核销码完成订单核销</div>
        <el-input style="width: 100%" v-model="hxcode" placeholder="请输入核销码"></el-input>
      </div>
      <base-dialog-footer
        confirmText="确认"
        @cancel="showHXconfirm = false"
        @confirm="confirmHXBtn"
      ></base-dialog-footer>
    </el-dialog>
    <!-- 订单详情 -->
    <orderDetail :itemList="rowIndexList" ref="orderDetailDialog"></orderDetail>
    <!-- 切换自提点 -->
    <OrderAddress ref="changeOrderAddresslDialog" @close="closeOrderAddress"></OrderAddress>
    <!-- 核销记录 -->
    <OrderRecords ref="showOrderRecordslDialog"></OrderRecords>
  </div>
</template>

<script>
  import store from '@/store'
  import { mapGetters } from 'vuex'
  import { getOrderListApi, batchVerifyRecord, batchVerify } from '@/api/shop/orderVerification'
  import orderDetail from './components/orderVerifDetail.vue'
  import OrderAddress from './components/changeOrderAddress.vue'
  import OrderRecords from './components/showOrderRecords.vue'
  import { settingDetailApi } from '@/api/shop/order'
  import { getmaparrList } from '@/utils/index'
  export default {
    name: 'orderVerification',
    components: {
      orderDetail,
      OrderAddress,
      OrderRecords,
    },
    data() {
      return {
        imgPath: require('../../../../assets/warn_20250626.png'),
        searchInputValue: '', // '725VU35KCLH,725VU35KCLH1,725VU35KCLH2', //搜索
        rowIndexList: [],
        curOrderPicket: {}, //当前自提地点
        pickup_verification_type: '', //自提核销方式  0无码1扫码
        batchVerifyRecordList: [], //批量核销列表
        showHXconfirm: false,
        loading: true,
        placeholder: '',
        hxcode: '', //订单核销码
      }
    },
    computed: {
      ...mapGetters({
        hxOrderList: 'goodsDetaile/hxOrderList',
      }),
    },
    created() {
      console.log()
      if (this.hxOrderList != '') {
        // this.searchValue()
      }
      this.getOrderListApiFn()
      this.getSetDetail()
    },
    methods: {
      //批量核销列表
      searchValue() {
        if (!this.searchInputValue) {
          this.$message.error('请输入核销码')
          return false
        }
        batchVerifyRecord({
          codes: this.searchInputValue
            .split(/[，,]+/) // 直接按中英文逗号分割
            .map((s) => s.trim())
            .filter((s) => s)
            .join(),
          sub_order_nos: this.hxOrderList,
        }).then((res) => {
          if (res.code == 200) {
            this.batchVerifyRecordList = res.data
          }
        })
      },
      keyDownEnter(e) {
        console.log(11)
        var keyCode = window.event ? e.keyCode : e.which
        if (keyCode === 13) {
          this.searchValue() // 搜索按钮的回调
        }
      },
      //切换自提点
      changeAddress() {
        this.$refs.changeOrderAddresslDialog.open()
      },
      //查看核销记录
      LookOrderRecords() {
        this.$refs.showOrderRecordslDialog.open()
      },

      // 表单搜索事件
      searchForm(form) {
        this.tableForm = Object.assign({}, this.tableForm, form)
      },
      //获取当前店铺自提点
      getOrderListApiFn() {
        getOrderListApi().then((res) => {
          if (res.code == 200) {
            this.curOrderPicket = res.data
          }
        })
      },
      //核销确认
      batchVerifyFn() {
        this.showHXconfirm = true
      },
      //删除
      deleteOrderList(data, i) {
        this.batchVerifyRecordList = getmaparrList(
          this.batchVerifyRecordList,
          'code',
          data.code,
          false
        )
        this.searchInputValue = this.searchInputValue
          .split(',') // 将字符串拆分成数组
          .filter((item) => item !== data.code) // 过滤掉严格等于目标子串的项
          .join(',')
      },
      //查看详情
      lookItemDetail(data) {
        this.rowIndexList = data
        this.$refs.orderDetailDialog.open(this.rowIndexList)
      },

      // 核销确认
      confirmHXBtn() {
        let sub_order_noStr = ''
        this.batchVerifyRecordList.forEach((item) => {
          sub_order_noStr += item.sub_order_no + ','
        })
        sub_order_noStr = sub_order_noStr.substring(0, sub_order_noStr.length - 1)
        let params = {
          code: this.hxcode,
          sub_order_no: sub_order_noStr,
        }
        verifyOrder(params).then((res) => {
          if (res.code == 200) {
            this.$message.success('核销成功')
            this.showHXconfirm = false
            this.searchValue()
          } else {
            this.searchValue()
          }
        })
      },
      // 获取详情数据
      getSetDetail() {
        settingDetailApi().then((res) => {
          if (res.code == 200) {
            this.pickup_verification_type = res.data.pickup_verification_type
            this.placeholder =
              this.pickup_verification_type == '0'
                ? '请输入核销码/下单人/手机号/订单号'
                : '请输入/扫描核销码'
          }
        })
      },
      //自提点关闭
      closeOrderAddress() {
        this.getOrderListApiFn()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .el-button.searchValue {
    color: #fff;
    background: #0071fe;
    border-radius: 0px 4px 4px 0px;
  }
  .content {
    width: 48%;
    min-width: 380px;
    margin-top: 10px;
    border: 1px solid #e9e9eb;
    border-radius: 8px;
    .contentTop {
      margin: 0 20px;
      border-bottom: 1px solid #e9e9eb;
      .contentTopLeft {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 14px;
      }
    }
    .contentBottom {
      margin-top: 15px;
      margin-left: 20px;

      .item {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #6e6e7a;
        line-height: 14px;
        margin-bottom: 12px;
      }
    }
  }
  .content_HX {
    margin-top: 10px;
    flex-wrap: wrap;
    height: 100%;
    .contentCenter {
      min-width: 380px;
      width: 48%;
      border: 1px solid #e9e9eb;
      margin-bottom: 20px;
      border-radius: 8px;
      .contentCenterTop {
        margin: 0 20px;
        padding: 10px 0;
        border-bottom: 1px solid #f4f4f5;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        .sort {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.85);
        }
        .delete {
          font-size: 18px;
        }
      }
      .contentCenterBottom {
        margin-top: 15px;
        margin-left: 20px;
        .detail {
          margin-left: 10px;
          color: #0071fe;
          cursor: pointer;
        }
        .item {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #6e6e7a;
          line-height: 14px;
          margin-bottom: 12px;
        }
      }
    }
    .contentCenter:nth-child(odd) {
      margin-right: 20px;
    }
  }

  .title {
    font-weight: 500;
    font-size: 16px;
    color: #222222;
  }
  .codevalue {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #222222;
    text-align: center;
    margin-bottom: 10px;
  }
  .codevalueno {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #606266;
    line-height: 14px;
    text-align: center;
    margin-bottom: 10px;
  }
  html body div::-webkit-scrollbar {
    width: 6px;
  }
  .el-col {
    display: flex;
    flex-wrap: wrap;
  }
</style>
