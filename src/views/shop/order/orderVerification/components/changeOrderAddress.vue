<!--
 * @Author: liqian <EMAIL>
 * @Email: <EMAIL>
 * @Date: 2025-06-23 11:21:38
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-08-02 15:54:34
 * @FilePath: \qst-merchant-admin-2.0\src\views\shop\order\orderVerification\components\changeOrderAddress.vue
 * @Description: 
-->
<template>
  <div>
    <!-- 切换自提点 -->
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="showAddressOrder"
      title="切换自提点"
      width="650px"
    >
      <div class="contentmain">
        <div class="flex-b dialogcontent" v-for="item in pickupPointList">
          <div class="dialogleft">
            <div class="item item1">当前自提点：{{ item.name }}</div>
            <div class="item">
              <span>联系人：{{ item.manage_name }}</span>
            </div>
            <div class="item">
              <span>联系电话：{{ item.phone }}</span>
            </div>
            <div class="item">
              <span>地址：{{ item.address }}</span>
            </div>
          </div>
          <div class="contentBottom">
            <el-button v-if="item.is_bind == 1">当前选择</el-button>
            <el-button v-else type="primary" @click="selectAddress(item)">选择</el-button>
          </div>
        </div>
      </div>
      <base-dialog-footer
        confirmText="确定"
        @cancel="showAddressOrder = false"
        @confirm="confirm"
      ></base-dialog-footer>
    </el-dialog>
    <!-- 验证手机号 -->
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="showPhoneBind"
      width="550px"
    >
      <template slot="title">
        <span>验证手机号</span>
      </template>
      <el-form ref="basicInfo" :model="form" :rules="rules" label-width="80px" label-position="top">
        <el-form-item prop="sms_code">
          <el-input
            style="width: 100%"
            v-model="form.sms_code"
            placeholder="请输入手机验证码"
          ></el-input>
          <div class="codetip">验证码已发送至管理员手机，请注意查收</div>
        </el-form-item>
      </el-form>
      <base-dialog-footer
        confirmText="确定"
        @cancel="showPhoneBind = false"
        @confirm="confirmPhone"
      ></base-dialog-footer>
    </el-dialog>
  </div>
</template>
<script>
  import {
    switchPickupPointList,
    verifyCodeBinding,
    sendVerificationCode,
  } from '@/api/shop/orderVerification'
  export default {
    name: 'changeOrderAddress',
    components: {},
    props: {},
    data() {
      return {
        showAddressOrder: false,
        showPhoneBind: false,
        form: {
          sms_code: '',
        },
        rules: {
          sms_code: [{ required: true, message: '请输入手机验证码', trigger: 'blur' }],
        },
        pickupPointList: [], //自提点列表
        selectPicketAddress: {}, //选择的自提地址
      }
    },
    methods: {
      open() {
        this.switchPickupPointListfn()
        this.showAddressOrder = true
      },
      //切换自提点确定
      confirm() {
        this.showAddressOrder = false
      },

      // 切换店铺自提点列表
      switchPickupPointListfn() {
        switchPickupPointList().then((res) => {
          if (res.code == 200) {
            this.pickupPointList = res.data
          }
        })
      },
      //选择自提点地址
      selectAddress(data) {
        this.selectPicketAddress = data
        sendVerificationCode({ phone: data.phone }).then((res) => {
          if (res.code == 200) {
            this.form.sms_code = ''
            this.$nextTick(() => {
              this.showPhoneBind = true
              setTimeout(() => {
                this.$message.success('验证码已发送至管理员手机，请注意查收')
              }, 1000)
            })
          }
        })
      },
      //手机号绑定
      confirmPhone() {
        this.$refs.basicInfo.validate((valid) => {
          if (valid) {
            let params = {
              phone: this.selectPicketAddress.phone,
              sms_code: this.form.sms_code,
              is_debug: '1',
              address_id: this.selectPicketAddress.id,
            }
            verifyCodeBinding(params).then((res) => {
              if (res.code == 200) {
                this.showPhoneBind = false
                this.$message.success('切换自提点成功')
                this.switchPickupPointListfn()
                this.showAddressOrder = false
                this.$emit('close')
              }
            })
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .contentmain {
    max-height: 500px;
    overflow: auto;
    .dialogcontent {
      border-bottom: 1px solid #f4f4f5;
      margin-bottom: 20px;
      padding-bottom: 8px;
      .dialogleft {
        .item {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #6e6e7a;
          line-height: 14px;
          margin-bottom: 12px;
          font-style: normal;
        }
        .item1 {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.85);
          line-height: 14px;
        }
      }
    }
  }
  .codetip {
    margin-top: 10px;
  }
</style>
