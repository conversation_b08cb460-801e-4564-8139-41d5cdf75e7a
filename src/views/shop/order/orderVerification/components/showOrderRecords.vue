<template>
  <div>
    <!-- 核销记录 -->
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="showOrderRecords"
      title="核销记录"
      width="750px"
    >
      <base-form
        ref="baseForm"
        :tableForm="tableForm"
        :formArray="formArray"
        @searchForm="searchForm"
      ></base-form>
      <div class="operate">
        <el-button type="primary" icon="el-icon-plus" @click="exportRecords">导出记录</el-button>
      </div>
      <base-table
        :tableColumn="tableColumn"
        :tableRequest="verifyRecord"
        :tableForm="tableForm"
        ref="tableRef"
      >
        <template #status="{ scope }">
          <span class="statusSty">{{ order_status[scope.row.status] }}</span>
        </template>
      </base-table>
    </el-dialog>
  </div>
</template>
<script>
  import {
    verifyRecord,
    exportVerifyRecord,
    switchPickupPointList,
  } from '@/api/shop/orderVerification'

  export default {
    name: 'showOrderRecords',
    components: {},
    props: {},
    data() {
      return {
        showOrderRecords: false,
        // 表单
        tableForm: {},
        verifyRecord,
        order_status: ['待核销', '已核销', '已失效'], //核销状态: 0=待核销, 1=已核销, 2=已失效
        // 表单key
        formArray: [
          {
            label: '订单号',
            type: 'input',
            key: 'sub_order_no',
            placeholder: '请输入订单编号',
          },
          {
            label: '核销码',
            type: 'input',
            key: 'code',
            placeholder: '请输入核销码',
          },
          {
            label: '核销时间',
            type: 'time',
            key: 'time',
            timeKey: ['start_time', 'end_time'],
          },
          {
            label: '操作人',
            type: 'input',
            key: 'username',
            placeholder: '请输入操作人',
          },
          {
            label: '自提地址',
            type: 'select',
            key: 'search_pickup_id',
            placeholder: '请选择自提地址',
            options: [],
          },
        ],
        // 表格配置
        tableColumn: [
          {
            label: '核销记录ID',
            prop: 'id',
          },
          {
            label: '订单号',
            prop: 'sub_order_no',
            width: '140',
          },
          {
            label: '核销码',
            prop: 'code',
            width: '140',
          },
          {
            label: '核销时间',
            prop: 'verify_time',
            width: '200px',
          },
          {
            label: '操作人',
            prop: 'username',
          },
          {
            label: '自提点',
            prop: 'name',
            width: '100px',
          },

          {
            label: '状态',
            type: 'customize',
            prop: 'status',
          },
        ],
      }
    },
    methods: {
      // 表单搜索事件
      searchForm(form) {
        this.tableForm = Object.assign({}, this.tableForm, form)
      },
      open() {
        this.showOrderRecords = true
        this.switchPickupPointListfn()
      },
      switchPickupPointListfn() {
        switchPickupPointList().then((res) => {
          if (res.code == 200) {
            let addressList = res.data.map((item) => {
              return {
                label: item.name,
                value: item.id,
              }
            })
            this.$set(this.formArray[4], 'options', [...addressList])
          }
        })
      },
      //导出记录
      exportRecords() {
        exportVerifyRecord({ ...this.tableForm }).then((res) => {
          console.log(res)
          if (res.code == 200) {
            this.downloadFile(res.data)
          }
        })
      },
      downloadFile(url, filename) {
        const link = document.createElement('a')
        link.setAttribute('downloadFile', '')
        link.href = url
        // link.download = filename || 'file'; // 设置下载文件名
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      },
    },
  }
</script>
<style lang="scss" scoped>
  .operate {
    margin-top: 20px;
    text-align: right;
  }
</style>
