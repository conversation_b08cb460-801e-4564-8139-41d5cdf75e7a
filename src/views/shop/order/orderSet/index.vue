<!--
 * @FilePath: \qst-merchant-admin-2.0\src\views\shop\order\orderSet\index.vue
 * @Description: 订单设置
-->
<template>
  <div>
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div></div>
          <div>订单基础设置</div>
        </div>
      </template>
      <el-form ref="form" :model="form" :rules="formRule">
        <el-form-item prop="auto_confirm_days">
          <div>
            发货后自动确认收货时间：发货后
            <el-input-number
              v-model="form.auto_confirm_days"
              :controls="false"
              :min="10"
              :max="30"
              :precision="0"
              placeholder="请输入"
              style="margin: 0 5px"
            />天，自动确认收货（仅针对于新订单生效）；
          </div>
        </el-form-item>
        <el-form-item prop="unpaid_timeout">
          <div>
            未支付订单自动关闭：拍下
            <el-input-number
              v-model="form.unpaid_timeout"
              :controls="false"
              :min="10"
              :precision="0"
              placeholder="请输入"
              style="margin: 0 5px"
            />分钟未付款，自动关闭订单。
          </div>
        </el-form-item>
        <el-form-item>
          一个订单是否支持多收货地址？
          <el-radio-group v-model="form.multiple_addresses">
            <el-radio label="Y">支持</el-radio>
            <el-radio label="N">不支持</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          待付款订单是否支持改价
          <el-radio-group v-model="form.allow_edit_price">
            <el-radio label="Y">支持改价</el-radio>
            <el-radio label="N">不支持改价</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          改价维度：
          <el-radio-group v-model="form.price_edit_scope" :disabled="form.allow_edit_price == 'N'">
            <el-radio label="order">订单维度（改价金额会根据比例自动分配到各商品）</el-radio>
            <el-radio label="product">商品维度（可手动对订单种某商品进行改价，不影响其他商品金额）</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div></div>
          <div>售后设置</div>
        </div>
      </template>
      <el-form ref="afterForm" :model="afterForm" label-position="top" :rules="printRule">
        <el-row :gutter="24">
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="支持仅退款的订单状态" prop="refund_only_status">
              <el-select
                @change="afterChange"
                v-model="afterForm.refund_only_status"
                multiple
                placeholder="请选择"
                class="w100"
              >
                <el-option
                  v-for="item in refund_nodes"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item>
              <template slot="label">
                仅退款自动审核通过的订单状态
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="若买家所选退款原因需退运费，会按商品金额比例分摊运费，可能造成运费多退，请谨慎选择！"
                  placement="top"
                >
                  <img
                    :src="imgOssPath + '20250619_sf_wenhao.png'"
                    alt
                    class="icon_wenhao"
                    style="transform: translateY(2px)"
                  />
                </el-tooltip>
              </template>
              <el-select
                v-model="afterForm.approve_order_status"
                multiple
                placeholder="请先选择支持仅退款的订单状态"
                class="w100"
              >
                <el-option
                  v-for="item in afteroptions1_1"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="支持退货退款的订单状态" prop="return_refund_status">
              <el-select
                v-model="afterForm.return_refund_status"
                multiple
                placeholder="请选择"
                class="w100"
              >
                <el-option
                  v-for="item in afteroptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="支持换货的订单状态" prop="exchange_status">
              <el-select
                v-model="afterForm.exchange_status"
                multiple
                placeholder="请选择"
                class="w100"
              >
                <el-option
                  v-for="item in afteroptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item prop="after_sales_days">
              <template slot="label">
                交易完成支持售后节点
                <el-tooltip class="item" effect="dark" content="可填写范围15-90天" placement="top">
                  <img
                    :src="imgOssPath + '20250619_sf_wenhao.png'"
                    alt
                    class="icon_wenhao"
                    style="transform: translateY(2px)"
                  />
                </el-tooltip>
              </template>
              <div>
                交易完成
                <el-input-number
                  :disabled="getaftersales"
                  v-model="afterForm.after_sales_days"
                  :controls="false"
                  :min="15"
                  :max="90"
                  :precision="0"
                  placeholder="请输入"
                  style="margin: 0 5px"
                />天后，不支持售后
              </div>
            </el-form-item>
          </el-col>

          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item prop="sales_timeout_method">
              <template slot="label">
                商家超时未处理售后单处理方式
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="售后单审核超时未审批自动默认通过；待商家收货超时未确认自动确认收货"
                  placement="top"
                >
                  <img
                    :src="imgOssPath + '20250619_sf_wenhao.png'"
                    alt
                    class="icon_wenhao"
                    style="transform: translateY(2px)"
                  />
                </el-tooltip>
              </template>
              <div class="flex">
                <div style="margin-right: 20px">
                  <el-radio-group v-model="afterForm.sales_timeout_method">
                    <el-radio label="1">自动通过</el-radio>
                    <el-radio label="2">暂不处理</el-radio>
                  </el-radio-group>
                </div>
                <div v-if="afterForm.sales_timeout_method == '1'">
                  商家
                  <el-input-number
                    v-model="afterForm.sales_timeout_days"
                    :controls="false"
                    :min="1"
                    :max="7"
                    :precision="0"
                    placeholder="请输入"
                    style="margin: 0 5px"
                  />天后未处理售后订单自动通过处理
                </div>
              </div>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <el-form-item prop="sales_negotiate_days">
              <template slot="label">
                买家售后单设置
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="售后单待买家寄回/协商待买家同意，买家超时未处理售后单自动关闭"
                  placement="top"
                >
                  <img
                    :src="imgOssPath + '20250619_sf_wenhao.png'"
                    alt
                    class="icon_wenhao"
                    style="transform: translateY(2px)"
                  />
                </el-tooltip>
              </template>

              <div>
                买家
                <el-input-number
                  v-model="afterForm.sales_negotiate_days"
                  :controls="false"
                  :min="1"
                  :max="3"
                  :precision="0"
                  placeholder="请输入"
                  style="margin: 0 5px"
                />天未操作售后单自动关闭
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div></div>
          <div>售后原因维护</div>
        </div>
      </template>
      <el-table class="base-table" :data="order_refund_reason_configs" style="width: 100%">
        <el-table-column prop="reason" label="退款原因">
          <template slot-scope="scope">
            <el-input :disabled="scope.$index <= 6" v-model="scope.row.reason" placeholder="请输入内容"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="refund_type" label="售后类型">
          <template slot-scope="scope">
            <el-select
              :disabled="scope.$index <= 6"
              v-model="scope.row.refund_type"
              multiple
              placeholder="请选择"
              class="w100"
            >
              <el-option
                v-for="item in refund_config_nodes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="is_supported" label="是否退运费" width="200">
          <template slot-scope="scope">
            <el-switch
              :disabled="scope.$index <= 6"
              :active-value="'Y'"
              :inactive-value="'N'"
              v-model="scope.row['is_supported']"
            ></el-switch>
            <span>{{ scope.row.is_supported == 'Y' ? '是' : '否' }}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              v-if="scope.$index > 6"
              class="del-color"
              size="mini"
              type="text"
              @click="handledelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div @click="addTemplate" class="addTemplate">
        <i class="el-icon-plus"></i>
        添加退款原因
      </div>
    </el-card>

    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div></div>
          <div>自提设置</div>
        </div>
      </template>
      <el-form ref="setForm" :model="setForm" label-position="top" :rules="printRule">
        <el-row :gutter="24">
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item prop="stock_nodes">
              <template slot="label">
                是否需要备货节点
                <el-tooltip class="item" effect="dark" content="仅对修改后新生成的订单有效" placement="top">
                  <img
                    :src="imgOssPath + '20250619_sf_wenhao.png'"
                    alt
                    class="icon_wenhao"
                    style="transform: translateY(2px)"
                  />
                </el-tooltip>
              </template>
              <el-radio-group v-model="setForm.stock_nodes">
                <el-radio label="Y">需要</el-radio>
                <el-radio label="N">不需要</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="自提核销方式" prop="pickup_verification_type">
              <el-radio-group v-model="setForm.pickup_verification_type">
                <el-radio label="0">无码核销</el-radio>
                <el-radio label="1">扫码核销</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div></div>
          <div>打印设置</div>
        </div>
      </template>
      <el-form ref="printForm" :model="printForm" label-position="top" :rules="printRule">
        <el-row :gutter="24">
          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <el-form-item label="出库单打印设置" class="no-bottom"></el-form-item>
          </el-col>

          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="可打印节点" prop="outbound_nodes">
              <el-select v-model="printForm.outbound_nodes" multiple placeholder="请选择" class="w100">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="打印份数" prop="outbound_copies">
              <el-input-number
                class="w100"
                :controls="false"
                v-model="printForm.outbound_copies"
                :min="1"
                :precision="0"
                placeholder="请输入打印份数"
              />
            </el-form-item>
          </el-col>

          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="默认打印模板" prop="outbound_template">
              <el-select v-model="printForm.outbound_template" placeholder="请选择" class="w100">
                <el-option
                  v-for="item in templateList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <el-form-item label="小票打印设置" class="no-bottom"></el-form-item>
          </el-col>

          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="可打印节点" prop="receipt_nodes">
              <el-select v-model="printForm.receipt_nodes" multiple placeholder="请选择" class="w100">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="打印份数" prop="receipt_copies">
              <el-input-number
                class="w100"
                :controls="false"
                v-model="printForm.receipt_copies"
                :min="1"
                :precision="0"
                placeholder="请输入打印份数"
              />
            </el-form-item>
          </el-col>

          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="默认打印模板" prop="receipt_template">
              <el-select v-model="printForm.receipt_template" placeholder="请选择" class="w100">
                <el-option
                  v-for="item in templateListXP"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <el-form-item label="电子面单打印设置" class="no-bottom"></el-form-item>
          </el-col>

          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="可打印节点" prop="waybill_nodes">
              <el-select v-model="printForm.waybill_nodes" multiple placeholder="请选择" class="w100">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="打印份数" prop="waybill_copies">
              <el-input-number
                class="w100"
                :controls="false"
                v-model="printForm.waybill_copies"
                :min="1"
                :precision="0"
                placeholder="请输入打印份数"
              />
            </el-form-item>
          </el-col>

          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="默认承运商" prop="waybill_carrier">
              <el-select v-model="printForm.waybill_carrier" placeholder="请选择" class="w100">
                <el-option
                  v-for="item in carrierList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <el-form-item>
              <el-button type="primary" @click="saveSetDetail">保存设置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import {
  settingDetailApi,
  settingSaveApi,
  templateListSelectApi,
  getCarrierList,
  setNodeList,
} from '@/api/shop/order.js'
export default {
  name: 'orderSet',
  data() {
    return {
      id: '',
      form: {},
      order_refund_reason_configs: [
        // { reason: '商品质量问题', refund_type: [1, 2, 3], is_supported: 'Y' },
        // { reason: '收到商品与描述不符', refund_type: [1, 2, 3], is_supported: 'Y' },
        // { reason: '未按约定时间发货', refund_type: [1, 2], is_supported: 'Y' },
        // { reason: '发错货/漏发', refund_type: [1, 2, 3], is_supported: 'Y' },
        // { reason: '商品降价/少用了优惠券', refund_type: [1, 2], is_supported: 'Y' },
        // { reason: '不想要了', refund_type: [1, 2], is_supported: 'N' },
        // { reason: '其他原因', refund_type: [1, 2, 3], stais_supportedtus: 'N' },
      ],
      options: [
        // {
        //   value: '1',
        //   label: '待发货',
        // },
        // {
        //   value: '2',
        //   label: '待收货',
        // },
        // {
        //   value: '3',
        //   label: '已完成',
        // },
      ],
      afteroptions1_1: [],
      refund_nodes: [
        // {
        //   value: '1',
        //   label: '待发货',
        // },
        // {
        //   value: '2',
        //   label: '待收货',
        // },
        // {
        //   value: '3',
        //   label: '已完成',
        // },
        // {
        //   value: '7',
        //   label: '备货中',
        // },
        // {
        //   value: '8',
        //   label: '待核销',
        // },
      ],
      afteroptions: [
        {
          value: '2',
          label: '待收货',
        },
        {
          value: '3',
          label: '已完成',
        },
      ],
      refund_config_nodes: [
        // {
        //   value: '1',
        //   label: '退货退款',
        // },
        // {
        //   value: '2',
        //   label: '仅退款',
        // },
        // {
        //   value: '3',
        //   label: '换货',
        // },
      ],
      // 打印设置
      printForm: {},
      //售后设置
      afterForm: {},
      //自提设置
      setForm: {},
      // 模板列表
      templateList: [],
      templateListXP: [], //小票
      // 承运商
      carrierList: [],
      formRule: {
        auto_confirm_days: [
          { required: true, message: '请输入发货后自动确认收货时间', trigger: 'blur' },
          {
            required: true,
            validator: (rule, value, callback) => {
              console.log(11)
              if (value < 10) {
                this.form.auto_confirm_days = '10'
                return callback(new Error('自动确认收货时间范围为10天~30天,默认为1天'))
              } else {
                callback()
              }
            },
            message: '自动确认收货时间范围为10天~30天,默认为1天1',
            trigger: 'blur',
          },
        ],
        unpaid_timeout: [
          { required: true, message: '请输入未支付订单自动关闭时间', trigger: 'blur' },
        ],
      },
      printRule: {
        outbound_nodes: [{ required: true, message: '请选择可打印节点', trigger: 'change' }],
        outbound_copies: [{ required: true, message: '请输入打印份数', trigger: 'blur' }],
        // outbound_template: [{ required: true, message: '请选择默认打印模板', trigger: 'change' }],
        waybill_nodes: [{ required: true, message: '请选择可打印节点', trigger: 'change' }],
        waybill_copies: [{ required: true, message: '请输入打印份数', trigger: 'blur' }],
        // waybill_carrier: [{ required: true, message: '请选择默认承运商', trigger: 'change' }],
        refund_only_status: [
          { required: false, message: '请选择支持仅退款的订单状态', trigger: 'change' },
        ],
        return_refund_status: [
          { required: false, message: '请选择支持退货退款的订单状态', trigger: 'change' },
        ],
        exchange_status: [
          { required: false, message: '请选择支持换货的订单状态', trigger: 'change' },
        ],
        after_sales_days: [
          { required: true, message: '请输入交易完成后可申请售后的期限', trigger: 'blur' },
        ],
        sales_timeout_method: [
          { required: true, message: '请选择商家超时未处理售后单处理方式', trigger: 'change' },
          {
            required: false,
            validator: (rule, value, callback) => {
              if (this.afterForm.sales_timeout_method == 1) {
                console.log(11)
                if (value) {
                  callback()
                } else {
                  return callback(new Error('请输入'))
                }
              } else {
                callback()
              }
            },
            message: '请输入',
            trigger: 'blur',
          },
        ],
        sales_negotiate_days: [
          { required: true, message: '请输入买家售后单设置', trigger: 'blur' },
        ],
        stock_nodes: [{ required: true, message: '请选择是否需要备货节点', trigger: 'change' }],
        pickup_verification_type: [
          { required: true, message: '请选择自提核销方式', trigger: 'change' },
        ],
        receipt_nodes: [{ required: true, message: '请选择可打印节点', trigger: 'change' }],
        receipt_copies: [{ required: true, message: '请输入打印份数', trigger: 'blur' }],
      },
    }
  },
  computed: {
    getaftersales() {
      let aa = [].concat(
        this.afterForm.refund_only_status,
        this.afterForm.return_refund_status,
        this.afterForm.exchange_status,
        this.afterForm.approve_order_status
      )
      console.log(aa)
      if (aa.includes('3')) {
        return false
      } else {
        return true
      }
    },
    watch: {
      refund_nodes: {
        handler() {
          if (this.refund_nodes.length) {
            this.refund_nodes.forEach((item) => {
              if (
                this.afterForm.refund_only_status &&
                this.afterForm.refund_only_status.map(Number).includes(Number(item.value))
              ) {
                this.afteroptions1_1.push(item)
              }
            })
          }
        },
        immediate: true,
        deep: true,
      },
    },
  },
  methods: {
    // 获取承运商
    getCarrierFn() {
      getCarrierList().then((res) => {
        if (res.code == 200) {
          this.carrierList = res.data
        }
      })
    },
    // 获取出库单
    templateListSelectFn() {
      templateListSelectApi({
        type: 3,
      }).then((res) => {
        if (res.code == 200) {
          this.templateList = res.data
        }
      })
    },
    //下拉选项
    setNodeListFn() {
      setNodeList().then((res) => {
        if (res.code == 200) {
          this.options = res.data.print_nodes.map((item) => {
            return {
              label: item.label,
              value: item.value,
            }
          })
          this.refund_config_nodes = res.data.refund_config_nodes.map((item) => {
            return {
              label: item.label,
              value: item.value,
            }
          })
          this.refund_nodes = res.data.refund_nodes.map((item) => {
            return {
              label: item.label,
              value: item.value,
            }
          })
          console.log(res.data)
        }
      })
    },
    // 获取出库单
    templateListSelectFnXP() {
      templateListSelectApi({
        type: 2,
      }).then((res) => {
        if (res.code == 200) {
          this.templateListXP = res.data
        }
      })
    },
    // 获取详情数据
    getSetDetail() {
      settingDetailApi().then((res) => {
        if (res.code == 200) {
          this.id = res.data.id
          this.form = {
            auto_confirm_days: res.data.auto_confirm_days,
            unpaid_timeout: res.data.unpaid_timeout,
            multiple_addresses: res.data.multiple_addresses,
            allow_edit_price: res.data.allow_edit_price,
            price_edit_scope: res.data.price_edit_scope,
          }
          this.printForm = {
            outbound_nodes: res.data.outbound_nodes,
            outbound_copies: res.data.outbound_copies,
            outbound_template:
              res.data.outbound_template == 0 ? undefined : res.data.outbound_template,
            waybill_nodes: res.data.waybill_nodes,
            waybill_copies: res.data.waybill_copies,
            waybill_carrier: res.data.waybill_carrier == 0 ? undefined : res.data.waybill_carrier,
            waybill_template: res.data.waybill_template,
            receipt_nodes: res.data.receipt_nodes,
            receipt_copies: res.data.receipt_copies,
            receipt_templateres:
              res.data.receipt_templateres == 0 ? undefined : res.data.receipt_templateres,
          }
          this.afterForm = {
            refund_only_status: res.data.refund_only_status,
            approve_order_status: res.data.approve_order_status,
            return_refund_status: res.data.return_refund_status,
            exchange_status: res.data.exchange_status,
            after_sales_days: res.data.after_sales_days,
            sales_timeout_method: res.data.sales_timeout_method
              ? res.data.sales_timeout_method.toString()
              : res.data.sales_timeout_method,
            sales_timeout_days: res.data.sales_timeout_days,
            sales_negotiate_days: res.data.sales_negotiate_days,
          }
          this.setForm = {
            stock_nodes: res.data.stock_nodes,
            pickup_verification_type: res.data.pickup_verification_type,
          }
          this.order_refund_reason_configs = res.data.order_refund_reason_configs
          this.afteroptions1_1 = []
          this.refund_nodes.forEach((item) => {
            if (
              this.afterForm.refund_only_status &&
              this.afterForm.refund_only_status.map(Number).includes(Number(item.value))
            ) {
              this.afteroptions1_1.push(item)
            }
          })
          this.$nextTick(() => {
            this.$refs.form.clearValidate()
            this.$refs.printForm.clearValidate()
            this.$refs.afterForm.clearValidate()
            this.$refs.setForm.clearValidate()
          })
        }
      })
    },
    afterChange() {
      this.afteroptions1_1 = []
      this.refund_nodes.forEach((item) => {
        if (this.afterForm.refund_only_status.map(Number).includes(Number(item.value))) {
          this.afteroptions1_1.push(item)
        }
      })
    },
    // 保存设置
    saveSetDetail() {
      let isOk = true

      this.$refs.form.validate((valid) => {
        if (!valid) {
          isOk = false
        }
      })
      this.$refs.afterForm.validate((valid) => {
        if (!valid) {
          isOk = false
        }
      })
      this.$refs.setForm.validate((valid) => {
        if (!valid) {
          isOk = false
        }
      })
      this.$refs.printForm.validate((valid) => {
        if (!valid) {
          isOk = false
        }
      })

      if (!isOk) return
      let params = {
        id: this.id,
        ...this.form,
        ...this.afterForm,
        order_refund_reason_configs: this.order_refund_reason_configs,
        ...this.setForm,
        ...this.printForm,
      }
      // params.outbound_nodes = params.outbound_nodes.join(',')
      // params.waybill_nodes = params.waybill_nodes.join(',')
      settingSaveApi(params).then((res) => {
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: '保存成功!',
          })
        }
      })
    },
    handledelete(row) {
      this.$confirm('是否确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.order_refund_reason_configs = this.order_refund_reason_configs.filter(
            (item) => item !== row
          )
        })
        .catch((action) => {})
    },
    addTemplate() {
      this.$set(this.order_refund_reason_configs, this.order_refund_reason_configs.length, {
        reason: '',
        refund_type: [],
        is_supported: '',
        mch_uid: localStorage.getItem('currentMchUid'),
      })
    },
  },
  created() {
    this.getSetDetail()
    this.templateListSelectFn()
    this.templateListSelectFnXP()
    this.setNodeListFn()
    this.getCarrierFn()
  },
  mounted() {},
}
</script>

<style lang="scss" scoped>
.w100 {
  width: 100%;
}
.w100 ::v-deep input {
  text-align: left !important;
}
.no-bottom {
  margin-bottom: 0 !important;
  font-weight: 600;
  font-size: 18px;
}
.addTemplate {
  text-align: center;
  background: #ffffff;
  border-radius: 4px;
  padding: 8px;
  margin-top: 20px;
  cursor: pointer;
  border: 1px dashed #dcdfe6;
}
.base-table ::v-deep {
  // 表格头部样式
  .el-table th.el-table__cell {
    background: #f5f7fa;
    box-shadow: inset 0px -1px 0px 0px #ebeef5, inset 0px -1px 0px 0px #ebeef5;
  }
  th.el-table__cell {
    background-color: #f5f7fa;
  }
  th.el-table__cell .cell {
    color: #303133;
    font-weight: 600;
  }
}
</style>
