<!--
 * @FilePath: /qst-merchant-admin-2.0/src/views/shop/order/orderResult/index.vue
 * @Description: 结果查询
-->
<template>
  <div>
    <el-card>
      <base-form :tableForm="tableForm" :formArray="formArray" @searchForm="searchForm"></base-form>
      <base-table
        :tableColumn="tableColumn"
        :tableRequest="taskListApi"
        :tableForm="tableForm"
        ref="tableRef"
      >
        <template #status="{ scope }">
          <div class="flex">
            <el-button type="danger" v-if="scope.row.status == 3" plain>{{scope.row.status_text}}</el-button>
            <el-button type="success" v-if="scope.row.status == 2" plain>{{scope.row.status_text}}</el-button>
            <el-button type="primary" v-if="scope.row.status == 1" plain>{{scope.row.status_text}}</el-button>
            <el-button v-if="scope.row.status == 0" plain>{{scope.row.status_text}}</el-button>
          </div>
        </template>
        <template #error="{ scope }">
          <div class="public-operate-btn">
            <el-button
              v-if="scope.row.fail > 0"
              size="mini"
              type="text"
              @click="openDetail(scope.row)"
            >错误详情</el-button>
            <div v-else>-</div>
          </div>
        </template>

        <template #down="{ scope }">
          <div class="public-operate-btn">
            <!-- "type": 1, //类型：1 批量导入发货 2 批量导出 3 批量打印发货 -->
            <el-button v-if="scope.row.url" size="mini" type="text" @click="downFn(scope.row)">下载</el-button>
            <div v-else>-</div>
          </div>
        </template>
      </base-table>
    </el-card>
    <order-result-error ref="orderResultError"></order-result-error>
  </div>
</template>

<script>
import { filterTaskListApi, taskListApi, errExportApi } from '@/api/shop/order'
import orderResultError from './orderResultError'
export default {
  name: 'orderResult',
  components: {
    orderResultError,
  },
  data() {
    return {
      formArray: [
        {
          label: '任务号',
          type: 'input',
          key: 'task_no',
          placeholder: '请输入任务号',
        },
        {
          label: '操作时间',
          type: 'time',
          key: 'time',
          timeKey: ['start_time', 'end_time'],
        },

        {
          label: '操作类型',
          type: 'select',
          key: 'type',
          placeholder: '全部',
          options: [],
        },
        {
          label: '状态',
          type: 'select',
          key: 'status',
          placeholder: '全部',
          options: [],
        },
      ],

      tableColumn: [
        {
          label: '任务号',
          prop: 'task_no',
        },
        {
          label: '操作时间',
          prop: 'created_at',
        },
        {
          label: '操作类型',
          prop: 'type_text',
        },
        {
          label: '任务总数',
          prop: 'total',
        },
        {
          label: '成功数量',
          prop: 'success',
        },
        {
          label: '失败数量',
          prop: 'fail',
        },
        {
          label: '状态',
          prop: 'status',
          type: 'customize',
          width: '150px',
        },
        {
          label: '错误信息',
          prop: 'error',
          type: 'customize',
          width: '150px',
        },
        {
          label: '操作',
          prop: 'down',
          type: 'customize',
          width: '150px',
          fixed: 'right',
        },
      ],
      taskListApi,
      tableForm: {
        type: '',
        status: '',
      },
    }
  },
  created() {
    this.filterTaskListFn()
  },
  methods: {
    // 打开错误详情
    openDetail(row) {
      this.$refs.orderResultError.open(row.task_id)
    },

    // 初始化
    filterTaskListFn() {
      filterTaskListApi().then((res) => {
        if (res.code == 200) {
          let type = res.data.type
          let status = res.data.status
          status = status.map((item) => {
            return {
              label: item.label,
              value: item.value + '',
            }
          })
          this.formArray[2].options = [{ label: '全部', value: '' }, ...type]
          this.formArray[3].options = [{ label: '全部', value: '' }, ...status]
          console.log(this.formArray[2].options)
        }
      })
    },

    // 表单搜索事件
    searchForm(form) {
      this.tableForm = Object.assign({}, this.tableForm, form)
    },

    // 下载
    downFn(row) {
      // "type": 1, //类型：1 批量导入发货 2 批量导出 3 批量打印发货
      switch (row.type) {
        // 批量导入发货 url
        case 1:
          window.location.href = row.url
          break
        // 批量导出 报错信息
        case 2:
          if (row.fail > 0) {
            this.errExportFn(row.task_id)
            return
          }
          window.location.href = row.url
          break
      }
    },
    // 报错
    errExportFn(task_id) {
      errExportApi({ task_id }).then((res) => {
        if (res.code == 200) {
          this.downloadFile(res.data.file_url)
        }
      })
    },

    downloadFile(url, filename) {
      const link = document.createElement('a')
      link.href = url
      // link.download = filename || 'file'; // 设置下载文件名
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },

    // 使用示例
    // 更新表格数据
    update() {
      this.$refs.tableRef.tableRequestFn()
    },
  },
  mounted() {},
}
</script>

<style lang="scss" scoped>
.operate {
  margin-top: 20px;
}
</style>