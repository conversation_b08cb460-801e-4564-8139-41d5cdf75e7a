<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-05-29 10:42:36
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-29 11:09:59
 * @FilePath: /qst-merchant-admin-2.0/src/views/shop/order/orderResult/orderResultError.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE

-->
<template>
  <div>
    <el-dialog
      title="错误详情列表"
      :show-close="false"
      :visible.sync="isOrderResult"
      :close-on-click-modal="false"
    >
      <base-table
        :tableColumn="tableColumn"
        :tableRequest="errDetailApi"
        :tableForm="{
          task_id: task_id
        }"
        ref="baseTable"
      ></base-table>
      <base-dialog-footer :isCancel="false" :confirmText="'关闭'" @confirm="isOrderResult=false"></base-dialog-footer>
    </el-dialog>
  </div>
</template>

<script>
import { errDetailApi } from '@/api/shop/order'
export default {
  name: 'orderResultError',
  data() {
    return {
      task_id: '',
      errDetailApi,
      isOrderResult: false,
      tableColumn: [
        {
          label: '序号',
          prop: 'line_number',
        },
        {
          label: '订单编号',
          prop: 'source',
        },
        {
          label: '错误原因',
          prop: 'err_msg',
        },
      ],
    }
  },
  methods: {
    open(e) {
      this.task_id = e
      this.isOrderResult = true
    },
  },
  mounted() {},
}
</script>

<style scoped>
::v-deep .el-dialog__body {
  padding-top: 0 !important;
}
</style>