<!--
 * @Author: liq<PERSON> liqian@123
 * @Date: 2025-05-20 10:36:55
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-28 16:54:54
 * @FilePath: \qst-merchant-admin-2.0\src\views\shop\order\orderList\components\orderShipments.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-dialog
    append-to-body
    :visible.sync="showOrderShipmentDialog"
    :close-on-click-modal="false"
    title="订单发货"
    width="800px"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="80px" label-position="top">
      <el-form-item label="发货类型" prop="is_needs_logistics">
        <el-radio-group v-model="form.is_needs_logistics" @input="changeType">
          <el-radio label="1">物流发货</el-radio>
          <el-radio label="2">无需物流</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="发货备注" v-if="form.is_needs_logistics == 2">
        <el-input
          type="textarea"
          :rows="2"
          placeholder="请输入发货备注信息，如：商家将亲自送货上门，预计下午3点送达"
          v-model="form.express_remark"
        ></el-input>
      </el-form-item>
      <div v-if="form.is_needs_logistics == 1">
        <el-form-item label="发货方式" prop="delivery_method">
          <el-radio-group v-model="form.delivery_method" @input="changeTypeMethod">
            <el-radio label="1">录入单号</el-radio>
            <el-radio label="2">在线拉取单号</el-radio>
          </el-radio-group>
        </el-form-item>
        <div class="flex flex-b">
          <el-form-item
            label="承运商"
            prop="carrier_id"
            v-if="!templateArr.length && form.delivery_method == 2"
            style="width: 70%"
          >
            <el-select
              v-model="form.carrier_id"
              placeholder="请选择承运商"
              style="width: 80%"
              @change="getSelsctExpress($event)"
            >
              <el-option
                v-for="item in expressList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
              <!-- <el-option label="区域二" value="beijing"></el-option> -->
            </el-select>
          </el-form-item>
          <el-form-item
            label="物流单号"
            prop="tracking_number"
            v-if="!templateArr.length && form.delivery_method == 2"
            style="width: 60%"
          >
            <el-input disabled placeholder="请输入物流单号" v-model="form.tracking_number">
              <el-button
                style="background: #0071fe; color: #fff"
                slot="append"
                @click="getOrderNumber"
                type="primary"
              >
                获取单号
              </el-button>
            </el-input>
          </el-form-item>
          <el-form-item
            v-if="!templateArr.length && form.delivery_method == 1"
            label="物流单号"
            prop="tracking_number"
            style="width: 60%"
          >
            <el-input
              placeholder="请输入物流单号"
              @blur="getPacketNumber($event)"
              v-model="form.tracking_number"
            ></el-input>
          </el-form-item>
        </div>
        <el-button type="text" v-if="!templateArr.length" @click="addOrderPicket">
          一个包裹装不下？加一个
        </el-button>

        <div class="templateMain" v-if="templateArr.length && form.delivery_method == 1">
          <div v-for="(item, index) in templateArr" :key="item.id">
            <div class="templateTitle">
              <h4>包裹{{ index + 1 }}</h4>
              <span @click="deleteTemplate(index)" v-if="templateArr.length > 1">删除</span>
            </div>
            <el-form
              label-width="180px"
              :model="item"
              ref="basicInfo1"
              :rules="basicInfoRule"
              label-position="top"
            >
              <el-form-item label="物流单号" prop="tracking_number">
                <el-input
                  v-model="item.tracking_number"
                  @blur="getPacketNumber($event, index)"
                  type="text"
                  placeholder="请输入物流单号"
                ></el-input>
              </el-form-item>
              <el-form-item label="选择商品" prop="goods_items">
                <el-select
                  style="width: 100%"
                  v-model="item.goods_items"
                  multiple
                  filterable
                  allow-create
                  default-first-option
                  @change="addSelectValue($event, item, index)"
                  @remove-tag="removeSelectValue($event, item, index)"
                  placeholder="请选择规格值选择商品"
                >
                  <el-option
                    v-for="item1 in orderList"
                    :key="item1.sku_name"
                    :label="item1.sku_name"
                    :value="item1.order_goods_id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-form>
            <div class="flex packetContent" v-if="item.packet_items.length">
              <div class="packet" v-for="(iitem, iindex) in item.packet_items">
                <div class="packetgoods flex">
                  <img :src="iitem.goodsItem.image" alt />
                  <div class="packetRight">
                    <span class="saleInfotitle" style="float: left">
                      {{ iitem.goodsItem.sku_name }}
                    </span>
                    <span class="saleInfotitle" style="float: left">
                      {{ iitem.goodsItem.spec_value_items }}
                    </span>
                    <div>
                      <span>总数量：{{ iitem.goodsItem.quantity }}</span>
                      <span>可分配: {{ iitem.syfpnum }}</span>
                    </div>
                  </div>
                </div>
                <div>
                  <shop-number
                    :refresh="refresh"
                    :shopNum="iitem.fpnum"
                    :max-num="iitem.syfpnum"
                    :min-num="0"
                    @numChange="(e) => numChange(e, iitem)"
                  ></shop-number>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="templateMain" v-if="templateArr.length && form.delivery_method == 2">
          <div v-for="(item, index) in templateArr" :key="item.id">
            <div class="templateTitle">
              <h4>包裹{{ index + 1 }}</h4>
              <span @click="deleteTemplate(index)" v-if="templateArr.length > 1">删除</span>
            </div>
            <el-form
              label-width="180px"
              :model="item"
              ref="basicInfo1"
              :rules="basicInfoRule"
              label-position="top"
            >
              <el-form-item label="选择商品" prop="goods_items">
                <el-select
                  style="width: 100%"
                  v-model="item.goods_items"
                  multiple
                  filterable
                  allow-create
                  default-first-option
                  @change="addSelectValue($event, item, index)"
                  @remove-tag="removeSelectValue($event, item, index)"
                  placeholder="请选择规格值选择商品"
                >
                  <el-option
                    v-for="item1 in orderList"
                    :key="item1.sku_name"
                    :label="item1.sku_name"
                    :value="item1.order_goods_id"
                  ></el-option>
                </el-select>
              </el-form-item>
              <div class="flex packetContent" v-if="item.packet_items.length">
                <div class="packet" v-for="(iitem, iindex) in item.packet_items">
                  <div class="packetgoods flex">
                    <img :src="iitem.goodsItem.image" alt />
                    <div class="packetRight">
                      <span class="saleInfotitle" style="float: left">
                        {{ iitem.goodsItem.sku_name }}
                      </span>
                      <span class="saleInfotitle" style="float: left">
                        {{ iitem.goodsItem.spec_value_items }}
                      </span>
                      <div>
                        <span>总数量：{{ iitem.goodsItem.quantity }}</span>
                        <span>可分配: {{ iitem.syfpnum }}</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <shop-number
                      :refresh="refresh"
                      :shopNum="iitem.fpnum"
                      :max-num="iitem.syfpnum"
                      :min-num="0"
                      @numChange="(e) => numChange(e, iitem)"
                    ></shop-number>
                  </div>
                </div>
              </div>

              <div class="flex flex-b">
                <el-form-item label="承运商" prop="carrier_id" style="width: 70%">
                  <el-select
                    :disabled="index != 0"
                    v-model="item.carrier_id"
                    placeholder="请选择承运商"
                    @change="getSelsctExpress($event, index)"
                    style="width: 80%"
                  >
                    <el-option
                      v-for="item in expressList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                    <!-- <el-option label="区域二" value="beijing"></el-option> -->
                  </el-select>
                </el-form-item>
                <el-form-item label="物流单号" prop="tracking_number" style="width: 60%">
                  <el-input disabled placeholder="请输入物流单号" v-model="item.tracking_number">
                    <el-button
                      style="background: #0071fe; color: #fff"
                      slot="append"
                      @click="getOrderNumber(index)"
                      type="primary"
                    >
                      获取单号
                    </el-button>
                  </el-input>
                </el-form-item>
              </div>
            </el-form>
          </div>
        </div>
        <div v-if="templateArr.length" @click="addOrderPicket" class="addTemplate">
          <i class="el-icon-plus"></i>
          添加包裹
        </div>
        <el-form-item v-if="form.delivery_method == 2" label="打印模板" prop="print_template">
          <el-select v-model="form.print_template" placeholder="请选择打印模板" style="width: 100%">
            <el-option
              v-for="item in printTemplateList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-button
          @click="printwaybill"
          v-if="form.delivery_method == 2 && templateArr.length"
          type="primary"
          icon="el-icon-printer"
        >
          批量打印电子面单
        </el-button>
        <el-button
          @click="printwaybill"
          v-if="form.delivery_method == 2 && !templateArr.length"
          type="primary"
          icon="el-icon-printer"
        >
          打印电子面单
        </el-button>
      </div>
    </el-form>
    <base-dialog-footer
      confirmText="确定"
      @cancel="showOrderShipmentDialog = false"
      @confirm="confirm"
    ></base-dialog-footer>
  </el-dialog>
</template>

<script>
  import shopNumber from './shopNumber.vue'
  import { getmaparrList } from '@/utils/index'
  import {
    getCarrierList,
    createWaybillNo,
    getPackageGoods,
    expressNumberRecognition,
    templateListSelect,
    getShipments,
    waybillPrintDetail,
  } from '@/api/shop/order.js'
  export default {
    name: 'orderShipments',
    components: {
      shopNumber,
    },
    props: {
      settingListDetail: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        that: this,
        refresh: false,
        showOrderShipmentDialog: false,
        orderDetailData: {},
        orderList: [], //商品列表
        expressList: [], //承运商列表
        printTemplateList: [], //打印模版列表
        form: {
          is_needs_logistics: '',
          express_remark: '',
          delivery_method: 1,
          carrier_id: '',
          tracking_number: '',
          print_template: '',
        },
        rules: {
          is_needs_logistics: [{ required: true, message: '请选择发货类型', trigger: 'change' }],
          delivery_method: [{ required: true, message: '请选择发货类方式', trigger: 'change' }],
          carrier_id: [{ required: true, message: '请选择承运商', trigger: 'change' }],
          tracking_number: [{ required: true, message: '请输入物流单号', trigger: 'blur' }],
          print_template: [{ required: true, message: '请选择打印模版', trigger: 'change' }],
        },
        basicInfoRule: {
          tracking_number: [{ required: true, message: '请输入物流单号', trigger: 'blur' }],
          goods_items: [{ required: true, message: '请选择商品', trigger: 'blur' }],
          carrier_id: [{ required: true, message: '请选择承运商', trigger: 'change' }],
        },
        templateArr: [],
        ordernum: 0,
        deleteFpnum: 0, //得到删除已分配的数量
        packetArr: [], //duo包裹
        onePacketJson: {}, //dan包裹
      }
    },
    methods: {
      open(data) {
        this.showOrderShipmentDialog = true
        this.cancelDataJson()
        this.orderDetailData = data
        this.getPackageGoodsFn()
        this.getCarrierListFn()
        this.$nextTick(() => {
          this.$refs.form.resetFields()
          this.form.delivery_method = '1'
        })
      },
      //承运商列表
      getCarrierListFn() {
        getCarrierList().then((res) => {
          if (res.code == 200) {
            this.expressList = res.data
          }
          // console.log(res)
        })
      },
      // 单订单发货获取可用商品列表
      getPackageGoodsFn() {
        getPackageGoods({ sub_order_no: this.orderDetailData.sub_order_no }).then((res) => {
          if (res.code == 200) {
            this.orderList = res.data
            console.log(res.data)
          }
        })
      },
      //选择承运商
      getSelsctExpress(value, i) {
        let getSelectExpress = getmaparrList(this.expressList, 'value', value)
        if (this.templateArr.length) {
          this.form.print_template = ''
          this.templateArr.forEach((item) => {
            item.express_id = getSelectExpress[0].express_id
            item.carrier_id = value
          })
          // this.templateArr[i].express_id = getSelectExpress[0].express_id
        }
        templateListSelect({
          type: 1, //1-电子面单 2-小票 3-出库单，必选参数
          express_id: getSelectExpress.length > 0 ? getSelectExpress[0].express_id : '', //物流公司ID，非必填参数，当类型为1时候必填
        }).then((res) => {
          if (res.code == 200) {
            this.printTemplateList = res.data
            this.form.print_template = this.printTemplateList[0].value
          }
        })
      },
      // 获取物流单号
      getOrderNumber(i) {
        if (this.templateArr.length > 0) {
          let carrier_id = this.templateArr[i].carrier_id
          if (!carrier_id) {
            this.$message({
              type: 'error',
              message: '请选择承运商!',
            })
            return false
          } else {
            this.getWaybillNoFn(i)
          }
        }
        this.$refs['form'].validateField('carrier_id', (errorMessage) => {
          let valid = errorMessage == '' ? true : false
          if (valid) {
            this.getWaybillNoFn()
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },

      getWaybillNoFn(i) {
        let packetGoods = []
        if (this.templateArr.length) {
          this.templateArr[i].packet_items.forEach((item, idx) => {
            packetGoods.push({
              order_goods_id: item.goodsItem.order_goods_id,
              num: item.fpnum,
            })
          })
        }
        let params = {
          carrier_id:
            this.templateArr.length > 0 ? this.templateArr[i].carrier_id : this.form.carrier_id, //承运商id
          shop_id: this.orderDetailData.shop_id,
          sub_order_no: this.orderDetailData.sub_order_no, //子订单号
          order_goods_list: packetGoods, //包裹商品
        }
        createWaybillNo(params).then((res) => {
          if (res.code == 200) {
            this.templateArr.length > 0
              ? (this.templateArr[i].tracking_number = res.data.waybill_no)
              : (this.form.tracking_number = res.data.waybill_no)
            console.log(res)
          }
        })
      },

      //新增包裹
      addOrderPicket() {
        if (this.form.delivery_method == 2) {
          this.$set(this.templateArr, this.templateArr.length, {
            tracking_number: this.templateArr.length > 1 ? '' : this.form.tracking_number,
            print_template: this.printTemplateList.length > 0 && this.printTemplateList[0].value,
            express_id: getmaparrList(
              this.expressList,
              'value',
              this.form.carrier_id || this.settingListDetail.waybill_carrier
            )[0].express_id,
            carrier_id: this.form.carrier_id || this.settingListDetail.waybill_carrier,
            goods_items: [],
            packet_items: [],
          })
        }
        if (this.form.delivery_method == 1) {
          this.$set(this.templateArr, this.templateArr.length, {
            tracking_number: this.templateArr.length > 1 ? '' : this.form.tracking_number,
            print_template: '',
            express_id: '',
            carrier_id: '',
            goods_items: [],
            packet_items: [],
          })
          this.templateArr[0].carrier_id = this.onePacketJson.carrier_id
          this.templateArr[0].express_id = this.onePacketJson.express_id
        }
      },

      //删除包裹
      deleteTemplate(index) {
        let deletePacket = []
        this.templateArr[index].packet_items.forEach((item) => {
          deletePacket.push({ code: item.goodsItem.order_goods_id, num: item.fpnum })
        })
        this.templateArr.splice(index, 1)

        this.templateArr.forEach((item) => {
          if (item.packet_items.length) {
            item.packet_items.forEach((pItem) => {
              deletePacket.forEach((iitem) => {
                if (iitem.code == pItem.goodsItem.order_goods_id) {
                  pItem.syfpnum += iitem.num
                }
              })
            })
          }
        })
        console.log(this.templateArr, index)
      },
      //获取包裹单号
      getPacketNumber(event, index) {
        if (!event.target.value) return
        let params = {
          tracking_number: event.target.value,
        }
        expressNumberRecognition(params).then((res) => {
          if (res.code == 200) {
            if (this.templateArr.length) {
              this.getPacketMoreList(event, index, res)
            } else {
              this.getPacketList(event, res)
            }
          }
        })
      },
      //单包裹获取单号
      getPacketList(event, res) {
        this.onePacketJson = {
          tracking_number: event.target.value,
          express_id: res.data.express_id,
          carrier_id: res.data.carrier_id,
          print_template: '',
          goods_items: [],
        }
      },
      //多包裹 获取单号
      getPacketMoreList(event, index, res) {
        let returnJson = {
          tracking_number: event.target.value,
          express_id: res.data.express_id,
          carrier_id: res.data.carrier_id,
        }
        this.templateArr[index].tracking_number
        this.$set(
          this.templateArr,
          index,
          Object.assign(
            {},
            {
              tracking_number: '',
              print_template: '',
              express_id: '',
              carrier_id: '',
              goods_items: [],
              packet_items: [],
            },
            returnJson
          )
        )
      },
      //将商品添加包裹
      addSelectValue(curitem, data, index) {
        let goodsList = [] //得到选择的商品

        let newquantity = 0
        this.templateArr.forEach((item) => {
          item.packet_items.forEach((iii) => {
            if (curitem[curitem.length - 1] == iii.goodsItem.order_goods_id) {
              newquantity += iii.fpnum
            }
          })
        })

        let curList = getmaparrList(this.orderList, 'order_goods_id', curitem[curitem.length - 1])
        if (data.packet_items.length) {
          data.packet_items.forEach((ii, idx) => {
            if (!curitem.includes(ii.goodsItem.order_goods_id)) {
              this.deleteFpnum = data.packet_items[idx].fpnum
              this.$delete(data.packet_items, idx)
            }
          })
        } else {
          let fpJson = {
            fpnum: 0,
            syfpnum:
              this.templateArr.length > 1 ? curList[0].quantity - newquantity : curList[0].quantity,
            goodsItem: curList[0],
          }
          goodsList.push(fpJson)
        }
        data.packet_items.forEach((ii) => {
          if (!(curitem[curitem.length - 1] == ii.goodsItem.order_goods_id)) {
            let fpJson = {
              fpnum: 0,
              syfpnum:
                this.templateArr.length > 1
                  ? curList[0].quantity - newquantity
                  : curList[0].quantity,
              goodsItem: curList[0],
            }
            goodsList.push(fpJson)
          }
        })
        //给每个包裹添加选择的商品
        this.$set(this.templateArr[index], 'packet_items', data.packet_items.concat(goodsList))

        console.log(this.templateArr, index)
      },
      //移除包裹里的商品
      removeSelectValue(curitem, data, index) {
        this.templateArr.forEach((item, idx) => {
          if (index != idx) {
            if (item.packet_items.length) {
              item.packet_items.forEach((pItem) => {
                if (curitem == pItem.goodsItem.order_goods_id) {
                  pItem.syfpnum += this.deleteFpnum
                }
              })
            }
          }
        })
      },
      //分配商品数量
      numChange(e, item) {
        item.fpnum = e
        this.getFpnumGoods(item)
      },
      getFpnumGoods(data) {
        let prefpnum = 0
        let syfpnum = 0
        this.templateArr.forEach((item) => {
          if (item.packet_items.length) {
            item.packet_items.forEach((pItem) => {
              if (data.goodsItem.order_goods_id == pItem.goodsItem.order_goods_id) {
                prefpnum += Number(pItem.fpnum)
              }
            })
          }
        })
        syfpnum = Number(data?.goodsItem.quantity) - prefpnum

        this.templateArr.forEach((item) => {
          if (item.packet_items.length) {
            item.packet_items.forEach((pItem) => {
              if (data.goodsItem.order_goods_id == pItem.goodsItem.order_goods_id) {
                pItem.syfpnum = syfpnum
              }
            })
          }
        })
        console.log(this.templateArr)
      },
      //切换发货类型
      changeType(value) {
        if (value == 2) {
          this.form.delivery_method = ''
          // this.form.carrier_id = ''
          this.form.tracking_number = ''
          this.templateArr = []
        } else {
          this.form.delivery_method = '1'
          this.express_remark = ''
        }
        console.log(value)
      },
      // 切换发货方式
      changeTypeMethod(value) {
        this.templateArr = []
        if (value == 2) {
          this.form.tracking_number = ''
          this.form.carrier_id = this.form.carrier_id || this.settingListDetail.waybill_carrier
          this.$refs.form.clearValidate()
          templateListSelect({
            type: 1, //1-电子面单 2-小票 3-出库单，必选参数
            express_id: getmaparrList(
              this.expressList,
              'value',
              this.settingListDetail.waybill_carrier
            )[0].express_id, //物流公司ID，非必填参数，当类型为1时候必填
          }).then((res) => {
            if (res.code == 200) {
              this.printTemplateList = res.data
              this.form.print_template = this.printTemplateList[0].value
            }
          })
        }
        if (value == 1 && !this.form.tracking_number) {
          this.form.carrier_id = ''
        }
        if (value == 1 && this.form.tracking_number) {
          this.$confirm('切换后会将已拉取到的单号进行取消，请确认！', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            // showCancelButton: false,
            showClose: false,
            type: 'warning',
          })
            .then(() => {
              // deleteGoodsSpecList({ shop_id: this.shop_idtab, template_id: row.id }).then((res) => {
              //   if (res.code == 200) {
              //     this.$message.success('取消成功')
              //     this.$refs.tableRef.tableRequestFn()
              //   }
              // })
              this.$refs.form.clearValidate()
              this.form.tracking_number = ''
              this.form.carrier_id = ''
              this.templateArr = []
            })
            .catch((action) => {
              this.form.delivery_method = value == 2 ? '1' : '2'
              // this.$message({
              //   type: 'info',
              //   message: action === 'cancel' ? '取消删除' : '',
              // })
            })
        }
      },
      // 打印电子面单
      printwaybill() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            let data = []
            this.templateArr.forEach((item, index) => {
              data.push({
                waybill_no: item.tracking_number, //面单单号
                print_template_id: item.print_template, //打印模板ID})
              })
            })

            let params = {
              order_no: this.orderDetailData.sub_order_no, //子订单编号
              package: this.templateArr.length
                ? data
                : [
                    {
                      waybill_no: this.form.tracking_number, //面单单号
                      print_template_id: this.form.print_template, //打印模板ID
                    },
                  ], //发货信息
            }
            this.$emit('printWaybill', params)
            // waybillPrintDetail(params).then((res) => {
            //   if (res.code == 200) {
            //     this.$message.success('打印电子面单成功')
            //   }
            // })
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
      // 確定
      confirm() {
        let getParams = []

        this.$refs.form.validate((valid) => {
          if (valid) {
            //录入单号单包裹
            if (this.templateArr.length == 0) {
              getParams = this.submitOnePacket()
              console.log(getParams)
              getShipments(getParams).then((res) => {
                if (res.code == 200) {
                  this.$message({
                    type: 'success',
                    message: '发货成功!',
                  })
                  this.$emit('update')
                  this.showOrderShipmentDialog = false
                }
              })
            }

            if (this.$refs.basicInfo1) {
              this.$refs.basicInfo1.forEach((item, index) => {
                item.validate((valid) => {
                  if (valid) {
                    if (index + 1 == this.$refs.basicInfo1.length) {
                      //录入单号多包裹
                      if (this.templateArr.length > 0) {
                        getParams = this.submitMorePacket()
                        console.log(getParams)
                        getShipments(getParams).then((res) => {
                          if (res.code == 200) {
                            this.$message({
                              type: 'success',
                              message: '发货成功!',
                            })
                            this.$emit('update')
                            this.showOrderShipmentDialog = false
                          }
                        })
                      }
                    }
                  } else {
                    console.log('error submit!!')
                    return false
                  }
                })
              })
            }
          } else {
            console.log('error submit!!')
            return false
          }
        })

        console.log(getParams)
      },
      //录入单号多包裹确认
      submitMorePacket() {
        this.packetArr = []
        let packetGoods = []
        this.templateArr.forEach((item, index) => {
          item.packet_items.forEach((iitem, idx) => {
            packetGoods = []
            packetGoods.push({
              order_goods_id: iitem.goodsItem.order_goods_id,
              quantity: iitem.fpnum,
            })
          })
          this.packetArr.push({
            tracking_number: item.tracking_number,
            print_template: this.form.print_template,
            express_id: item.express_id,
            carrier_id: item.carrier_id,
            goods_items: packetGoods,
          })
        })

        let params = {
          sub_order_no: this.orderDetailData.sub_order_no,
          is_needs_logistics: this.form.is_needs_logistics,
          delivery_method: this.form.delivery_method,
          express_remark: this.form.express_remark,
          express_packages: this.packetArr,
        }
        return params
      },
      //录入单号单包裹确认
      submitOnePacket() {
        let packetGoods = []
        this.orderList.forEach((item) => {
          packetGoods.push({
            order_goods_id: item.order_goods_id,
            quantity: item.quantity,
          })
        })
        if (this.form.delivery_method == 2) {
          let getSelectExpress = getmaparrList(this.expressList, 'value', this.form.carrier_id)
          this.onePacketJson = {
            tracking_number: this.form.tracking_number,
            express_id: getSelectExpress[0].express_id,
            carrier_id: getSelectExpress[0].value,
            print_template: this.form.print_template,
            goods_items: [],
          }
        }
        this.onePacketJson.goods_items = packetGoods
        let params = {
          sub_order_no: this.orderDetailData.sub_order_no,
          is_needs_logistics: this.form.is_needs_logistics,
          delivery_method: this.form.delivery_method,
          express_remark: this.form.express_remark,
          express_packages: this.form.is_needs_logistics == 1 ? [this.onePacketJson] : [],
        }
        return params
      },
      //切换置空
      cancelDataJson() {
        this.form.is_needs_logistics = ''
        this.form.express_remark = ''
        this.form.delivery_method = ''
        // this.form.carrier_id = ''
        this.form.tracking_number = ''
        this.form.print_template = ''
        this.packetArr = []
        this.templateArr = []
      },
    },
  }
</script>

<style lang="scss" scoped>
  .templateTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    h4 {
      font-size: 16px;
      color: #303133;
      font-weight: 500;
    }
    span {
      color: #ff3c3c;
      cursor: pointer;
    }
  }
  .addTemplate {
    text-align: center;
    background: #ffffff;
    border-radius: 4px;
    padding: 8px;
    cursor: pointer;
    border: 1px dashed #dcdfe6;
  }
  .packetContent {
    justify-content: space-between;
    margin: 15px 0;
  }
  .packet {
    display: flex;
    flex-direction: row;
    width: 330px;
    height: 80px;
    background: #f7f7f7;
    border-radius: 8px;
    align-items: center;
    justify-content: space-around;
    .packetgoods {
      justify-content: center;
      img {
        width: 50px;
        height: 50px;
      }
      .packetRight {
        display: flex;
        flex-direction: column;
        margin-left: 7px;
        .saleInfotitle {
          display: inline-block;
          white-space: nowrap;
          width: 100px;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
  .el-input-group__append {
    background: #0071fe !important;
    color: #ffffff !important;
  }
</style>
