<!--
 * @Author: liq<PERSON> liqian@123
 * @Date: 2025-05-20 10:36:55
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-31 14:09:44
 * @FilePath: \qst-merchant-admin-2.0\src\views\shop\order\orderList\components\orderChangePrice.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-dialog
    append-to-body
    :visible.sync="showOrderPriceDialog"
    :close-on-click-modal="false"
    title="订单改价"
    width="70%"
  >
    <div class="n_tips">
      <img class="tip_icon" :src="imgOssPath + '20250517_shuoming.png'" alt />
      <div class="tip_title">
        仅待支付订单支持改价，改价后请联系买家刷新订单核实金额无误后再支付！
      </div>
    </div>
    <div class="content" v-if="type == 1">
      <el-descriptions
        direction="horizontal"
        :column="1"
        border
        :labelStyle="label_Style"
        :contentStyle="content_style"
      >
        <el-descriptions-item>
          <template slot="label">金额项</template>
          金额(元)
        </el-descriptions-item>
        <el-descriptions-item v-for="item in orderListInfo" :key="item.code">
          <template slot="label">
            <span type="text">{{ item.name }}</span>
            <el-tooltip class="item" effect="dark" placement="top" :content="item.tip">
              <img
                :src="imgOssPath + '20250619_sf_wenhao.png'"
                alt
                class="icon_wenhao"
                style="transform: translateY(2px)"
              />
            </el-tooltip>
          </template>
          <div v-if="['change_order_price', 'freight_difference'].includes(item.code)">
            <el-input-number
              :controls="false"
              v-model="form[item.code]"
              @input="getChangePrice($event, item.code)"
            ></el-input-number>
          </div>
          <div v-else>
            {{ orderPriceInfo[item.code] }}
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="content" v-if="type == 2">
      <el-table
        :data="tableData"
        border
        show-summary
        :summary-method="getPayAmount"
        style="width: 100%"
      >
        <el-table-column prop="sku_name" label="商品名称"></el-table-column>
        <el-table-column prop="spec_value_items" label="规格"></el-table-column>
        <el-table-column prop="price" width="100">
          <template slot="header" slot-scope="scope">
            <span type="text">销售单价</span>
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              content="商品的原始销售单价，不包含任何价格调整"
            >
              <img
                :src="imgOssPath + '20250619_sf_wenhao.png'"
                alt
                class="icon_wenhao"
                style="transform: translateY(2px)"
              />
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="数量" width="80"></el-table-column>
        <el-table-column prop="discount_amount" width="100">
          <template slot="header" slot-scope="scope">
            <span type="text">优惠金额</span>
            <el-tooltip class="item" effect="dark" placement="top" content="分配到该商品的优惠金额">
              <img
                :src="imgOssPath + '20250619_sf_wenhao.png'"
                alt
                class="icon_wenhao"
                style="transform: translateY(2px)"
              />
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column prop="total_price">
          <template slot="header" slot-scope="scope">
            <span type="text">待支付金额</span>
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              content="改价前该商品待支付的金额，销售总额减去优惠金额再加上运费后的应付金额"
            >
              <img
                :src="imgOssPath + '20250619_sf_wenhao.png'"
                alt
                class="icon_wenhao"
                style="transform: translateY(2px)"
              />
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="change_order_price">
          <template slot="header" slot-scope="scope">
            <span type="text">涨价/减价金额</span>
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              content="对商品金额进行调整的金额，正数表示涨价，负数表示减价。调整后将自动计算新的单价"
            >
              <img
                :src="imgOssPath + '20250619_sf_wenhao.png'"
                alt
                class="icon_wenhao"
                style="transform: translateY(2px)"
              />
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.change_order_price"
              @input="getGoodsChangePrice(scope.row, scope.$index)"
              placeholder="请输入"
              :controls="false"
            ></el-input-number>
          </template>
        </el-table-column>
        <el-table-column prop="pay_amount">
          <template slot="header" slot-scope="scope">
            <span type="text">实付金额</span>
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              content="最终该商品需要支付的金额，包含所有价格调整"
            >
              <img
                :src="imgOssPath + '20250619_sf_wenhao.png'"
                alt
                class="icon_wenhao"
                style="transform: translateY(2px)"
              />
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="cost_price" width="120">
          <template slot="header" slot-scope="scope">
            <span type="text">成本金额</span>
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              content="该商品的成本价总和，若成本不正确请检查各商品基础资料中的成本价格是否正确设置"
            >
              <img
                :src="imgOssPath + '20250619_sf_wenhao.png'"
                alt
                class="icon_wenhao"
                style="transform: translateY(2px)"
              />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <div class="flex bottonContent">
        <span>运费 ¥{{ adjust_shipping_fee }}</span>

        <div class="flex bottomright">
          <div>
            运费差额
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              content="对运费进行调整的金额，正数表示增加运费，负数表示减少运费"
            >
              <img
                :src="imgOssPath + '20250619_sf_wenhao.png'"
                alt
                class="icon_wenhao"
                style="transform: translateY(2px)"
              />
            </el-tooltip>
          </div>
          <el-input-number
            :controls="false"
            v-model="change_shipping_feeL"
            placeholder="请输入"
            @input="changeShipping1"
          ></el-input-number>
          <el-button type="text" @click="changeShipping">运费减免</el-button>
        </div>
      </div>
    </div>
    <el-dialog width="600px" title="改价风险" :visible.sync="innerVisible" append-to-body>
      <div class="n_tips1">
        <img class="tip_icon" :src="image" alt />
        <div>
          <div class="tip_title">当前改价可能导致亏损</div>
          <div class="tip_name">
            修改实付金额低于总成本金额
            <span class="warnMoney">{{ warnPrice }}</span>
            元
          </div>
        </div>
      </div>
      <div class="warn" style="text-align: center; margin-top: 24px">
        继续执行此操作可能会导致订单亏损，请确认是否继续
      </div>
      <base-dialog-footer
        cancelText="返回修改"
        confirmText="确定继续"
        @cancel="innerVisible = false"
        @confirm="confirmWarn"
      ></base-dialog-footer>
    </el-dialog>
    <base-dialog-footer
      confirmText="确定"
      @cancel="showOrderPriceDialog = false"
      @confirm="confirm"
    ></base-dialog-footer>
  </el-dialog>
</template>

<script>
  import {
    changePriceInfoByOrders,
    changePriceByOrders,
    changePriceInfoByGoods,
    changePriceByGoods,
    getChangePriceDimension,
  } from '@/api/shop/order.js'
  export default {
    name: 'orderChangePrice',
    components: {},
    props: {
      settingListDetail: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        image: require('../../../../../assets/warn.png'),
        type: 1,
        label_Style: {
          'font-size': '16px',
        },
        content_style: {
          'margin-top': '20px',
          'font-size': '16px',
        },
        form: {
          change_order_price: '',
          freight_difference: '',
        },
        showOrderPriceDialog: false,
        innerVisible: false,
        orderListInfo: [
          {
            name: '销售总额',
            code: 'total_origin_price',
            tip: '订单中所有商品的原始总金额，不包含优惠、运费等其他费用',
          },
          {
            name: '优惠总额',
            code: 'total_discount_price',
            tip: '订单使用的优惠券、满减等各类优惠的总金额',
          },
          {
            name: '运费',
            code: 'total_shipping_fee',
            tip: '根据收货地址和运费模板计算的配送费用',
          },
          {
            name: '待支付金额',
            code: 'order_amount',
            tip: '改价前消费者待支付的金额，销售总额减去优惠金额再加上运费后的应付金额',
          },
          {
            name: '涨价/减价金额',
            code: 'change_order_price',
            tip: '对订单金额进行调整的金额，正数表示涨价，负数表示减价',
          },
          {
            name: '运费差额',
            code: 'freight_difference',
            tip: '对运费进行调整的金额，正数表示增加运费，负数表示减少运费',
          },
          {
            name: '修改后实付金额',
            code: 'change_order_amount',
            tip: '最终顾客需要支付的金额，包含所有价格调整',
          },
          {
            name: '总成本金额',
            code: 'total_cost_price',
            tip: '订单内所有商品的成本价总和，若成本不正确请检查各商品基础资料中的成本价格是否正确设置',
          },
        ],
        tableData: [],
        tableDataTatal: [],
        orderPriceInfo: {}, //订单维度详细信息
        orderDetailData: {},
        change_order_amount: '', //修改后实付金额
        shipping_feeL: '', //商品运费
        change_shipping_feeL: '0', //订单运费
        adjust_shipping_fee: '', //订单运费
        change_shipping_feeL_after: '', //修改后得订单运费
        change_order_price: '',
        warnPrice: '', //实付比成本少
        costPriceNum: '', //成本金额
        pay_amountNum: '', //实付金额
        discountAountNum: '', //优惠金额
      }
    },
    methods: {
      open(data) {
        this.getChangePriceDimensionFn()
        this.form.change_order_price = '0.00'
        this.form.freight_difference = '0.00'
        this.change_shipping_feeL = '0'
        this.orderDetailData = data
        this.showOrderPriceDialog = true
        this.type = this.settingListDetail?.price_edit_scope == 'order' ? 1 : 2 //product (product商品维度 order订单维度)
        if (this.type == 1) {
          this.changePriceInfoByOrdersFn()
        } else {
          this.changePriceInfoByGoodsFn()
        }
        this.$nextTick(() => {
          // this.$refs.form.resetFields()
        })
      },
      // 改价维度
      getChangePriceDimensionFn() {
        getChangePriceDimension().then((res) => {
          console.log(res)
        })
      },
      //运费价格
      getChangePrice(value, code) {
        if (code == 'change_order_price') {
          if (this.form.change_order_price < 0) {
            if (Math.abs(this.form.change_order_price) > this.orderPriceInfo['order_amount']) {
              this.$message.error('最小减价金额为待支付金额')
              this.form.change_order_price = '0.00'
            }
          }
        }
        if (code == 'freight_difference') {
          if (this.form.freight_difference < 0) {
            if (
              Math.abs(this.form.freight_difference) > this.orderPriceInfo['total_shipping_fee']
            ) {
              this.$message.error('最小运费差额为运费金额')
              this.form.freight_difference = '0.00'
            }
          }
        }
        let orderPrice = Number(this.change_order_amount)
        let changePrice =
          Number(this.form.change_order_price) + Number(this.form.freight_difference)
        this.orderPriceInfo['change_order_amount'] = Number(orderPrice + changePrice).toFixed(2)
      },
      // 详细信息（订单维度）
      changePriceInfoByOrdersFn() {
        let params = {
          sub_order_no: this.orderDetailData.sub_order_no,
          change_order_price: '',
          freight_difference: '',
        }
        changePriceInfoByOrders(params).then((res) => {
          if (res.code == 200) {
            this.orderPriceInfo = res.data.price_info
            this.change_order_amount = this.orderPriceInfo.change_order_amount
          }
        })
      },
      // （订单维度）
      changePriceByOrdersFn() {
        let params = {
          sub_order_no: this.orderDetailData.sub_order_no,
          ...this.form,
        }
        changePriceByOrders(params).then((res) => {
          if (res.code == 200) {
            this.$message.success('改价成功')
            this.showOrderPriceDialog = false
            this.innerVisible = false
            this.$emit('update')
          }
        })
      },
      // （商品维度）
      changePriceByGoodsFn() {
        let tabelChangePrice = []
        this.tableData.forEach((item) => {
          tabelChangePrice.push({
            id: item.id,
            adjust_amount: item?.change_order_price || 0,
          })
        })
        let params = {
          sub_order_no: this.orderDetailData.sub_order_no,
          adjust_shipping_fee: this.change_shipping_feeL_after,
          order_goods_adjust: tabelChangePrice,
        }
        changePriceByGoods(params).then((res) => {
          if (res.code == 200) {
            this.$message.success('改价成功')
            this.showOrderPriceDialog = false
            this.innerVisible = false
            this.$emit('update')
          }
        })
      },
      // 確定
      confirm() {
        if (this.type == 1) {
          let price =
            Number(this.orderPriceInfo.total_cost_price) -
            Number(this.orderPriceInfo.change_order_amount)
          this.warnPrice = price.toFixed(2)
          if (Number(this.warnPrice) > 0) {
            this.innerVisible = true
          } else {
            this.changePriceByOrdersFn()
          }
        }
        if (this.type == 2) {
          let price = Number(this.costPriceNum) - Number(this.pay_amountNum)
          this.warnPrice = price.toFixed(2)
          if (Number(this.warnPrice) > 0) {
            this.innerVisible = true
          } else {
            this.changePriceByGoodsFn()
          }
        }
      },
      // 继续修改
      confirmWarn() {
        if (this.type == 1) {
          this.changePriceByOrdersFn()
        } else {
          this.changePriceByGoodsFn()
        }
      },
      // 订单改价详细信息（商品维度）
      changePriceInfoByGoodsFn() {
        changePriceInfoByGoods({ sub_order_no: this.orderDetailData.sub_order_no }).then((res) => {
          console.log(res)
          if (res.code == 200) {
            this.shipping_feeL = res.data.shipping_fee
            this.adjust_shipping_fee = res.data.shipping_fee
            this.tableData = res.data.list
            this.tableData.forEach((item) => {
              this.$set(item, 'change_order_price', 0)
            })
            this.tableDataTatal = res.data.count
          }
        })
      },
      //商品实付金额
      getGoodsChangePrice(row, index) {
        if (row.change_order_price < 0) {
          if (Math.abs(row.change_order_price) > row.total_price) {
            this.$message.error('最小减价金额为待支付金额')
            row.change_order_price = '0.00'
          }
        }
        let price = Number(Number(row.change_order_price || 0) + Number(row.total_price)).toFixed(2)
        this.$set(this.tableData[index], 'pay_amount', price)
      },
      //直接减免运费
      changeShipping() {
        this.change_shipping_feeL = '-' + this.shipping_feeL
        this.change_shipping_feeL_after = '-' + this.shipping_feeL
        this.adjust_shipping_fee = 0
      },
      //输入改变运费
      changeShipping1() {
        if (!this.change_shipping_feeL) {
          this.change_shipping_feeL = 0
        }
        if (this.change_shipping_feeL < 0) {
          if (Math.abs(this.change_shipping_feeL) > Number(this.shipping_feeL)) {
            this.$message.error('最小运费差额为运费金额')
            this.change_shipping_feeL = '0.00'
          }
        }
        let price = Number(Number(this.shipping_feeL) + Number(this.change_shipping_feeL)).toFixed(
          2
        )
        this.adjust_shipping_fee = price
        this.change_shipping_feeL_after = this.change_shipping_feeL
      },
      getPayAmount(param) {
        const { columns, data } = param

        const sums = []

        console.log(columns, data)

        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '合计'
            return
          }

          switch (column.property) {
            //这是根据prop来判断是哪一列数据

            case 'quantity':
              //表示的是每一空格是要写什么内容
              let quantityNum = 0
              data.forEach((item) => {
                quantityNum += Number(item.quantity)
              })

              sums[index] = this.tableDataTatal['total_quantity'] //quantityNum

              break
            // 实付总额
            case 'pay_amount':
              //表示的是每一空格是要写什么内容
              let pay_amountNum = 0
              data.forEach((item) => {
                pay_amountNum += Number(item.pay_amount)
              })
              pay_amountNum = pay_amountNum + Number(this.adjust_shipping_fee)
              sums[index] = '¥' + pay_amountNum.toFixed(2) + ` (含运费¥${this.adjust_shipping_fee})`
              this.pay_amountNum = pay_amountNum.toFixed(2)
              break
            // 待支付金额
            case 'total_price':
              //表示的是每一空格是要写什么内容
              let total_priceNum = 0
              data.forEach((item) => {
                total_priceNum += Number(item.total_price)
              })

              sums[index] =
                '¥' + this.tableDataTatal['total_unpaid_amount'] + ` (含运费¥${this.shipping_feeL})`

              break
            // 涨价/减价金额
            case 'change_order_price':
              //表示的是每一空格是要写什么内容
              let change_order_priceNum = 0
              data.forEach((item) => {
                change_order_priceNum += Number(item.change_order_price)
              })

              sums[index] = '¥' + change_order_priceNum

              break
            //优惠金额
            case 'discount_amount':
              //表示的是每一空格是要写什么内容
              let discount_amountNum = 0
              data.forEach((item) => {
                discount_amountNum += Number(item.discount_amount)
              })

              sums[index] = '¥' + this.tableDataTatal['total_discount_amount']
              this.discountAountNum = this.tableDataTatal['total_discount_amount']

              break
            //成本金额
            case 'cost_price':
              //表示的是每一空格是要写什么内容
              let cost_priceNum = 0
              data.forEach((item) => {
                cost_priceNum += Number(item.cost_price)
              })
              sums[index] = this.tableDataTatal['total_cost_price']
              this.costPriceNum = this.tableDataTatal['total_cost_price']
              break

            default:
              break
          }
        })

        return sums
      },
    },
  }
</script>

<style lang="scss" scoped>
  .n_tips {
    display: flex;
    align-items: center;
    padding: 18px 20px;
    background: #eef6ff;
    border-radius: 8px;
    border: 1px solid #94c3ff;
    .tip_icon {
      display: block;
      width: 24px;
      height: 24px;
      margin-right: 10px;
    }
    .tip_title {
      font-size: 16px;
      color: #222222;
      line-height: 16px;
      font-weight: 600;
    }
  }
  .n_tips1 {
    display: flex;
    align-items: center;
    padding: 18px 20px;
    background: #fff6f6;
    border-radius: 8px;
    border: 1px solid #ffbdbd;
    .tip_icon {
      display: block;
      width: 24px;
      height: 24px;
      margin-right: 10px;
    }
    .tip_title {
      color: #222222;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #222222;
    }
    .tip_name {
      margin-top: 5px;
      .warnMoney {
        color: #ff2727;
      }
    }
  }
  .warn {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #222222;
    line-height: 16px;
    font-style: normal;
    text-align: center;
    margin-top: 24px;
  }
  .content {
    margin-top: 20px;
  }
  .bottonContent {
    margin-top: 20px;
    gap: 30px;
    .bottomright {
      width: 50%;
      gap: 20px;
      .el-input {
        width: 50%;
      }
    }
  }
</style>
