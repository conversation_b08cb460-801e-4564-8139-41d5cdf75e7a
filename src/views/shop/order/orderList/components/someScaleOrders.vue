<!--
 * @Author: liqian liqian@123
 * @Date: 2025-05-28 11:50:48
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-08-01 10:12:18
 * @FilePath: \qst-merchant-admin-2.0\src\views\shop\order\orderList\components\someScaleOrders.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-dialog
    append-to-body
    :visible.sync="someScaleOrdersDialog"
    :close-on-click-modal="false"
    title="批量打单发货"
    width="800px"
  >
    <BaseContentTip tip="请认真核对订单是否存在完成售后商品，防止多发！" />

    <el-form ref="form" :model="form" :rules="rules" label-width="80px" label-position="top">
      <el-form-item label="发货类型" prop="is_needs_logistics">
        <el-radio-group v-model="form.is_needs_logistics" @input="changeTypeMethod">
          <el-radio label="1">物流发货</el-radio>
          <el-radio label="2">无需物流</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="打印类型" prop="type" v-if="form.is_needs_logistics == 2">
        <el-radio-group v-model="form.type" @input="changeTypeMethod">
          <el-radio label="3">出库单</el-radio>
          <el-radio label="2" disabled>小票</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="承运商" prop="carrier_id" v-if="form.is_needs_logistics == 1">
        <el-select
          v-model="form.carrier_id"
          placeholder="请选择承运商"
          style="width: 100%"
          @change="getSelsctExpress"
        >
          <el-option
            v-for="item in carrierListData"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="发货地址" prop="mch_address_id" v-if="form.is_needs_logistics == 1">
        <el-select v-model="form.mch_address_id" placeholder="请选择发货地址" style="width: 100%">
          <el-option
            v-for="item in mchaddressList"
            :key="item.id"
            :label="item.address"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="打印模板" prop="print_template_id">
        <el-select
          v-model="form.print_template_id"
          placeholder="请选择打印模板"
          style="width: 100%"
        >
          <el-option
            v-for="item in printTemplateList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <base-dialog-footer
      confirmText="确定"
      @cancel="someScaleOrdersDialog = false"
      @confirm="confirm"
    ></base-dialog-footer>
  </el-dialog>
</template>
<script>
  import {
    batchWaybillNo,
    shipmentAddressList,
    getCarrierList,
    templateListSelect,
    batchOutboundOrderPrintDetail,
  } from '@/api/shop/order.js'
  import { getmaparrList } from '@/utils/index'

  export default {
    props: {
      settingListDetail: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        someScaleOrdersDialog: false,

        form: {
          is_needs_logistics: '1',
          carrier_id: '',
          mch_address_id: '',
          print_template_id: '',
          type: '3',
        },
        rules: {
          is_needs_logistics: [{ required: true, message: '请选择发货类型', trigger: 'change' }],
          print_template_id: [{ required: true, message: '请选择打印类型', trigger: 'change' }],
          carrier_id: [{ required: true, message: '请选择承运商', trigger: 'change' }],
          type: [{ required: true, message: '请选择发货地址', trigger: 'change' }],
          mch_address_id: [{ required: true, message: '请选择打印类型', trigger: 'change' }],
        },
        carrierListData: [],
        mchaddressList: [],
        printTemplateList: [],
      }
    },

    methods: {
      open(data) {
        this.someScaleOrdersDialog = true
        this.orderDetailData = data
        this.form.carrier_id = this.settingListDetail.waybill_carrier || ''
        this.form.print_template_id = this.settingListDetail.outbound_template || ''

        this.getcarrierList()
        this.getmchaddressList()
        this.form.carrier_id && this.getprintList()
        this.$nextTick(() => {
          this.$refs.form.resetFields()
        })
      },
      //承运商列表
      getcarrierList() {
        getCarrierList().then((res) => {
          if (res.code == 200) {
            this.carrierListData = res.data
          }
        })
      },
      //发货地址列表
      getmchaddressList() {
        shipmentAddressList().then((res) => {
          if (res.code == 200) {
            this.mchaddressList = res.data
            this.form.mch_address_id =
              this.mchaddressList.length > 0 ? this.mchaddressList[0].id : ''
          }
        })
      },
      //打印模版
      getprintList() {
        templateListSelect({
          type: this.form.is_needs_logistics == 1 ? 1 : this.form.type, //1-电子面单 2-小票 3-出库单，必选参数
          express_id: this.form.is_needs_logistics == 1 ? this.settingListDetail?.express_id : '', //物流公司ID，非必填参数，当类型为1时候必填
        }).then((res) => {
          if (res.code == 200) {
            this.printTemplateList = res.data
            this.form.print_template_id = this.printTemplateList[0].value
          }
        })
      },
      //选择承运商
      getSelsctExpress(value) {
        let getSelectExpress = getmaparrList(this.carrierListData, 'value', value)

        templateListSelect({
          type: this.form.is_needs_logistics == 1 ? 1 : this.form.type, //1-电子面单 2-小票 3-出库单，必选参数
          express_id: getSelectExpress.length > 0 ? getSelectExpress[0].express_id : '', //物流公司ID，非必填参数，当类型为1时候必填
        }).then((res) => {
          if (res.code == 200) {
            this.printTemplateList = res.data
          }
        })
      },
      //
      changeTypeMethod(value) {
        if (value == 2) {
          this.form.carrier_id = ''
          this.form.mch_address_id = ''
        } else {
          this.form.carrier_id = this.settingListDetail.waybill_carrier || ''
          this.form.print_template_id = this.settingListDetail.outbound_template || ''
          this.getmchaddressList()
        }
        this.getprintList()
      },
      confirm() {
        if (this.form.is_needs_logistics == 2) {
          this.$refs.form.validate((valid) => {
            if (valid) {
              let order_no = []
              this.orderDetailData.forEach((item) => {
                order_no.push(item.sub_order_no)
              })
              let params = {
                print_template_id: this.form.print_template_id, //打印模板ID
                order_no: order_no, //子订单编号
              }
              batchOutboundOrderPrintDetail(params).then((res) => {
                if (res.code == 200) {
                  this.$message.success(res.msg)
                  this.$emit('update')
                  this.someScaleOrdersDialog = false
                }
              })
            }
          })
        }

        if (this.form.is_needs_logistics == 1) {
          this.$refs.form.validate((valid) => {
            if (valid) {
              let order_no = []
              this.orderDetailData.forEach((item) => {
                order_no.push(item.sub_order_no)
              })
              let params = {
                carrier_id: this.form.carrier_id, //承运商ID
                print_template_id: this.form.print_template_id, //打印模板ID
                mch_address_id: this.form.mch_address_id, //发货地址ID
                order_no: order_no, //子订单编号
              }
              batchWaybillNo(params).then((res) => {
                if (res.code == 200) {
                  this.$message.success(res.msg)
                  this.$emit('update')
                  this.someScaleOrdersDialog = false
                }
              })
            }
          })
        }
      },
    },
  }
</script>
<style lang="scss" scoped></style>
