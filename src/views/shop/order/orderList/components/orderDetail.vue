<!--
 * @Author: liqian liqian@123
 * @Date: 2025-05-19 15:17:29
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-28 14:46:14
 * @FilePath: \qst-merchant-admin-2.0\src\views\shop\order\orderList\components\orderDetail.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-dialog
    append-to-body
    :visible.sync="showDetailDialog"
    :close-on-click-modal="false"
    title="订单详情"
    width="800px"
  >
    <!-- <el-steps :active="activeTip" align-center v-if="orderDetailData.order_status"> -->
    <!-- <el-step
        v-for="(item, index) in orderDetailData.order_status"
        :key="index"
        :title="item.change_message"
        :description="item.created_at || '-'"
      ></el-step> -->
    <div class="stepcontent flex">
      <div
        class="flex steps"
        style="width: 130px"
        v-for="(item, index) in orderDetailData.order_status"
      >
        <div class="stepdiv">
          <div style="position: relative; text-align: center">
            <div
              class="stepName"
              :class="{ active: index <= activeTip && item.type == 'complete' }"
            >
              {{ item.change_message }}
            </div>
            <div
              class="stepline"
              :class="{ cancel: 5 == orderDetailData.order_status.length }"
            ></div>
          </div>
          <span style="color: #999999">{{ item.created_at ? item.created_at : '-' }}</span>
        </div>
      </div>
    </div>
    <!-- </el-steps> -->
    <div class="content" v-if="orderDetailData">
      <el-descriptions
        title="订单信息"
        :column="2"
        :labelStyle="label_Style"
        :contentStyle="content_style"
      >
        <el-descriptions-item label="父单号">
          <div v-if="orderDetailData.order_info && orderDetailData.order_info.order_no">
            {{ orderDetailData.order_info && orderDetailData.order_info.order_no }}
            <span
              @click="copysuborderno(orderDetailData.order_info, 'order_no')"
              style="cursor: pointer; margin-left: 5px"
            >
              <i class="el-icon-copy-document"></i>
            </span>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="订单号">
          <div v-if="orderDetailData.order_info && orderDetailData.order_info.sub_order_no">
            {{ orderDetailData.order_info && orderDetailData.order_info.sub_order_no }}
            <span
              @click="copysuborderno(orderDetailData.order_info, 'sub_order_no')"
              style="cursor: pointer; margin-left: 5px"
            >
              <i class="el-icon-copy-document"></i>
            </span>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="所属门店">
          {{ orderDetailData.order_info && orderDetailData.order_info.shop_name }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ orderDetailData.order_info && orderDetailData.order_info.created_at }}
        </el-descriptions-item>
        <!-- <el-descriptions-item label="订单金额">
          <span v-if="orderDetailData.order_info">
            ¥{{ orderDetailData.order_info.order_amount }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="支付金额">
          <span v-if="orderDetailData.order_info">
            ¥{{ orderDetailData.order_info.pay_amount }}
          </span>
        </el-descriptions-item> -->
        <el-descriptions-item label="买家备注">
          {{ orderDetailData.order_info && orderDetailData.order_info.remark }}
        </el-descriptions-item>
        <el-descriptions-item label="下单人">
          {{ orderDetailData.order_info && orderDetailData.order_info.member_name }}
        </el-descriptions-item>
      </el-descriptions>
      <el-divider></el-divider>
      <el-descriptions
        title="收货人信息"
        :column="2"
        :labelStyle="label_Style"
        :contentStyle="content_style"
      >
        <el-descriptions-item label="收货人">
          {{ orderDetailData.member_address_info && orderDetailData.member_address_info.name }}
        </el-descriptions-item>
        <el-descriptions-item label="联系电话">
          {{ orderDetailData.member_address_info && orderDetailData.member_address_info.phone }}
        </el-descriptions-item>
        <el-descriptions-item label="收货地址" v-if="shipping_method == 'express'">
          {{ orderDetailData.member_address_info && orderDetailData.member_address_info.address }}
        </el-descriptions-item>
      </el-descriptions>
      <el-divider></el-divider>
      <el-descriptions
        title="配送信息"
        :column="2"
        :labelStyle="label_Style"
        :contentStyle="content_style"
      >
        <template v-if="addressList.length > 0 && shipping_method == 'express'">
          <template v-for="(item, index) in addressList">
            <el-descriptions-item label="配送方式">
              {{ item.delivery_type }}
            </el-descriptions-item>
            <el-descriptions-item label="物流公司">
              {{ item.express_name }}
            </el-descriptions-item>
            <el-descriptions-item label="物流单号">
              {{ item.tracking_number }}
            </el-descriptions-item>
            <el-descriptions-item label="发布时间">
              {{ item.created_at }}
            </el-descriptions-item>
          </template>
        </template>
        <template v-else-if="addressList && shipping_method == 'pickup'">
          <el-descriptions-item label="配送方式">
            {{ addressList.delivery_type }}
          </el-descriptions-item>
        </template>
        <el-descriptions-item v-else label="配送方式">-</el-descriptions-item>
      </el-descriptions>
      <el-divider></el-divider>
      <el-descriptions title="商品信息"></el-descriptions>
      <el-table
        :data="orderDetailData.order_goods_info && orderDetailData.order_goods_info"
        :header-cell-style="{ background: '#F5F7FA' }"
        style="width: 100%"
      >
        <el-table-column prop="info" label="商品信息" width="250">
          <template slot-scope="scope">
            <div class="flex">
              <el-image
                style="width: 50px; height: 50px"
                :src="scope.row.product_image"
                :preview-src-list="[scope.row.product_image]"
              ></el-image>
              <div class="saleInfo">
                <span :title="scope.row.sku_name" class="saleInfotitle">
                  {{ scope.row.sku_name }}
                </span>
                <span :title="scope.row.spec_value_items" class="saleInfotitle1">
                  规格：{{ scope.row.spec_value_items }}*{{ scope.row.quantity }}
                </span>
                <span class="saleInfoprice">支付单价:¥{{ scope.row.price }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="销售单价">
          <template slot-scope="scope">
            <span class="settle_price1" v-if="scope.row.price">¥{{ scope.row.price }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="total_price" label="销售总价">
          <template slot-scope="scope">
            <span class="settle_price1" v-if="scope.row.total_price">
              ¥{{ scope.row.total_price }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="reduce_amount" label="优惠金额">
          <template slot-scope="scope">
            <span class="settle_price1" v-if="scope.row.reduce_amount">
              ¥{{ scope.row.reduce_amount }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="settle_price" label="实付金额">
          <template slot-scope="scope">
            <span class="settle_price" v-if="scope.row.settle_price">
              ¥{{ scope.row.settle_price }}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <div class="payContent flex" v-if="orderDetailData.pay_info">
        <div class="contentBottom">
          <span class="title">商品总数：</span>
          <span class="pay1">{{ orderDetailData.pay_info.total_quantity }}件</span>
        </div>
        <div class="contentBottom">
          <span class="title">商品总价：</span>
          <span class="pay1">¥{{ orderDetailData.pay_info.total_price }}</span>
        </div>
        <div class="contentBottom">
          <span class="title">运费：</span>
          <span class="pay1">¥{{ orderDetailData.pay_info.total_shipping_fee }}</span>
        </div>
      </div>
      <div class="payContent flex" v-if="orderDetailData.pay_info">
        <div class="contentBottom">
          <span class="title">各类优惠金额/抵扣金额：</span>
          <span class="pay2">¥{{ orderDetailData.pay_info.discount_amount }}</span>
        </div>
        <div class="contentBottom">
          <span class="title">随机立减金额：</span>
          <span class="pay2">¥{{ orderDetailData.pay_info.reduce_amount }}</span>
        </div>
        <div class="contentBottom">
          <span class="title">商品优惠：</span>
          <span class="pay2">¥{{ orderDetailData.pay_info.total_goods_discount_amount }}</span>
        </div>
        <div class="contentBottom">
          <span class="title">优惠总额：</span>
          <span class="pay2">¥{{ orderDetailData.pay_info.total_discount_amount }}</span>
        </div>
      </div>
      <div class="payInfo" v-if="orderDetailData.pay_info">
        <span class="money">实付金额:</span>
        <span>¥{{ orderDetailData.pay_info.total_settle_amount }}</span>
      </div>
    </div>
  </el-dialog>
</template>

<script>
  import { getarrindex } from '@/utils/index'
  import { getOrderDetail } from '@/api/shop/order.js'
  export default {
    name: 'orderDetail',
    components: {},
    data() {
      return {
        content_style: {
          'font-size': '14px',
          color: '#8C8C8C ',
        },
        label_Style: {
          'font-size': '14px',
          color: '#8C8C8C ',
        },
        activeTip: 0,
        showDetailDialog: false,
        tableData: [],
        orderDetailData: {},
        express_infoJson: ['delivery_type', 'express_name', 'tracking_number', 'created_at'],
        shipping_method: '', //express(快递),pickup(自提)
        addressList: [], //地址列表
      }
    },
    methods: {
      open(data) {
        this.getOrderDetailfn(data)
        this.$nextTick(() => {
          this.showDetailDialog = true
        })
      },
      getOrderDetailfn(data) {
        getOrderDetail({ sub_order_no: data.sub_order_no }).then((res) => {
          if (res.code == 200) {
            this.orderDetailData = res.data
            this.shipping_method = res.data.shipping_method
            this.addressList =
              this.shipping_method == 'express' ? res.data.express_info : res.data.pickup_info
            this.getActiveList()
          }
        })
      },
      //得到订单流程
      getActiveList() {
        //   描述：类型（complete已完成） （unfinished未完成）
        const condition = (element) => element.type == 'complete' // 例如，我们想找到所有大于3的元素的索引
        const indexes = this.orderDetailData.order_status
          .map((element, index) => (condition(element) ? index : -1))
          .filter((index) => index !== -1)
        this.activeTip = indexes[indexes.length - 1]
        if (this.activeTip == this.orderDetailData.order_status.length - 1) {
          this.activeTip = this.orderDetailData.order_status.length
        }
        console.log(this.activeTip)
      },
      //复制订单编号
      async copysuborderno(data, code = 'sub_order_no') {
        // 创建临时输入框来复制文本
        const textarea = document.createElement('textarea')
        textarea.value = data[code]
        document.body.appendChild(textarea)
        textarea.select()

        try {
          document.execCommand('copy')
          this.$message.success('复制成功！')
        } catch (err) {
          this.$message.error('复制失败，请手动复制')
        }

        document.body.removeChild(textarea)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .content {
    padding: 15px;
    .el-descriptions {
      margin-top: 10px;
    }
  }
  .stepcontent {
    justify-content: space-around;
    .steps:last-child .stepline {
      display: none;
    }
    .stepdiv {
      text-align: center;
      width: 100%;
      .stepName {
        margin-bottom: 5px;
        &.active {
          color: #0071fe;
        }
      }
      .stepline {
        border-top: 1px dashed #d7d7d7;
        width: 70px;
        position: absolute;
        left: 96%;
        top: 8px;
        &.cancel {
          left: 81%;
        }
      }
    }
  }
  .saleInfo {
    display: flex;
    flex-direction: column;
    margin-left: 10px;
    .saleInfotitle {
      display: inline-block;
      white-space: nowrap;
      width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .saleInfotitle1 {
      display: inline-block;
      white-space: nowrap;
      width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 12px;
    }
    .saleInfoprice {
      color: #ff5500;
      font-size: 12px;
    }
    span {
      height: 20px;
    }
  }
  .payContent {
    margin: 10px;
    font-size: 16px;
    justify-content: flex-end;
    .contentBottom {
      margin-right: 20px;
      .title {
        font-size: 14px;
        color: #999999;
        text-align: left;
      }
      .pay1 {
        font-size: 13px;
        font-weight: 400;
        color: #222222;
      }
      .pay2 {
        font-size: 13px;
        color: #ff5500;
      }
    }
  }
  .payInfo {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
    font-size: 16px;
    .money {
      font-weight: 500;
      color: #222222;
    }
    span:last-child {
      color: #ff5500;
      font-weight: 600;
    }
  }
  ::v-deep {
    // 表格头部样式
    .el-table th.el-table__cell {
      background: #f5f7fa;
      box-shadow: inset 0px -1px 0px 0px #ebeef5, inset 0px -1px 0px 0px #ebeef5;
    }
    th.el-table__cell {
      background-color: #f5f7fa;
    }
    th.el-table__cell .cell {
      color: #303133;
      font-weight: 600;
    }
  }
  .settle_price {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #222222;
  }
  .settle_price1 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #6e6e7a;
  }
  ::v-deep {
    .el-descriptions__title {
      font-size: 14px;
      font-weight: 500;
      color: #222222;
    }
  }
</style>
