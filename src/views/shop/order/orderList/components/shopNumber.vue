<!-- 商品数量组件 -->
<template>
  <div class="flex cart-num">
    <img
      class="symbol"
      @click="reduceNum"
      :src="imgOssPath + (shopNum <= minNum ? '20250514_reduce.png' : '20250514_reduce1.png')"
      mode=""
    ></img>
    <input disabled :value="num" @blur="numBlur" @focus="numFocus" />
    <img
      class="symbol"
      @click="addNum"
      :src="imgOssPath + (shopNum >= maxNum ? '20250514_add.png' : '20250514_add1.png')"
      mode=""
    ></img>
  </div>
</template>

<script>
  export default {
    name: 'shopNumber',
    props: {
      // 购物车场景点一加一
      initType: String,

      // 输入数量
      shopNum: {
        type: Number,
        default: 1,
      },
      refresh: Boolean,

      // 最大
      maxNum: {
        type: Number,
        default: 999,
      },

      // 最小
      minNum: {
        type: Number,
        default: 1,
      },

      // 是否加载
      isLoading: {
        type: Bo<PERSON>an,
        default: false,
      },
    },
    watch: {
      shopNum: {
        handler() {
          this.num = this.shopNum
        },
        deep: true,
        immediate: true,
      },
      refresh: {
        handler() {
          this.num = this.shopNum
        },
        deep: true,
        immediate: true,
      },
    },
    data() {
      return {
        num: '',
        imgOssPath: 'https://7dd-statics.oss-cn-beijing.aliyuncs.com/smallProgram/qst_new_merchant_image/',
      }
    },
    methods: {
      addNum() {
        if (this.maxNum == 0) {
          return
        }
        this.num += 1
        this.$emit('numChange', this.num)
      },
      reduceNum() {
        if (this.num == this.minNum) {
          return
        }
        this.num -= 1
        this.$emit('numChange', this.num)
      },
      numBlur(e) {
        console.log(e)
        if (this.num == '' || this.num < this.minNum) {
          this.num = this.minNum
        } else {
          this.num = Number(e.detail.value)
        }
        this.$emit('numBlur')
        this.$emit('numChange', this.num)
      },
      // 焦点
      numFocus() {
        this.$emit('numFocus')
      },
    },
  }
</script>

<style scoped lang="scss">
  .cart-num {
    width: 100px;
    height: 30px;
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #ebebeb;
    text-align: center;
    position: relative;
    input {
      width: 30px;
      border: 1px solid #f2f3f5;
    }
    .symbol {
      width: 12px;
      height: 12px;
      padding: 0 8px;
    }

  }
</style>
