<!--
 * @Author: liq<PERSON> liqian@123
 * @Date: 2025-05-28 11:50:48
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-25 09:44:04
 * @FilePath: \qst-merchant-admin-2.0\src\views\shop\order\orderList\components\someimportOrders.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-dialog
    append-to-body
    :visible.sync="someImportOrdersDialog"
    :close-on-click-modal="false"
    title="批量导入发货"
    width="750px"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="80px" label-position="top">
      <el-form-item label="下载模板">
        <div class="flex">
          <el-button icon="el-icon-download">整单发货模板</el-button>
          <el-button icon="el-icon-download">拆单发货模板</el-button>
        </div>
      </el-form-item>
      <el-form-item label="上传文件" prop="type">
        <el-upload
          class="upload-demo"
          action="fakeaction"
          :limit="1"
          accept=".xlsx, .xls,"
          :http-request="(e) => upLoadImg(e)"
          :before-upload="handelUpload"
        >
          <div class="uploadContent flex-c">
            <i class="el-icon-plus"></i>
            <div>上传文件</div>
          </div>
          <div class="el-upload__tip" slot="tip">
            点击或拖拽文件到此区域上传，支持 .xlsx, .xls 格式的文件
          </div>
          <!-- 自定义文件列表项 -->
        </el-upload>
      </el-form-item>
      <BaseContentTip
        :srcImg="imgOssPath + '20250517_shuoming.png'"
        tipColor="#eef6ff"
        tiptitle="操作提示"
        :tip="tipmain"
      />
      <!-- <div class="n_tips">
        <img class="tip_icon" :src="imgOssPath + '20250517_shuoming.png'" alt />
        <div>
          <div class="tip_title">操作提示</div>
          <div class="tip_name flex">
            <span class="dian"></span>
            请确保填写的物流信息准确无误
          </div>
          <div class="tip_name flex">
            <span class="dian"></span>
            建议使用系统提供的模板填写物流信息
          </div>
          <div class="tip_name flex">
            <span class="dian"></span>
            导入成功后订单状态将自动更新为已发货
          </div>
        </div>
      </div> -->
    </el-form>

    <base-dialog-footer
      confirmText="确定"
      @cancel="someImportOrdersDialog = false"
      @confirm="confirm"
    ></base-dialog-footer>
  </el-dialog>
</template>
<script>
  import { uploadExcelVerify } from '@/api/shop/order.js'
  import { uploadFile } from '@/api/common.js'

  export default {
    props: {
      settingListDetail: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        someImportOrdersDialog: false,

        form: {
          is_needs_logistics: '1',
          carrier_id: '',
        },
        rules: {},
        fileList: [],
        tipmain: [
          '请确保填写的物流信息准确无误',
          '建议使用系统提供的模板填写物流信息',
          '导入成功后订单状态将自动更新为已发货',
          '请认真核对订单是否存在完成售后商品，防止多发！',
        ],
      }
    },

    methods: {
      open() {
        this.someImportOrdersDialog = true
        this.$nextTick(() => {
          this.fileList = []
          this.$refs.form.resetFields()
        })
      },
      upLoadImg(file) {
        // 上传
        let data = new FormData()
        data.append('file', file.file)
        uploadFile(data).then((res) => {
          this.fileList = [res.data]
          console.log(this.fileList)
        })
      },
      //上传校验
      handelUpload(file) {
        // if (file.size > 3 * 1024 * 1024) {
        //   this.$message.error('文件大小不能超过3MB！')
        //   return false
        // }
        const index = file.name.lastIndexOf('.')
        const suffix = file.name.substring(index, file.name.length)
        console.log('suffix', suffix)
        if (!this.checkSuffix(suffix)) {
          this.$message.error('上传文件只能是xls,xlsx格式!')
          return false
        }
      },
      checkSuffix(str) {
        var strRegex = /\.(xls|xlsx)$/
        if (strRegex.test(str.toLowerCase())) {
          return true
        } else {
          return false
        }
      },
      confirm() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            if (!this.fileList.length) {
              this.$message.error('请先上传文件')
              return false
            }
            uploadExcelVerify({ file_path: this.fileList[0].url }).then((res) => {
              if (res.code == 200) {
                this.$emit('update')
                this.someImportOrdersDialog = false
              }
            })
          }
        })
      },
    },
  }
</script>
<style lang="scss" scoped>
  .uploadContent {
    width: 100px;
    height: 100px;
    border-radius: 6px;
    border: 1px solid #dcdfe6;
    flex-direction: column;
    font-weight: 400;
    font-size: 12px;
    color: #606266;
    line-height: 17px;
  }
  .n_tips {
    display: flex;
    padding: 18px 20px;
    background: #eef6ff;
    border-radius: 8px;
    border: 1px solid #94c3ff;
    .tip_icon {
      display: block;
      width: 20px;
      height: 20px;
    }
    .tip_title {
      font-size: 16px;
      color: #222222;
      line-height: 16px;
      font-weight: 600;
      margin-left: 15px;
      margin-top: 3px;
    }
    .tip_name {
      font-size: 14px;
      color: #666666;
      line-height: 20px;
      margin-top: 8px;
      .dian {
        display: inline-block;
        width: 4px;
        height: 4px;
        background: #0071fe;
        margin-right: 10px;
      }
    }
  }
</style>
