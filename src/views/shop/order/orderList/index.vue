<template>
  <div class="goods">
    <base-account-shop @checkAccountShopFn="checkAccountShopFn"></base-account-shop>
    <div v-if="isShowPage">
      <el-card>
        <base-form
          ref="baseForm"
          :tableForm="tableForm"
          :formArray="formArray"
          @searchForm="searchForm"
        >
          <!-- <el-form ref="form" :model="form" label-width="80px"> -->
          <el-form-item label v-if="['1'].includes(tableForm.search_order_status)">
            <div class="afterOrder">
              <el-checkbox @change="getchangeOrder" v-model="form.checkedOrder">
                过滤存在的售后订单
              </el-checkbox>
              <el-tooltip
                class="item"
                effect="dark"
                placement="top"
                content="存在售后的订单建议核对售后数量后再发货，防止多发！"
              >
                <img :src="imgOssPath + '20250619_sf_wenhao.png'" alt class="icon_wenhao ml10" />
              </el-tooltip>
            </div>
          </el-form-item>
          <!-- </el-form> -->
        </base-form>
      </el-card>
      <el-card>
        <template slot="header">
          <div class="flex scroll" ref="scroll">
            <div
              v-for="(item, index) in selectTabList"
              :class="
                tableForm.search_order_status === `${item.id}` ||
                (tableForm.search_order_status != '' &&
                  !tableForm.search_order_status &&
                  index == 0)
                  ? 'key'
                  : ''
              "
              :key="index"
              @click="searchGoodsStatusFn(item.id)"
            >
              <el-tooltip
                :disabled="item.id === '' || item.id == 4"
                class="item"
                effect="dark"
                placement="top"
                :content="item.tip"
              >
                <span class="titlename" type="text">{{ item.name }}({{ item.count }})</span>
              </el-tooltip>
            </div>
          </div>
        </template>
        <div class="operate flex">
          <el-button
            v-if="btnArrList.includes(1)"
            :disabled="getSelectTableLsit.length == 0"
            icon="el-icon-printer"
            @click="moreScaleOrders"
            :class="{ btns: getSelectTableLsit.length == 0 }"
          >
            批量打单发货
          </el-button>
          <el-button
            v-if="btnArrList.includes(2)"
            type="primary"
            icon="el-icon-download"
            @click="moreimportOrders"
          >
            批量导入发货
          </el-button>
          <el-button
            v-if="btnArrList.includes(3)"
            :disabled="getSelectTableLsit.length == 0"
            type="primary"
            icon="el-icon-printer"
            @click="goodsOrderPrint"
          >
            批量打印
          </el-button>
          <el-button v-if="btnArrList.includes(4)" type="primary" @click="exportSelectData">
            批量导出
          </el-button>
          <el-button
            v-if="btnArrList.includes(5)"
            :disabled="getSelectTableLsit.length == 0"
            @click="goodsOrderRemarks1"
          >
            批量添加备注
          </el-button>
          <el-button
            v-if="btnArrList.includes(7)"
            :disabled="getSelectTableLsit.length == 0"
            @click="goodsPlwaitSuccess"
          >
            批量完成备货
          </el-button>
          <el-button
            v-if="btnArrList.includes(8)"
            :disabled="getSelectTableLsit.length == 0"
            @click="goodsHxjump"
          >
            批量核销
          </el-button>
        </div>
        <base-table
          :tableColumn="tableColumn"
          :tableRequest="getOrderListApi"
          :tableForm="tableForm"
          rowKey="good_id"
          ref="baseTable"
          :isSelect="true"
          :isInitCallback="true"
          v-if="!isLoading"
          @scrollX="tableScrollX"
          @initCallback="tableCallback"
          @selectiKey="selectTableData"
        >
          <template #order_no="{ scope }">
            <div>
              {{ scope.row.order_no }}
              <span
                @click="copysuborderno(scope.row, 'order_no')"
                style="cursor: pointer; margin-left: 5px"
              >
                <i class="el-icon-copy-document"></i>
              </span>
            </div>
          </template>
          <template #sub_order_no="{ scope }">
            <div>
              {{ scope.row.sub_order_no }}
              <span
                @click="copysuborderno(scope.row, 'sub_order_no')"
                style="cursor: pointer; margin-left: 5px"
              >
                <i class="el-icon-copy-document"></i>
              </span>
            </div>
          </template>
          <template #phone="{ scope }">
            <!-- 手机号 -->
            <div @click="showPhoneContent(scope.row, 'phone')">{{ scope.row.phone }}</div>
          </template>
          <template #address="{ scope }">
            <!-- 地址 -->
            <div @click="showPhoneContent(scope.row, 'address')">{{ scope.row.address }}</div>
          </template>

          <template #is_refund="{ scope }">
            <span>{{ scope.row.is_refund == 'Y' ? '是' : '否' }}</span>
          </template>
          <template #goodsInfo="{ scope }">
            <!-- 商品信息 -->
            <div v-for="item in scope.row.goods" :key="item.value">
              <div class="flex">
                <el-image
                  class="showImg"
                  :src="item.product_image"
                  :preview-src-list="[item.product_image]"
                ></el-image>
                <div class="saleInfo">
                  <span :title="item.goods_name" class="saleInfotitle">{{ item.goods_name }}</span>
                  <span :title="item.spec_name" class="saleInfotitle1">
                    规格：{{ item.spec_name }}*{{ item.quantity }}
                  </span>
                  <span class="saleInfoprice">支付单价:¥{{ item.origin_price }}</span>
                </div>
              </div>
            </div>
          </template>
          <template #pay_amount="{ scope }">
            <!-- 商品类型 -->
            <div>¥{{ scope.row.pay_amount }}</div>
          </template>
          <template #express_name="{ scope }">
            <span class="statusSty">{{ scope.row.express_name && scope.row.express_name[0] }}</span>
          </template>
          <!-- <template #order_status="{ scope }">
            <span class="statusSty">{{ order_status[scope.row.order_status] }}</span>
          </template>-->
          <template #operate="{ scope }">
            <!-- 订单状态0-待支付 1-已支付 2-已发货 3-已完成 4-已取消 -->
            <div class="public-operate-btn" v-if="scope.row.order_status == 0">
              <el-button size="mini" type="text" @click="lookOrderDetial(scope.row)">
                查看详情
              </el-button>
              <el-button size="mini" type="text" @click="calcelGoodsOrder(scope.row)">
                取消订单
              </el-button>
              <el-button
                v-if="editPrice == 'Y'"
                size="mini"
                type="text"
                @click="goodsChangeOrder(scope.row)"
              >
                改价
              </el-button>
              <el-button size="mini" type="text" @click="goodsOrderRemarks(scope.row)">
                添加备注
              </el-button>
            </div>
            <div class="public-operate-btn" v-if="scope.row.order_status == 1">
              <el-button size="mini" type="text" @click="lookOrderDetial(scope.row)">
                查看详情
              </el-button>
              <el-button size="mini" type="text" @click="goodsOrderRemarks(scope.row)">
                添加备注
              </el-button>
              <el-button size="mini" type="text" @click="goodsOrderSales(scope.row)">
                发货
              </el-button>
            </div>
            <div class="public-operate-btn" v-if="scope.row.order_status == 2">
              <el-button size="mini" type="text" @click="lookOrderDetial(scope.row)">
                查看详情
              </el-button>
              <el-button size="mini" type="text" @click="goodsOrderRemarks(scope.row)">
                添加备注
              </el-button>
            </div>
            <div class="public-operate-btn" v-if="scope.row.order_status == 3">
              <el-button size="mini" type="text" @click="lookOrderDetial(scope.row)">
                查看详情
              </el-button>
              <el-button size="mini" type="text" @click="goodsOrderRemarks(scope.row)">
                添加备注
              </el-button>
            </div>
            <!-- 核销 -->
            <div class="public-operate-btn" v-if="scope.row.order_status == 8">
              <el-button size="mini" type="text" @click="lookOrderDetial(scope.row)">
                查看详情
              </el-button>
              <el-button size="mini" type="text" @click="goodsHxDetial(scope.row)">核销</el-button>
              <el-button size="mini" type="text" @click="goodsOrderRemarks(scope.row)">
                添加备注
              </el-button>
            </div>
            <!-- 备货完成 -->
            <div class="public-operate-btn" v-if="scope.row.order_status == 7">
              <el-button size="mini" type="text" @click="lookOrderDetial(scope.row)">
                查看详情
              </el-button>
              <el-button size="mini" type="text" @click="goodswaitSuccess(scope.row)">
                备货完成
              </el-button>
              <!-- <el-button size="mini" type="text" @click="printGoodsMenu(scope.row)">打印小票</el-button> -->
              <el-button size="mini" type="text" @click="goodsOrderRemarks(scope.row)">
                添加备注
              </el-button>
            </div>
            <div class="public-operate-btn" v-if="scope.row.order_status == 4">
              <el-button size="mini" type="text" @click="lookOrderDetial(scope.row)">
                查看详情
              </el-button>
            </div>
          </template>
        </base-table>
      </el-card>
    </div>
    <el-dialog :title="showTitle" :visible.sync="dialogVisible" :close-on-click-modal="false">
      <div class="view-phone">{{ modelPhone }}</div>
    </el-dialog>
    <!-- 取消订单 -->
    <el-dialog
      append-to-body
      :visible.sync="showCancelOrder"
      :close-on-click-modal="false"
      title="取消订单"
      width="750px"
    >
      <el-alert
        title="风险提示：当消费者正在支付时，取消订单会导致支付失败。建议先与消费者沟通并达成一致后再操作取消。"
        type="error"
        :closable="false"
      ></el-alert>
      <el-form ref="basicInfo" :model="form" :rules="rules" label-width="80px" label-position="top">
        <el-form-item label="取消理由" prop="cancel_remark">
          <el-input
            type="textarea"
            v-model="form.cancel_remark"
            placeholder="请输入取消理由"
          ></el-input>
        </el-form-item>
      </el-form>
      <base-dialog-footer @cancel="showCancelOrder = false" @confirm="confirm"></base-dialog-footer>
    </el-dialog>
    <!-- 批量导出 -->
    <el-dialog append-to-body :visible.sync="showExportOrder" title="批量导出" width="500px">
      <el-form
        ref="basicInfo"
        :rules="rules"
        :model="form1"
        label-width="80px"
        label-position="top"
      >
        <el-form-item label="导出类型" prop="export_type">
          <el-radio-group v-model="form1.export_type">
            <el-radio :label="1">订单导出</el-radio>
            <el-radio :label="2">商品导出</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div style="text-align: center">{{ `确定要导出选中的 ${exportDataLength}个订单吗？` }}</div>
      <base-dialog-footer
        @cancel="showExportOrder = false"
        confirmText="确定"
        @confirm="confirmExport"
      ></base-dialog-footer>
    </el-dialog>
    <!-- 订单备注 -->
    <el-dialog
      append-to-body
      :visible.sync="showRemarksOrder"
      :close-on-click-modal="false"
      :title="remarksTitle"
      width="750px"
    >
      <el-form ref="basicInfo" :model="form" label-width="80px" label-position="top">
        <el-form-item>
          <template slot="label">
            <span class="textTitle">商家内部备注</span>
            <el-tooltip class="item" effect="dark" placement="top" content="仅商家可见的备注信息">
              <img
                :src="imgOssPath + '20250619_sf_wenhao.png'"
                alt
                class="icon_wenhao"
                style="transform: translateY(2px)"
              />
            </el-tooltip>
          </template>
          <el-input
            type="textarea"
            show-word-limit
            maxlength="100"
            v-model="form.merchant_remark"
            placeholder="请输入商家内部备注信息"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <template slot="label">
            <span class="textTitle">客户可见备注</span>
            <el-tooltip class="item" effect="dark" placement="top" content="客户可以看到的备注信息">
              <img
                :src="imgOssPath + '20250619_sf_wenhao.png'"
                alt
                class="icon_wenhao"
                style="transform: translateY(2px)"
              />
            </el-tooltip>
          </template>
          <el-input
            type="textarea"
            show-word-limit
            maxlength="100"
            v-model="form.customer_remark"
            placeholder="请输入客户可见备注信息"
          ></el-input>
        </el-form-item>
      </el-form>
      <base-dialog-footer
        @cancel="showRemarksOrder = false"
        @confirm="confirmRemarks"
      ></base-dialog-footer>
    </el-dialog>
    <!-- 订单详情 -->
    <orderDetail :itemList="rowIndexList" ref="orderDetailDialog"></orderDetail>
    <!-- 订单改价 -->
    <orderChangePrice
      @update="refreshTable"
      :settingListDetail="settingListDetail"
      :itemList="rowIndexList"
      ref="orderChangeDialog"
    ></orderChangePrice>
    <!-- 订单发货 -->
    <orderShipments
      @printWaybill="printWaybill"
      @update="refreshTable"
      :settingListDetail="settingListDetail"
      :itemList="rowIndexList"
      ref="orderShipmentDialog"
    ></orderShipments>
    <!-- 批量打单发货 -->
    <someScaleOrders
      @update="refreshTable"
      :settingListDetail="settingListDetail"
      :itemList="getSelectTableLsit"
      ref="someScaleOrdersDialog"
    ></someScaleOrders>
    <!-- 批量导入发货 -->
    <someimportOrders
      @update="refreshTable"
      :settingListDetail="settingListDetail"
      ref="importOrdersDialog"
    ></someimportOrders>
    <!-- 核销确认 1无码核销  2 扫码核销-->
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="showHXconfirm"
      width="480px"
    >
      <template slot="title">
        <div class="flex">
          <img class="tip_icon" v-if="pickup_verification_type == 0" :src="imgPath" alt />
          <span class="title">核销确认</span>
        </div>
      </template>
      <div v-if="this.pickup_verification_type == 0">
        <div class="codevalueno">确认核销此订单吗？</div>
        <div class="codevalueno">请仔细核对联系人信息，避免冒领或者错误核销造成损失</div>
      </div>
      <div v-if="this.pickup_verification_type == 1">
        <el-form
          ref="basicInfo"
          :model="form"
          :rules="rules"
          label-width="80px"
          label-position="top"
        >
          <el-form-item prop="hxcode">
            <div class="codevalue">请输入核销码完成订单核销</div>
            <el-input
              style="width: 100%"
              v-model="form.hxcode"
              placeholder="请输入核销码"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <base-dialog-footer
        confirmText="确认"
        @cancel="showHXconfirm = false"
        @confirm="confirmHXBtn"
      ></base-dialog-footer>
    </el-dialog>
    <print ref="print"></print>
  </div>
</template>

<script>
  import orderDetail from './components/orderDetail.vue'
  import orderChangePrice from './components/orderChangePrice.vue'
  import orderShipments from './components/orderShipments.vue'
  import someScaleOrders from './components/someScaleOrders.vue'
  import someimportOrders from './components/someimportOrders.vue'
  import print from '@/components/PrintView/print.vue'
  import {
    getOrderListApi,
    getSearchConfig,
    getOrderCancel,
    getOrderRemarks,
    exportByConditions,
    exportBySubOrderNo,
    settingDetail,
    batchOutboundOrderPrintDetail,
    getOrderHideFiled,
    prepareApi,
    verifyOrder,
    pickupAddressListBySelect,
  } from '@/api/shop/order.js'
  import { getmaparrList } from '@/utils/index'

  export default {
    name: 'order',
    components: {
      orderDetail,
      orderChangePrice,
      orderShipments,
      someScaleOrders,
      someimportOrders,
      print,
    },
    data() {
      return {
        imgPath: require('../../../../assets/warn_20250626.png'),
        tableWidth: '100%',
        tableLeft: '10px',
        getOrderListApi,
        value: false,
        showCancelOrder: false,
        showRemarksOrder: false,
        dialogVisible: false,
        showExportOrder: false,
        showHXconfirm: false,
        getOrderCancel,
        getOrderRemarks,
        // tab
        modelPhone: '',
        showTitle: '',
        selectTabList: [],
        rowIndexList: [],
        exportDataLength: '', //导出数据
        exportUseApi: null, //导出类型api
        form: {
          cancel_remark: '',
          merchant_remark: '',
          customer_remark: '',
          hxcode: '', //订单核销码
        },
        form1: {
          export_type: '',
        },
        rules: {
          cancel_remark: [{ required: true, message: '请输入取消理由', trigger: 'blur' }],
          export_type: [{ required: true, message: '请选择导出类型', trigger: 'blur' }],
          hxcode: [{ required: true, message: '请输入核销码', trigger: 'blur' }],
        },
        // 表单
        tableForm: {
          search_order_status: '',
          search_is_filter_refund: 'N',
        },
        order_status: ['待支付', '已支付', '已发货', '已完成', '已取消'], //订单状态0-待支付 1-已支付 2-已发货 3-已完成 4-已取消
        // 表单key
        form: {
          checkedOrder: false,
        },
        formArray: [
          {
            label: '所属门店',
            type: 'select',
            key: 'search_shop_id',
            placeholder: '请选择所属门店',
            options: [],
            callback: (e) => {
              console.log(e, 'chuccc')
              this.tableForm = Object.assign(
                {},
                { search_order_status: this.tableForm.search_order_status, search_shop_id: e }
              )
              // 获取地址下拉列表
              this.pickupAddressListBySelectFn(e)
            },
          },
          {
            label: '订单编号',
            type: 'input',
            key: 'search_sub_order_no',
            placeholder: '请输入订单编号',
          },
          {
            label: '商品名称',
            type: 'input',
            key: 'search_goods_name',
            placeholder: '请输入商品名称',
          },
          {
            label: '下单人账号',
            type: 'input',
            key: 'search_member_name',
            placeholder: '请输入下单人账号',
          },
          {
            label: '收货人',
            type: 'input',
            key: 'search_consigned',
            placeholder: '请输入收货人',
          },
          {
            label: '收货人电话',
            type: 'input',
            key: 'search_phone',
            placeholder: '请输入收货人电话',
          },

          {
            label: '配送方式',
            type: 'select',
            key: 'search_shipping_method',
            placeholder: '请选择配送方式',
            options: [],
          },
          {
            label: '承运商',
            type: 'select',
            key: 'search_carrier_id',
            placeholder: '请选择承运商',
            options: [],
          },
          {
            label: '自提地址',
            type: 'select',
            key: 'search_pickup_id',
            placeholder: '请选择自提地址',
            options: [],
          },
          {
            label: '支付时间',
            type: 'time',
            key: 'time',
            timeKey: ['search_start_time', 'search_end_time'],
          },
        ],
        // 表格配置
        tableColumn: [
          {
            prop: 'expand',
            width: '55',
          },
          {
            label: '订单编号',
            prop: 'sub_order_no',
            type: 'customize',
            width: '160',
          },
          {
            label: '所属门店',
            prop: 'shop_name',
            width: '140',
          },
          {
            label: '收货人',
            prop: 'consigner',
            width: '80',
          },

          {
            label: '收货人电话',
            prop: 'phone',
            type: 'customize',
            width: '110px',
          },

          {
            label: '收货地址',
            type: 'customize',
            prop: 'address',
            width: 200,
          },
          {
            label: '自提地址',
            prop: 'pickup_name',
            width: 200,
          },
          {
            label: '配送方式',
            prop: 'shipping_method_name',
            width: '100px',
          },
          {
            label: '商品信息',
            type: 'customize',
            prop: 'goodsInfo',
            width: '250px',
          },
          {
            label: '销售总额',
            prop: 'order_amount',
          },
          {
            label: '支付金额',
            prop: 'pay_amount',
          },
          {
            label: '买家备注',
            prop: 'remark',
          },
          {
            label: '订单备注',
            prop: 'merchant_remark',
          },
          {
            label: '承运商',
            type: 'customize',
            prop: 'express_name',
          },
          {
            label: '下单人',
            prop: 'member_name',
          },
          {
            label: '订单状态',
            prop: 'order_status_name',
          },
          {
            label: '支付时间',
            prop: 'paied_at',
            width: '200px',
          },
          {
            label: '操作',
            type: 'customize',
            prop: 'operate',
            fixed: 'right',
            width: 250,
          },
        ],
        search_order_status: '',
        goodsConfig: {},
        getSelectTableLsit: [], //选择的数据
        settingListDetail: {}, // 订单配置
        editPrice: '', //是否支持改价
        pickup_verification_type: '', //自提核销方式  0无码1扫码

        btnArrList: [1, 2, 4, 5],
        remarksTitle: '添加备注',
        isLoading: true,

        isShowPage: false,
      }
    },
    computed: {
      tableHeight(i) {
        let priveTable = this.$refs
        console.log('priveTable', priveTable)
      },
    },
    created() {
      console.log(this.$route.query.id)
      if (this.$route.query.id) {
        this.tableForm.search_order_status = '8'
        this.tableForm.search_pickup_id = Number(this.$route.query.id)
      }
      // 初始化
      // this.init()
    },
    methods: {
      checkAccountShopFn(isShowPage) {
        this.isShowPage = isShowPage
        if (this.isShowPage) {
          // 初始化
          this.pickupAddressListBySelectFn('')
          this.init()
        }
      },
      init() {
        // 获取商品类型
        getSearchConfig({
          is_search: 'Y',
        }).then((res) => {
          if (res.code == 200) {
            this.goodsConfig = res.data
            this.$set(this.formArray[0], 'options', [...res.data.shop_list])
            this.$set(this.formArray[6], 'options', [...res.data.shipping_list])
            this.$set(this.formArray[7], 'options', [...res.data.carrier_list])
            this.isLoading = false
            // 获取表格宽度
            this.$nextTick(() => {
              this.tableWidth = this.$refs.baseTable?.$el.offsetWidth - 50 + 'px'
            })
          }
        })
        settingDetail().then((res) => {
          if (res.code == 200) {
            this.settingListDetail = res.data
            console.log(this.settingListDetail)
          }
        })
      },
      // 获取商品分类
      pickupAddressListBySelectFn(e) {
        pickupAddressListBySelect({
          shop_id: e ? e : this.tableForm.search_shop_id,
        }).then((res) => {
          if (res.code == 200) {
            this.$set(this.formArray[8], 'options', [...res.data])
          }
        })
      },
      // 刷新表格
      refreshTable() {
        this.$refs.baseTable.tableRequestFn()
        this.$refs.baseTable.clearSelection()
        this.tableCallback()
      },
      tableCallback() {
        this.selectTabList = this.$refs.baseTable.TableDataAll.census
        this.editPrice = this.$refs.baseTable.TableDataAll.is_edit_price
        this.pickup_verification_type = this.$refs.baseTable.TableDataAll.pickup_verification_type
      },
      //勾选表格数据
      selectTableData(data) {
        this.getSelectTableLsit = data
      },
      // 表格滚动事件
      tableScrollX(e) {
        this.tableLeft = e
      },

      // 搜索对应状态的商品类白哦
      searchGoodsStatusFn(val) {
        this.tableColumn = getmaparrList(this.tableColumn, 'prop', 'is_refund', false)
        this.tableColumn = getmaparrList(this.tableColumn, 'prop', 'order_no', false)

        this.$refs.baseTable.clearSelection()
        this.$set(this.tableForm, 'search_order_status', `${val}`)
        this.refreshTable()
        if (['', 1].includes(val)) this.btnArrList = [1, 2, 4, 5]
        if ([0, 2, 3].includes(val)) this.btnArrList = [4, 5]
        // if (val == 1) this.btnArrList = [1, 2, 3, 4, 5]
        // if (val == 2) this.btnArrList = [4, 5]
        // if (val == 3) this.btnArrList = [4, 5]
        if (val == 4) this.btnArrList = []
        if (val == 8) this.btnArrList = [5, 8]
        if (val == 7) this.btnArrList = [5, 7]
        if (val == 1 || val == 7) {
          this.tableColumn.splice(9, 0, {
            label: '是否存在售后',
            type: 'customize',
            prop: 'is_refund',
            width: '150px',
          })
        }
        if (val === 0) {
          this.tableColumn.splice(1, 0, {
            label: '父单号',
            prop: 'order_no',
            type: 'customize',
            width: '160px',
          })
        }
      },

      // 查看详情
      lookOrderDetial(row) {
        this.rowIndexList = row
        this.$refs.orderDetailDialog.open(this.rowIndexList)
      },
      // 取消订单
      calcelGoodsOrder(row) {
        this.rowIndexList = row
        this.showCancelOrder = true
      },
      //订单备注
      goodsOrderRemarks(row) {
        this.remarksTitle = '添加备注'
        this.rowIndexList = row
        this.flag = 'one'
        this.showRemarksOrder = true
        for (let key in this.form) {
          this.form[key] = ''
        }
        this.$set(this.form, 'customer_remark', row.customer_remark)
        this.$set(this.form, 'merchant_remark', row.merchant_remark)
      },
      //批量订单备注
      goodsOrderRemarks1() {
        this.remarksTitle = '批量添加备注'
        if (!this.getSelectTableLsit.length) {
          this.$message.error('请选择数据')
          return false
        }
        this.flag = 'more'
        this.showRemarksOrder = true
        for (let key in this.form) {
          this.form[key] = ''
        }
        this.$nextTick(() => {
          this.$refs['basicInfo'].resetFields()
        })
      },
      //订单改价
      goodsChangeOrder(row) {
        this.rowIndexList = row
        this.$refs.orderChangeDialog.open(this.rowIndexList)
      },
      //发货
      goodsOrderSales(row) {
        this.rowIndexList = row
        this.$refs.orderShipmentDialog.open(this.rowIndexList)
      },
      //批量打单发货
      moreScaleOrders() {
        if (!this.getSelectTableLsit.length) {
          this.$message.error('请选择数据')
          return false
        }
        this.$refs.someScaleOrdersDialog.open(this.getSelectTableLsit)
      },
      //批量导入发货
      moreimportOrders() {
        this.$refs.importOrdersDialog.open()
      },
      confirmExportJSON() {
        let paramsData = []
        if (this.getSelectTableLsit.length) {
          this.getSelectTableLsit.forEach((item) => {
            paramsData.push(item.sub_order_no)
          })
        }
        this.exportDataLength = this.getSelectTableLsit.length
          ? this.getSelectTableLsit.length
          : this.$refs.baseTable.total
        let exportUseApi = paramsData.length > 0 ? exportBySubOrderNo : exportByConditions
        let paramsConditions = {
          export_type: this.form1.export_type, // (2商品维度1订单维度)
          ...this.tableForm,
        }
        let paramsSubOrderNo = {
          export_type: this.form1.export_type, //(2商品维度 1订单维度)
          sub_order_no_list: paramsData,
        }
        let params = paramsData.length ? paramsSubOrderNo : paramsConditions
        exportUseApi(params).then((res) => {
          if (res.code == 200) {
            this.$message.success('批量导出成功')
            this.showExportOrder = false
            this.refreshTable()
          }
          this.$nextTick(() => {
            this.$refs['basicInfo'].resetFields()
          })
        })
      },
      //批量备货
      goodsbhjump() {},
      //批量核销
      goodsHxjump() {
        if (!this.getSelectTableLsit.length) {
          this.$message.error('请选择数据')
          return false
        }
        // store.dispatch('goodsDetaile/sethxOrderList', sub_order_noStr)
        if (this.pickup_verification_type == 0) {
          this.showHXconfirm = true
        } else {
          this.$router.push({
            path: '/order/orderVerification',
          })
        }
      },
      //核销
      goodsHxDetial(row) {
        this.rowIndexList = row
        this.showHXconfirm = true
      },

      // 核销确认
      confirmHXBtn() {
        if (this.pickup_verification_type == 0) {
          let sub_order_noStr = ''
          if (this.getSelectTableLsit.length > 0) {
            this.getSelectTableLsit.forEach((item) => {
              sub_order_noStr += item.sub_order_no + ','
            })
            sub_order_noStr = sub_order_noStr.substring(0, sub_order_noStr.length - 1)
          }

          let hx_sub_order_noStr =
            sub_order_noStr != '' ? sub_order_noStr : this.rowIndexList.sub_order_no
          this.verifyOrderFnApi(hx_sub_order_noStr)
        } else {
          this.$refs.basicInfo.validate((valid) => {
            if (valid) {
              this.verifyOrderFnApi()
            } else {
              console.log('error submit!!')
              return false
            }
          })
        }
      },
      //核销订单
      verifyOrderFnApi(sub_order_no = '') {
        let params = {
          code: this.form.hxcode,
          sub_order_no: sub_order_no,
        }
        verifyOrder(params).then((res) => {
          if (res.code == 200) {
            this.$message.success('核销成功')
            this.showHXconfirm = false
            this.refreshTable()
          }
        })
      },

      //备货完成
      goodswaitSuccess(row) {
        this.$confirm('确认已完成备货?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            let params = {
              sub_order_no: row.sub_order_no,
            }
            prepareApi(params).then((res) => {
              if (res.code == 200) {
                this.$message.success('备货完成')
                this.refreshTable()
              }
            })
          })
          .catch((action) => {})
      },

      //批量备货完成
      goodsPlwaitSuccess() {
        let sub_order_noStr = ''
        if (this.getSelectTableLsit.length > 0) {
          this.getSelectTableLsit.forEach((item) => {
            sub_order_noStr += item.sub_order_no + ','
          })
          sub_order_noStr = sub_order_noStr.substring(0, sub_order_noStr.length - 1)
        }
        let params = {
          sub_order_no: sub_order_noStr,
        }
        prepareApi(params).then((res) => {
          if (res.code == 200) {
            this.$message.success('批量备货完成')
            this.refreshTable()
          }
        })
      },

      //打印小票
      printGoodsMenu(row) {},

      //批量导出确定
      confirmExport() {
        this.$refs.basicInfo.validate((valid) => {
          if (valid) {
            this.confirmExportJSON()
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
      //批量导出
      exportSelectData() {
        this.exportDataLength = this.getSelectTableLsit.length
          ? this.getSelectTableLsit.length
          : this.$refs.baseTable.total
        this.form1.export_type = ''
        this.showExportOrder = true
        this.$nextTick(() => {
          this.$refs.basicInfo.resetFields()
        })
      },
      // 打印电子面单
      printWaybill(params) {
        this.$refs.print.open(
          {
            ...params,
            url: 'merchant/print/waybillPrintDetail',
          },
          1
        )
      },
      //批量打印
      goodsOrderPrint() {
        if (!this.getSelectTableLsit.length) {
          this.$message.error('请选择数据')
          return false
        }
        let paramsData = []
        this.getSelectTableLsit.forEach((item) => {
          paramsData.push(item.sub_order_no)
        })
        let params = {
          print_template_id: 4, //product (product商品维度 order订单维度)
          order_no: paramsData,
        }
        this.$confirm(`确定要打印选中的 ${this.getSelectTableLsit.length}个订单吗？`, '批量打印', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            batchOutboundOrderPrintDetail(params).then((res) => {
              if (res.code == 200) {
                this.$message.success('批量打印成功')
                this.refreshTable()
              }
            })
          })
          .catch((action) => {})
      },
      // 確定
      confirm() {
        this.$refs.basicInfo.validate((valid) => {
          if (valid) {
            let params = {
              sub_order_no: this.rowIndexList.sub_order_no,
              ...this.form,
            }
            getOrderCancel(params).then((res) => {
              if (res.code == 200) {
                this.$message.success('取消成功')
                this.cancel()
                this.refreshTable()
              }
            })
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
      //添加备注
      confirmRemarks(flag) {
        this.$refs.basicInfo.validate((valid) => {
          if (valid) {
            let paramsData = []
            this.getSelectTableLsit.forEach((item) => {
              paramsData.push(item.sub_order_no)
            })
            let params = {
              sub_order_no: this.flag == 'more' ? paramsData : [this.rowIndexList.sub_order_no],
              ...this.form,
            }
            getOrderRemarks(params).then((res) => {
              if (res.code == 200) {
                this.$message.success('添加备注成功')
                this.cancel()
                this.refreshTable()
              }
            })
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
      cancel() {
        this.showCancelOrder = false
        this.showRemarksOrder = false
      },
      // 表单搜索事件
      searchForm(form, isResetFields) {
        this.tableForm = Object.assign({}, this.tableForm, form)
        this.pickupAddressListBySelectFn('')
      },
      getchangeOrder(value) {
        this.tableForm.search_is_filter_refund = value ? 'Y' : 'N'
      },
      showPhoneContent(data, code) {
        if (code == 'phone') {
          this.showTitle = '手机号'
        }
        if (code == 'address') {
          this.showTitle = '收货地址'
        }
        let params = {
          shop_id: data.shop_id,
          sub_order_no: data.sub_order_no, //子订单号
          filed_type: code, //查询字段 phone手机号 address 地址
        }
        getOrderHideFiled(params).then((res) => {
          if (res.code === 200) {
            this.dialogVisible = true
            this.modelPhone = res.data[code]
          }
        })
      },
      //复制订单编号
      async copysuborderno(data, code) {
        // 创建临时输入框来复制文本
        const textarea = document.createElement('textarea')
        textarea.value = data[code]
        document.body.appendChild(textarea)
        textarea.select()

        try {
          document.execCommand('copy')
          this.$message.success('复制成功！')
        } catch (err) {
          this.$message.error('复制失败，请手动复制')
        }

        document.body.removeChild(textarea)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .ml10 {
    margin-left: 10px;
  }
  .select {
    cursor: pointer;
  }
  .operate {
    &.btns {
      .el-button {
        background: #f5f7fa;
        color: #a8abb2;
        border-color: #dcdfe6;
      }
      .el-button:hover {
        border-color: #dcdfe6;
      }
      .el-button:focus {
        border-color: #dcdfe6;
      }
    }
  }

  .el-image {
    img {
      width: 80px;
    }
  }
  .saleInfo {
    display: flex;
    flex-direction: column;
    margin-left: 10px;
    .saleInfotitle {
      display: inline-block;
      white-space: nowrap;
      width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .saleInfotitle1 {
      display: inline-block;
      white-space: nowrap;
      width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 12px;
    }
    .saleInfoprice {
      color: #ff5500;
      font-size: 12px;
    }
    span {
      height: 20px;
    }
  }
  .scroll {
    overflow-x: auto;
    flex-shrink: 0;
    padding-bottom: 15px;
    & > div {
      flex-shrink: 0;
      white-space: nowrap;
      cursor: pointer;
    }
    & > div + div {
      margin-left: 50px;
      font-weight: 400;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
    }
    .key {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #0071fe;
      position: relative;
      &::after {
        content: '';
        display: block;
        width: 28px;
        height: 2px;
        background: #0071fe;
        position: absolute;
        left: 50%;
        bottom: -15px;
        transform: translateX(-50%);
      }
    }
  }

  .delfate-image {
    width: 50px;
    height: 50px;
  }

  // 第一列表格的样式
  .goods ::v-deep {
    .el-card__header {
      padding-bottom: 0;
    }
    .el-table-column--selection .cell {
      padding-left: 10px;
    }

    th:first-child,
    tr.el-table__row td:first-child {
      transform: translateX(55px) !important;
    }

    th:nth-child(2),
    tr.el-table__row td:nth-child(2) {
      transform: translateX(-55px) !important;
    }
    // tr:not(.el-table__row) > td {
    //   padding-top: 0 !important;
    //   padding-bottom: 0 !important;
    // }
  }

  .afterOrder {
    display: flex;
    align-items: center;
  }
  .view-phone {
    text-align: center;
  }
  .textTitle {
    font-size: 14px;
    color: #303133;
    font-weight: 600;
    margin-right: 4px;
  }
  .codevalueno {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #606266;
    line-height: 14px;
    text-align: center;
    margin-bottom: 10px;
  }
  .hxcodeMain {
    .el-form {
      .el-form-item {
        text-align: center;
      }
    }
  }
  .codevalue {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #222222;
    text-align: center;
    margin-bottom: 10px;
  }
  .titleHX {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #303133;
    margin-left: 8px;
  }
  .view-phone {
    text-align: center;
  }
  .showImg {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    border: 1px solid #ededed;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }
  .no-tip {
    position: absolute;
    font-size: 25px;
    z-index: 200;
    background: #fff;
    width: 100%;
    height: 100%;
    text-align: center;
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
  }
</style>
