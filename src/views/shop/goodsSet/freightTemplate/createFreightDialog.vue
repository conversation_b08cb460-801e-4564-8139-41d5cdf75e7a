<!--
 * @Author: liq<PERSON> liqian@123
 * @Date: 2025-03-28 14:37:19
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-14 10:05:22
 * @FilePath: \qst-merchant-admin-2.0\src\views\shop\goodsSet\freightTemplate\createFreightDialog.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->

<template>
  <!--添加模版 -->
  <el-dialog
    :title="title"
    width="900px"
    top="15vh"
    :visible.sync="showFreightDialog"
    :close-on-click-modal="false"
  >
    <div
      v-loading="loadingEdit"
      element-loading-text="加载中"
      element-loading-spinner="el-icon-loading"
    >
      <el-form
        label-width="180px"
        :rules="basicInfoRule"
        :model="basicInfo"
        ref="basicInfo"
        :label-position="labelPosition"
      >
        <el-form-item label="模版名称" prop="name">
          <el-input type="text" v-model="basicInfo.name" placeholder="请输入模版名称"></el-input>
        </el-form-item>
        <el-form-item label="计费方式" prop="billing_method">
          <el-select style="width: 100%" v-model="basicInfo.billing_method" placeholder="请选择">
            <el-option
              v-for="item in billing_methodList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="保存后自动启用" prop="status">
          <el-radio-group v-model="basicInfo.status">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="2">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="首件/重/体积" prop="first">
          <el-input
            v-model="basicInfo.first"
            :placeholder="basicInfo.billing_method | appendPlaceholder"
          >
            <template slot="append">{{ basicInfo.billing_method | appendFlag }}</template>
          </el-input>
        </el-form-item>
        <el-form-item label="首费" prop="first_fee">
          <div class="input-number-tem">
            <el-input
              class="w100 input-number"
              v-model="basicInfo.first_fee"
              :min="0"
              placeholder="请输入首费金额"
              prefix-icon="¥"
              @blur="inputFree($event, 'first_fee')"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="续件/重/体积" prop="continued">
          <el-input
            v-model="basicInfo.continued"
            :placeholder="basicInfo.billing_method | appendPlaceholder1"
          >
            <template slot="append">{{ basicInfo.billing_method | appendFlag }}</template>
          </el-input>
        </el-form-item>
        <el-form-item label="续费" prop="continued_fee">
          <div class="input-number-tem">
            <el-input
              class="w100 input-number"
              v-model="basicInfo.continued_fee"
              :min="0"
              placeholder="请输入续费金额"
              prefix-icon="¥"
              @blur="inputFree($event, 'continued_fee')"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="指定条件包邮" prop="cond_free_shipping">
          <el-switch
            active-value="Y"
            inactive-value="N"
            v-model="basicInfo.cond_free_shipping"
          ></el-switch>
          <span>{{ basicInfo.cond_free_shipping | getcondfreeshipping(that) }}</span>
        </el-form-item>
        <el-form-item
          label="包邮条件"
          prop="cond_free_shipping_type"
          v-if="basicInfo['cond_free_shipping'] == 'Y'"
        >
          <el-select
            style="width: 100%"
            v-model="basicInfo.cond_free_shipping_type"
            placeholder="请选择"
          >
            <el-option
              v-for="item in condfreeshipping"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <div class="selectDiv" v-if="basicInfo.cond_free_shipping_type == '1'">
            <!-- <div>单次购物满</div> -->
            <el-form-item label="单次购物满" prop="full_amount">
              <div class="input-number-tem">
                <el-input
                  style="width: 60%"
                  class="w100 input-number"
                  v-model="basicInfo.full_amount"
                  :min="0"
                  placeholder="请输入金额"
                  prefix-icon="¥"
                  @blur="inputFree($event, 'full_amount')"
                >
                  <template slot="append">元包邮</template>
                </el-input>
              </div>
            </el-form-item>
          </div>
          <div class="selectDiv" v-if="basicInfo.cond_free_shipping_type == '2'">
            <!-- <div>单次购物满</div> -->
            <el-form-item label="单次购物满" prop="full_piece">
              <el-input
                style="width: 60%"
                @blur="inputFreePiece($event, 'full_piece')"
                v-model="basicInfo.full_piece"
                placeholder="请输入件数"
              >
                <template slot="append">件包邮</template>
              </el-input>
            </el-form-item>
          </div>
          <div v-if="basicInfo.cond_free_shipping_type == '3'">
            <!-- <div>单次购物满</div> -->
            <el-form-item label="单次购物满" prop="full_amount">
              <div class="input-number-tem">
                <el-input
                  style="width: 60%"
                  class="w100 input-number"
                  v-model="basicInfo.full_amount"
                  :min="0"
                  placeholder="请输入金额"
                  prefix-icon="¥"
                  @blur="inputFree($event, 'full_amount')"
                >
                  <template slot="append">元包邮</template>
                </el-input>
              </div>
            </el-form-item>
            <el-form-item label="并且满" prop="full_piece">
              <!-- <div>并且满</div> -->
              <el-input
                @blur="inputFreePiece($event, 'full_piece')"
                v-model="basicInfo.full_piece"
                placeholder="请输入件数"
              >
                <template slot="append">件包邮</template>
              </el-input>
            </el-form-item>
          </div>
        </el-form-item>
      </el-form>
      <h4>区域运费规则（若设置区域运费规则，运费计算该规则优先）</h4>
      <h3>区域运费规则</h3>
      <el-button type="primary" icon="el-icon-plus" @click="handelAdd">添加区域运费规则</el-button>

      <el-table :data="tableData" stripe style="width: 100%">
        <el-table-column prop="area_ids" label="适用区域" width="200">
          <template slot-scope="scope">
            <el-cascader
              ref="cascader"
              :props="props"
              collapse-tags
              v-model="scope.row.area_ids_json"
              :options="areaList"
              @change="handelChange(scope.row.area_ids_json, scope.$index)"
              clearable
            ></el-cascader>
          </template>
        </el-table-column>
        <el-table-column prop="delivery_opt" label="配送选项" width="120">
          <template slot-scope="scope, index">
            <el-switch
              active-value="Y"
              inactive-value="N"
              v-model="scope.row.delivery_opt"
              @change="changeSwitch(scope.row, scope.$index, 'delivery_opt')"
            ></el-switch>
            <span>{{ scope.row['delivery_opt' + 'name'] ? '配送' : '不配送' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="free_shipping" label="包邮" width="120">
          <template slot-scope="scope">
            <div v-if="scope.row['delivery_opt' + 'name']">
              <el-switch
                active-value="Y"
                inactive-value="N"
                v-model="scope.row.free_shipping"
                @change="changeSwitch(scope.row, scope.$index, 'free_shipping')"
              ></el-switch>
              <span>{{ scope.row['free_shipping' + 'name'] ? '包邮' : '不包邮' }}</span>
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column prop="cond_free_shipping" label="条件包邮" width="130">
          <template slot-scope="scope">
            <div v-if="scope.row['delivery_opt' + 'name'] && !scope.row['free_shipping' + 'name']">
              <el-switch
                active-value="Y"
                inactive-value="N"
                v-model="scope.row.cond_free_shipping"
                @change="changeSwitch(scope.row, scope.$index, 'cond_free_shipping')"
              ></el-switch>
              <span>
                {{ scope.row['cond_free_shipping' + 'name'] ? '条件包邮' : '不条件包邮' }}
              </span>
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column prop="cond_free_shipping_type" label="条件类型" width="180">
          <template slot-scope="scope">
            <div
              v-if="
                scope.row['delivery_opt' + 'name'] &&
                !scope.row['free_shipping' + 'name'] &&
                scope.row['cond_free_shipping' + 'name']
              "
            >
              <el-select
                v-model="scope.row.cond_free_shipping_type"
                @change="changehipping_type(scope.row, scope.$index, 'cond_free_shipping_type')"
                placeholder="请选择包邮条件"
              >
                <el-option
                  v-for="item in condfreeshipping"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column prop="F" label="条件值" width="250">
          <template slot-scope="scope">
            <div
              v-if="
                scope.row['delivery_opt' + 'name'] &&
                !scope.row['free_shipping' + 'name'] &&
                scope.row['cond_free_shipping' + 'name']
              "
            >
              <div class="selectDiv" v-if="scope.row.cond_free_shipping_type == '1'">
                <div>单次购物满</div>
                <el-input
                  @blur="inputFree1(scope.row, 'full_amount')"
                  v-model="scope.row.full_amount"
                  placeholder="请输入金额"
                >
                  <template slot="append">元包邮</template>
                </el-input>
              </div>
              <div class="selectDiv" v-if="scope.row.cond_free_shipping_type == '2'">
                <div>单次购物满</div>
                <el-input
                  @blur="inputFreePieceTab(scope.row, 'full_piece')"
                  v-model="scope.row.full_piece"
                  placeholder="请输入件数"
                >
                  <template slot="append">件包邮</template>
                </el-input>
              </div>
              <div v-if="scope.row.cond_free_shipping_type == '3'">
                <div>单次购物满</div>
                <el-input
                  @blur="inputFree1(scope.row, 'full_amount')"
                  v-model="scope.row.full_amount"
                  placeholder="请输入金额"
                >
                  <template slot="append">元</template>
                </el-input>
                <div>并且满</div>
                <el-input
                  @blur="inputFreePieceTab(scope.row, 'full_piece')"
                  v-model="scope.row.full_piece"
                  placeholder="请输入件数"
                >
                  <template slot="append">件包邮</template>
                </el-input>
              </div>
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column prop="first" label="首件/重/体积" width="200">
          <template slot-scope="scope">
            <div
              v-if="
                scope.row['delivery_opt' + 'name'] &&
                !scope.row['free_shipping' + 'name'] &&
                !scope.row['cond_free_shipping' + 'name']
              "
            >
              <el-input
                @blur="inputFreeTab(scope.row, 'first')"
                v-model="scope.row.first"
                placeholder="请输入"
              >
                <template slot="append">{{ basicInfo.billing_method | appendFlag }}</template>
              </el-input>
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column prop="first_fee" label="首费（元）" width="200">
          <template slot-scope="scope">
            <div
              v-if="
                scope.row['delivery_opt' + 'name'] &&
                !scope.row['free_shipping' + 'name'] &&
                !scope.row['cond_free_shipping' + 'name']
              "
            >
              <el-input
                @blur="inputFree1(scope.row, 'first_fee')"
                v-model="scope.row.first_fee"
                placeholder="请输入金额"
              >
                <template slot="append">元</template>
              </el-input>
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column prop="continued" label="续件/重/体积" width="200">
          <template slot-scope="scope">
            <div
              v-if="
                scope.row['delivery_opt' + 'name'] &&
                !scope.row['free_shipping' + 'name'] &&
                !scope.row['cond_free_shipping' + 'name']
              "
            >
              <el-input
                @blur="inputFreeTab(scope.row, 'continued')"
                v-model="scope.row.continued"
                placeholder="请输入"
              >
                <template slot="append">{{ basicInfo.billing_method | appendFlag }}</template>
              </el-input>
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column prop="continued_fee" label="续费（元）" width="200">
          <template slot-scope="scope">
            <div
              v-if="
                scope.row['delivery_opt' + 'name'] &&
                !scope.row['free_shipping' + 'name'] &&
                !scope.row['cond_free_shipping' + 'name']
              "
            >
              <el-input
                @blur="inputFree1(scope.row, 'continued_fee')"
                v-model="scope.row.continued_fee"
                placeholder="请输入金额"
              >
                <template slot="append">元</template>
              </el-input>
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button @click="handledelete(scope.row)" type="text" size="small">删除</el-button>
          </template>
        </el-table-column>
        <template slot="empty">
          <base-tabel-empty />
        </template>
      </el-table>

      <base-dialog-footer
        @cancel="showFreightDialog = false"
        @confirm="confirm"
      ></base-dialog-footer>
    </div>
  </el-dialog>
</template>

<script>
  import {
    createDeliveryList,
    updateDeliveryList,
    getAreaList,
  } from '@/api/shop/goodsSet/freightTemplate'
  import { appendDestjsonFromsrcjson } from '@/utils/index'
  export default {
    name: 'createFreightDialog',
    components: {},
    props: {
      datailTemplateList: {
        type: Object,
        default: () => {},
      },
      operateFlag: {
        type: String,
        default: 'ADD',
      },
      title: {
        type: String,
        default: '新建模版',
      },
      billing_methodList: {
        type: Array,
        default: () => [],
      },
      loadingEdit: {
        type: Boolean,
        default: true,
      },
    },
    data() {
      return {
        that: this,
        labelPosition: 'top',
        showFreightDialog: false,
        appendFlag: '',
        basicInfo: {
          name: '', // 模版名称
          billing_method: '', //计费方式
          status: 1, // 是否启用
          first: '', // 首件/重/体积
          first_fee: '', // 首费
          continued: '', // 续件/重/体积
          continued_fee: '', // 续费
          cond_free_shipping: 'N', // 是否包邮Y 开启 N 未开启
          cond_free_shipping_type: '', // 包邮条件
          full_piece: '', // 件数
          full_amount: '', // 金额
        },
        areaList: [],
        //级联选择规则
        props: {
          multiple: true,
          label: 'label',
          value: 'value',
          children: 'children',
        },
        condfreeshipping: [
          {
            value: 1,
            label: '满额包邮',
          },
          {
            value: 2,
            label: '满件包邮',
          },
          {
            value: 3,
            label: '满额且满件包邮',
          },
        ],
        tableData: [],
        basicInfoRule: {
          name: [{ required: true, message: '请输入模版名称', trigger: 'blur' }],
          billing_method: [{ required: true, message: '请选择计费方式', trigger: 'blur' }],
          status: [{ required: true, message: '请选择是否启用', trigger: 'blur' }],
          first: [
            { required: true, message: '请输入首件/重/体积', trigger: 'blur' },
            {
              required: true,
              validator: (rule, value, callback) => {
                if (!isNaN(value) || value > 0) {
                  callback()
                } else {
                  this.basicInfo.first = ''
                  return callback(new Error('输入必须大于0'))
                }
              },
              trigger: 'blur',
            },
          ],
          first_fee: [
            { required: true, message: '请输入首费', trigger: 'blur' },
            {
              required: true,
              validator: (rule, value, callback) => {
                if (value > 0) {
                  callback()
                } else {
                  return callback(new Error('输入金额必须大于0'))
                }
              },
              trigger: 'blur',
            },
          ],
          continued: [
            { required: true, message: '请输入续件/重/体积', trigger: 'blur' },
            {
              required: true,
              validator: (rule, value, callback) => {
                if (!isNaN(value) || value > 0) {
                  callback()
                } else {
                  this.basicInfo.continued = ''
                  return callback(new Error('输入必须大于0'))
                }
              },
              trigger: 'blur',
            },
          ],
          continued_fee: [
            { required: true, message: '请输入续费', trigger: 'blur' },

            {
              required: true,
              validator: (rule, value, callback) => {
                if (value > 0) {
                  callback()
                } else {
                  return callback(new Error('输入金额必须大于0'))
                }
              },
              trigger: 'blur',
            },
          ],
          cond_free_shipping: [{ required: true, message: '请选择是否包邮', trigger: 'blur' }],
          cond_free_shipping_type: [{ required: true, message: '请选择包邮条件', trigger: 'blur' }],
          full_piece: [
            { required: true, message: '请输入件数', trigger: 'blur' },
            {
              required: true,
              validator: (rule, value, callback) => {
                if (value >= 0) {
                  callback()
                } else {
                  return callback(new Error('输入件数必须大于0'))
                }
              },
              trigger: 'blur',
            },
          ],
          full_amount: [
            { required: true, message: '请输入金额', trigger: 'blur' },
            {
              required: true,
              validator: (rule, value, callback) => {
                if (value >= 0) {
                  callback()
                } else {
                  return callback(new Error('输入金额必须大于0'))
                }
              },
              trigger: 'blur',
            },
          ],
        },
        updateLi: {}, //点击修改的那条数据
      }
    },
    watch: {
      datailTemplateList: {
        handler(newVal) {
          if (this.operateFlag == 'Edit') {
            let transKey = ['cond_free_shipping', 'delivery_opt', 'free_shipping']
            appendDestjsonFromsrcjson(newVal, this.basicInfo)
            this.tableData = newVal.rules || []
            this.tableData.forEach((item, index) => {
              for (let key in item) {
                if (transKey.includes(key)) {
                  item[key + 'name'] = item[key] == 'Y' ? true : false
                }
              }
              item.area_ids_json = item.area_ids
              let aa = item.area_ids
              let areaJson = ''
              aa.forEach((iitem) => {
                areaJson += iitem[iitem.length - 1] + ','
              })
              areaJson = areaJson.slice(0, -1)
              item.area_ids = areaJson
            })
          } else {
            for (let key in this.basicInfo) {
              this.basicInfo[key] = ''
            }
            this.basicInfo.status = 1
            this.basicInfo.cond_free_shipping = 'N'

            this.tableData = []
          }
        },
      },
      deep: true,
    },
    filters: {
      appendFlag(value) {
        let jsonArr = ['', 'kg', '件', 'cm³']
        return jsonArr[value] || '请输入'
      },
      appendPlaceholder(value) {
        let jsonArr = ['', '请输入首件重量', '请输入首件数量', '请输入首件体积']
        return jsonArr[value] || '请输入'
      },
      appendPlaceholder1(value) {
        let jsonArr = ['', '请输入续件重量', '请输入续件数量', '请输入续件体积']
        return jsonArr[value] || '请输入'
      },
      getcondfreeshipping(value, that) {
        if (value == 'N') {
          that.basicInfo.cond_free_shipping_type = ''
          that.basicInfo.full_amount = ''
          that.basicInfo.full_piece = ''
        }
        return value == 'Y' ? '开启' : '关闭'
      },
      formatDecimal: function (value) {
        return value.toFixed(2)
      },
    },
    mounted() {},
    methods: {
      getAreaListFn(parent_id = 0) {
        let params = {
          parent_id: parent_id,
        }
        getAreaList(params).then((res) => {
          if (res.code == 200) {
            this.areaList = res.data
            this.changeKey(this.areaList)
          }
        })
      },
      // 处理数据方法  把children 为空的字段去掉 这样就能触发change
      changeKey(arr) {
        for (var i = 0; i < arr.length; i++) {
          arr[i].value = arr[i].value
          arr[i].label = arr[i].label
          if (arr[i].children && arr[i].children.length) {
            this.changeKey(arr[i].children)
          } else {
            delete arr[i].children
          }
        }
      },
      // 表格新增
      handelAdd() {
        this.tableData.push({
          area_ids_json: [],
          area_ids: '',
          delivery_opt: 'Y',
          free_shipping: 'N',
          cond_free_shipping: 'N',
          delivery_optname: true,
          free_shippingname: false,
          cond_free_shippingname: false,
          cond_free_shipping_type: '',
          F: '',
          first: '',
          first_fee: '',
          continued: '',
          continued_fee: '',
          full_amount: '',
          full_piece: '',
        })
      },
      // 删除表格
      handledelete(row) {
        this.$confirm('是否删除该区域运费规则?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.tableData = this.tableData.filter((item) => item !== row)
            this.$message.success('删除成功')
          })
          .catch((action) => {
            // this.$message({
            //   type: 'info',
            //   message: action === 'cancel' ? '取消删除' : '',
            // })
          })
      },
      open(row = {}) {
        this.showFreightDialog = true
        this.updateLi = row
        this.getAreaListFn()
        this.$nextTick(() => {
          this.$refs.basicInfo.resetFields()
        })
      },

      //条件类型
      changehipping_type(scope, index, key) {
        let aa = {
          full_amount: '',
          full_piece: '',
        }
        this.tableData[index] = Object.assign(scope, aa)
      },

      // 相应字段 Y-true 转换
      changeSwitch(scope, index, key) {
        let transKey = ['delivery_opt', 'free_shipping', 'cond_free_shipping']
        if (scope[key] == 'Y') {
          this.tableData[index][key + 'name'] = true
        } else {
          this.tableData[index][key + 'name'] = false
        }
        if (scope['delivery_opt'] == 'N') {
          let aa = {
            free_shipping: 'N',
            cond_free_shipping: 'N',
            free_shippingname: false,
            cond_free_shippingname: false,
            cond_free_shipping_type: '',
            F: '',
            first: '',
            first_fee: '',
            continued: '',
            continued_fee: '',
            full_amount: '',
            full_piece: '',
          }
          this.tableData[index] = Object.assign(scope, aa)
        }
        if (scope['free_shipping'] == 'Y') {
          let aa = {
            cond_free_shipping: 'N',
            cond_free_shippingname: false,
            cond_free_shipping_type: '',
            F: '',
            first: '',
            first_fee: '',
            continued: '',
            continued_fee: '',
            full_amount: '',
            full_piece: '',
          }
          this.tableData[index] = Object.assign(scope, aa)
        }
        if (scope['cond_free_shipping']) {
          let aa = {
            cond_free_shipping_type: '',
            F: '',
            first: '',
            first_fee: '',
            continued: '',
            continued_fee: '',
            full_amount: '',
            full_piece: '',
          }
          this.tableData[index] = Object.assign(scope, aa)
        }
        console.log(this.tableData)
      },
      //新增模版
      createDeliveryListFn() {
        this.tableData.forEach((item) => {
          delete item.area_ids_json
        })
        let params = {
          ...this.basicInfo,
          rules: this.tableData,
        }
        createDeliveryList(params).then((res) => {
          if (res.code == 200) {
            this.$message.success('添加成功')
            this.showFreightDialog = false
            this.$emit('confirm')
          }
        })
      },
      // 修改模版
      updateDeliveryListFn() {
        this.tableData.forEach((item) => {
          delete item.area_ids_json
        })
        let params = {
          id: this.updateLi.id,
          ...this.basicInfo,
          rules: this.tableData,
        }
        updateDeliveryList(params).then((res) => {
          if (res.code == 200) {
            this.$message.success('添加成功')
            this.showFreightDialog = false
            this.$emit('confirm')
          }
        })
      },
      // 確定
      confirm() {
        this.$refs.basicInfo.validate((valid) => {
          if (valid) {
            if (this.operateFlag == 'ADD') {
              this.createDeliveryListFn()
            }
            if (this.operateFlag == 'Edit') {
              this.updateDeliveryListFn()
            }
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
      handelChange(value, index) {
        if (value) {
          let areaJson = ''
          value.forEach((item) => {
            areaJson += item[item.length - 1] + ','
          })
          areaJson = areaJson.slice(0, -1)
          this.tableData[index].area_ids = areaJson
        }
      },
      inputFree(e, code) {
        let val = e.target.value
        if (isNaN(val) || val <= 0) {
          this.basicInfo[code] = ''
        } else {
          this.basicInfo[code] = Number(val).toFixed(2)
        }
      },
      inputFree1(data, code) {
        if (isNaN(data[code]) || data[code] <= 0) {
          this.$message.error('请输入大于0的数字')
          data[code] = ''
        } else {
          data[code] = Number(data[code]).toFixed(2)
        }
      },
      inputFreePieceTab(data, code) {
        if (isNaN(data[code]) || data[code] <= 0) {
          this.$message.error('请输入大于0的数字')
          data[code] = ''
        } else {
          data[code] = Number(data[code]).toFixed(0)
        }
      },
      inputFreePiece(e, code) {
        let val = e.target.value
        if (isNaN(val) || val <= 0) {
          this.basicInfo[code] = ''
        } else {
          this.basicInfo[code] = Number(val).toFixed(0)
        }
      },
      inputFreeTab(data, code) {
        if (this.basicInfo.billing_method == 1) {
          //1 重量 2件数 3体积
          if (isNaN(data[code]) || data[code] <= 0) {
            this.$message.error('请输入大于0的数字')
            data[code] = ''
          } else {
            data[code] = Number(data[code]).toFixed(3)
          }
        } else {
          //1 重量 2件数 3体积
          if (isNaN(data[code]) || data[code] <= 0) {
            this.$message.error('请输入大于0的数字')
            data[code] = ''
          } else {
            data[code] = Number(data[code]).toFixed(0)
          }
        }
      },
      inputFreeFix(e, code) {
        if (this.basicInfo.billing_method == 1) {
          //1 重量 2件数 3体积
          let val = e.target.value
          if (isNaN(val) || val <= 0) {
            this.basicInfo[code] = ''
          } else {
            this.basicInfo[code] = Number(val).toFixed(3)
          }
        } else {
          //1 重量 2件数 3体积
          let val = e.target.value
          if (isNaN(val) || val <= 0) {
            this.basicInfo[code] = ''
          } else {
            this.basicInfo[code] = Number(val).toFixed(0)
          }
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .selectDiv {
    display: flex;
    flex-direction: column;
    margin-top: 10px;
  }
  h4 {
    text-align: center;
  }
  html body {
    .el-dialog {
      top: 10% !important;
      transform: none;
      left: 0 !important;
      margin: auto !important;
    }
    .el-dialog__body {
      padding: 10px 20px;
    }
    .el-input--small .el-input__inner {
      line-height: 1px !important;
    }
  }
  ::v-deep {
    .el-form-item__label {
      font-size: 14px;
      color: #303133;
      font-weight: 600;
    }
    .el-table__empty-block {
      width: 100% !important;
    }
  }
  .input-number-tem ::v-deep {
    .el-input-number--small {
      width: 110px;
    }
    .el-input-number.is-controls-right .el-input__inner {
      padding-left: 22px;
      padding-right: 36px;
      width: 110px;
      box-sizing: border-box;
      text-align: left !important;
    }
    .input-number::after {
      content: '¥';
      position: absolute;
      top: 1px;
      left: 2px;
      width: 18px;
      line-height: 30px;
      text-align: center;
      background: #fff;
      color: #222222;
      box-sizing: border-box;
      z-index: 1;
      display: block;
    }
  }
</style>
