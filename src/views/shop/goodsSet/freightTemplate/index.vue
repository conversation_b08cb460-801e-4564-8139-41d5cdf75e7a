<!--
 * @Author: liqian liqian@123
 * @Date: 2025-03-28 14:31:06
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-14 10:02:34
 * @FilePath: \qst-merchant-admin-2.0\src\views\shop\goodsSet\freightTemplate\index.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <el-card class="store-model">
      <base-form :tableForm="tableForm" :formArray="formArray" @searchForm="searchForm"></base-form>
      <div class="operate">
        <el-button type="primary" icon="el-icon-plus" @click="addStore">新建模版</el-button>
      </div>
      <base-table
        :tableColumn="tableColumn"
        :tableRequest="getdeliveryList"
        :tableForm="tableForm"
        ref="tableRef"
      >
        <template #first="{ scope }">
          <span v-if="scope.row.first">
            {{ scope.row.first }} {{ billing_methods[scope.row.billing_method] }}
          </span>
        </template>
        <template #first_fee="{ scope }">
          <span v-if="scope.row.first_fee">¥{{ scope.row.first_fee }}</span>
        </template>
        <template #continued="{ scope }">
          <span v-if="scope.row.continued">
            {{ scope.row.continued }} {{ billing_methods[scope.row.billing_method] }}
          </span>
        </template>
        <template #continued_fee="{ scope }">
          <span v-if="scope.row.continued_fee">¥{{ scope.row.continued_fee }}</span>
        </template>
        <template #status="{ scope }">
          <div class="flex">
            <el-switch
              @change="changeStatus(scope)"
              :active-value="1"
              :inactive-value="2"
              v-model="scope.row['status']"
            ></el-switch>
            <span>{{ scope.row.status == 1 ? '启用' : '停用' }}</span>
          </div>
        </template>
        <template #operate="{ scope }">
          <div class="public-operate-btn">
            <el-button size="mini" type="text" @click="openDetail(scope.row)">修改</el-button>
            <el-button
              size="mini"
              type="text"
              v-if="scope.row.status == 2"
              @click="handelDelete(scope.row)"
            >
              删除
            </el-button>
          </div>
        </template>
      </base-table>
    </el-card>
    <createFreightDialog
      :title="title"
      :operateFlag="operateFlag"
      :datailTemplateList="datailTemplateList"
      :billing_methodList="billing_methodList"
      :loadingEdit="loadingEdit"
      ref="createFreight"
      @confirm="confirm"
    ></createFreightDialog>
  </div>
</template>

<script>
  import createFreightDialog from './createFreightDialog.vue'
  import {
    getdeliveryList,
    deleteDeliveryList,
    changeDeliveryStatus,
    getDeliveryDetail,
    getdeliveryMethod,
  } from '@/api/shop/goodsSet/freightTemplate'
  export default {
    name: 'shopFreightTemplate',
    components: {
      createFreightDialog,
    },
    data() {
      return {
        loading: true,
        loadingTab: true,
        loadingEdit: true,
        activeName: 'first',
        datailTemplateList: {}, //查看详情数据
        operateFlag: 'ADD', //新增还是修改
        formArray: [
          {
            label: '模板名称',
            type: 'input',
            key: 'name',
            placeholder: '请输入模板名称',
          },
          {
            label: '计费方式',
            type: 'select',
            key: 'billing_method',
            placeholder: '全部',
            options: [],
          },
        ],

        tableColumn: [
          {
            label: '模板名称',
            prop: 'name',
          },
          {
            label: '计费方式',
            prop: 'billing_method_text',
          },
          {
            label: '首件/重/体积',
            prop: 'first',
            type: 'customize',
          },
          {
            label: '首费（元）',
            prop: 'first_fee',
            type: 'customize',
          },
          {
            label: '续件/重/体积',
            prop: 'continued',
            type: 'customize',
          },
          {
            label: '续费（元）',
            prop: 'continued_fee',
            type: 'customize',
          },
          {
            label: '状态',
            prop: 'status',
            type: 'customize',
          },
          {
            label: '创建时间',
            prop: 'created_at',
            width: '200px',
          },
          {
            label: '操作',
            prop: 'operate',
            type: 'customize',
            width: '150px',
          },
        ],

        getdeliveryList,
        title: '新建模版',

        tableForm: {
          billing_method: '',
        },
        billing_methodList: [], //计费方式
        storeId: '', //店铺Id
        billing_methods: ['', 'kg', '件', 'cm³'],
      }
    },
    created() {
      this.getBillmethods()
    },
    methods: {
      getBillmethods() {
        getdeliveryMethod().then((res) => {
          this.billing_methodList = res.data.map((item) => {
            return {
              label: item.label,
              value: item.value,
            }
          })
          this.formArray[1].options = [{ label: '全部', value: '' }, ...this.billing_methodList]
        })
      },
      // 表单搜索事件
      searchForm(form) {
        this.tableForm = Object.assign({}, this.tableForm, form)
      },

      // 删除
      handelDelete(row) {
        this.$confirm('是否删除该运费模版?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            deleteDeliveryList({ id: row.id }).then((res) => {
              if (res.code == 200) {
                this.$message.success('删除成功')
                this.$refs.tableRef.tableRequestFn()
              } else {
                this.$refs.tableRef.tableRequestFn()
              }
            })
          })
          .catch((action) => {
            // this.$message({
            //   type: 'info',
            //   message: action === 'cancel' ? '取消删除' : '',
            // })
          })
      },
      //切换模版状态
      changeStatus(scope) {
        let statusTip = scope.row.status == 1 ? '启用' : '停用'
        this.$confirm(`是否${statusTip}该运费模版?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            changeDeliveryStatus({
              id: scope.row.id,
              status: scope.row.status,
            }).then((res) => {
              if (res.code == 200) {
                this.$message.success(`${statusTip}成功`)
                this.$refs.tableRef.tableRequestFn()
              } else {
                this.$refs.tableRef.tableRequestFn()
              }
            })
          })
          .catch((action) => {
            this.$refs.tableRef.tableRequestFn()
            // this.$message({
            //   type: 'info',
            //   message: action === 'cancel' ? '取消切换' : '停留在当前页面',
            // })
          })
      },
      // 查看详情
      openDetail(row) {
        this.loadingEdit = true
        this.operateFlag = 'Edit'
        this.title = '编辑模版'
        getDeliveryDetail({ id: row.id }).then((res) => {
          if (res.code == 200) {
            this.datailTemplateList = res.data
            this.loadingEdit = false
          }
        })
        this.$refs.createFreight.open({ id: row.id })
      },
      // 新增
      addStore() {
        this.loadingEdit = false
        this.operateFlag = 'ADD'
        this.title = '新建模版'
        this.datailTemplateList = {}
        this.$refs.createFreight.open()
      },
      // 新增确定返回   更新表格数据
      confirm() {
        this.$refs.tableRef.tableRequestFn()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .operate {
    margin-top: 20px;
  }
  .del-color {
    color: #6e6e7a;
  }
  .ml10 {
    margin-left: 10px;
  }
  .el-dialog__wrapper {
    overflow: auto !important;
  }
</style>
