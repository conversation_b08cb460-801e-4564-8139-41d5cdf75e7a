<!--
 * @Author: liq<PERSON> liqian@123
 * @Date: 2025-03-27 09:48:05
 * @LastEditors: liqian liqian@123
 * @LastEditTime: 2025-05-27 15:42:41
 * @FilePath: \qst-merchant-admin-2.0\src\views\shop\goodsSet\pictureManagement\index.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="mianSelectPicyure">
    <getSelectUploadImages />
  </div>
</template>

<script>
  export default {
    name: 'mianSelectPicyure',
    components: {},
    data() {
      return {}
    },
    created() {},
    methods: {},
  }
</script>

<style lang="scss" scoped>
  html body {
    .el-dialog {
      top: 10% !important;
      transform: none;
      left: 0 !important;
      margin: auto !important;
    }
    .el-dialog__body {
      padding: 10px 20px;
    }
  }
</style>
