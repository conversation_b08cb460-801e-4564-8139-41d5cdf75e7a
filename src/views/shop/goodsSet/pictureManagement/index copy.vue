<!--
 * @Author: liqian liqian@123
 * @Date: 2025-03-27 09:48:05
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-19 14:36:59
 * @FilePath: \qst-merchant-admin-2.0\src\views\shop\goodsSet\pictureManagement\index.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="picture-management">
    <el-row>
      <!-- 左侧分类树 -->
      <el-col :span="3" class="category-tree">
        <div class="tree-header">
          <span class="title">图片分类</span>
          <el-button type="text" icon="el-icon-plus" size="big" @click="addCategory"></el-button>
        </div>
        <div class="tree-content">
          <el-tree
            :data="categoryDataAll"
            :props="treeProps"
            node-key="id"
            :highlight-current="true"
            :default-expanded-keys="['1']"
            @node-click="handleCategoryClick"
          >
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <span>{{ node.label }}</span>
              <div @click.stop>
                <el-dropdown trigger="click" @command="handleCommand">
                  <span class="el-dropdown-link">
                    <el-button icon="el-icon-more" type="text" size="mini"></el-button>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      icon="el-icon-plus"
                      :command="beforeHandleCommand(data, 'ADD')"
                    >新增下级</el-dropdown-item>
                    <el-dropdown-item
                      icon="el-icon-edit"
                      :command="beforeHandleCommand(data, 'Edit')"
                    >修改</el-dropdown-item>
                    <el-dropdown-item
                      icon="el-icon-delete"
                      :command="beforeHandleCommand(data, 'Delete')"
                    >删除</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </span>
          </el-tree>
        </div>
      </el-col>

      <!-- 右侧内容 -->
      <el-col :span="20" class="picture-content">
        <!-- 搜索框 -->
        <el-row class="search-bar" :gutter="10" align="middle">
          <el-col :span="6">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索图片名称或标签"
              clearable
              prefix-icon="el-icon-search"
            />
          </el-col>
          <el-col :span="6">
            <el-button type="primary" block @click="searchPictures">查询</el-button>
          </el-col>
        </el-row>

        <!-- 操作按钮 -->
        <el-row class="action-buttons" :gutter="10">
          <el-button icon="el-icon-plus" type="primary" @click="uploadPicture">上传图片</el-button>
          <el-button type="primary" :disabled="!pictureListChecked.length" @click="batchEdit">批量编辑</el-button>
          <el-button type="danger" :disabled="!pictureListChecked.length" @click="batchDelete">批量删除</el-button>
        </el-row>

        <!-- 图片列表 -->
        <div class="picture-list">
          <div class="picture-main" v-for="picture in pictureList" :key="picture.id">
            <el-card class="picture-card">
              <el-checkbox
                v-model="picture.checked"
                @change="changeCheck(picture)"
                class="picture-checkbox"
              ></el-checkbox>
              <img :src="picture.url" class="picture-preview" />
              <div class="picture-info">
                <div class="picture-title">{{ picture.name }}</div>
                <div class="picture-tags">
                  <el-tag v-for="tag in picture.tags" :key="tag" type="success">{{ tag }}</el-tag>
                </div>
                <div class="picture-time">上传时间：{{ picture.uploadTime }}</div>
              </div>
              <div class="picture-actions">
                <i class="el-icon-view" @click="viewPicture(picture)"></i>
                <i class="el-icon-edit" @click="editPicture(picture)"></i>
                <i class="el-icon-delete" @click="deletePicture(picture)"></i>
              </div>
            </el-card>
          </div>
        </div>

        <!-- 分页 -->
        <el-pagination
          background
          layout="prev, pager, next"
          :total="totalPictures"
          :page-size="pageSize"
          @current-change="handlePageChange"
        ></el-pagination>
      </el-col>
    </el-row>
    <!-- 图片分组弹窗 -->
    <el-dialog
      :title="dialoagName"
      width="700px"
      top="15vh"
      :visible.sync="pictureGroupDialog"
      :close-on-click-modal="false"
    >
      <el-form :model="basicInfo" ref="basicInfo" :label-position="labelPosition">
        <el-form-item
          :label="pictureTitle"
          prop="address"
          v-if="['ADD', 'Upload', 'EditPic', 'EditPicMore'].includes(basicRloeBtn)"
          width="100%"
        >
          <el-cascader
            v-model="selcategoryData"
            :options="categoryData"
            :props="{ checkStrictly: true }"
            clearable
          ></el-cascader>
          <div v-if="['ADD'].includes(basicRloeBtn)">不选择则创建为顶级分类</div>
        </el-form-item>
        <el-form-item label="分类名称" v-if="['ADD', 'Edit'].includes(basicRloeBtn)" prop="name">
          <el-input type="text" v-model="basicInfo.name" placeholder="请输入分类名称"></el-input>
        </el-form-item>
        <el-form-item label="图片名称" v-if="['EditPic'].includes(basicRloeBtn)" prop="address2">
          <el-input v-model="basicInfo.address2" placeholder="请输入图片名称" clearable />
        </el-form-item>
        <el-form-item
          label="图片标签"
          v-if="['Upload', 'EditPic', 'EditPicMore'].includes(basicRloeBtn)"
          prop="address1"
        >
          <el-input v-model="basicInfo.address1" placeholder="请输入图片标签，多个标签用逗号分隔" clearable />
          <div>
            <el-button type="text" @click="showPicture(0)">Banner</el-button>
            <el-button type="text" @click="showPicture(1)">商品主题</el-button>
          </div>
        </el-form-item>
        <el-form-item label="本地上传" v-if="['Upload'].includes(basicRloeBtn)" prop="address3">
          <el-upload
            class="upload-demo"
            action="fakeaction"
            :file-list="fileList"
            list-type="picture-card"
            multiple
            :on-remove="handleRemoveImages"
            :on-preview="handlePictureCardPreview"
            accept=".jpg, .png, .jpeg, .bmp"
            :http-request="(e) => upLoadImg(e, 'license')"
          >
            <i class="el-icon-plus"></i>
            <!-- <div class="el-upload__text">上传logo</div> -->
            <div class="el-upload__tip" slot="tip">建议尺寸：200x200像素，支持jpg、png格式，文件大小不超过2MB</div>
            <!-- 自定义文件列表项 -->

            <template #file="{ file }">
              <div
                class="image-item-wrapper"
                @mouseenter="setActiveFile(file.url)"
                @mouseleave="activeFileUid = null"
              >
                <!-- 图片显示 -->
                <el-image
                  class="upload-image"
                  :src="file.url"
                  :ref="(el) => setImageRef(file.url, el)"
                  fit="cover"
                  :preview-src-list="previewSrcList"
                >
                  <template #error>
                    <div class="image-error">加载失败</div>
                  </template>
                </el-image>

                <!-- 操作按钮容器（增加v-show条件） -->
                <div class="action-buttons" v-show="activeFileUid === file.url">
                  <!-- 预览按钮 -->
                  <el-button
                    class="btn-preview"
                    type="text"
                    icon="el-icon-zoom-in"
                    @click.stop="handlePreview(file)"
                  ></el-button>
                  <!-- 删除按钮 -->
                  <el-button
                    class="btn-delete"
                    type="text"
                    icon="el-icon-delete"
                    @click.stop="handleRemoveFile(file)"
                  ></el-button>
                </div>
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="网络下载" v-if="['Upload'].includes(basicRloeBtn)" prop="address4">
          <el-input v-model="basicInfo.address4" type="text" placeholder="请输入图片链接">
            <el-button
              @click="uploadImages"
              class="uploadImagesDiv"
              slot="append"
              icon="el-icon-download"
            >拉取图片</el-button>
          </el-input>
          <div
            class="networkimage"
            v-for="file in fileList"
            :key="file.url"
            @mouseenter="setActiveFile(file.url)"
            @mouseleave="activeFileUid = null"
          >
            <!-- 图片显示 -->
            <el-image
              class="upload-networkimage"
              :ref="(el) => setImageRef(file.url, el)"
              :src="file.url"
              fit="cover"
              :preview-src-list="previewSrcList"
            >
              <template #error>
                <div class="image-error">加载失败</div>
              </template>
            </el-image>
            <!-- 操作按钮容器 -->
            <div class="action-buttons" v-show="activeFileUid === file.url">
              <!-- 预览按钮 -->
              <el-button
                class="btn-preview"
                type="text"
                icon="el-icon-zoom-in"
                size="big"
                @click.stop="handlePreview(file)"
              ></el-button>
              <!-- 删除按钮 -->
              <el-button
                class="btn-delete"
                type="text"
                icon="el-icon-delete"
                size="big"
                @click.stop="handleRemoveFile(file)"
              ></el-button>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <base-dialog-footer @cancel="pictureGroupDialog = false" @confirm="confirm"></base-dialog-footer>
    </el-dialog>

    <!-- 示例图片 -->
    <el-dialog
      title="示例图片"
      width="300"
      top="15vh"
      :visible.sync="showImgDialog"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="storeBasicInfoPage">
        <el-image v-for="url in urls" :key="url" :src="url" lazy></el-image>
      </div>
    </el-dialog>

    <!-- 预览图片 -->
    <el-dialog
      title="预览图片"
      width="400px"
      top="15vh"
      :visible.sync="previewImgDialog"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="previewImgDialog">
        <el-image :src="previewImgSrc" lazy></el-image>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getmaparrList } from '@/utils/index'
export default {
  name: 'pictureManagement',
  data() {
    return {
      previewImgDialog: false,
      previewImgSrc: '',
      dialogImageUrl: '',
      dialogVisible: false,
      fileList: [],
      imageRefMap: new Map(), // 存储文件 UID 和组件实例的映射
      activeFileUid: null, // 当前激活的文件UID
      previewSrcList: [],
      showImgDialog: false,
      urls: ['https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg'],
      dialoagName: '新增分类',
      pictureTitle: '父级分类',
      basicRloeBtn: [], //'ADD', 'Edit', 'Upload', 'EditPic', 'EditPicMore'
      basicInfo: {
        name: '',
        address: '',
      },
      selcategoryData: [],
      labelPosition: 'top',
      categoryDataAll: [{ value: '全部图片', label: '全部图片', children: [] }],
      categoryData: [
        { value: '商品图片', label: '商品图片', children: [{ value: '商品图片1', label: '商品图片1', children: [] }] },
        { value: '营销图片', label: '营销图片', children: [] },
        { value: '其他', label: '其他', children: [] },
      ],
      treeProps: {
        label: 'label',
        children: 'children',
      },
      searchForm: {
        keyword: '',
      },
      pictureList: [
        {
          id: 1,
          name: '示例图片1',
          url: 'https://qst-static-resource.oss-cn-shenzhen.aliyuncs.com/storage/2025032811443667e61b247d9e5.png',
          tags: ['食品', '主图'],
          uploadTime: '2024-01-01 10:00:00',
        },
        {
          id: 2,
          name: '示例图片2',
          url: 'https://qst-static-resource.oss-cn-shenzhen.aliyuncs.com/storage/2025032811443067e61b1ed5ed7.png',
          tags: ['服装', '详情'],
          uploadTime: '2024-01-01 11:00:00',
        },
      ],
      pictureListChecked: [],
      totalPictures: 50,
      pageSize: 10,
      pictureGroupDialog: false,
    }
  },
  created() {
    this.previewSrcList = [
      'https://qst-static-resource.oss-cn-shenzhen.aliyuncs.com/storage/2025032811443067e61b1ed5ed7.png',
      'https://qst-static-resource.oss-cn-shenzhen.aliyuncs.com/storage/2025032811443667e61b247d9e5.png',
    ]
    this.fileList = [
      { name: '1', url: 'https://qst-static-resource.oss-cn-shenzhen.aliyuncs.com/storage/2025032811443067e61b1ed5ed7.png' },
      { name: '2', url: 'https://qst-static-resource.oss-cn-shenzhen.aliyuncs.com/storage/2025032811443667e61b247d9e5.png' },
    ]

    this.categoryDataAll = this.categoryDataAll.concat(this.categoryData)
    // 获取图片数据
    this.getPictureCategory()
    this.getPictureCategoryFGroup()
  },
  methods: {
    getPictureCategoryFGroup() {
      console.log('获取图片分组')
    },
    getPictureCategory() {
      console.log('获取图片')
    },
    confirm() {
      this.pictureGroupDialog = false
      console.log('确认')
    },
    handleAdd(row) {
      this.basicRloeBtn = 'ADD'
      this.dialoagName = '新增分类'
      this.pictureTitle = '父级分类'
      this.selcategoryData = [row.label]
      this.pictureGroupDialog = true
      console.log('新增', row)
    },
    handleEdit(row) {
      this.basicRloeBtn = 'Edit'
      this.dialoagName = '编辑分类'
      this.pictureTitle = '父级分类'
      this.basicInfo.name = row.label
      this.pictureGroupDialog = true
      console.log('编辑', row)
    },
    handledelete(row) {
      console.log('删除', row)
    },

    beforeHandleCommand(row, command) {
      return {
        row: row,
        command: command,
      }
    },
    handleCommand(command) {
      switch (command.command) {
        case 'ADD': //新增下一级
          this.handleAdd(command.row)
          break
        case 'Edit': //修改
          this.handleEdit(command.row)
          break
        case 'Delete': //删除
          this.handledelete(command.row)
          break
      }
    },
    addCategory() {
      this.basicRloeBtn = 'ADD'
      this.dialoagName = '新增分类'
      this.selcategoryData = []
      this.pictureGroupDialog = true
    },
    appendPictureGroup(data, node) {
      console.log('添加图片组', data)
      console.log('添加图片组', node)
    },
    handleCategoryClick(node) {
      console.log('分类点击', node)
    },
    searchPictures() {
      console.log('搜索图片', this.searchForm.keyword)
    },
    uploadPicture() {
      this.basicRloeBtn = 'Upload'
      this.dialoagName = '上传图片'
      this.pictureTitle = '图片分类'
      this.pictureGroupDialog = true
      console.log('上传图片')
    },
    changeCheck(picture) {
      this.pictureListChecked = getmaparrList(this.pictureList, 'checked', true)
      console.log('选择图片', this.pictureListChecked)
    },
    batchEdit() {
      this.basicRloeBtn = 'EditPicMore'
      this.dialoagName = '批量编辑图片'
      this.pictureTitle = '图片分类'

      this.pictureGroupDialog = true
      console.log('批量编辑', this.pictureListChecked)
    },
    batchDelete() {
      console.log('批量删除', this.pictureListChecked)
    },
    viewPicture(picture) {
      this.previewImgDialog = true
      this.previewImgSrc = picture.url
      console.log('查看图片', picture)
    },
    editPicture(picture) {
      this.basicRloeBtn = 'EditPic'
      this.dialoagName = '编辑图片'
      this.pictureTitle = '图片分类'
      this.pictureGroupDialog = true
      console.log('编辑图片', picture)
    },
    deletePicture(picture) {
      console.log('删除图片', picture)
    },

    handlePageChange(page) {
      console.log('分页切换到', page)
    },
    uploadImages() {
      console.log('拉取图片')
    },
    showPicture(index) {
      this.showImgDialog = true
      console.log('查看示例图片', this.urls)
    },
    upLoadImg(file, key) {
      // 上传
      this.$upLoadImg(file.file).then((res) => {
        this.fileList.push({
          name: file.file.name,
          url: res.data.url,
        })
      })
    },
    handleRemoveImages(file, fileList) {
      // 删除图片
      this.fileList = fileList
      console.log('删除图片', file, fileList)
    },
    handlePictureCardPreview(file) {
      // 预览图片
      this.previewSrcList = this.fileList.map((item) => item.url)
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },

    // 设置当前激活文件
    setActiveFile(uid) {
      this.activeFileUid = uid
    },

    // 删除文件
    handleRemoveFile(file) {
      const index = this.fileList.findIndex((f) => f.uid === file.uid)
      if (index !== -1) {
        this.fileList.splice(index, 1)
      }
    },
    setImageRef(uid, el) {
      if (el) {
        this.imageRefMap.set(uid, el)
      } else {
        this.imageRefMap.delete(uid)
      }
    },
    // 预览处理
    handlePreview(file) {
      this.previewSrcList = this.fileList.map((item) => item.url)
      // 通过DOM操作触发el-image的预览
      const imageComponent = this.imageRefMap.get(file.url)
      if (imageComponent) {
        imageComponent.clickHandler()
      }

      // const index = this.fileList.indexOf(file)
      // if (index !== -1) {
      //   debugger
      //   const imageComponent = this.$refs.previewImageRefs
      //   if (imageComponent && imageComponent.$el) {
      //     imageComponent.clickHandler()
      //   }
      // }
      this.$nextTick(() => {})
    },
  },
}
</script>

<style lang="scss" scoped>
.uploadImagesDiv {
  color: #fff;
  background: #0071fe;
  border-radius: 0px 4px 4px 0px;
}
.picture-management {
  padding: 20px;

  .el-cascader {
    width: 100%;
  }

  .category-tree {
    background: #fff;
    border-right: 1px solid #e6e6e6;

    .tree-header {
      padding: 5px;
      border-bottom: 1px solid #e6e6e6;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }
    }

    .tree-content {
      padding-top: 10px;

      .allnode {
        border-radius: 6px;
        background-color: #e6f4ff;
        color: rgb(24, 144, 255);
        cursor: pointer;
        margin-left: 23px;
      }

      .el-tree {
        padding-right: 5px;

        .custom-tree-node {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 14px;
          padding-right: 8px;
        }
      }
    }
  }

  .picture-content {
    margin-left: 20px;

    .search-bar {
      margin-bottom: 20px;

      .el-input {
        width: 100%;
      }
    }

    .action-buttons {
      margin-bottom: 20px;
      display: flex;
      gap: 10px;
    }

    .picture-list {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-top: 20px;

      .picture-card {
        position: relative;

        .picture-checkbox {
          position: absolute;
          top: 10px;
          left: 10px;
        }

        .picture-preview {
          width: 170px;
          height: 150px;
          object-fit: cover;
          border-radius: 4px;
        }

        .picture-info {
          margin-top: 10px;

          .picture-title {
            font-weight: bold;
            font-size: 14px;
          }

          .picture-tags {
            margin: 5px 0;
          }

          .picture-time {
            color: #909399;
            font-size: 12px;
          }
        }

        .picture-actions {
          display: flex;
          justify-content: space-around;
          margin-top: 10px;
        }
      }
    }
  }
  // .uploadspan {
  //   .uploadlogo {
  //     width: 100px;
  //     height: 100px;
  //     border: 1px dashed #d9d9d9;
  //     border-radius: 4px;
  //     cursor: pointer;
  //     display: flex;
  //     justify-content: center;
  //     align-items: center;
  //     position: relative;
  //     .el-upload--picture-card {
  //       width: 100px;
  //       height: 100px;
  //       border-radius: 4px;
  //       .uploadDiv {
  //         height: 100%;
  //         line-height: 100px;
  //         display: flex;
  //         flex-direction: column;
  //         justify-content: center;
  //         align-items: center;
  //         .el-icon-plus {
  //           font-size: 20px;
  //           color: #c0c4cc;
  //         }

  //         .el-upload__text {
  //           line-height: 20px;
  //           color: #c0c4cc;
  //           font-size: 12px;
  //         }
  //       }
  //     }

  //     .el-upload__tip {
  //       color: #c0c4cc;
  //       font-size: 12px;
  //       margin-top: 10px;
  //     }

  //     .license_image {
  //       width: 100px;
  //       height: 100px;
  //       border-radius: 4px;
  //     }
  //   }
  // }
  /* 文件项容器 */
  .image-item-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    /* 操作按钮容器 */
    .action-buttons {
      display: flex;
      justify-content: center;
      position: absolute;
      width: 100%;
      height: 100%;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      gap: 12px;
      opacity: 0; /* 默认透明 */
      transition: opacity 0.3s ease;
      background: rgba(0, 0, 0, 0.5);
      padding: 8px 16px;
      border-radius: 4px;
      /* 按钮样式 */
      .el-button {
        color: white !important;
        font-size: 20px;
      }
    }
  }
  /* 鼠标悬停时显示按钮 */
  .image-item-wrapper:hover .action-buttons {
    opacity: 1;
  }

  /* 图片hover效果 */
  // .image-item-wrapper:hover .upload-image {
  //   transform: scale(1.05);
  //   transition: transform 0.3s ease;
  // }
  /* 隐藏默认删除按钮 */
  .el-upload-list__item .el-icon-close {
    display: none !important;
  }

  /* 图片显示样式 */
  .upload-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
  }

  .networkimage {
    position: relative;
    overflow: hidden;
    background-color: #fff;
    border: 1px solid #c0ccda;
    border-radius: 6px;
    box-sizing: border-box;
    width: 148px;
    height: 148px;
    margin: 10px 8px 8px 0;
    display: inline-block;
    .upload-networkimage {
      width: 100%;
      height: 100%;
    }
    /* 操作按钮容器 */
    .action-buttons {
      display: flex;
      justify-content: center;
      position: absolute;
      width: 100%;
      height: 100%;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      gap: 12px;
      opacity: 0; /* 默认透明 */
      transition: opacity 0.3s ease;
      background: rgba(0, 0, 0, 0.5);
      padding: 8px 16px;
      border-radius: 4px;
      /* 按钮样式 */
      .el-button {
        color: white !important;
        font-size: 20px;
      }
    }
  }
  /* 鼠标悬停时显示按钮 */
  .networkimage:hover .action-buttons {
    opacity: 1;
  }
}

html body {
  .el-dialog__body {
    padding: 10px 20px;
  }
}
</style>
