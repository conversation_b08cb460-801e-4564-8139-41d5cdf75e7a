<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-04-01 15:54:15
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-28 15:06:12
 * @FilePath: /qst-merchant-admin-2.0/src/views/shop/goodsSet/goodsCate/compontes/cate.vue
 * @Description: 分类图标组件
-->
<template>
  <div>
    <el-dialog
      :title="type == 'add' ? '添加分类' : '修改分类'"
      :visible.sync="isCate"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <div>
        <el-form ref="form" :model="form" label-position="top" :rules="rules">
          <el-form-item>
            <div slot="label" class="flex-b solt-header">
              <span>父级分类</span>
              <span class="solt-text">不选择父级分类将创建一级分类</span>
            </div>
            <el-cascader
              class="w100"
              v-model="form.parent_id"
              :disabled="type == 'edit'"
              placeholder="请选择父级分类"
              :props="{ 
                  label: 'name',
                  value: 'cate_id',
                  checkStrictly: true
                }"
              :options="cateList"
            ></el-cascader>
          </el-form-item>

          <el-form-item label="分类名称（最多可输入10个字）" prop="name">
            <el-input maxlength="10" v-model="form.name" placeholder="请输入分类名称"></el-input>
          </el-form-item>

          <el-form-item label="分类图标" prop="cate_pic">
            <div class="flex">
              <el-image
                class="mr20 cate_image"
                :src="form.cate_pic"
                :preview-src-list="[form.cate_pic]"
                v-if="form.cate_pic"
              ></el-image>
              <el-button class="mr20" @click="openSystem">选择系统图标</el-button>
              <el-button class="up_image_relative">
                本地上传图标
                <el-upload
                  class="upload_img"
                  action="fakeaction"
                  :show-file-list="false"
                  drag
                  accept=".jpg, .png"
                  :http-request="upLoadImg"
                ></el-upload>
              </el-button>
            </div>
          </el-form-item>

          <el-form-item label="是否显示">
            <div class="flex">
              <el-switch v-model="form.is_show"></el-switch>
              <span class="ml10">{{form.is_show ? '显示' : '隐藏'}}</span>
            </div>
          </el-form-item>

          <el-form-item label="分类描述">
            <el-input type="textarea" v-model="form.desc" placeholder="请输入描述"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <base-dialog-footer @confirm="saveCate" @cancel="cancelFn"></base-dialog-footer>
    </el-dialog>

    <system-cate ref="systemCate" @selectSystem="selectSystem"></system-cate>
  </div>
</template>

<script>
import { getListBySelectApi, createShopCategoryApi, updateShopCategoryApi } from '@/api/shop/goodsSet/goodsCate.js'
import { upLoadImg } from '@/utils/uploadImage.js'
import systemCate from './systemCate.vue'
export default {
  name: 'cateImage',
  data() {
    return {
      shop_id: '',
      isCate: false,
      // 表单数据
      form: {
        name: '',
        desc: '',
        cate_pic: '',
        parent_id: [],
        sort: 0,
        status: '1',
        is_show: true,
      },
      // 表单验证规则
      rules: {
        name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
        cate_pic: [{ required: true, message: '请选择图标', trigger: 'change' }],
      },

      // 是否显示系统图标弹窗
      isSystem: false,
      // add edit 修改分类图标弹窗类型
      type: '',
      cateList: [],
    }
  },
  components: {
    systemCate,
  },
  methods: {
    // 打开分类图标弹窗
    openCate(type, shop_id, obj) {
      this.type = type
      this.shop_id = shop_id
      this.isCate = true
      if (obj) {
        this.form = {
          ...obj,
          is_show: obj.is_show == 'Y',
        }
      } else {
        this.$set(this, 'form', {
          name: '',
          desc: '',
          cate_pic: '',
          parent_id: [],
          sort: 0,
          status: '1',
          is_show: true,
        })
      }
      this.getShopCategoryFn()
    },

    // 打开系统图标弹窗
    openSystem() {
      this.$refs.systemCate.openSystem()
    },

    // 选择系统图标
    selectSystem(res) {
      console.log(res)
      this.form.cate_pic = res
    },

    // 获取上级分类列表
    getShopCategoryFn() {
      getListBySelectApi({
        shop_id: this.shop_id,
      }).then((res) => {
        if (res.code == 200) {
          this.cateList = [
            {
              id: '',
              name: '无',
            },
            ...res.data,
          ]
          console.log(this.cateList)
        }
      })
    },

    upLoadImg(file) {
      console.log(file)
      upLoadImg(file.file).then((res) => {
        if (res.code == 200) {
          console.log(res, '图片上传成功')
          this.form.cate_pic = res.data.url
        }
      })
    },

    // 保存分类图标
    saveCate() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let params = {}
          let api = null
          if (this.type == 'add') {
            params = {
              ...this.form,
              shop_id: this.shop_id,
              is_show: this.form.is_show ? 'Y' : 'N',
              parent_id: this.form.parent_id[this.form.parent_id.length - 1],
            }
            api = createShopCategoryApi
          } else {
            params = {
              category_id: this.form.id,
              shop_id: this.shop_id,
              name: this.form.name,
              desc: this.form.desc,
              parent_id: this.form.parent_id[this.form.parent_id.length - 1],
              is_show: this.form.is_show ? 'Y' : 'N',
              cate_pic: this.form.cate_pic,
            }
            api = updateShopCategoryApi
          }
          api(params).then((res) => {
            if (res.code == 200) {
              this.$message({
                type: 'success',
                message: '保存成功',
              })
              if (this.type == 'add') {
                this.$emit('update', [...this.form.parent_id, res.data.id])
              } else {
                this.$emit('update')
              }
              this.isCate = false
            }
          })
        }
      })
    },
    cancelFn() {
      console.log('取消操作')
      this.isCate = false
    },
  },
}
</script>

<style lang="scss" scoped>
.solt-header {
  width: 440px;
  .solt-text {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #89919a;
    line-height: 14px;
  }
}
.w100 {
  width: 100%;
}
.mr20 {
  margin-right: 10px;
}
.ml10 {
  margin-left: 10px;
}

.cate_image {
  width: 60px;
  height: 60px;
}
.updata-image {
  width: 60px;
  height: 60px;
  border: 1px dashed #d9d9d9;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  display: flex;
  line-height: 60px;
}

html body .el-dialog__body .el-form,
html body .el-message-box__body .el-form {
  padding-right: 0;
}

.up_image_relative {
  position: relative;
  overflow: hidden;
  .upload_img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    opacity: 0;
  }
}
</style>