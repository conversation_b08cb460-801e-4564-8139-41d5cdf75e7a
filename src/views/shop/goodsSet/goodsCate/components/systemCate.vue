<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-04-30 15:57:35
 * @LastEditors: shangfei <EMAIL>
-->
<template>
  <el-dialog
    title="选择系统图标"
    :close-on-click-modal="false"
    :visible.sync="isSystem"
    :before-close="systemClose"
    width="780px"
  >
    <div>
      <img
        @click="selectKey(item.image)"
        class="sys-image"
        :class="{ 'sys-image-key': systemImage === item.image }"
        :src="item.image"
        v-for="(item,i) in systemImageList"
        :key="i"
      />
    </div>
    <base-dialog-footer @confirm="saveImageSystem" @cancel="isSystem = false"></base-dialog-footer>
  </el-dialog>
</template>

<script>
import { systemIconShopCategoryApi } from '@/api/shop/goodsSet/goodsCate.js'
export default {
  name: 'systemCate',
  data() {
    return {
      isSystem: false,
      systemImage: '',
      systemImageList: [],
    }
  },
  methods: {
    initSystem() {
      systemIconShopCategoryApi().then((res) => {
        this.systemImageList = res.data || []
        if (res.data.length === 0) {
          this.$message({
            type: 'warning',
            message: '暂无系统图标',
          })
        }
        this.systemImage = res.data[0].image || ''
      })
    },
    openSystem() {
      this.initSystem()
      this.isSystem = true
    },
    systemClose() {
      this.isSystem = false
    },
    selectKey(e) {
      this.systemImage = e
    },
    saveImageSystem() {
      this.isSystem = false
      this.$emit('selectSystem', this.systemImage || '')
    },
  },
}
</script>

<style lang="scss" scoped>
.sys-image {
  width: 60px;
  height: 60px;
  margin: 5px;
  cursor: pointer;
  border-radius: 6px;
  border: 2px solid #fff;
}
.sys-image-key {
  border: 2px solid #0071fe;
}
</style>
