<template>
  <div>
    <el-dialog
      title="修改图标"
      :close-on-click-modal="false"
      :visible.sync="isIcon"
      :before-close="beforeClose"
    >
      <el-form ref="iocnForm" :model="iocnForm" label-position="top" :rules="iocnFormRules">
        <el-form-item label="图标" prop="cate_pic">
          <div class="flex">
            <el-image
              class="mr20 cate_image"
              :src="iocnForm.cate_pic"
              v-if="iocnForm.cate_pic"
              :preview-src-list="[iocnForm.cate_pic]"
            ></el-image>
            <el-button class="mr20" @click="openSystem">选择系统图标</el-button>
            <el-button class="up_image_relative">
              本地上传图标
              <el-upload
                class="upload_img"
                action="fakeaction"
                :show-file-list="false"
                drag
                accept=".jpg, .png"
                :http-request="upLoadImg"
              ></el-upload>
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <base-dialog-footer @confirm="saveIcon" @cancel="isIcon=false"></base-dialog-footer>
    </el-dialog>
    <system-cate ref="selectCate" @selectSystem="selectSystem"></system-cate>
  </div>
</template>


<script>
import { upLoadImg } from '@/utils/uploadImage.js'
import { updateIconShopCategoryApi } from '@/api/shop/goodsSet/goodsCate'
import systemCate from './systemCate.vue'
export default {
  name: 'IconDialog',
  props: {},
  data() {
    return {
      shop_id: '',
      isIcon: false,
      iocnForm: {
        cate_pic: '',
      },
      iocnFormRules: {
        cate_pic: [{ required: true, message: '请上传图标', trigger: 'change' }],
      },
    }
  },
  components: {
    systemCate,
  },
  methods: {
    // 打开图标选择弹窗
    openCate(e) {
      this.shop_id = e.shop_id || ''
      this.id = e.id || ''
      this.iocnForm = {
        cate_pic: e.cate_pic,
      }
      this.isIcon = true
    },
    // 打开系统图标选择弹窗
    openSystem() {
      this.$refs.selectCate.openSystem()
    },
    // 选择系统图标
    selectSystem(e) {
      this.iocnForm.cate_pic = e
    },
    // 关闭图标选择弹窗
    beforeClose() {
      this.isIcon = false
    },

    // 上传图片方法
    upLoadImg(file) {
      upLoadImg(file.file).then((res) => {
        if (res.code == 200) {
          console.log(res, '图片上传成功')
          this.iocnForm.cate_pic = res.data.url
        }
      })
    },

    // 保存图标
    saveIcon() {
      updateIconShopCategoryApi({
        shop_id: this.shop_id,
        category_id: this.id,
        cate_pic: this.iocnForm.cate_pic,
      }).then((res) => {
        if (res.code == 200) {
          this.isIcon = false
          this.$message.success('修改成功')
          this.$emit('update')
        }
      })
    },
  },
}
</script>



<style lang="scss" scoped>
.w100 {
  width: 100%;
}
.mr20 {
  margin-right: 10px;
}
.ml10 {
  margin-left: 10px;
}

.cate_image {
  width: 100px;
  height: 100px;
}

.up_image_relative {
  position: relative;
  overflow: hidden;
  .upload_img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    opacity: 0;
  }
}
</style>