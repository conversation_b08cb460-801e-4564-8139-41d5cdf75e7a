<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-03-22 15:01:29
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-28 12:11:34
 * @Description: 商品分类 
-->
<template>
  <div>
    <el-card class="store-model">
      <base-form :tableForm="tableForm" :formArray="formArray" @searchForm="searchForm"></base-form>
      <div class="operate">
        <el-button @click="createdCate('add')" type="primary" icon="el-icon-plus">新建</el-button>
        <el-button @click="batchOperate('N')" type="primary">批量显示</el-button>
        <el-button @click="batchOperate('Y')" type="primary">批量隐藏</el-button>
      </div>

      <el-table
        ref="tableref"
        :data="tableData"
        style="width: 100%;margin-top: 20px;"
        row-key="id"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        tooltip-effect="dark"
        @selection-change="handleSelectionChange"
        v-loading="isLoading"
        :expand-row-keys="defaultExpand"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="cate"></el-table-column>
        <el-table-column prop="cate_pic" label="分类图标">
          <template #default="scope">
            <div>
              <el-image class="cate_pic_size" v-if="scope.row.cate_pic" :src="scope.row.cate_pic"></el-image>
              <div v-else class="cate_pic_size">暂无</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="分类名称"></el-table-column>
        <el-table-column prop="desc" label="描述"></el-table-column>
        <el-table-column prop="belongto_name" label="所属分类"></el-table-column>
        <el-table-column label="显示状态">
          <template #default="scope">
            <div class="flex">
              <div @click="switchChange([scope.row.id],scope.row.is_show)">
                <el-switch
                  class="ml6"
                  :value="scope.row.is_show == 'Y'"
                  active-color="#0071FE"
                  inactive-color="#d9d9d9"
                ></el-switch>
              </div>
              <span>{{scope.row.is_show == 'Y' ? '显示' : scope.row.is_show == 'N' ? '隐藏' : ''}}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <div class="public-operate-btn">
              <el-dropdown
                @command="(e)=>moveFn(e, scope.row)"
                v-if="!scope.row.isTop || !scope.row.isBottom"
              >
                <span class="el-dropdown-link">移动</span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-if="!scope.row.isTop" command="moveTop">
                    <i class="el-icon-upload2"></i>置顶
                  </el-dropdown-item>
                  <el-dropdown-item v-if="!scope.row.isTop" command="moveUp">
                    <i class="el-icon-top"></i>上移
                  </el-dropdown-item>
                  <el-dropdown-item v-if="!scope.row.isBottom" command="moveDown">
                    <i class="el-icon-bottom"></i>下移
                  </el-dropdown-item>
                  <el-dropdown-item v-if="!scope.row.isBottom" command="moveBottom">
                    <i class="el-icon-download"></i>置底
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-button
                type="text"
                v-if="scope.row.belongto_name == '系统分类'"
                @click="openIcon(scope.row)"
              >修改图标</el-button>
              <template v-else>
                <el-button type="text" @click="createdCate('edit', scope.row)">修改</el-button>
                <el-popconfirm @confirm="delCate(scope.row)" title="您确定要删除该分类吗？">
                  <el-button slot="reference" class="cate-btn" type="text">删除</el-button>
                </el-popconfirm>
              </template>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <addCate ref="classification" @update="tableInit"></addCate>
    <editCateIcon ref="editCateIcon" @update="tableInit"></editCateIcon>
  </div>
</template>

<script>
import {
  shopCategoryListApi,
  updateStatusShopCategoryApi,
  deleteShopCategoryApi,
  sortShopCategoryApi,
} from '@/api/shop/goodsSet/goodsCate'
import addCate from './components/addCate.vue'
import editCateIcon from './components/editCateIcon.vue'
export default {
  name: 'shopClassification',
  components: {
    addCate,
    editCateIcon,
  },
  data() {
    return {
      defaultExpand: [],
      tableData: [],
      formArray: [
        {
          label: '分类描述',
          type: 'input',
          key: 'desc',
          placeholder: '请输入分类描述',
        },
        {
          label: '分类名称',
          type: 'input',
          key: 'name',
          placeholder: '请输入分类名称',
        },
        {
          label: '显示状态',
          type: 'select',
          key: 'status',
          options: [
            { label: '全部', value: '' },
            { label: '显示', value: 1 },
            { label: '隐藏', value: '0' },
          ],
        },
      ],
      tableForm: {
        status: 1,
      },
      isLoading: true,
      shop_id: '',
      multipleSelection: [],
    }
  },
  mounted() {
    this.tableInit()
  },
  methods: {
    // 表格初始化数据
    tableInit() {
      this.isLoading = true
      shopCategoryListApi({
        ...this.tableForm,
        shop_id: this.shop_id,
        page: 1,
        limit: 100,
      }).then((res) => {
        if (res.code == 200) {
          this.tableData = this.tableEdit(res.data.list)
          this.isLoading = false
          this.openExpandRow()
        }
      })
    },
    // 关闭所有展开行
    closeExpandRow(data, isExpansion) {
      data.forEach((item) => {
        this.$refs.tableref.toggleRowExpansion(item, isExpansion)
        if (item.childList?.length && item.childList.length > 0) {
          this.closeExpandRow(item.childList, isExpansion)
        }
      })
    },

    // 编辑table数据
    tableEdit(list) {
      return list.map((item, index) => {
        if (item.children) {
          item.children = this.tableEdit(item.children)
        }
        return {
          ...item,
          isTop: index == 0 ? true : false,
          isBottom: index == list.length - 1 ? true : false,
        }
      })
    },

    // 选择框事件处理函数
    handleSelectionChange(val) {
      console.log(val)
      this.multipleSelection = val
    },
    // 批量显示隐藏
    batchOperate(state) {
      if (this.multipleSelection.length == 0) {
        return this.$message.warning('请选择数据')
      }
      let ids = this.multipleSelection.map((item) => item.id)
      this.switchChange(ids, state)
    },

    // 切换
    switchChange(id, isShow) {
      updateStatusShopCategoryApi({
        shop_id: this.shop_id,
        category_ids: id,
        is_show: isShow == 'Y' ? 'N' : 'Y',
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success('操作成功')
          this.tableInit()
          this.multipleSelection = []
        }
      })
    },

    // 新建分类
    createdCate(type, row) {
      this.$refs.classification.openCate(type, this.shop_id, row)
    },
    // 表单搜索事件
    searchForm(form) {
      this.tableForm = Object.assign({ shop_id: this.shop_id }, this.tableForm, form)
      this.tableInit()
    },
    // 展开行
    extractIdsFromList(list) {
      const ids = []
      list.forEach((item) => {
        const traverse = (node) => {
          ids.push(node.id.toString())
          if (node.children) {
            node.children.forEach(traverse)
          }
        }
        traverse(item)
      })
      return ids
    },
    openExpandRow() {
      if (this.tableForm.desc || this.tableForm.name) {
        let expandID = this.extractIdsFromList(this.tableData) //假设 id 是每行的唯一标识符
        this.defaultExpand = [...expandID]
      } else {
        this.defaultExpand = []
        this.closeExpandRow(this.tableData, false) //关闭所有展开行
      }
    },

    // 修改图标事件处理函数
    openIcon(val) {
      this.$refs.editCateIcon.openCate(val)
    },
    // 移动处理函数
    moveFn(type, row) {
      sortShopCategoryApi({
        type,
        shop_id: this.shop_id,
        category_id: row.id,
        status: this.tableForm.status,
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success('操作成功')
          this.tableInit()
        }
      })
    },

    // 删除分类
    delCate(row) {
      deleteShopCategoryApi({
        shop_id: this.shop_id,
        category_id: row.id,
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success('删除成功')
          this.tableInit()
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.ml6 {
  margin-right: 6px;
}
.operate {
  margin-top: 20px;
}
.cate_pic_size {
  width: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
}
::v-deep {
  .el-dropdown {
    color: #409eff !important;
    font-size: 12px;
    margin-right: 10px !important;
    position: relative;
    cursor: pointer;
  }
  .el-dropdown::after {
    content: '';
    display: block;
    width: 1px;
    height: 8px;
    background: rgba(0, 0, 0, 0.08);
    box-shadow: inset 0px -1px 0px 0px #ebeef5;
    position: absolute;
    right: -6px;
    top: 6px;
  }
}

::v-deep {
  // 表格头部样式
  .el-table th.el-table__cell {
    background: #f5f7fa;
    box-shadow: inset 0px -1px 0px 0px #ebeef5, inset 0px -1px 0px 0px #ebeef5;
  }

  .el-pagination.is-background .btn-prev,
  .el-pagination.is-background .btn-next,
  .el-pagination.is-background .el-pager li {
    background-color: #fff;
    border: 1px solid #dcdfe6;
    color: #606266;
  }

  .el-pagination.is-background .btn-prev:disabled,
  .el-pagination.is-background .btn-next:disabled {
    color: #c0c4cc;
  }
}

// 公共操作按钮样式
.cate-btn {
  margin-left: 4px !important;
  position: relative;
  padding: 0 0 0 5px;

  &::before {
    content: '';
    display: block;
    width: 1px;
    height: 8px;
    background: rgba(0, 0, 0, 0.08);
    box-shadow: inset 0px -1px 0px 0px #ebeef5;
    position: absolute;
    left: 0;
    top: 2px;
  }
}
</style>

