<!--
 * @Author: liqian liqian@123
 * @Date: 2025-03-26 16:35:23
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-14 10:06:43
 * @FilePath: \qst-merchant-admin-2.0\src\views\shop\goodsSet\specsTemplate\index.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <el-card class="store-model">
      <base-form :tableForm="tableForm" :formArray="formArray" @searchForm="searchForm"></base-form>
      <div class="operate">
        <el-button type="primary" icon="el-icon-plus" @click="addStore">新建</el-button>
      </div>
      <base-table
        :tableColumn="tableColumn"
        :tableRequest="getGoodsSpecList"
        :tableForm="tableForm"
        ref="tableRef"
      >
        <template #spec_list="{ scope }">
          <div v-for="item in scope.row.spec_list">{{ item }}</div>
        </template>

        <template #operate="{ scope }">
          <div class="public-operate-btn">
            <el-button size="mini" type="text" @click="openDetail(scope.row)">修改</el-button>
            <el-button size="mini" type="text" @click="handelDelete(scope.row)">删除</el-button>
          </div>
        </template>
      </base-table>
    </el-card>
    <createSpersTemplate ref="reateSpers" @confirm="confirm"></createSpersTemplate>
  </div>
</template>

<script>
  import { getGoodsSpecList, deleteGoodsSpecList } from '@/api/shop/goodsSet/goodsSpec'
  import createSpersTemplate from './createSpersTemplate.vue'
  export default {
    name: 'shopGrouping',
    components: {
      createSpersTemplate,
    },
    data() {
      return {
        activeName: 'first',
        formArray: [
          {
            label: '模板名称',
            type: 'input',
            key: 'name',
            placeholder: '请输入模板名称',
          },
        ],

        tableColumn: [
          {
            label: '模板名称',
            prop: 'name',
            width: '150px',
          },
          {
            label: '规格组数量',
            prop: 'count',
          },
          {
            label: '规格详情',
            prop: 'spec_list',
            type: 'customize',
          },
          {
            label: '操作',
            prop: 'operate',
            type: 'customize',
            width: '150px',
          },
        ],
        getGoodsSpecList,
        tableForm: {},
      }
    },
    created() {},
    methods: {
      // 表单搜索事件
      searchForm(form) {
        this.tableForm = Object.assign({}, this.tableForm, form)
      },
      // 删除
      handelDelete(row) {
        this.$confirm('是否删除该规格模版?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            deleteGoodsSpecList({ template_id: row.id }).then((res) => {
              if (res.code == 200) {
                this.$message.success('删除成功')
                this.$refs.tableRef.tableRequestFn()
              }
            })
          })
          .catch((action) => {
            // this.$message({
            //   type: 'info',
            //   message: action === 'cancel' ? '取消删除' : '',
            // })
          })
      },

      // 查看详情
      openDetail(row) {
        this.$refs.reateSpers.open(row, 'Edit')
      },
      // 新增
      addStore() {
        this.$refs.reateSpers.open()
      },
      // 新增确定返回   更新表格数据
      confirm() {
        this.$refs.tableRef.tableRequestFn()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .operate {
    margin-top: 20px;
  }
  .del-color {
    color: #6e6e7a;
  }
</style>
