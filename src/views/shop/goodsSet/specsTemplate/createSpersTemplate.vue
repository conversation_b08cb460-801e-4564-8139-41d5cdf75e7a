<!--
 * @Author: liqian liqian@123
 * @Date: 2025-03-26 16:46:59
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-17 17:21:12
 * @FilePath: \qst-merchant-admin-2.0\src\views\shop\goodsSet\specsTemplate\createSpersTemplate.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-dialog
    :title="title"
    width="700"
    top="15vh"
    :visible.sync="templateDialog"
    :close-on-click-modal="false"
    append-to-body
  >
    <div class="share" v-loading="loading">
      <el-form
        label-width="180px"
        :rules="basicInfoRule"
        :model="basicInfo"
        ref="basicInfo"
        :label-position="labelPosition"
      >
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="basicInfo.name" type="text" placeholder="请输入模板名称"></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="desc">
          <el-input type="textarea" v-model="basicInfo.desc" placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>

      <div class="templateMain" v-if="templateArr.length">
        <div v-for="(item, index) in templateArr" :key="item.id">
          <div class="templateTitle">
            <h4>规格组{{ index + 1 }}</h4>
            <span @click="deleteTemplate(index)" v-if="templateArr.length > 1">删除</span>
          </div>
          <el-form
            label-width="180px"
            :model="item"
            ref="basicInfo1"
            :rules="basicInfoRule1"
            :label-position="labelPosition"
          >
            <el-form-item label="规格组名称" prop="name">
              <el-input
                v-model="item.name"
                type="text"
                placeholder="请输入规格组名称，如颜色、尺寸等"
              ></el-input>
            </el-form-item>
            <el-form-item label="规格值" prop="selectValue">
              <el-select
                style="width: 100%"
                v-model="item.selectValue"
                multiple
                filterable
                allow-create
                default-first-option
                @change="addSelectValue(item, index)"
                placeholder="请输入规格值"
              >
                <el-option
                  v-for="item1 in item.tempList"
                  :key="item1.name"
                  :label="item1.name"
                  :value="item1.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div @click="addTemplate" class="addTemplate">
        <i class="el-icon-plus"></i>
        添加规格组
      </div>
    </div>
    <base-dialog-footer
      @cancel="templateDialog = false"
      confirmText="确定"
      @confirm="confirm"
    ></base-dialog-footer>
  </el-dialog>
</template>

<script>
  import {
    createGoodsSpecList,
    detailsGoodsSpecList,
    updateGoodsSpecList,
  } from '@/api/shop/goodsSet/goodsSpec'
  export default {
    name: 'selectAddressMap',
    components: {},
    props: {
      shop_id: {
        // type: Number,
        default: '',
      },
    },
    data() {
      return {
        title: '添加规格模板',
        templatevalue: '',
        templateDialog: false,
        dialogVisible: false,
        loading: true,
        labelPosition: 'top',
        basicInfo: {
          name: '', // 模版名称
          desc: '', // 描述
          spec_list: [],
        },
        editAndFlag: '',
        currentLi: {},
        templateArr: [],
        basicInfoRule: {
          name: [{ required: true, message: '请输入模版名称', trigger: 'blur' }],
        },
        basicInfoRule1: {
          name: [{ required: true, message: '请输入规格组名称，如颜色、尺寸等 ', trigger: 'blur' }],
          selectValue: [{ required: true, message: '请输入规格值', trigger: 'blur' }],
        },
      }
    },
    created() {},
    methods: {
      /**
       * @description: 新增规格组
       * @param {type} name 规格组名称
       * @param {type} value 规格值
       * @param {type} selectValue 选择的规格值
       * @return {*}
       */
      addTemplate() {
        this.$set(this.templateArr, this.templateArr.length, {
          name: '',
          tempList: [],
          selectValue: [],
          spec_values: [],
        })
      },
      deleteTemplate(index) {
        this.templateArr.splice(index, 1)
      },

      // 规格组数据转换
      addSelectValue(item, index) {
        let getSpecValues = []
        // 添加规格值
        if (item.selectValue.length > item.spec_values.length) {
          getSpecValues = this.generateCC(item.selectValue, item.spec_values, 'merge', {
            autoType: false,
            generateId: () => '', // 强制id为空字符串
          })
        }
        // 删除规格值
        if (item.selectValue.length < item.spec_values.length) {
          getSpecValues = this.generateCC(item.selectValue, item.spec_values, 'filter', {
            autoType: false,
            generateId: () => '', // 强制id为空字符串
          })
        }
        this.$nextTick(() => {
          this.$set(this.templateArr[index], 'tempList', getSpecValues)
          this.$set(this.templateArr[index], 'spec_values', getSpecValues)
        })
      },
      open(row = {}, flag = 'ADD') {
        this.currentLi = row
        this.editAndFlag = flag
        this.templateDialog = true
        if (flag == 'Edit') {
          this.title = '编辑模板'
          this.getDetailsGoods(row)
        } else {
          this.title = '添加模板'
          this.basicInfo = {
            name: '',
            desc: '',
            spec_list: [],
          }
          this.templateArr = []
        }
        this.loading = false
        this.$nextTick(() => {
          this.$refs.basicInfo.resetFields()
        })
      },
      //得到详情模版信息
      getDetailsGoods(row) {
        detailsGoodsSpecList({ shop_id: this.shop_id, template_id: row.id }).then((res) => {
          if (res.code == 200) {
            let resultData = res.data
            this.basicInfo.name = resultData.name
            this.basicInfo.desc = resultData.desc
            resultData.spec_list.forEach((item, index) => {
              item.tempList = item.spec_values
              item.selectValue = []
              item.spec_values.forEach((iitem) => {
                item.selectValue = item.selectValue.concat(iitem.id)
              })
              this.$set(this.templateArr, index, Object.assign({}, item))
            })
          }
        })
      },
      //新增模版
      createGoodsSpec() {
        let params = {
          shop_id: this.shop_id,
          ...this.basicInfo,
        }
        createGoodsSpecList(params).then((res) => {
          if (res.code == 200) {
            this.templateDialog = false

            this.$emit('confirm')
          }
        })
      },
      //更新模版
      updateGoodsSpec() {
        let params = {
          template_id: this.currentLi.id,
          shop_id: this.shop_id,
          ...this.basicInfo,
        }
        updateGoodsSpecList(params).then((res) => {
          if (res.code == 200) {
            this.templateDialog = false
            this.$emit('confirm')
          }
        })
      },
      confirm() {
        this.$refs.basicInfo.validate((valid) => {
          if (valid) {
            console.log(this.templateArr)
            if (!this.templateArr.length) {
              this.$message.error('请添加规格组')
              return false
            } else {
              this.$refs.basicInfo1.forEach((item, index) => {
                item.validate((valid) => {
                  if (valid) {
                    if (index + 1 == this.$refs.basicInfo1.length) {
                      this.basicInfo.spec_list = this.templateArr

                      if (this.editAndFlag == 'Edit') {
                        this.updateGoodsSpec()
                      } else {
                        this.createGoodsSpec()
                      }
                    }
                  } else {
                    console.log('error submit!!')
                    return false
                  }
                })
              })
            }
          } else {
            console.log('error submit!!')
            return false
          }
        })
      },
      /**
       * 根据数组aa和bb生成cc数组，支持过滤和合并两种模式
       * @param {number[]} aa - 源数字数组
       * @param {Object[]} bb - 源对象数组（对象必须包含id字段）
       * @param {string} [mode='merge'] - 模式：'merge'合并 / 'filter'过滤
       * @param {Object} [options={}] - 配置项
       * @param {boolean} [options.autoType=true] - 是否自动转换数字类型以匹配id类型（默认true）
       * @param {Function} [options.generateId] - 自定义新增对象的id生成函数
       * @param {Function} [options.generateName] - 自定义新增对象的name生成函数
       * @returns {Object[]} 生成的新数组
       */
      generateCC(aa, bb, mode = 'merge', options = {}) {
        // 参数校验
        if (!Array.isArray(aa) || !Array.isArray(bb)) {
          throw new TypeError('aa和bb必须是数组')
        }
        if (!['merge', 'filter'].includes(mode)) {
          throw new Error('mode参数必须为 "merge" 或 "filter"')
        }
        // if (bb.some((item) => typeof item.id === 'undefined')) {
        //   throw new Error('bb中的对象必须包含id字段')
        // }

        // 解构配置项
        const {
          autoType = true,
          generateId = (num) => '', // 默认id为空字符串
          generateName = (num) => num.toString(),
        } = options

        // 类型统一处理
        const aaProcessed = autoType ? aa.map((num) => num.toString()) : aa
        // const bbIds = new Set(bb.map((item) => item.id))
        // 收集 bb 的 id 和 name 集合（过滤 undefined）
        const bbIds = new Set(bb.map((item) => item.id).filter(Boolean))
        const bbNames = new Set(bb.map((item) => item.name))

        // 模式处理分支
        if (mode === 'filter') {
          // 过滤模式：保留bb中id存在于aa的元素
          return bb.filter((item) => aaProcessed.includes(item.id))
        } else {
          // 合并模式：保留bb原有数据 + 补充aa中缺失的项
          // let bbb = bb.filter((item) => item.id != '') //过滤出id不为空的数组
          // const cc = [...bbb] // 深拷贝可用JSON.parse(JSON.stringify(bb))

          // 保留 bb 的所有原始对象
          const cc = [...bb]

          // 遍历 aa 并处理每个元素
          aa.forEach((item, index) => {
            const compareValue = autoType ? item.toString() : item // 统一转为字符串比较
            const str = item.toString()
            // 仅当既不在 id 集合也不在 name 集合时添加新对象
            if (!bbIds.has(compareValue) && !bbNames.has(compareValue)) {
              cc.push({
                name: generateName(item),
                // id: generateId(item, index),
              }) // 新增对象不包含 id 字段
            }
          })
          return cc
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  html body {
    .el-dialog {
      top: 10% !important;
      transform: none;
      left: 0 !important;
      margin: auto !important;
    }
    .el-dialog__body {
      padding: 10px 20px;
    }
  }
  .templateTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    h4 {
      font-size: 16px;
      color: #303133;
      font-weight: 500;
    }
    span {
      color: #ff3c3c;
      cursor: pointer;
    }
  }
  .addTemplate {
    text-align: center;
    background: #ffffff;
    border-radius: 4px;
    padding: 8px;
    cursor: pointer;
    border: 1px dashed #dcdfe6;
  }
</style>
