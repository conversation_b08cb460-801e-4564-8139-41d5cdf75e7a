<template>
  <div>
    <el-card class="store-model" v-loading="loading">
      <div class="stock-warning-container">
        <!-- 预警提示说明 -->
        <div class="warning-tip">
          <i class="el-icon-info"></i>
          <div class="tip-content">
            <h4>库存预警提示</h4>
            <p>设置库存预警值后，当商品库存低于预警值时，系统会自动将该商品加入库存预警列表，以便您及时进行库存管理。</p>
          </div>
        </div>

        <!-- 预警设置表单 -->
        <el-form
          ref="warningForm"
          :model="warningForm"
          :rules="warningRules"
          label-width="160px"
          label-position="top"
          class="warning-form"
        >
          <!-- 预警方式 -->
          <el-row :gutter="24">
          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <el-form-item label="预警方式" prop="warningType">
              <el-select
                v-model="warningForm.warningType"
                placeholder="请选择预警方式"
                class="warning-select-h"
                @change="handleWarningTypeChange"
              >
                <el-option label="按商品类型" value="byCategory" />
                <el-option label="按商品分类" value="byClassification" />
                <!-- <el-option label="按商品分组" value="byGroup" /> -->
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 实物商品库存预警值 -->
            <template v-if="warningForm.warningType === 'byCategory'">
              <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                <el-form-item prop="physicalWarningValue">
                  <template #label>
                    <span>实物商品库存预警值</span>
                    <el-tooltip
                      effect="dark"
                      content="当实物商品库存低于此值时将触发预警"
                      placement="top"
                      :open-delay="200"
                    >
                      <img src="/static/views/shop/goodsSet/stock/<EMAIL>" class="question-icon" alt="帮助" />
                    </el-tooltip>
                  </template>
                  <el-input
                    v-model.number="warningForm.physicalWarningValue"
                    type="number"
                    placeholder="请输入预警值"
                    class="warning-input"
                    @input="handleInput($event, 'physicalWarningValue')"
                  >
                  </el-input>
                </el-form-item>
              </el-col>

            <!-- 卡券商品库存预警值 -->
              <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                <el-form-item prop="cardWarningValue">
                  <template #label>
                    <span>卡券商品库存预警值</span>
                    <el-tooltip
                      effect="dark"
                      content="当卡券商品库存低于此值时将触发预警"
                      placement="top"
                      :open-delay="200"
                    >
                      <img src="/static/views/shop/goodsSet/stock/<EMAIL>" class="question-icon" alt="帮助" />
                    </el-tooltip>
                  </template>
                  <el-input
                    v-model.number="warningForm.cardWarningValue"
                    type="number"
                    placeholder="请输入预警值"
                    class="warning-input"
                    @input="handleInput($event, 'cardWarningValue')"
                  >
                  </el-input>
                </el-form-item>
              </el-col>

            <!-- 虚拟商品库存预警值 -->
              <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                <el-form-item prop="virtualWarningValue">
                  <template #label>
                    <span>虚拟商品库存预警值</span>
                    <el-tooltip
                      effect="dark"
                      content="当虚拟商品库存低于此值时将触发预警"
                      placement="top"
                      :open-delay="200"
                    >
                      <img src="/static/views/shop/goodsSet/stock/<EMAIL>" class="question-icon" alt="帮助" />
                    </el-tooltip>
                  </template>
                  <el-input
                    v-model.number="warningForm.virtualWarningValue"
                    type="number"
                    placeholder="请输入预警值"
                    class="warning-input"
                    @input="handleInput($event, 'virtualWarningValue')"
                  >
                  </el-input>
                </el-form-item>
             </el-col>
            </template>
          </el-row>

            <!-- 按商品分类的预警值设置 -->
            <template v-if="warningForm.warningType === 'byClassification'">
              <div v-for="(item, index) in warningForm.classificationSettings" :key="index" class="warning-group">
                  <div class="warning-group-header">
                    <span><span style="color: #F56C6C; margin-right: 4px">*</span>商品分类{{ index + 1 }}预警值
                        <el-tooltip
                          effect="dark"
                          content="当该分类商品库存低于此值时将触发预警"
                          placement="top"
                          :open-delay="200"
                      >
                        <img src="/static/views/shop/goodsSet/stock/<EMAIL>" class="class-question-icon" alt="帮助" />
                      </el-tooltip>
                    </span>

                  </div>
                <el-row :gutter="24">
                  <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
                  <el-form-item
                    :prop="'classificationSettings.' + index + '.classificationId'"
                    :rules="{ required: true, message: '请选择商品分类', trigger: 'change' }"
                  >

                    <el-cascader
                      v-model="item.classificationId"
                      :options="cascaderClassificationOptions"
                      :props="{ checkStrictly: true, emitPath: false, value: 'id', label: 'name', children: 'children' }"
                      placeholder="请选择商品分类"
                      class="warning-select"
                      filterable
                      clearable
                      @change="() => handleClassificationChange(index)"
                    />
                  </el-form-item>
                  </el-col>

                  <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
                    <el-form-item
                      :prop="'classificationSettings.' + index + '.warningValue'"
                      :rules="[
                      { required: true, message: '请输入预警值', trigger: 'blur' },
                      { type: 'number', message: '预警值必须为数字', trigger: 'blur' }
                    ]"
                    >

                      <el-input
                        v-model.number="item.warningValue"
                        type="number"
                        placeholder="请输入预警值"
                        class="warning-input"
                        @input="value => handleInput(value, 'classificationSettings.' + index + '.warningValue')"
                      >
                      </el-input>
                    </el-form-item>
                  </el-col>

                  <el-col :xs="4" :sm="4" :md="4" :lg="4" :xl="4">
                    <el-form-item>
                      <el-button type="text" class="delete-btn" @click="removeClassification(index)" v-if="warningForm.classificationSettings.length > 1">
                        删除
                      </el-button>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
              <div class="add-btn-wrapper">
                <el-button type="dashed" @click="addClassification" icon="el-icon-plus">
                  添加分类预警值
                </el-button>
              </div>
            </template>


            <!-- 按商品分组的预警值设置 -->
            <template v-if="warningForm.warningType === 'byGroup'">
              <div v-for="(item, index) in warningForm.groupSettings" :key="index" class="warning-group">
                <div class="warning-group-header">
                  <span>商品分组{{ index + 1 }}预警值</span>
                </div>

                <el-row :gutter="24">
                  <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
                    <el-form-item
                      :prop="'groupSettings.' + index + '.groupId'"
                      :rules="{ required: true, message: '请选择商品分组', trigger: 'change' }"
                    >
                      <el-cascader
                        v-model="item.groupId"
                        :options="cascaderGroupOptions"
                        :props="{ checkStrictly: true, emitPath: false, value: 'id', label: 'name', children: 'children' }"
                        placeholder="请选择商品分组"
                        class="warning-select"
                        filterable
                        clearable
                        @change="() => handleGroupChange(index)"
                      />
                    </el-form-item>
                  </el-col>

                  <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
                    <el-form-item
                      :prop="'groupSettings.' + index + '.warningValue'"
                      :rules="[
                      { required: true, message: '请输入预警值', trigger: 'blur' },
                      { type: 'number', message: '预警值必须为数字', trigger: 'blur' }
                    ]"
                    >
                      <el-input
                        v-model.number="item.warningValue"
                        type="number"
                        placeholder="请输入预警值"
                        class="warning-input"
                        @input="value => handleInput(value, 'groupSettings.' + index + '.warningValue')"
                      >
                      </el-input>
                    </el-form-item>
                  </el-col>

                  <el-col :xs="4" :sm="4" :md="4" :lg="4" :xl="4">
                    <el-form-item>
                      <el-button type="text" class="delete-btn" @click="removeGroup(index)" v-if="warningForm.groupSettings.length > 1">
                        删除
                      </el-button>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
              <div class="add-btn-wrapper">
                <el-button type="dashed" @click="addGroup" icon="el-icon-plus">
                  添加分组预警值
                </el-button>
              </div>
            </template>

          <!-- 保存按钮 -->
          <el-form-item>
            <el-button
              type="primary"
              @click="handleSave"
            >
              保存设置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script>
  import { getClassificationList, getGroupList, getStockWarning, updateStockWarning } from '@/api/shop/stockWarning'
  import createSpersTemplate from '@/views/shop/goodsSet/specsTemplate/createSpersTemplate.vue'


export default {
  name: 'StockWarning',
  components: { createSpersTemplate },
  data() {
    return {
      // 表单数据
      warningForm: {
        warningType: 'byCategory',
        physicalWarningValue: '',
        cardWarningValue: '',
        virtualWarningValue: '',
        // 按商品分类的预警值设置
        classificationSettings: [{
          classificationId: '',
          warningValue: ''
        }],
        // 按商品分组的预警值设置
        groupSettings: [{
          groupId: '',
          warningValue: ''
        }]
      },
      loading: true,

      // 商品分类列表
      classificationList: [],

      // 商品分组列表
      groupList: [],

      // 表单验证规则
      warningRules: {
        warningType: [
          { required: true, message: '请选择预警方式', trigger: 'change' }
        ],
        physicalWarningValue: [
          { required: true, message: '请输入实物商品库存预警值', trigger: 'blur' },
          { pattern: /^\d+$/, message: '预警值必须为正整数', trigger: 'blur' }
        ],
        cardWarningValue: [
          { required: true, message: '请输入卡券商品库存预警值', trigger: 'blur' },
          { pattern: /^\d+$/, message: '预警值必须为正整数', trigger: 'blur' }
        ],
        virtualWarningValue: [
          { required: true, message: '请输入虚拟商品库存预警值', trigger: 'blur' },
          { pattern: /^\d+$/, message: '预警值必须为正整数', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.loading = true
    Promise.all([
      this.fetchWarningSettings(),
      this.getClassificationList()
    ]).finally(() => {
      this.loading = false
    })
  },
  methods: {

    // 获取预警设置
    async fetchWarningSettings() {
      try {
        const res = await getStockWarning({})
        if (res.code === 200) {
          const { 
            method, // 预警方式：1 按商品类型 2 按商品分类 3 按商品分组
            physical, // 实物商品库存预警值
            coupon,  // 卡券商品库存预警值
            virtual, // 虚拟商品库存预警值
            detail   // method = 2 才会有该下方的数据
          } = res.data

          // 根据method设置warningType
          let warningType = 'byCategory'
          if (method === 2) {
            warningType = 'byClassification'
          } else if (method === 3) {
            warningType = 'byGroup'
          }

          this.warningForm = {
            warningType,
            physicalWarningValue: physical || '',
            cardWarningValue: coupon || '',
            virtualWarningValue: virtual || '',
            classificationSettings: [],
            groupSettings: []
          }

          // 处理分类或分组的预警设置
          if (method === 2 && detail && Array.isArray(detail)) {
            this.warningForm.classificationSettings = detail.map(item => ({
              classificationId: item.cate_id,
              warningValue: item.value
            }))
          } else if (method === 3 && detail && Array.isArray(detail)) {
            this.warningForm.groupSettings = detail.map(item => ({
              groupId: item.cate_id,
              warningValue: item.value
            }))
          }

          // 确保至少有一个设置项
          if (this.warningForm.classificationSettings.length === 0) {
            this.warningForm.classificationSettings = [{
              classificationId: '',
              warningValue: ''
            }]
          }
          if (this.warningForm.groupSettings.length === 0) {
            this.warningForm.groupSettings = [{
              groupId: '',
              warningValue: ''
            }]
          }
        }
      } catch (error) {
        console.error('获取库存预警设置失败:', error)
        this.$message.error('获取库存预警设置失败')
      }
    },

    // 获取分组列表
    async getGroupList() {
      try {
        const res = await getGroupList({})
        if (res.code === 200) {
          this.groupList = res.data
        }
      } catch (error) {
        console.error('获取分组列表失败:', error)
        this.$message.error('获取分组列表失败')
      }
    },

    // 获取分类列表
    async getClassificationList() {
      try {
        const res = await getClassificationList({type:"list"})
        if (res.code === 200) {
          this.classificationList = res.data
        }
      } catch (error) {
        console.error('获取分类列表失败:', error)
        this.$message.error('获取分类列表失败')
      }
    },



    // 处理预警方式变更
    handleWarningTypeChange(value) {
      // 重置表单
      this.$refs.warningForm.clearValidate()
    },

    // 添加商品分类预警设置
    addClassification() {
      this.warningForm.classificationSettings.push({
        classificationId: '',
        warningValue: ''
      })
    },

    // 移除商品分类预警设置
    removeClassification(index) {
      this.warningForm.classificationSettings.splice(index, 1)
    },


    // 添加商品分组预警设置
    addGroup() {
      this.warningForm.groupSettings.push({
        groupId: '',
        warningValue: ''
      })
    },

    // 移除商品分组预警设置
    removeGroup(index) {
      this.warningForm.groupSettings.splice(index, 1)
    },


    // 处理输入，确保值大于0
    handleInput(value, field) {
      if (value < 0) {
        this.$message.warning('预警值必须大于0')
        // 不再自动赋值为1
      }
    },

    // 保存设置
    handleSave() {
      this.$refs.warningForm.validate(async (valid) => {
        if (valid) {
          // 验证所有预警值是否大于0
          let isValid = true

          if (this.warningForm.warningType === 'byCategory') {
            const values = ['physicalWarningValue', 'cardWarningValue', 'virtualWarningValue']
            isValid = values.every(field => this.warningForm[field] > 0)
          } else if (this.warningForm.warningType === 'byClassification') {
            isValid = this.warningForm.classificationSettings.every(item => item.warningValue > 0)
          } else if (this.warningForm.warningType === 'byGroup') {
            isValid = this.warningForm.groupSettings.every(item => item.warningValue > 0)
          }

          if (!isValid) {
            return this.$message.warning('所有预警值必须大于0')
          }

          try {
            // 构造保存的数据结构
            const saveData = {
              method: this.warningForm.warningType === 'byCategory' ? 1 
                : this.warningForm.warningType === 'byClassification' ? 2 
                : 3,
              physical: this.warningForm.physicalWarningValue,
              coupon: this.warningForm.cardWarningValue,
              virtual: this.warningForm.virtualWarningValue,
              detail: []
            }

            // 新增逻辑：method=2时，physical、coupon、virtual都设为空
            if (saveData.method === 2) {
              saveData.physical = null
              saveData.coupon = null
              saveData.virtual = null
            }

            // 根据不同预警方式处理detail数据
            if (this.warningForm.warningType === 'byClassification') {
              saveData.detail = this.warningForm.classificationSettings.map(item => ({
                cate_id: item.classificationId,
                value: item.warningValue
              }))
            } else if (this.warningForm.warningType === 'byGroup') {
              saveData.detail = this.warningForm.groupSettings.map(item => ({
                cate_id: item.groupId,
                value: item.warningValue
              }))
            }

            const res = await updateStockWarning(saveData)
            if (res.code === 200) {
              this.$message.success('保存成功')
            }
          } catch (error) {
            console.error('保存库存预警设置失败:', error)
            this.$message.error('保存失败')
          }
        }
      })
    },

    handleClassificationChange(index) {
      // 自动关闭下拉框由 el-cascader 默认行为实现
      // 互斥已由 disabled 实现
    },

    handleGroupChange(index) {
      // 自动关闭下拉框由 el-cascader 默认行为实现
      // 互斥已由 disabled 实现
    },

    getDisabledTree(list, selectedIds, currentId) {
      return (list || []).map(item => {
        const disabled = selectedIds.includes(item.id) && item.id !== currentId;
        const children = item.children ? this.getDisabledTree(item.children, selectedIds, currentId) : undefined;
        return { ...item, disabled, children };
      });
    }
  },
  computed: {
    selectedClassificationIds() {
      return this.warningForm.classificationSettings.map(item => item.classificationId).filter(Boolean)
    },
    selectedGroupIds() {
      return this.warningForm.groupSettings.map(item => item.groupId).filter(Boolean)
    },
    normalizedClassificationList() {
      // 递归转换 cate_id -> id, name, children
      const mapTree = (list) => (list || []).map(item => ({
        id: item.cate_id,
        name: item.name,
        children: item.children ? mapTree(item.children) : undefined
      }))
      return mapTree(this.classificationList)
    },
    normalizedGroupList() {
      // 如果 groupList 结构同分类，做同样处理，否则保持原样
      const mapTree = (list) => (list || []).map(item => ({
        id: item.cate_id || item.id,
        name: item.name,
        children: item.children ? mapTree(item.children) : undefined
      }))
      return mapTree(this.groupList)
    },
    cascaderClassificationOptions() {
      return this.getDisabledTree(this.normalizedClassificationList, this.selectedClassificationIds, '');
    },
    cascaderGroupOptions() {
      return this.getDisabledTree(this.normalizedGroupList, this.selectedGroupIds, '');
    }
  }
}
</script>

<style lang="scss" scoped>
/* 页面容器样式 */
.store-model {
  min-height: calc(100vh - 120px);
  background-color: #fff;
}

.stock-warning-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  min-height: calc(100vh - 200px); /* 确保容器至少占满视口高度 */

  .warning-tip {
    display: flex;
    align-items: flex-start;
    padding: 16px 20px;
    margin-bottom: 24px;
    background-color: #ecf5ff;
    border-radius: 4px;
    
    i {
      font-size: 20px;
      color: #409eff;
      margin-right: 12px;
      margin-top: 2px;
    }

    .tip-content {
      flex: 1;
      
      h4 {
        margin: 0 0 8px;
        font-size: 16px;
        color: #303133;
        font-weight: 500;
      }

      p {
        margin: 0;
        font-size: 14px;
        color: #606266;
        line-height: 1.6;
      }
    }
  }

  .warning-form {
    max-width: 600px;
    margin-top: 10px;

    .warning-select-h {
      width: 76.5%;
    }

    .warning-select {
      width: 100%;
    }

    .warning-input {
      width: 76.5%;

      ::v-deep .el-input-group__append {
        padding: 0 15px;
      }

      ::v-deep .el-input__inner {
        padding-right: 10px;
      }
    }

    .question-icon {
      width: 14px;
      height: 14px;
      cursor: pointer;
      margin-left: 6px;
      margin-bottom: 2px;
      flex-shrink: 0;
      user-select: none;
      vertical-align: middle;
      display: inline-block;
      object-fit: contain;
      border: none;
      outline: none;
      background: transparent;
    }

    .class-question-icon {
      width: 14px;
      height: 14px;
      cursor: pointer;
      margin-left: 3px;
      margin-bottom: 2px;
      flex-shrink: 0;
      user-select: none;
      vertical-align: middle;
      display: inline-block;
      object-fit: contain;
      border: none;
      outline: none;
      background: transparent;
    }

    .el-form-item:last-child {
      margin-top: 10px;
      margin-bottom: 0;
    }
  }

  .warning-group {
    margin-bottom: 1px;
    padding-top: 20px;
    padding-bottom: 20px;
    border: 0px solid #EBEEF5;
    border-radius: 4px;

    .warning-group-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        font-size: 14px;
        color: #303133;
        font-weight: 500;
      }

    }
    .delete-btn {
      font-size: 15px;
      color: #F56C6C;
      padding: 0;
      margin-top: 8px;
      height: auto;
      line-height: 1;
    }
    .el-form-item {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  .add-btn-wrapper {
    margin-bottom: 24px;

    .el-button {
      width: 76.5%;
      border-style: dashed;
    }
  }
}
.el-button--small {
  margin-top: 10px;
}

</style>