<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-03-29 14:29:20
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-26 11:29:56
 * @FilePath: /qst-merchant-admin-2.0/src/views/shop/goodsSet/goodsGrouping/index.vue
 * @Description: 商品分组
-->
<template>
  <div>
    <el-card class="store-model">
      <base-form :tableForm="tableForm" :formArray="formArray" @searchForm="searchForm"></base-form>
      <div class="operate">
        <el-button @click="groupFn('add')" type="primary" icon="el-icon-plus">新建</el-button>
      </div>
      <base-table
        :tableForm="tableForm"
        :tableColumn="tableColumn"
        :tableRequest="goodsGroupListApi"
        ref="baseTable"
      >
        <template #operate="{scope}">
          <div class="public-operate-btn">
            <el-button type="text" @click="groupFn('edit', scope.row)">修改</el-button>
            <el-button type="text" @click="associationShopFn(scope.row)">关联商品</el-button>
            <el-button type="text" @click="delGroup(scope.row)">删除</el-button>
          </div>
        </template>
      </base-table>
    </el-card>

    <addGroup ref="addGroup" @updateGroupList="updateGroupList"></addGroup>
    <association-shop ref="associationShop" @updateGroupList="updateGroupList"></association-shop>
  </div>
</template>

<script>
import addGroup from './components/addGroup.vue'
import associationShop from './components/associationShop.vue'
import { goodsGroupListApi, goodsGroupDeleteApi } from '@/api/shop/goodsSet/goodsGrouping'

export default {
  name: 'shopGrouping',
  components: {
    addGroup,
    associationShop,
  },
  data() {
    return {
      goodsGroupListApi,
      formArray: [
        {
          key: 'name',
          label: '分组名称',
          type: 'input',
          placeholder: '请输入分组名称',
        },
      ],
      tableForm: {},
      tableColumn: [
        {
          label: '分组名称',
          prop: 'name',
        },
        {
          label: '描述',
          prop: 'desc',
        },
        {
          label: '商品数量',
          prop: 'goods_group_num',
        },
        {
          label: '创建时间',
          prop: 'create_time',
        },
        {
          label: '操作',
          prop: 'operate',
          type: 'customize',
          width: '200px',
        },
      ],
      shop_id: 0,
    }
  },
  created() {},
  methods: {
    // 关联商品事件处理函数
    associationShopFn(row) {
      this.$refs.associationShop.open(this.shop_id, row.id)
    },
    // 新建分组事件处理函数
    groupFn(type, row) {
      this.$refs.addGroup.open(type, this.shop_id, row)
    },

    // 删除分组事件处理函数
    delGroup(row) {
      this.$confirm('删除后，该商品分组下的商品自动取消该分组，确认删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          goodsGroupDeleteApi({
            group_id: row.id,
            shop_id: this.shop_id,
          }).then((res) => {
            if (res.code === 200) {
              this.$message({
                type: 'success',
                message: '删除成功!',
              })
              this.$refs.baseTable.tableRequestFn()
            }
          })
        })
        .catch(() => {})
    },
    // 表单搜索事件
    searchForm(form) {
      this.tableForm = Object.assign({ shop_id: this.shop_id }, this.tableForm, form)
    },

    updateGroupList() {
      this.$refs.baseTable.tableRequestFn()
    },
  },
}
</script>

<style lang="scss" scoped>
.operate {
  margin-top: 20px;
}
</style>