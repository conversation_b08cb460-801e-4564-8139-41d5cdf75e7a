<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-04-02 09:23:34
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-24 17:19:41
 * @FilePath: /qst-merchant-admin-2.0/src/views/shop/goodsSet/goodsGrouping/components/associationShop.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="association-shop">
    <el-dialog
      title="关联商品"
      :visible.sync="isAssociation"
      width="850px"
      :close-on-click-modal="false"
    >
      <div class="dialog-scroll">
        <div>
          <div class="associa-title">未关联商品</div>
          <association-shop-tem
            v-if="isAssociation"
            ref="noSelect"
            :parseFrom="{
            shop_id: shop_id,
            group_id: group_id,
            select_goods_id: selectGoodsId,
            remove_goods_id: removeGoodsId
          }"
            :associaType="1"
            :api="goodsGroupNotRelationGoodsApi"
            @selectiKey="selectiKey"
          ></association-shop-tem>
        </div>
        <div class="flex-c m20">
          <el-button @click="addGoods" type="primary">添加到已关联商品</el-button>
        </div>
        <div>
          <div class="associa-title">已关联商品</div>
          <association-shop-tem
            v-if="isAssociation"
            :parseFrom="{
            shop_id: shop_id,
            group_id: group_id,
            select_goods_id: selectGoodsId,
            remove_goods_id: removeGoodsId
          }"
            :associaType="2"
            :api="goodsGroupRelationGoodsApi"
            @selectiKey="selectiKey"
            @remove="removeGoods"
          ></association-shop-tem>
        </div>
      </div>

      <base-dialog-footer @confirm="saveAddGroup" @cancel="isAssociation=false"></base-dialog-footer>
    </el-dialog>
  </div>
</template>

<script>
import { goodsGroupNotRelationGoodsApi, goodsGroupRelationGoodsApi, goodsGroupAddGoodsApi } from '@/api/shop/goodsSet/goodsGrouping'
import associationShopTem from './associationShopTem.vue'
export default {
  name: 'Dialog',
  components: {
    associationShopTem,
  },
  data() {
    return {
      goodsGroupNotRelationGoodsApi,
      goodsGroupRelationGoodsApi,
      isAssociation: false,
      shop_id: '',
      group_id: '',

      // 已选列表 未选列表
      selectList: [],
      noSelectList: [],

      noSelectListDefault: [],
      // 已选商品id
      selectGoodsId: [],

      // 移除商品id
      removeGoodsId: [],
    }
  },
  methods: {
    // 打开弹窗
    open(shop_id, group_id) {
      this.shop_id = shop_id
      this.group_id = group_id
      this.selectList = []
      this.noSelectList = []
      this.selectGoodsId = []
      this.removeGoodsId = []
      this.isAssociation = true
    },

    // 保存添加分组
    saveAddGroup() {
      goodsGroupAddGoodsApi({
        shop_id: this.shop_id,
        group_id: this.group_id,
        goods_ids: this.selectGoodsId,
        remove_goods_ids: this.removeGoodsId,
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success('保存成功')
          this.isAssociation = false
          this.$emit('updateGroupList')
        }
      })
    },

    // 选择商品回调
    selectiKey(list, key) {
      key == 2 ? (this.selectList = list) : (this.noSelectList = list)
      if (key == 1 && this.noSelectListDefault && list.length == 0) {
        this.noSelectList = this.noSelectListDefault
        this.noSelectListDefault = []
      }
    },

    // 添加到已关联商品
    addGoods() {
      if (this.noSelectList.length == 0) return this.$message.warning('请选择商品')
      this.selectGoodsId = this.selectGoodsId.concat(this.noSelectList.map((item) => item.id))
      this.uniqueArray()
      // 因为清空时会触发 selectiKey 导致清空   需要保存下
      this.noSelectListDefault = [].concat(this.noSelectList)
      this.$refs.noSelect.clearSelection()
    },

    // 移除已关联商品
    removeGoods(id) {
      if (this.selectGoodsId.includes(id)) {
        this.selectGoodsId = this.selectGoodsId.filter((item) => {
          return item != id
        })
      } else {
        this.removeGoodsId = this.removeGoodsId.concat(id)
      }
      this.uniqueArray()
    },

    // 过滤数组中重复的元素
    uniqueArray() {
      let selectGoodsId = []
      let removeGoodsId = []
      selectGoodsId = this.selectGoodsId.filter((item) => !this.removeGoodsId.includes(item))
      removeGoodsId = this.removeGoodsId.filter((item) => !this.selectGoodsId.includes(item))
      this.selectGoodsId = selectGoodsId
      this.removeGoodsId = removeGoodsId
    },
  },
}
</script>

<style lang="scss" scoped>
.associa-title {
  font-size: 14px;
  color: #000000;
  line-height: 22px;
  text-align: left;
  margin: 10px 0;
}
.m20 {
  margin-top: 20px;
}
.dialog-scroll {
  max-height: calc(100vh - 40px - 130px);
  overflow: auto;
}
.association-shop ::v-deep {
  .el-dialog__body {
    padding-top: 10px;
  }
  .el-dialog {
    margin-top: 0 !important;
  }
}
</style>