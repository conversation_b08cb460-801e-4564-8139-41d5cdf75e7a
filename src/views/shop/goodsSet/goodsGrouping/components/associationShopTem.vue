<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-04-02 09:27:14
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-26 15:29:10
 * @FilePath: /qst-merchant-admin-2.0/src/views/shop/goodsSet/goodsGrouping/components/associationShopTem.vue
 * @Description: 关联商品子模块
-->
<template>
  <div>
    <base-form
      :labelWidth="'0px'"
      :dialogClass="'associationShopTem'"
      :formArray="formArray"
      @searchForm="searchForm"
    ></base-form>
    <base-table
      v-if="isGroup"
      :tableColumn="associaType == 2 ? tableColumnAdd : tableColumn"
      :tableRequest="api"
      :tableForm="associaType == 2 ? tableFormAdd : tableForm"
      rowKey="id"
      ref="baseTable"
      :isSelect="associaType == 1"
      @selectiKey="selectiKey"
      :tableHeight="'200px'"
    >
      <template #operate="{scope}">
        <el-button type="text" @click="handleDel(scope.row)">移除</el-button>
      </template>
    </base-table>
  </div>
</template>

<script>
import { getGoodsConfigApi, goodsCategoryListApi } from '@/api/shop/goodsManagement.js'

export default {
  name: 'associationShopTem',
  props: {
    // 关联类型 1:关联商品 2：添加商品
    associaType: {
      type: Number,
      default: 1,
    },
    api: {
      type: Function,
      default: () => {},
    },
    parseFrom: {},
  },
  watch: {
    parseFrom: {
      handler(val) {
        if (val) {
          // 使用 Object.assign 方法将 val 的属性复制到 this.tableForm 中，保持 this.tableForm 的原始属性不变
          this.tableForm = Object.assign({}, this.tableForm, val)
          this.tableFormAdd = Object.assign({}, this.tableForm, val)
          this.tableFormInit = JSON.parse(JSON.stringify(val))
          this.isGroup = true
        }
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    this.init()
    this.getCategoryListFn()
    this.$nextTick(() => {
      this.clearSelection()
    })
  },

  data() {
    return {
      formArray: [
        {
          type: 'input',
          label: '',
          placeholder: '商品名称',
          width: '100px',
          key: 'good_name',
        },
        {
          type: 'input',
          label: '',
          placeholder: '商品编码',
          width: '100',
          key: 'goods_code',
        },
        {
          type: 'select',
          placeholder: '商品类别',
          width: '100',
          key: 'goods_type',
          options: [],
        },
        {
          type: 'cascader',
          key: 'search_cate_id',
          placeholder: '请选择商品分类',
          props: {
            label: 'name',
            value: 'cate_id',
            checkStrictly: true,
          },
          options: [],
        },
      ],
      tableForm: {},
      tableFormAdd: {},
      tableFormInit: {},
      tableData: [],
      // 表格配置
      tableColumn: [
        {
          label: '商品名称',
          prop: 'goods_name',
        },
        {
          label: '商品编码',
          prop: 'goods_code',
        },
        {
          label: '商品类型',
          prop: 'goods_type',
        },
        {
          label: '商品分类',
          prop: 'goods_category',
        },
      ],
      tableColumnAdd: [
        {
          label: '商品名称',
          prop: 'goods_name',
        },
        {
          label: '商品编码',
          prop: 'goods_code',
        },
        {
          label: '商品类型',
          prop: 'goods_type',
        },
        {
          label: '商品分类',
          prop: 'goods_category',
        },
        {
          label: '操作',
          prop: 'operate',
          type: 'customize',
        },
      ],
      isGroup: false,
    }
  },
  methods: {
    // 初始化数据
    init() {
      // 获取商品类型
      getGoodsConfigApi({
        is_search: 'Y',
      }).then((res) => {
        if (res.code == 200) {
          this.goodsConfig = res.data
          this.$set(this.formArray[2], 'options', [
            {
              label: '全部',
              value: '',
            },
            ...res.data.type_list,
          ])
        }
      })
    },

    // 获取商品分类
    getCategoryListFn() {
      goodsCategoryListApi({
        page: 1,
        limit: 100,
        shop_id: this.parseFrom.shop_id,
      }).then((res) => {
        if (res.code == 200) {
          this.$set(this.formArray[3], 'options', [
            {
              name: '全部',
              cate_id: '',
            },
            ...res.data.list,
          ])
        }
      })
    },

    // 搜索表单回调
    searchForm(form) {
      let keyForm = this.associaType == 1 ? 'tableForm' : 'tableFormAdd'
      this[keyForm] = Object.assign({}, this.tableFormInit, {
        ...form,
        cate_id: form.search_cate_id && form.search_cate_id.length > 0 ? form.search_cate_id[form.search_cate_id.length - 1] : '',
      })
    },
    // 选择商品回调
    selectiKey(val) {
      if (val) {
        this.selectiKeys = val
        this.$emit('selectiKey', val, this.associaType)
      }
    },
    // 移除
    handleDel(row) {
      console.log(row)
      this.$emit('remove', row.id)
      // this.$refs.baseTable.handleDelete()
    },

    // 清除选择
    clearSelection() {
      this.$refs.baseTable.clearSelection()
    },
  },
}
</script>
