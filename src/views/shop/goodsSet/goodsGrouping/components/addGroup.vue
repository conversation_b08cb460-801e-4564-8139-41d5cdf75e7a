<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-04-02 09:03:56
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-28 16:42:07
 * @FilePath: /qst-merchant-admin-2.0/src/views/shop/goodsSet/goodsGrouping/components/addGroup.vue
 * @Description: 添加分组的弹窗
-->
<template>
  <div>
    <el-dialog
      :title="type == 'add' ? '添加分组' : '修改分组'"
      :visible.sync="isAddGroup"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form :model="form" label-position="top" ref="addGroupRef" :rules="addGroupRules">
        <el-form-item label="分组名称(最多可输入10个字)" prop="name">
          <el-input class="w100" :maxlength="10" v-model="form.name" placeholder="请输入分组名称"></el-input>
        </el-form-item>
        <el-form-item label="描述">
          <el-input class="w100" v-model="form.desc" type="textarea" placeholder="请输入分组描述"></el-input>
        </el-form-item>
      </el-form>
      <base-dialog-footer @confirm="saveAddGroup" @cancel="isAddGroup=false"></base-dialog-footer>
    </el-dialog>
  </div>
</template>

<script>
import { goodsGroupCreateApi, goodsGroupUpdateApi } from '@/api/shop/goodsSet/goodsGrouping'
export default {
  name: 'addGroup',
  data() {
    return {
      type: 'add',
      form: {
        name: '',
        desc: '',
      },
      isAddGroup: false,
      addGroupRules: {
        name: [
          {
            required: true,
            message: '请输入分组名称',
            trigger: 'blur',
          },
        ],
      },
      shop_id: '',
      id: '',
    }
  },
  methods: {
    open(type, shop_id, row) {
      this.shop_id = shop_id
      this.type = type
      this.isAddGroup = true
      this.form = {
        name: row?.name || '',
        desc: row?.desc || '',
      }
      this.id = row?.id
      console.log(row)
    },
    // 保存添加分组信息
    saveAddGroup() {
      this.$refs.addGroupRef.validate((valid) => {
        if (valid) {
          let api = this.id ? goodsGroupUpdateApi : goodsGroupCreateApi
          let params = this.id ? { group_id: this.id } : {}
          api({
            ...this.form,
            ...params,
            shop_id: this.shop_id,
          }).then((res) => {
            if (res.code === 200) {
              this.$emit('updateGroupList', res.data.id)
              this.isAddGroup = false
              if (this.id) {
                this.$message.success('修改成功')
              } else {
                this.$message.success('保存成功')
              }
            }
          })
        } else {
          return false
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
html body .el-dialog__body .el-form,
html body .el-message-box__body .el-form {
  padding-right: 0;
}
</style>