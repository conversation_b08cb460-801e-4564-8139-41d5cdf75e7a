<template>
  <div>
    <el-card>
      <!-- 页面标题 -->
      <div class="page-header">
        <div class="page-title-container">
          <div class="page-title">
            系统通知
            <span class="title-suffix">共 {{ totalCount }} 条通知，{{ unreadCount }} 条未读</span>
          </div>
        </div>
      </div>

      <!-- 搜索表单 -->
      <base-form
        ref="baseForm"
        :tableForm="tableForm"
        :formArray="formArray"
        @searchForm="searchForm"
      ></base-form>
    </el-card>

    <el-card>
      <!-- 刷新按钮 -->
      <div class="operate flex">
        <el-button
          @click="refreshData"
          type="primary"
          icon="el-icon-refresh"
          class="refresh-btn"
        >
          刷新
        </el-button>
      </div>

      <!-- 表格 -->
      <base-table
        :tableColumn="tableColumn"
        :tableRequest="getSystemNotificationList"
        :tableForm="tableForm"
        ref="baseTable"
      >
        <!-- 标题列 -->
        <template #title="{ scope }">
          <div class="title-cell" @click="viewDetail(scope.row)" style="cursor: pointer;">
            <span :class="{ 'unread-title': isUnread(scope.row.is_read) }">
              {{ scope.row.title }}
            </span>
            <el-tag v-if="isUnread(scope.row.is_read)" type="danger" size="mini" style="margin-left: 8px;">
              未读
            </el-tag>
          </div>
        </template>

        <!-- 类型列 -->
        <template #type="{ scope }">
          <el-tag
            :style="getTypeTagStyle(scope.row.type_text)"
            size="small"
          >
            {{ scope.row.type_text }}
          </el-tag>
        </template>

        <!-- 重要等级列 -->
        <template #level="{ scope }">
          <el-tag
            :style="getLevelTagStyle(scope.row.level)"
            size="small"
          >
            {{ scope.row.level_text }}
          </el-tag>
        </template>

        <!-- 状态列 -->
        <template #is_read="{ scope }">
          <el-tag :class="getStatusTagClass(scope.row.is_read_text)" size="small">
            {{ scope.row.is_read_text }}
          </el-tag>
        </template>

        <!-- 操作列 -->
        <template #operate="{ scope }">
          <div class="public-operate-btn">
            <el-button size="mini" type="text" @click="viewDetail(scope.row)">
              查看
            </el-button>
          </div>
        </template>
      </base-table>

    </el-card>

    <!-- 通知详情弹窗 -->
    <SystemDetails
      :visible.sync="detailDialogVisible"
      :notification-data="currentNotification"
      @close="handleCloseDetail"
      @closed="handleDetailClosed"
    />
  </div>
</template>

<script>
import BaseForm from '@/components/Public/BaseForm.vue'
import BaseTable from '@/components/Public/BaseTable.vue'
import SystemDetails from '@/components/SystemDetails/index.vue'
// 引入消息相关API - 接口位置预留
import {
  noticeList,
  noticeDetail,
  markNoticeRead,
  filterNotices,
  countNotices
} from '@/api/shop/message.js'

export default {
  name: 'SystemNotification',
  components: {
    BaseForm,
    BaseTable,
    SystemDetails
  },
  data() {
    return {
      // 表单数据
      tableForm: {
        title: '',
        type: '',
        level: '',
        is_read: '',
        start: '',
        end: '',
        page: 1,
        limit: 10
      },

      // 弹窗相关数据
      detailDialogVisible: false,
      currentNotification: null,

      // 搜索表单配置
      formArray: [
        {
          label: '通知标题',
          type: 'input',
          key: 'title',
          placeholder: '请输入通知标题搜索'
        },
        {
          label: '通知类型',
          type: 'select',
          key: 'type',
          placeholder: '全部',
          options: [
            { label: '全部', value: '' }
          ]
        },
        {
          label: '重要等级',
          type: 'select',
          key: 'level',
          placeholder: '全部',
          options: [
            { label: '全部', value: '' }
          ]
        },
        {
          label: '读取状态',
          type: 'select',
          key: 'is_read',
          placeholder: '全部',
          options: [
            { label: '全部', value: '' }
          ]
        },
        {
          label: '通知时间',
          type: 'time',
          key: 'notification_time',
          timeKey: ['start', 'end'],
          timeType: 'datetimerange',
          valueFormat: 'yyyy-MM-dd HH:mm:ss'
        }
      ],

      // 表格列配置
      tableColumn: [
        {
          label: '标题',
          prop: 'title',
          type: 'customize',
          minWidth: '250px'
        },
        {
          label: '类型',
          prop: 'type',
          type: 'customize',
          width: '200px'
        },
        {
          label: '重要等级',
          prop: 'level',
          type: 'customize',
          width: '200px'
        },
        {
          label: '状态',
          prop: 'is_read',
          type: 'customize',
          width: '200px'
        },
        {
          label: '通知时间',
          prop: 'created_at',
          width: '200px'
        },
        {
          label: '操作',
          prop: 'operate',
          type: 'customize',
          width: '80px',
          fixed: 'right'
        }
      ],

      // 未读消息数量
      unreadCount: 0,

      // 总通知数量
      totalCount: 0,
    }
  },

  async mounted() {
    this.loadUnreadCount()
    await this.loadFilterOptions()

    // 检查是否有消息ID参数，如果有则自动打开对应消息详情
    const messageId = this.$route.query.messageId
    if (messageId) {
      this.openMessageDetail(messageId)
    }
  },

  methods: {
    // 搜索表单回调
    searchForm(form) {
      this.tableForm = Object.assign({}, this.tableForm, form)
    },

    // 加载筛选条件选项
    async loadFilterOptions() {
      try {
        const result = await filterNotices()
        if (result.code === 200 && result.data) {
          // 更新通知类型选项
          const typeOptions = [{ label: '全部', value: '' }]
          if (result.data.type) {
            typeOptions.push(...result.data.type)
          }

          // 更新重要等级选项
          const levelOptions = [{ label: '全部', value: '' }]
          if (result.data.level) {
            levelOptions.push(...result.data.level)
          }

          // 更新读取状态选项
          const readOptions = [{ label: '全部', value: '' }]
          if (result.data.read) {
            readOptions.push(...result.data.read)
          }

          // 更新formArray中的options
          this.formArray.forEach(item => {
            if (item.key === 'type') {
              item.options = typeOptions
            } else if (item.key === 'level') {
              item.options = levelOptions
            } else if (item.key === 'is_read') {
              item.options = readOptions
            }
          })
        }
      } catch (error) {
        console.error('加载筛选条件失败:', error)
      }
    },

    // 加载统计数据
    async loadUnreadCount() {
      try {
        // 调用获取统计数据接口
        const result = await countNotices()

        if (result.code === 200 && result.data) {
          this.unreadCount = parseInt(result.data.unread_count) || 0
          this.totalCount = parseInt(result.data.total_count) || 0
        } else {
          console.error('获取统计数据失败:', result.msg)
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },

    // 刷新数据
    refreshData() {
      this.$refs.baseTable.tableRequestFn(true)
      this.loadUnreadCount()
      this.$message.success('刷新成功')
    },

    // 获取系统通知列表
    async getSystemNotificationList(params) {
      try {
        const result = await noticeList(params)
        return result
      } catch (error) {
        console.error('获取通知列表失败:', error)
        throw error
      }
    },

    // 获取类型标签样式（内联样式方式）
    getTypeTagStyle(typeText) {
      // 根据接口返回的文本设置样式
      if (typeText === '系统') {
        return {
          color: '#1890FF',
          backgroundColor: '#E6F7FF',
          border: '1px solid #91D5FF'
        }
      } else if (typeText === '平台商品') {
        return {
          color: '#FA8C16',
          backgroundColor: '#FFF7E6',
          border: '1px solid #FFD591'
        }
      } else if (typeText === '卡券到期') {
        return {
          color: '#722ED1',
          backgroundColor: '#F9F0FF',
          border: '1px solid #D3ADF7'
        }
      } else {
        // 默认样式
        return {
          color: '#666666',
          backgroundColor: '#F5F5F5',
          border: '1px solid #D9D9D9'
        }
      }
    },





    // 获取重要等级标签样式（内联样式方式）
    getLevelTagStyle(level) {
      // 根据汉字设置样式
      if (level === 1) {
        return {
          color: '#FF2727',
          backgroundColor: '#FFF2F2',
          border: '1px solid #FFCCCC'
        }
      } else if (level === 2) {
        return {
          color: '#F6A70E',
          backgroundColor: '#FFFBE6',
          border: '1px solid #FFE4AF'
        }
      } else {
        // 普通或其他
        return {
          color: '#A8ABB2',
          backgroundColor: '#F5F7FA',
          border: '1px solid #E9E9EB'
        }
      }
    },





    // 判断是否未读
    isUnread(isRead) {
      // 兼容多种数据格式：'N', 0, false, '0'
      return isRead === 'N' || isRead === 0 || isRead === false || isRead === '0'
    },

    // 获取状态标签样式类
    getStatusTagClass(isReadText) {
      // 根据接口返回的文本设置样式类
      return isReadText === '未读' ? 'status-unread' : 'status-read'
    },



    // 查看详情
    async viewDetail(row) {
      console.log('=== 点击查看详情 ===')
      console.log('行数据:', row)

      try {
        // 检查数据字段
        const noticeId = row.notice_id || row.id
        console.log('通知ID:', noticeId)

        if (!noticeId) {
          // 静默处理无法获取通知ID的情况
          console.log('无法获取通知ID，可用字段:', Object.keys(row))
          return
        }

        // 调用详情接口获取完整数据
        console.log('调用详情接口，参数:', { notice_id: noticeId })
        const result = await noticeDetail({ notice_id: noticeId })
        console.log('详情接口返回:', result)

        if (result.code === 200 && result.data) {
          // 设置当前通知数据
          this.currentNotification = result.data

          // 显示详情弹窗
          this.detailDialogVisible = true
          console.log('弹窗已显示，detailDialogVisible:', this.detailDialogVisible)

          // 如果是未读状态，自动标记为已读
          if (this.isUnread(result.data.is_read)) {
            console.log('消息未读，自动标记为已读')
            this.markAsRead(row)
          }
        } else {
          this.$message.error(result.msg || '获取通知详情失败')
          console.error('接口返回错误:', result)
        }
      } catch (error) {
        console.error('获取通知详情失败:', error)
        this.$message.error('获取通知详情失败: ' + error.message)
      }
    },



    // 关闭详情弹窗
    handleCloseDetail() {
      this.detailDialogVisible = false
    },

    // 详情弹窗关闭后的回调
    handleDetailClosed() {
      this.currentNotification = null
    },



    // 标记为已读
    async markAsRead(row) {
      try {
        // 调用标记已读接口
        const result = await markNoticeRead({ notice_id: row.notice_id })

        if (result.code === 200) {
          // 重新加载表格数据
          this.$refs.baseTable.tableRequestFn(true)
          this.loadUnreadCount()

          this.$message.success('标记已读成功')
        }
      } catch (error) {
        console.error('标记已读失败:', error)
      }
    },

    // 根据消息ID打开消息详情
    async openMessageDetail(messageId) {
      try {
        // 模拟根据ID查找消息
        const mockMessages = [
          {
            notice_id: 1,
            title: '系统维护通知',
            type: 1,
            level: 1,
            is_read: 'Y',
            created_at: '2024-01-10 14:30:00'
          },
          {
            notice_id: 2,
            title: '新订单提醒',
            type: 2,
            level: 2,
            is_read: 'N',
            created_at: '2024-01-08 10:15:00'
          },
          {
            notice_id: 3,
            title: '促销活动通知',
            type: 3,
            level: 3,
            is_read: 'Y',
            created_at: '2024-01-09 16:45:00'
          }
        ]

        const message = mockMessages.find(msg => msg.notice_id == messageId)
        if (message) {
          this.viewDetail(message)
        } else {
          this.$message.warning('未找到对应的消息')
        }
      } catch (error) {
        console.error('打开消息详情失败:', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-header {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #EBEEF5;

  .page-title-container {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .page-title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    display: flex;
    align-items: center;

    .title-suffix {
      font-size: 14px;
      color: #909399;
      font-weight: normal;
      margin-left: 16px;
    }
  }
}

.operate {
  margin-bottom: 20px;
}

.refresh-btn {
  background-color: white !important;
  border-color: #DCDFE6 !important;
  color: #303133 !important;

  &:hover {
    background-color: #303133 !important;
    color: white !important;
  }
}

.title-cell {
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 14px; // 标题字体大小

  &:hover {
    color: #409EFF;
  }

  .unread-title {
    font-weight: 600;
    color: #303133;
    font-size: 16px; // 未读标题字体大小
  }
}

.public-operate-btn {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
  width: 100%;
  text-align: center;

  .el-button {
    padding: 0;
    margin: 0;
    font-size: 14px !important; // 操作按钮字体大小
  }
}

// 全局样式调整
::v-deep {
  .el-table {
    font-size: 16px !important; // 表格整体字体大小

    .el-table__row {
      height: 60px; // 增加行高

      td {
        padding: 16px 0; // 增加单元格内边距
        font-size: 16px !important; // 表格内容字体大小
      }
    }

    .el-table__header-wrapper {
      .el-table__header {
        th {
          height: 50px; // 增加表头高度
          padding: 12px 0;
          font-size: 16px !important; // 表头字体大小
          font-weight: 600;
        }
      }
    }

    // 确保所有表格内容都是16px
    td, th {
      font-size: 16px !important;
    }

    // 标签样式
    .el-tag {
      font-size: 16px !important;
    }

    // 按钮样式 - 表格内的按钮保持14px
    .el-button {
      font-size: 14px !important;
    }
  }

  .el-tag {
    border: none;
    font-size: 16px !important;

    &.el-tag--small {
      height: 26px;
      line-height: 24px;
      padding: 0 8px;
      font-size: 14px !important;
    }

    // 自定义状态标签样式
    &.status-unread {
      color: #0071FE !important;
      background-color: #ECF5FF !important;
      border: 1px solid #D3E6FF !important;
    }

    &.status-read {
      color: #A8ABB2 !important;
      background-color: #F5F7FA !important;
      border: 1px solid #E9E9EB !important;
    }

    // 保留原有的状态标签样式
    // 其他标签样式已改为内联样式方式，避免全局污染

  }
}



</style>

