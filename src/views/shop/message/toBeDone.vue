<template>
  <div class="todo-config-container">
    <!-- 整体配置卡片 -->
    <div class="config-card">
      <!-- 页面标题和描述 -->
      <div class="page-header">
        <h2 class="page-title">待办配置</h2>
        <p class="page-description">
          配置各类消息的推送开关方式，支持各等级的信息提醒，公众号等级需和对应信息提醒
        </p>
      </div>

      <!-- 分隔线 -->
      <div class="divider"></div>

      <!-- 配置表格 -->
      <div class="config-table">
      <el-table
        :data="todoConfigList"
        v-loading="loading"
        style="width: 100%; text-align: left;"
        class="todo-table left-align-table"
        :header-cell-style="{ color: '#303133', fontWeight: '600', textAlign: 'left', paddingLeft: '16px' }"
        :cell-style="{ textAlign: 'left', paddingLeft: '16px' }"
        header-cell-class-name="custom-header-cell"
      >
        <el-table-column prop="title" label="待办事项" min-width="200" align="left">
          <template slot-scope="scope">
            <div class="category-info">
              <h4 class="category-title">{{ scope.row.title }}</h4>
              <p class="category-desc">{{ scope.row.description }}</p>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="type" label="分类" width="120" align="left">
          <template slot-scope="scope">
            <span :class="getCategoryClass(scope.row.type)" class="category-tag">
              {{ scope.row.typeName }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="push_rule" label="推送规则" min-width="180" align="left">
          <template slot-scope="scope">
            <span class="push-rule">{{ scope.row.pushRule }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="audio_content" label="语音内容" min-width="160" align="left">
          <template slot-scope="scope">
            <div class="voice-content">
              <div class="voice-switch-wrapper">
                <el-switch
                  v-model="scope.row.voiceEnabled"
                  @change="handleVoiceChange(scope.row)"
                />
                <span class="voice-label">启用语音提醒</span>
              </div>
              <p class="voice-hint" v-if="scope.row.voiceHint">{{ scope.row.voiceHint }}</p>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="is_backend" label="商家后台提醒" width="140" align="left">
          <template slot-scope="scope">
            <div class="switch-container">
              <el-switch
                v-model="scope.row.merchantBackend"
                @change="handleMerchantBackendChange(scope.row)"
              />
              <span class="switch-text">{{ scope.row.merchantBackend ? '开启' : '关闭' }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="is_wechat" label="公众号提醒" width="120" align="left">
          <template slot-scope="scope">
            <div class="switch-container">
              <el-switch
                v-model="scope.row.wechatPublic"
                @change="handleWechatPublicChange(scope.row)"
              />
              <span class="switch-text">{{ scope.row.wechatPublic ? '开启' : '关闭' }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="is_sms" label="短信提醒" width="120" align="left">
          <template slot-scope="scope">
            <div class="switch-container">
              <el-switch
                v-model="scope.row.smsNotify"
                @change="handleSmsNotifyChange(scope.row)"
              />
              <span class="switch-text">{{ scope.row.smsNotify ? '开启' : '关闭' }}</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      </div>
    </div>

    <!-- 底部提示信息 -->
    <div class="footer-tips">
      <div class="tip-header">
        <img src="/static/views/layouts/VabNavBar/<EMAIL>" class="tip-icon" alt="提示" />
        <span class="tip-text">
          <strong>提示</strong>
        </span>
      </div>
      <div class="tip-content">
        商家后台提醒：在商家管理后台显示待办事项！公众号提醒：通过微信公众号向商家发送提醒！短信提醒：通过短信向商家手机号发送提醒消！
      </div>
    </div>
  </div>
</template>

<script>
import {
  todoList,
  todoSetting
} from '@/api/shop/message.js'

export default {
  name: 'toBeDone',
  data() {
    return {
      // 待办配置列表数据
      todoConfigList: [],
      // 分页参数
      pagination: {
        page: 1,
        limit: 15,
        totalCount: 0,
        totalPage: 0
      },
      // 加载状态
      loading: false
    }
  },

  async mounted() {
    await this.loadTodoList()
  },
  methods: {
    // 加载待办列表数据
    async loadTodoList() {
      try {
        this.loading = true
        const params = {
          page: this.pagination.page,
          limit: this.pagination.limit
        }

        const result = await todoList(params)

        if (result.code === 200 && result.data) {
          // 更新分页信息
          this.pagination = {
            ...this.pagination,
            ...result.data.pagination
          }

          // 转换数据格式
          this.todoConfigList = result.data.list.map(item => ({
            id: item.todo_id,
            title: item.title,
            description: item.push_rule, // 使用推送规则作为描述
            type: this.getTypeKey(item.type),
            typeName: item.type,
            pushRule: item.push_rule,
            voiceEnabled: item.is_audio === 'Y',
            voiceHint: item.audio_content,
            merchantBackend: item.is_backend === 'Y',
            wechatPublic: item.is_wechat === 'Y',
            smsNotify: item.is_sms === 'Y'
          }))
        }
      } catch (error) {
        console.error('加载待办列表失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 根据类型名称获取类型key
    getTypeKey(typeName) {
      const typeMap = {
        '订单管理': 'order',
        '售后管理': 'service',
        '商品管理': 'goods',
        '客户服务': 'customer',
        '财务管理': 'finance',
        '商家管理': 'merchant',
        '系统管理': 'system'
      }
      return typeMap[typeName] || 'default'
    },

    // 获取分类样式类名
    getCategoryClass(type) {
      const classMap = {
        'order': 'category-order',      // 订单管理
        'service': 'category-service',  // 售后管理
        'goods': 'category-goods',      // 商品管理
        'customer': 'category-customer', // 客户服务
        'finance': 'category-finance',   // 财务管理
        'merchant': 'category-merchant', // 商家管理
        'system': 'category-system'      // 系统管理
      }
      return classMap[type] || 'category-default'
    },

    // 处理语音开关变化
    async handleVoiceChange(row) {
      console.log('语音开关变化：', row.title, row.voiceEnabled)
      await this.saveConfigItem(row, 'is_audio', row.voiceEnabled ? 'Y' : 'N')
      this.$message.success(`${row.title} 语音设置已${row.voiceEnabled ? '开启' : '关闭'}`)
    },

    // 处理商家后台开关变化
    async handleMerchantBackendChange(row) {
      console.log('商家后台开关变化：', row.title, row.merchantBackend)
      await this.saveConfigItem(row, 'is_backend', row.merchantBackend ? 'Y' : 'N')
      this.$message.success(`${row.title} 商家后台提醒已${row.merchantBackend ? '开启' : '关闭'}`)
    },

    // 处理公众号提醒开关变化
    async handleWechatPublicChange(row) {
      console.log('公众号提醒开关变化：', row.title, row.wechatPublic)
      await this.saveConfigItem(row, 'is_wechat', row.wechatPublic ? 'Y' : 'N')
      this.$message.success(`${row.title} 公众号提醒已${row.wechatPublic ? '开启' : '关闭'}`)
    },

    // 处理短信提醒开关变化
    async handleSmsNotifyChange(row) {
      console.log('短信提醒开关变化：', row.title, row.smsNotify)
      await this.saveConfigItem(row, 'is_sms', row.smsNotify ? 'Y' : 'N')
      this.$message.success(`${row.title} 短信提醒已${row.smsNotify ? '开启' : '关闭'}`)
    },

    // 保存单个配置项
    async saveConfigItem(row, field, value) {
      try {
        // 构建当前行的完整配置数据
        const settingData = {
          todo_id: row.id,
          is_audio: row.voiceEnabled ? 'Y' : 'N',
          is_backend: row.merchantBackend ? 'Y' : 'N',
          is_wechat: row.wechatPublic ? 'Y' : 'N',
          is_sms: row.smsNotify ? 'Y' : 'N'
        }

        // 更新当前修改的字段
        settingData[field] = value

        console.log('保存配置项：', settingData)

        // 调用保存配置接口
        const result = await todoSetting({
          setting: [settingData]
        })

        if (result.code === 200) {
          console.log('配置保存成功')
        }

      } catch (error) {
        console.error('保存配置失败:', error)
        // 如果保存失败，恢复原来的状态
        this.loadTodoList()
      }
    }
  }
}
</script>

<style lang="scss">
// 全局样式 - 表头颜色和对齐
.custom-header-cell {
  color: #303133 !important;
  font-weight: 600 !important;
  text-align: left !important;
}

.custom-header-cell .cell {
  color: #303133 !important;
  font-weight: 600 !important;
  text-align: left !important;
}

// 强制表格内容左对齐
.left-align-table {
  text-align: left !important;
}

.left-align-table .el-table__body td {
  text-align: left !important;
  padding-left: 16px !important;
}

.left-align-table .el-table__header th {
  text-align: left !important;
  padding-left: 16px !important;
}

.left-align-table .el-table__header th .cell {
  text-align: left !important;
  padding-left: 0 !important;
}

.left-align-table .el-table__body td .cell {
  text-align: left !important;
  padding-left: 0 !important;
}

// 确保所有内容容器左对齐
.left-align-table .category-info,
.left-align-table .voice-content,
.left-align-table .switch-container {
  text-align: left !important;
  justify-content: flex-start !important;
}
</style>

<style lang="scss" scoped>
.todo-config-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;

  .config-card {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 24px;
  }

  .page-header {
    padding: 32px;
    display: flex;
    align-items: center;
    gap: 24px;

    .page-title {
      font-size: 22px;
      font-weight: 600;
      color: #333;
      margin: 0;
      flex-shrink: 0;
    }

    .page-description {
      font-size: 14px;
      color: #A8ABB2;
      margin: 0;
      line-height: 1.6;
      flex: 1;
    }
  }

  .divider {
    height: 1px;
    background: #e8e8e8;
    margin: 0;
  }

  .config-table {
    padding: 0;

    .todo-table {
      .category-info {
        padding: 8px 0;

        .category-title {
          font-size: 15px;
          font-weight: 600;
          color: #222222;
          margin: 0 0 8px 0;
          line-height: 1.4;
        }

        .category-desc {
          font-size: 13px;
          color: #999;
          margin: 0;
          line-height: 1.5;
        }
      }

      // 分类标签样式
      .category-tag {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        border: 1px solid;
        line-height: 1.2;
      }

      // 订单管理 - 橙色
      .category-order {
        color: #F6A70E;
        background-color: #FFFBE6;
        border-color: #FFE4AF;
      }

      // 售后管理 - 橙红色
      .category-service {
        color: #F96401;
        background-color: #FFF3EB;
        border-color: #FFD3B6;
      }

      // 商品管理 - 绿色
      .category-goods {
        color: #67C23A;
        background-color: #F0F9EB;
        border-color: #B9E2A3;
      }

      // 客户服务 - 青色
      .category-customer {
        color: #0B979D;
        background-color: #DCFEFF;
        border-color: #B3E4E6;
      }

      // 财务管理 - 橙红色
      .category-finance {
        color: #F96401;
        background-color: #FFF3EB;
        border-color: #FFD3B6;
      }

      // 商家管理 - 紫色
      .category-merchant {
        color: #8652F0;
        background-color: #FAEAFF;
        border-color: #DDB6FF;
      }

      // 系统管理 - 蓝色
      .category-system {
        color: #0071FE;
        background-color: #ECF5FF;
        border-color: #D3E6FF;
      }

      // 默认样式
      .category-default {
        color: #666;
        background-color: #f5f7fa;
        border-color: #e9e9eb;
      }

      .push-rule {
        font-size: 13px;
        color: #666;
        line-height: 1.5;
        padding: 8px 0;
      }

      .voice-content {
        padding: 8px 0;

        .voice-switch-wrapper {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;

          .voice-label {
            font-size: 14px;
            color: #222222;
            font-weight: 500;
          }
        }

        .voice-hint {
          font-size: 12px;
          color: #999;
          margin: 8px 0 0 0;
          line-height: 1.4;
        }
      }

      .switch-container {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 8px;

        .switch-text {
          font-size: 13px;
          color: #666;
          min-width: 28px;
        }
      }

      // 表格样式优化
      ::v-deep .el-table__header {
        background-color: #fafafa;

        th {
          background-color: #fafafa !important;
          color: #303133;
          font-weight: 600;
          border-bottom: 1px solid #e8e8e8;
          padding: 20px 0;
          font-size: 14px;
        }
      }

      ::v-deep .el-table__body {
        tr {
          &:hover {
            background-color: #f8f9fa;
          }
        }

        td {
          border-bottom: 1px solid #f0f0f0;
          padding: 24px 12px;
          vertical-align: middle;
        }
      }

      // 开关样式
      ::v-deep .el-switch {
        .el-switch__label {
          display: none; // 隐藏默认的标签，使用自定义的文字
        }

        .el-switch__core {
          width: 40px;
          height: 20px;

          &:after {
            width: 16px;
            height: 16px;
          }
        }
      }
    }
  }

  .footer-tips {
    background: #ffffff;
    border: none;
    border-radius: 8px;
    padding: 20px 24px;
    margin-top: 0;

    .tip-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .tip-icon {
        width: 18px;
        height: 18px;
        margin-right: 8px;
      }

      .tip-text {
        font-size: 15px;
        color: #222222;
        font-weight: 600;
      }
    }

    .tip-content {
      font-size: 14px;
      color: #6E6E7A;
      line-height: 1.6;
      padding-left: 26px; // 与图标对齐 (18px图标 + 8px间距)
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .todo-config-container {
    .config-table {
      overflow-x: auto;
    }
  }
}
</style>