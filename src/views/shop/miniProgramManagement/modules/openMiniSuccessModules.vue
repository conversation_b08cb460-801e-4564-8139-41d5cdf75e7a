
<template>
  <div class>
    <el-card>
      <div class="ui_spane">
        <img class="ui_img" src="../../../../assets/mini/success_20250319.png" alt />
      </div>
      <p class="ui_spane ui_title">操作成功</p>
      <div class="ui_spane">
        <el-button type="primary" @click="miniSuccess">完成注册</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  created() {},
  mounted() {
    let _this = this
    setTimeout(() => {
      _this.miniSuccess()
    }, 10000)
  },
  methods: {
    // 完成注册
    miniSuccess() {
      this.$emit('miniSuccess')
    },
  },
}
</script>

<style lang='scss' scoped>
.ui_spane {
  display: flex;
  justify-content: center;
}
.ui_img {
  display: block;
  width: 72px;
  height: 72px;
}
.ui_title {
  font-size: 24px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 32px;
}
</style>