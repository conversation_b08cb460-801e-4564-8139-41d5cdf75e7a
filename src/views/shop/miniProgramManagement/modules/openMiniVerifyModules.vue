<!--
 * @Author: shang<PERSON>i <EMAIL>
 * @Date: 2025-05-17 11:13:14
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-03 16:13:38
 * @FilePath: \qst-merchant-admin-2.0\src\views\shop\miniProgramManagement\modules\miniVerifyModules.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class>
    <el-card>
      <div class="m10">
        1. 小程序名称校验
        <span class="n_success" v-if="detailInfo.is_name_checked == 'Y'">成功</span>
        <span class="n_error" v-if="detailInfo.is_name_checked == 'N'">失败</span>
      </div>
      <div class="m10" v-if="detailInfo.set_name_status > 0">
        2. 小程序名称设置
        <span class="n_success" v-if="detailInfo.set_name_status == '1'">成功</span>
        <span class="n_error" v-if="detailInfo.set_name_status == '3'">失败</span>
        <div class="error_div flex" v-if="detailInfo.set_name_status == '3'">
          <i class="el-icon-warning" style="color: #ff4d4d"></i>
          <div>{{ detailInfo.name_reason }}</div>
        </div>
        <span class="n_review" v-if="detailInfo.set_name_status == '2'">审核中...</span>
      </div>

      <div class="m10" v-if="name_checked">
        3. 小程序头像设置
        <span class="n_success" v-if="detailInfo.is_head_image == 'Y'">成功</span>
        <span class="n_error" v-if="detailInfo.is_head_image == 'N'">失败</span>
      </div>

      <div class="m10" v-if="name_checked && detailInfo.is_head_image == 'Y'">
        4. 小程序简介设置
        <span class="n_success" v-if="detailInfo.is_modify_signature_set == 'Y'">成功</span>
        <span class="n_error" v-if="detailInfo.is_modify_signature_set == 'N'">失败</span>
      </div>

      <div
        class="m10"
        v-if="
          name_checked &&
          detailInfo.is_head_image == 'Y' &&
          detailInfo.is_modify_signature_set == 'Y'
        "
      >
        5. 小程序类目设置
        <span class="n_success" v-if="detailInfo.set_category_status == '1'">成功</span>
        <span class="n_error" v-if="detailInfo.set_category_status == '3'">失败</span>
        <div class="error_div flex" v-if="detailInfo.set_category_status == '3'">
          <i class="el-icon-warning" style="color: #ff4d4d"></i>
          <div>{{ detailInfo.category_reason }}</div>
        </div>
        <span class="n_review" v-if="detailInfo.set_category_status == '2'">审核中...</span>
      </div>
    </el-card>
  </div>
</template>
<script>
  export default {
    props: {
      detailInfo: {
        type: Object, // 定义数据类型，可以是String, Number, Object等
      },
    },
    computed: {
      name_checked() {
        return this.detailInfo.set_name_status == '1'
      },
      category_status() {
        return this.detailInfo.set_category_status == '1'
      },
    },
    data() {
      return {}
    },
    created() {},
    mounted() {},
    methods: {},
  }
</script>
<style lang="scss" scoped>
  .n_success {
    color: #0071fe;
  }
  .n_error {
    color: red;
  }
  .n_review {
    color: #fe9c41;
  }
  .ui_spane {
    display: flex;
    justify-content: center;
  }
  .m10 + .m10 {
    margin-top: 10px;
  }

  .error_div {
    border-radius: 10px;
    border: 1px solid #ffefef;
    margin-top: 10px;
    align-items: flex-start;
    width: 346px;
    padding: 10px 20px 10px 10px;
    background: #ffefef;
    font-weight: 400;
    font-size: 14px;
    color: #ff4d4d;
    line-height: 20px;
    .el-icon-warning {
      margin-right: 2px;
      margin-top: 2px;
    }
  }
</style>
