<template>
  <div class>
    <el-card>
      <template #header>
        <div class="card-header">
          <div></div>
          <div>企业基本信息</div>
        </div>
      </template>
      <el-steps :active="1">
        <el-step title="代码上传" description="上传小程序代码包，填写版本号和描述信息"></el-step>
        <el-step title="代码审核" description="提交代码审核，等待微信官方审核"></el-step>
        <el-step title="代码发布" description="审核通过后发布代码到线上环境"></el-step>
      </el-steps>
      <el-button type="primary" style="margin-top: 20px;">
        上传代码
        <i class="el-icon-upload el-icon--right"></i>
      </el-button>
      <base-table :tableColumn="tableColumn" :tableRequest="''" :tableForm="{}" ref="tableRef">
        <template #operate="{ scope }">
          <div class="public-operate-btn">
            <el-button size="mini" type="text" @click="openDetail(scope.row)">提交审核</el-button>
            <el-button size="mini" type="text" @click="openDetail(scope.row)">扫码体验</el-button>
            <el-button size="mini" type="text" @click="openDetail(scope.row)">测试审核通过</el-button>
          </div>
        </template>
      </base-table>
    </el-card>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableColumn: [
        {
          label: '版本号',
          prop: 'id',
          width: '100px',
        },
        {
          label: '版本描述',
          prop: 'name',
          width: '150px',
        },
        {
          label: '上传时间',
          prop: 'trade_uid',
        },
        {
          label: '发布时间',
          prop: 'manage_name',
        },
        {
          label: '状态',
          prop: 'contact_phone',
          type: 'customizePhone',
          width: '110px',
        },
        {
          label: '备注',
          prop: 'username',
        },
        {
          label: '状态',
          prop: 'created_at',
          width: '200px',
        },
        {
          label: '操作',
          prop: 'operate',
          type: 'customize',
          width: '150px',
        },
      ],
    }
  },
  created() {},
  mounted() {},
  methods: {},
}
</script>

<style lang='scss' scoped>
</style>