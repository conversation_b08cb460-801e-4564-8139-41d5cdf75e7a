<template>
  <div class>
    <el-card>
      <template #header>
        <div class="card-header">
          <div></div>
          <div>企业基本信息</div>
        </div>
      </template>

      <!-- 当前状态信息 -->
<!--      <div class="current-status-info" style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px;">
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <div>
            <h4 style="margin: 0 0 8px 0; color: #303133;">当前线上版本</h4>
            <p style="margin: 0; color: #606266;">
              版本号：<strong>{{ currentStatus.current.version || '暂无' }}</strong>
              <span v-if="currentStatus.current.publish_time" style="margin-left: 20px;">
                发布时间：{{ currentStatus.current.publish_time }}
              </span>
            </p>
          </div>
          <div style="text-align: right;">
            <el-tag
              :type="currentStatus.is_upload ? 'warning' : 'info'"
              size="medium">
              {{ currentStatus.is_upload ? '有新版本待发布' : '无待发布版本' }}
            </el-tag>
          </div>
        </div>
      </div>

      &lt;!&ndash; 当前审核进度 &ndash;&gt;
      <div style="margin-bottom: 15px;">
        <h4 style="margin: 0 0 10px 0; color: #303133;">
          当前审核进度：{{ currentStatus.step_text }}
        </h4>
      </div>-->

      <el-steps :active="currentStep" v-loading="stepsLoading">
        <el-step
          v-for="step in stepsList"
          :key="step.id"
          :title="step.title"
          :description="step.desc">
        </el-step>
      </el-steps>
      <div style="margin-top: 20px;">
        <el-button type="primary">
          上传代码
          <i class="el-icon-upload el-icon--right"></i>
        </el-button>
        <el-button type="default" @click="refreshTable" style="margin-left: 10px;">
          刷新列表
          <i class="el-icon-refresh el-icon--right"></i>
        </el-button>
      </div>
      <base-table
        :tableColumn="tableColumn"
        :tableRequest="handleTableRequest"
        :tableForm="tableForm"
        ref="tableRef">

        <!-- 状态列自定义显示 -->
        <template #status="{ scope }">
          <el-tag
            :type="getStatusTagType(scope.row.status)"
            size="small">
            {{ scope.row.status_text || getStatusText(scope.row.status) }}
          </el-tag>
        </template>

        <!-- 操作列 -->
        <template #operate="{ scope }">
          <div class="public-operate-btn">
            <el-button
              size="mini"
              type="text"
              @click="submitReview(scope.row)"
              v-if="scope.row.status === 'uploaded'">
              提交审核
            </el-button>
            <el-button
              size="mini"
              type="text"
              @click="previewCode(scope.row)"
              v-if="scope.row.status !== 'draft'">
              扫码体验
            </el-button>
            <el-button
              size="mini"
              type="text"
              @click="testReviewPass(scope.row)"
              v-if="scope.row.status === 'reviewing'">
              测试审核通过
            </el-button>
            <el-button
              size="mini"
              type="text"
              @click="releaseVersion(scope.row)"
              v-if="scope.row.status === 'approved'">
              发布版本
            </el-button>
          </div>
        </template>
      </base-table>
    </el-card>
  </div>
</template>

<script>
import { getWechatSteps,
  getWechatReleaseList as apiGetWechatReleaseList,
  wechatManageCode,
  wechatPrivacyAuditStatus,
  wechatUploadCode
} from '@/api/miniMaageApi.js'

export default {
  data() {
    return {
      // 步骤条相关数据
      stepsList: [],
      currentStep: 1,
      stepsLoading: false,

      // 当前审核状态数据
      currentStatus: {
        current: {
          version: '',
          publish_time: ''
        },
        step: '1',
        step_text: '代码上传',
        is_upload: false
      },

      // 表格查询参数
      tableForm: {
        page: 1,
        limit: 10
      },

      tableColumn: [
        {
          label: '版本号',
          prop: 'version',
          width: '120px',
        },
        {
          label: '版本描述',
          prop: 'user_desc',
          minWidth: '200px',
        },
        {
          label: '上传时间',
          prop: 'created_at',
          width: '180px',
        },
        {
          label: '发布时间',
          prop: 'publish_time',
          width: '180px',
        },
        {
          label: '状态',
          prop: 'status',
          type: 'customize',
          width: '120px',
        },
        {
          label: '备注',
          prop: 'remark',
          minWidth: '150px',
        },
        {
          label: '操作',
          prop: 'operate',
          type: 'customize',
          width: '200px',
          fixed: 'right'
        },
      ],
    }
  },
  created() {
    this.loadInitialData()
  },
  mounted() {
    console.log('📱 codeManageListModules 组件已挂载')
    console.log('🔍 检查组件状态:', {
      tableForm: this.tableForm,
      tableColumn: this.tableColumn.length,
      handleTableRequest: typeof this.handleTableRequest
    })

    // 延迟一点时间确保组件完全挂载
    setTimeout(() => {
      console.log('🔄 延迟刷新表格数据')
      if (this.$refs.tableRef) {
        console.log('✅ 找到表格引用，开始刷新')
        console.log('🔍 表格组件实例:', this.$refs.tableRef)
        this.$refs.tableRef.tableRequestFn(true)
      } else {
        console.log('❌ 未找到表格引用')
      }
    }, 1000)
  },
  methods: {
    // 加载初始数据
    async loadInitialData() {
      try {
        this.stepsLoading = true
        console.log('🔄 开始加载初始数据')

        // 同时加载步骤条和当前状态
        await Promise.all([
          this.loadWechatSteps(),
          this.loadCurrentStatus()
        ])

        console.log('✅ 初始数据加载完成')
      } catch (error) {
        console.error('❌ 初始数据加载失败:', error)
      } finally {
        this.stepsLoading = false
      }
    },

    // 处理表格请求的方法
    async handleTableRequest(params) {
      console.log('🔄 base-table 调用接口，参数:', params)
      try {
        const res = await apiGetWechatReleaseList(params)
        console.log('📋 历史版本列表接口返回:', res)

        // 确保数据格式符合 base-table 组件的要求
        if (res.code === 200 && res.data) {
          // 如果没有 pagination 字段，添加默认值
          if (!res.data.pagination) {
            res.data.pagination = {
              totalCount: res.data.list ? res.data.list.length : 0
            }
          }

          // 如果没有 list 字段，添加空数组
          if (!res.data.list) {
            res.data.list = []
          }

          console.log('✅ 处理后的数据格式:', res)
        }

        return res
      } catch (err) {
        console.error('❌ 历史版本列表接口错误:', err)
        throw err
      }
    },

    // 获取微信小程序步骤条数据
    async loadWechatSteps() {
      try {
        console.log('🔄 获取步骤条数据')
        const result = await getWechatSteps()

        if (result.code === 200 && result.data) {
          this.stepsList = result.data
          console.log('✅ 步骤条数据:', result.data)
        } else {
          console.error('❌ 获取步骤条失败:', result.msg)
          this.$message.error(result.msg || '获取步骤信息失败')
        }
      } catch (error) {
        console.error('❌ 获取步骤条数据失败:', error)
        this.$message.error('获取步骤信息失败')
      }
    },

    // 获取当前审核状态
    async loadCurrentStatus() {
      try {
        console.log('🔄 获取当前审核状态')
        const result = await wechatManageCode({})

        if (result.code === 200 && result.data) {
          this.currentStatus = result.data
          // 根据接口返回的步骤设置当前步骤
          this.currentStep = parseInt(result.data.step) || 1

          console.log('✅ 当前审核状态:', result.data)
          console.log('📍 当前步骤:', this.currentStep)
        } else {
          console.error('❌ 获取当前状态失败:', result.msg)
          this.$message.error(result.msg || '获取当前状态失败')
        }
      } catch (error) {
        console.error('❌ 获取当前状态接口错误:', error)
        this.$message.error('获取当前状态失败')
      }
    },


    // 根据业务逻辑获取当前步骤（可以根据实际需求实现）
    getCurrentStepFromBusiness() {
      // 现在直接使用接口返回的步骤
      return parseInt(this.currentStatus.step) || 1
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        // 数字状态
        1: 'info',      // 已上传
        2: 'warning',   // 审核中
        3: 'success',   // 审核通过
        4: 'danger',    // 审核驳回
        5: 'success',   // 已发布
        // 字符串状态（兼容）
        'draft': 'info',        // 草稿
        'uploaded': 'warning',  // 已上传
        'reviewing': 'primary', // 审核中
        'approved': 'success',  // 审核通过
        'rejected': 'danger',   // 审核拒绝
        'released': 'success'   // 已发布
      }
      return statusMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusTextMap = {
        // 数字状态
        1: '已上传',
        2: '审核中',
        3: '审核通过',
        4: '审核驳回',
        5: '已发布',
        // 字符串状态（兼容）
        'draft': '草稿',
        'uploaded': '已上传',
        'reviewing': '审核中',
        'approved': '审核通过',
        'rejected': '审核拒绝',
        'released': '已发布'
      }
      return statusTextMap[status] || '未知状态'
    },

    // 提交审核
    submitReview(row) {
      this.$confirm('确认提交该版本进行审核？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用提交审核接口
        console.log('提交审核:', row)
        this.$message.success('已提交审核')
        // 刷新表格
        this.$refs.tableRef.tableRequestFn(true)
      }).catch(() => {
        this.$message.info('已取消提交')
      })
    },

    // 扫码体验
    previewCode(row) {
      // 显示二维码预览
      console.log('扫码体验:', row)
      this.$message.info('功能开发中...')
    },

    // 测试审核通过
    testReviewPass(row) {
      this.$confirm('确认测试审核通过该版本？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用测试审核通过接口
        console.log('测试审核通过:', row)
        this.$message.success('测试审核通过')
        // 刷新表格
        this.$refs.tableRef.tableRequestFn(true)
      }).catch(() => {
        this.$message.info('已取消操作')
      })
    },

    // 发布版本
    releaseVersion(row) {
      this.$confirm('确认发布该版本到线上环境？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用发布版本接口
        console.log('发布版本:', row)
        this.$message.success('版本发布成功')
        // 刷新表格
        this.$refs.tableRef.tableRequestFn(true)
      }).catch(() => {
        this.$message.info('已取消发布')
      })
    },

    // 刷新表格数据
    refreshTable() {
      console.log('🔄 手动刷新所有数据')
      // 刷新步骤条和当前状态
      this.loadInitialData()
      // 刷新表格
      if (this.$refs.tableRef) {
        console.log('✅ 找到表格引用，开始刷新')
        this.$refs.tableRef.tableRequestFn(true)
      } else {
        console.log('❌ 未找到表格引用')
      }
    },



    // 打开详情（原有方法）
    openDetail(row) {
      // 原有的详情处理逻辑
      console.log('打开详情:', row)
    }
  },
}
</script>

<style lang='scss' scoped>
.card-header {
  display: flex;
  align-items: center;
  font-weight: 600;
}

.public-operate-btn {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;

  .el-button {
    padding: 0;
    margin: 0;
    font-size: 14px;

    &:hover {
      color: #409EFF;
    }
  }
}

// 状态标签样式
::v-deep .el-tag {
  border: none;

  &.el-tag--info {
    background-color: #F4F4F5;
    color: #909399;
  }

  &.el-tag--warning {
    background-color: #FDF6EC;
    color: #E6A23C;
  }

  &.el-tag--primary {
    background-color: #ECF5FF;
    color: #409EFF;
  }

  &.el-tag--success {
    background-color: #F0F9FF;
    color: #67C23A;
  }

  &.el-tag--danger {
    background-color: #FEF0F0;
    color: #F56C6C;
  }
}

// 表格样式优化
::v-deep .el-table {
  .el-table__row {
    &:hover {
      background-color: #F5F7FA;
    }
  }

  .el-table__cell {
    padding: 12px 0;
  }
}
</style>