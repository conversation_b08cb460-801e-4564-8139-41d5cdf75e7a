<template>
  <div class>
    <el-card>
      <template #header>
        <div class="card-header">
          <div></div>
          <div>企业基本信息</div>
        </div>
      </template>
      <el-steps :active="currentStep" v-loading="stepsLoading">
        <el-step
          v-for="step in stepsList"
          :key="step.id"
          :title="step.title"
          :description="step.desc">
        </el-step>
      </el-steps>
      <el-button type="primary" style="margin-top: 20px;">
        上传代码
        <i class="el-icon-upload el-icon--right"></i>
      </el-button>
      <base-table :tableColumn="tableColumn" :tableRequest="''" :tableForm="{}" ref="tableRef">
        <template #operate="{ scope }">
          <div class="public-operate-btn">
            <el-button size="mini" type="text" @click="openDetail(scope.row)">提交审核</el-button>
            <el-button size="mini" type="text" @click="openDetail(scope.row)">扫码体验</el-button>
            <el-button size="mini" type="text" @click="openDetail(scope.row)">测试审核通过</el-button>
          </div>
        </template>
      </base-table>
    </el-card>
  </div>
</template>

<script>
import { getWechatSteps } from '@/api/miniMaageApi.js'

export default {
  data() {
    return {
      // 步骤条相关数据
      stepsList: [],
      currentStep: 1,
      stepsLoading: false,

      tableColumn: [
        {
          label: '版本号',
          prop: 'id',
          width: '100px',
        },
        {
          label: '版本描述',
          prop: 'name',
          width: '150px',
        },
        {
          label: '上传时间',
          prop: 'trade_uid',
        },
        {
          label: '发布时间',
          prop: 'manage_name',
        },
        {
          label: '状态',
          prop: 'contact_phone',
          type: 'customizePhone',
          width: '110px',
        },
        {
          label: '备注',
          prop: 'username',
        },
        {
          label: '状态',
          prop: 'created_at',
          width: '200px',
        },
        {
          label: '操作',
          prop: 'operate',
          type: 'customize',
          width: '150px',
        },
      ],
    }
  },
  created() {
    this.loadWechatSteps()
  },
  mounted() {},
  methods: {
    // 获取微信小程序步骤条数据
    async loadWechatSteps() {
      try {
        this.stepsLoading = true
        const result = await getWechatSteps()

        if (result.code === 200 && result.data) {
          this.stepsList = result.data
          // 可以根据实际业务逻辑设置当前步骤
          // this.currentStep = this.getCurrentStepFromBusiness()
        } else {
          this.$message.error(result.msg || '获取步骤信息失败')
          // 如果接口失败，使用默认数据
        }
      } catch (error) {
        console.error('获取步骤条数据失败:', error)
        this.$message.error('获取步骤信息失败')
        // 如果接口失败，使用默认数据
      } finally {
        this.stepsLoading = false
      }
    },


    // 根据业务逻辑获取当前步骤（可以根据实际需求实现）
    getCurrentStepFromBusiness() {
      // 这里可以根据实际的业务状态来确定当前步骤
      // 例如：根据小程序的状态、审核状态等
      return 1
    },

    // 打开详情（原有方法）
    openDetail(row) {
      // 原有的详情处理逻辑
      console.log('打开详情:', row)
    }
  },
}
</script>

<style lang='scss' scoped>
</style>