<template>
  <div class>
    <el-card>
      <div class="m10">
        1. 账号管理员人脸核验
        <span class="n_success" v-if="detailInfo.face_status == '1'">成功</span>
        <span class="n_error" v-if="detailInfo.face_status == '2'">失败</span>
        <div class="error_div flex" v-if="detailInfo.face_status == '2'">
          <i class="el-icon-warning" style="color: #FF4D4D;"></i>
          <div>{{detailInfo.face_reason}}</div>
        </div>
        <qrcode
          :ids="1"
          v-if="detailInfo.is_legal_face == 'Y' && detailInfo.face_verify_url"
          :url="detailInfo.face_verify_url"
        ></qrcode>
      </div>

      <div class="m10" v-if="detailInfo.face_status == '1'">
        2. 支付认证费用缴纳
        <span class="n_success" v-if="detailInfo.is_pay_auth == 'Y'">成功</span>
        <qrcode
          :ids="2"
          v-if="detailInfo.is_pay_auth == 'N' && detailInfo.pay_url"
          :url="detailInfo.pay_url"
        ></qrcode>
      </div>

      <div class="m10" v-if="detailInfo.is_auth_filing == 'Y'">3.{{detailInfo.icp_auth_status_text}}</div>
    </el-card>
  </div>
</template>
<script>
import qrcode from '@/components/Qrcode/qrcode.vue'
export default {
  props: {
    detailInfo: {
      type: Object, // 定义数据类型，可以是String, Number, Object等
    },
  },
  components: {
    qrcode,
  },
  computed: {
    name_checked() {
      return this.detailInfo.set_name_status == '1'
    },
    category_status() {
      return this.detailInfo.set_category_status == '1'
    },
  },
  data() {
    return {}
  },
  created() {},
  mounted() {},
  methods: {},
}
</script>
<style lang='scss' scoped>
.n_success {
  color: #0071fe;
}
.n_error {
  color: red;
}
.n_review {
  color: #fe9c41;
}
.ui_spane {
  display: flex;
  justify-content: center;
}
.m10 + .m10 {
  margin-top: 10px;
}

.error_div {
  border-radius: 10px;
  border: 1px solid #ffefef;
  margin-top: 10px;
  align-items: flex-start;
  width: 346px;
  padding: 10px 20px 10px 10px;
  background: #ffefef;
  font-weight: 400;
  font-size: 14px;
  color: #ff4d4d;
  line-height: 20px;
  .el-icon-warning {
    margin-right: 2px;
    margin-top: 2px;
  }
}
</style>