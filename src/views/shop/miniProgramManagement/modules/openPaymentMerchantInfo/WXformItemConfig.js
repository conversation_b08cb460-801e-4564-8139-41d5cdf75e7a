import FromUpload from '@/components/FreeFromComponent/fromUpload.vue'
import fromSelect from '@/components/FreeFromComponent/fromSelect.vue'
// el-form-item 配置项
const WXformItemConfig = [
  {
    title: '基本信息',
    ItemConfig: [
      {
        label: '商户名称',
        prop: 'company_name',
        prop1: '',
        component: 'el-input', // el-input可以省略，默认使用el-input
        placeholder: '请输入商户名称', // placeholder可以省略，默认显示“请输入+label”
        span: 12, // 使用栅格布局
        rules: [{ required: true, message: '请输入商户名称', trigger: 'blur' }],
      },
      {
        label: '商户简称',
        prop: 'company_name1',
        prop1: '',
        component: 'el-input', // el-input可以省略，默认使用el-input
        placeholder: '请输入商户简称', // placeholder可以省略，默认显示“请输入+label”
        span: 12, // 使用栅格布局
        rules: [{ required: true, message: '请输入商户简称', trigger: 'blur' }],
      },
      {
        label: '客服电话',
        prop: 'company_name_phone',
        prop1: '',
        component: 'el-input', // el-input可以省略，默认使用el-input
        placeholder: '将在交易记录中向买家展示，请确保电话畅通以便平台回拨确认', // placeholder可以省略，默认显示“请输入+label”
        span: 12, // 使用栅格布局
        rules: [{ required: true, message: '请输入客服电话', trigger: 'blur' }],
      },
      {
        label: '主体类型',
        prop: 'program_name_type',
        span: 12, // 使用栅格布局
        component: fromSelect, // el-input可以省略，默认使用el-input
        placeholder: '请选择主体类型', // placeholder可以省略，默认显示“请输入+label”
        rules: [{ required: true, message: '请选择主体类型', trigger: 'blur' }],
        clearable: true,
        width: '100%', // 设置宽度
        options: [
          { label: '个体户', value: '1' },
          { label: '企业', value: '2' },
          { label: '事业单位', value: '3' },
          { label: '政府机关', value: '4' },
          { label: '社会组织', value: '5' },
        ],
        onChange: (value) => {
          console.log(value)
        },
      },
      {
        label: '登记证书照片',
        prop: 'business_license_pic',
        prop1: '',
        type: 'upload',
        component: FromUpload, // el-input可以省略，默认使用el-input
        placeholder: '请上传登记证书照片', // placeholder可以省略，默认显示“请输入+label”
        span: 12, // 使用栅格布局
        rules: [{ required: true, message: '请上传登记证书照片', trigger: 'blur' }],
        tip: ` <div>1、照片应正面拍摄、清晰、四角完整、无反光或遮挡；不得翻拍、截图、镜像、PS；</div>
            <div>2、上传彩色照片、彩色扫描件，复印件需加盖公章鲜章；</div>
            <div>3、水印仅限于微信支付业务相关；</div>
          `,
      },
      {
        label: '单位证明函照片',
        prop: 'business_license_pic1',
        prop1: '',
        type: 'upload',
        component: FromUpload, // el-input可以省略，默认使用el-input
        placeholder: '请上传单位证明函照片', // placeholder可以省略，默认显示“请输入+label”
        span: 12, // 使用栅格布局
        rules: [{ required: true, message: '请上传单位证明函照片', trigger: 'blur' }],
        tip: ` <div>1、政府机关、事业单位必须上传单位证明函；</div>
            <div>请参照示例图打印单位证明函，全部信息需打印，不支持手写商户信息，并加盖公章；</div>
            <div style="height: 32px"></div>
          `,
      },
      {
        label: '证书号',
        prop: 'company_name2',
        prop1: '',
        component: 'el-input', // el-input可以省略，默认使用el-input
        placeholder: '请填写营业执照/登记证书的注册地址', // placeholder可以省略，默认显示“请输入+label”
        span: 12, // 使用栅格布局
        rules: [{ required: true, message: '请输入证书号', trigger: 'blur' }],
      },
      {
        label: '登记证书类型',
        prop: 'program_name_type1',
        span: 12, // 使用栅格布局
        component: fromSelect, // el-input可以省略，默认使用el-input
        placeholder: '请选择登记证书类型', // placeholder可以省略，默认显示“请输入+label”
        rules: [{ required: true, message: '请选择登记证书类型', trigger: 'blur' }],
        clearable: true,
        width: '100%', // 设置宽度
        options: [
          {
            label: '事业单位法人证书',
            value: 1,
          },
          {
            label: '政府设立的组织机构代码证',
            value: 2,
          },
          {
            label: '社会团体法人登记证书',
            value: 3,
          },
          {
            label: '基金会法人登记证书',
            value: 4,
          },
          {
            label: '其他证书',
            value: 5,
          },
        ],
        onChange: (value) => {
          console.log(value)
        },
      },
      {
        label: '注册地址',
        prop: 'company_name_address',
        prop1: '',
        component: 'el-input', // el-input可以省略，默认使用el-input
        placeholder: '请填写营业执照/登记证书的注册地址', // placeholder可以省略，默认显示“请输入+label”
        span: 12, // 使用栅格布局
        rules: [{ required: true, message: '请填写营业执照/登记证书的注册地址', trigger: 'blur' }],
      },
      {
        label: '证件有效期',
        prop: 'date',
        component: 'el-date-picker',
        clearable: true,
        type: 'daterange',
        format: 'yyyy.MM.dd',
        valueFormat: 'yyyy.MM.dd',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
        span: 12,
        width: '100%',
      },
      {
        label: '是否金融机构',
        prop: 'program_name_type2',
        span: 24, // 使用栅格布局
        component: fromSelect, // el-input可以省略，默认使用el-input
        placeholder: '请选择是否金融机构', // placeholder可以省略，默认显示“请输入+label”
        rules: [{ required: true, message: '请选择是否金融机构', trigger: 'blur' }],
        clearable: true,
        width: '50%', // 设置宽度
        options: [
          { label: '是', value: '1' },
          { label: '否', value: '2' },
        ],
        onChange: (value) => {
          console.log(value)
        },
      },
    ],
  },
  {
    title: '法人信息',
    ItemConfig: [
      {
        label: '法人证件类型',
        prop: 'program_name_type3',
        span: 24, // 使用栅格布局
        component: fromSelect, // el-input可以省略，默认使用el-input
        placeholder: '请选择法人证件类型', // placeholder可以省略，默认显示“请输入+label”
        rules: [{ required: true, message: '请选择法人证件类型', trigger: 'blur' }],
        clearable: true,
        width: '50%', // 设置宽度
        options: [
          { label: '中国大陆居民-身份证', value: '1' },
          { label: '其他国家或地区居民-护照', value: '2' },
          { label: '中国香港居民-来往内地通行证', value: '3' },
          { label: '中国澳门居民-来往内地通行证', value: '4' },
          { label: '中国台湾居民-来往内地通行证', value: '5' },
          { label: '外国人居留证', value: '6' },
          { label: '港澳居民证', value: '7' },
          { label: '台湾居民证', value: '8' },
        ],
        onChange: (value) => {
          console.log(value)
        },
      },
      {
        label: '法人证件正面照片',
        prop: 'legal_id_card_front_pic',
        prop1: '',
        type: 'upload',
        component: FromUpload, // el-input可以省略，默认使用el-input
        placeholder: '请上传法人证件正面照片', // placeholder可以省略，默认显示“请输入+label”
        span: 12, // 使用栅格布局
        rules: [{ required: true, message: '请上传法人证件正面照片', trigger: 'blur' }],
        tip: `  <div>1、当证件类型为身份证时，请上传个体户经营者/法人的身份证人像面照片；</div>
            <div>2、正面拍摄、清晰、四角完整、无反光或遮挡；不得翻拍、截图、镜像、PS；</div>
            <div>3、请上传彩色照片or彩色扫描件，复印件需加盖公章鲜章，可添加"微信支付"相关水印（如微信支付认证）；</div>
          `,
      },
      {
        label: '法人证件背面照片',
        prop: 'legal_id_card_back_pic',
        prop1: '',
        type: 'upload',
        component: FromUpload, // el-input可以省略，默认使用el-input
        placeholder: '请上传法人证件背面照片', // placeholder可以省略，默认显示“请输入+label”
        span: 12, // 使用栅格布局
        rules: [{ required: true, message: '请上传法人证件背面照片', trigger: 'blur' }],
        tip: `   <div>1、当证件类型为身份证时，请上传个体户经营者/法人的身份证国徽面照片；</div>
            <div>2、正面拍摄、清晰、四角完整、无反光或遮挡；不得翻拍、截图、镜像、PS；</div>
            <div>3、请上传彩色照片or彩色扫描件，复印件需加盖公章鲜章，可添加"微信支付"相关水印（如微信支付认证）；</div>
          `,
      },
      {
        label: '法人姓名',
        prop: 'legal_name',
        prop1: '',
        component: 'el-input', // el-input可以省略，默认使用el-input
        placeholder: '请输入法人姓名', // placeholder可以省略，默认显示“请输入+label”
        span: 12, // 使用栅格布局
        rules: [{ required: true, message: '请输入法人姓名', trigger: 'blur' }],
      },
      {
        label: '法人身份证号码',
        prop: 'legal_id_card_no',
        prop1: '',
        component: 'el-input', // el-input可以省略，默认使用el-input
        placeholder: '请输入法人身份证号码', // placeholder可以省略，默认显示“请输入+label”
        span: 12, // 使用栅格布局
        rules: [{ required: true, message: '请输入法人身份证号码', trigger: 'blur' }],
      },
      {
        label: '证件有效期',
        prop: 'date1',
        component: 'el-date-picker',
        clearable: true,
        type: 'daterange',
        format: 'yyyy.MM.dd',
        valueFormat: 'yyyy.MM.dd',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
        span: 12,
        width: '100%',
      },

      {
        label: '主体类型',
        prop: 'program_name_type',
        span: 12, // 使用栅格布局
        component: fromSelect, // el-input可以省略，默认使用el-input
        placeholder: '请选择主体类型', // placeholder可以省略，默认显示“请输入+label”
        rules: [{ required: true, message: '请选择主体类型', trigger: 'blur' }],
        clearable: true,
        width: '100%', // 设置宽度
        options: [
          { label: '企业', value: '1' },
          { label: '个体商户', value: '2' },
        ],
        onChange: (value) => {
          console.log(value)
        },
      },

      {
        label: '居住地址',
        prop: 'legal_address',
        prop1: '',
        component: 'el-input', // el-input可以省略，默认使用el-input
        placeholder: '建议填写证件上的居住地址', // placeholder可以省略，默认显示“请输入+label”
        span: 12, // 使用栅格布局
        rules: [{ required: true, message: '请输入居住地址', trigger: 'blur' }],
      },
    ],
  },
  {
    title: '超级管理员信息',
    ItemConfig: [
      {
        label: '超级管理员类型',
        prop: 'program_name_type4',
        span: 12, // 使用栅格布局
        component: fromSelect, // el-input可以省略，默认使用el-input
        placeholder: '请选择超级管理员类型', // placeholder可以省略，默认显示“请输入+label”
        rules: [{ required: true, message: '请选择超级管理员类型', trigger: 'blur' }],
        clearable: true,
        width: '100%', // 设置宽度
        options: [
          { label: '法人/经营者', value: '1' },
          { label: '经办人', value: '2' },
        ],
        onChange: (value) => {
          console.log(value)
        },
      },
      {
        label: '超级管理员证件类型',
        prop: 'program_name_type5',
        span: 12, // 使用栅格布局
        component: fromSelect, // el-input可以省略，默认使用el-input
        placeholder: '请选择超级管理员证件类型', // placeholder可以省略，默认显示“请输入+label”
        rules: [{ required: true, message: '请选择超级管理员证件类型', trigger: 'blur' }],
        clearable: true,
        width: '100%', // 设置宽度
        options: [
          { label: '中国大陆居民-身份证', value: '1' },
          { label: '其他国家或地区居民-护照', value: '2' },
          { label: '中国香港居民-来往内地通行证', value: '3' },
          { label: '中国澳门居民-来往内地通行证', value: '4' },
          { label: '中国台湾居民-来往内地通行证', value: '5' },
          { label: '外国人居留证', value: '6' },
          { label: '港澳居民证', value: '7' },
          { label: '台湾居民证', value: '8' },
        ],
        onChange: (value) => {
          console.log(value)
        },
      },
      {
        label: '超级管理员证件正面照片',
        prop: 'legal_id_card_front_pic1',
        prop1: '',
        type: 'upload',
        component: FromUpload, // el-input可以省略，默认使用el-input
        placeholder: '请上传超级管理员证件正面照片', // placeholder可以省略，默认显示“请输入+label”
        span: 12, // 使用栅格布局
        rules: [{ required: true, message: '请上传超级管理员证件正面照片', trigger: 'blur' }],
        tip: `   <div>1、若证件类型为身份证，请上传人像面照片；</div>
            <div>2、正面拍摄、清晰、四角完整、无反光或遮挡；不得翻拍、截图、镜像、PS；</div>
            <div>3、请上传彩色照片或彩色扫描件或复印件（需加盖公章鲜章）</div>
          `,
      },
      {
        label: '超级管理员证件反面照片',
        prop: 'legal_id_card_front_pic1',
        prop1: '',
        type: 'upload',
        component: FromUpload, // el-input可以省略，默认使用el-input
        placeholder: '请上传超级管理员证件反面照片', // placeholder可以省略，默认显示“请输入+label”
        span: 12, // 使用栅格布局
        rules: [{ required: true, message: '请上传超级管理员证件反面照片', trigger: 'blur' }],
        tip: `     <div>1、若证件类型为身份证，请上传国徽面照片；</div>
            <div>2、正面拍摄、清晰、四角完整、无反光或遮挡；不得翻拍、截图、镜像、PS；</div>
            <div>3、请上传彩色照片或彩色扫描件或复印件（需加盖公章鲜章）；</div>
          `,
      },
      {
        label: '超级管理员姓名',
        prop: 'legal_name1',
        prop1: '',
        component: 'el-input', // el-input可以省略，默认使用el-input
        placeholder: '请输入超级管理员姓名', // placeholder可以省略，默认显示“请输入+label”
        span: 12, // 使用栅格布局
        rules: [{ required: true, message: '请输入超级管理员姓名', trigger: 'blur' }],
      },
      {
        label: '超级管理员证件号码',
        prop: 'legal_number',
        prop1: '',
        component: 'el-input', // el-input可以省略，默认使用el-input
        placeholder: '请输入超级管理员证件号码', // placeholder可以省略，默认显示“请输入+label”
        span: 12, // 使用栅格布局
        rules: [{ required: true, message: '请输入超级管理员证件号码', trigger: 'blur' }],
      },
      {
        label: '证件有效期',
        prop: 'date2',
        component: 'el-date-picker',
        clearable: true,
        type: 'daterange',
        format: 'yyyy.MM.dd',
        valueFormat: 'yyyy.MM.dd',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
        span: 12,
        width: '100%',
      },
      {
        label: '居住地址',
        prop: 'legal_address1',
        prop1: '',
        component: 'el-input', // el-input可以省略，默认使用el-input
        placeholder: '请输入居住地址', // placeholder可以省略，默认显示“请输入+label”
        span: 12, // 使用栅格布局
        rules: [{ required: true, message: '请输入居住地址', trigger: 'blur' }],
      },
      {
        label: '超级管理员手机号',
        prop: 'legal_phone',
        prop1: '',
        component: 'el-input', // el-input可以省略，默认使用el-input
        placeholder: '请输入超级管理员手机号', // placeholder可以省略，默认显示“请输入+label”
        span: 12, // 使用栅格布局
        rules: [{ required: true, message: '请输入超级管理员手机号', trigger: 'blur' }],
      },
      {
        label: '超级管理员邮箱',
        prop: 'legal_email',
        prop1: '',
        component: 'el-input', // el-input可以省略，默认使用el-input
        placeholder: '请输入超级管理员邮箱', // placeholder可以省略，默认显示“请输入+label”
        span: 12, // 使用栅格布局
        rules: [{ required: true, message: '请输入超级管理员邮箱', trigger: 'blur' }],
      },
      {
        label: '超级管理员微信ID',
        prop: 'legal_wechat',
        prop1: '',
        component: 'el-input', // el-input可以省略，默认使用el-input
        placeholder: '请输入超级管理员微信ID', // placeholder可以省略，默认显示“请输入+label”
        span: 12, // 使用栅格布局
        rules: [{ required: true, message: '请输入超级管理员微信ID', trigger: 'blur' }],
      },
      {
        label: '业务办理授权函',
        prop: 'logo',
        prop1: '',
        type: 'upload',
        component: FromUpload, // el-input可以省略，默认使用el-input
        placeholder: '请上传业务办理授权函', // placeholder可以省略，默认显示“请输入+label”
        span: 12, // 使用栅格布局
        rules: [{ required: true, message: '请上传业务办理授权函', trigger: 'blur' }],
        tip: `      <div>1、当超级管理员类型是经办人时，请上传业务办理授权函；</div>
            <div>2、请参照示例图打印业务办理授权函，全部信息需打印，不支持手写商户信息，并加盖公章；</div>
          `,
        downLoad: true,
      },
    ],
  },
  {
    title: '结算信息',
    ItemConfig: [
      {
        label: '所属行业',
        prop: 'program_name_type10',
        span: 12, // 使用栅格布局
        component: fromSelect, // el-input可以省略，默认使用el-input
        placeholder: '请选择所属行业', // placeholder可以省略，默认显示“请输入+label”
        rules: [{ required: true, message: '请选择所属行业', trigger: 'blur' }],
        clearable: true,
        width: '100%', // 设置宽度
        options: [
          { label: '法人/经营者', value: '1' },
          { label: '经办人', value: '2' },
        ],
        onChange: (value) => {
          console.log(value)
        },
      },

      {
        label: '特殊资质图片',
        prop: 'manager_id_card_front_pic',
        prop1: '',
        type: 'upload',
        component: FromUpload, // el-input可以省略，默认使用el-input
        placeholder: '请上传特殊资质图片', // placeholder可以省略，默认显示“请输入+label”
        span: 12, // 使用栅格布局
        rules: [{ required: true, message: '请上传特殊资质图片', trigger: 'blur' }],
        tip: `  <div>该行业需要上传XXXXXXX；</div>
          `,
      },
      {
        label: '账户类型',
        prop: 'program_name_type6',
        span: 12, // 使用栅格布局
        component: fromSelect, // el-input可以省略，默认使用el-input
        placeholder: '请选择账户类型', // placeholder可以省略，默认显示“请输入+label”
        rules: [{ required: true, message: '请选择账户类型', trigger: 'blur' }],
        clearable: true,
        width: '100%', // 设置宽度
        options: [
          { label: '对公账户', value: '1' },
          { label: '经营者个人账户', value: '2' },
        ],
        onChange: (value) => {
          console.log(value)
        },
      },

      {
        label: '开户名称',
        prop: 'manager_name',
        prop1: '',
        component: 'el-input', // el-input可以省略，默认使用el-input
        placeholder: '个人银行卡请填写证件姓名，对公账户请填写商户名称', // placeholder可以省略，默认显示“请输入+label”
        span: 12, // 使用栅格布局
        rules: [{ required: true, message: '请输入开户名称', trigger: 'blur' }],
      },
      {
        label: '开户银行',
        prop: 'program_name_type7',
        span: 12, // 使用栅格布局
        component: fromSelect, // el-input可以省略，默认使用el-input
        placeholder: '请选择开户银行', // placeholder可以省略，默认显示“请输入+label”
        rules: [{ required: true, message: '请选择开户银行', trigger: 'blur' }],
        clearable: true,
        width: '100%', // 设置宽度
        options: [
          { label: '法人/经营者', value: '1' },
          { label: '经办人', value: '2' },
        ],
        onChange: (value) => {
          console.log(value)
        },
      },
      {
        label: '支行所在城市',
        prop: 'program_name_type8',
        span: 12, // 使用栅格布局
        component: fromSelect, // el-input可以省略，默认使用el-input
        placeholder: '请选择支行所在城市', // placeholder可以省略，默认显示“请输入+label”
        rules: [{ required: true, message: '请选择支行所在城市', trigger: 'blur' }],
        clearable: true,
        width: '100%', // 设置宽度
        options: [
          { label: '法人/经营者', value: '1' },
          { label: '经办人', value: '2' },
        ],
        onChange: (value) => {
          console.log(value)
        },
      },
      {
        label: '开户银行支行名称',
        prop: 'program_name_type9',
        span: 12, // 使用栅格布局
        component: fromSelect, // el-input可以省略，默认使用el-input
        placeholder: '请选择开户银行支行名称', // placeholder可以省略，默认显示“请输入+label”
        rules: [{ required: true, message: '请选择开户银行支行名称', trigger: 'blur' }],
        clearable: true,
        width: '100%', // 设置宽度
        options: [
          { label: '法人/经营者', value: '1' },
          { label: '经办人', value: '2' },
        ],
        onChange: (value, data, key = 'program_name_type9') => {
          console.log(value, data[key])
        },
      },
      {
        label: '银行账号',
        prop: 'manager_id_card_no',
        prop1: '',
        component: 'el-input', // el-input可以省略，默认使用el-input
        placeholder: '请输入银行账号', // placeholder可以省略，默认显示“请输入+label”
        span: 12, // 使用栅格布局
        rules: [{ required: true, message: '请输入银行账号', trigger: 'blur' }],
      },
    ],
  },
]
export default WXformItemConfig
