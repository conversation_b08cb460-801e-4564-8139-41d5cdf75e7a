<template>
  <div class>
    <el-form label-position="top" label-width="80px" ref="formName" :model="formLabelAlign">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <div></div>
            <div style="font-size: 16px; font-weight: 600">通联支付商户号申请</div>
          </div>
        </template>
        <el-steps :active="isOrder" finish-status="success" style="margin: 20px 0">
          <el-step title="资料填写" description="填写商户基本信息"></el-step>
          <el-step title="审核中" description="等待审核结果"></el-step>
          <el-step title="开通完成" description="商户号开通成功"></el-step>
        </el-steps>

        <div class="wapper"></div>
        <div v-if="isOrder == 0">
          <free-form
            ref="form"
            formRef="freeForm"
            :model="formData"
            :formItemConfig="formItemConfig"
            label-width="150px"
            label-position="top"
          />
          <!-- <el-row :gutter="24">
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="商户名称" prop="company_name">
                <el-input v-model="formLabelAlign.company_name" placeholder="请输入商户名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="主体类型" prop="program_name_type">
                <el-select v-model="formLabelAlign.program_name_type" placeholder="请选择主体类型" style="width: 100%">
                  <el-option v-for="item in programList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="营业执照" prop="business_license_pic">
                <div class="up_line">
          
                  <PictureComponent
                    v-if="formLabelAlign.business_license_pic && formLabelAlign.business_license_pic != ''"
                    :imgParams="formLabelAlign.business_license_pic"
                    imgWidth="120px"
                    imgHeight="120px"
                    @deleteImg="deleteImg"
                  ></PictureComponent>
                  <div v-else>
                    <div class="flex avatar-flex">
                      <el-upload
                        class="avatar-uploader"
                        action="fakeaction"
                        :show-file-list="false"
                        multiple
                        accept=".jpg, .png, .jpeg, .bmp"
                        :http-request="(e) => upLoadImg(e, 'business_license_pic')"
                      >
                        <img v-if="formLabelAlign.business_license_pic" :src="formLabelAlign.business_license_pic" class="avatar" />
                        <i v-if="!formLabelAlign.business_license_pic" class="el-icon-plus avatar-uploader-icon"></i>
                        <div v-if="!formLabelAlign.business_license_pic" class="el-upload__text">上传</div>
                      </el-upload>
                      <el-button type="text" @click="isIdcard = true">查看示例</el-button>
                    </div>
                    <div class="el-upload__tip">
                      <div>1、照片应正面拍摄、清晰、四角完整、无反光或遮挡</div>
                      <div>2、不得翻拍、截图、镜像、PS</div>
                      <div>3、营业执照信息应清晰可见，包括企业名称、统一社会信用代码等</div>
                    </div>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="管理员身份证" prop="manager_id_card_front_pic">
                <div class="flex avatar-flex">
                  <el-upload
                    class="avatar-uploader"
                    action="fakeaction"
                    :show-file-list="false"
                    multiple
                    accept=".jpg, .png, .jpeg, .bmp"
                    :http-request="(e) => upLoadImg(e, 'manager_id_card_back_pic')"
                  >
                    <img v-if="formLabelAlign.manager_id_card_back_pic" :src="formLabelAlign.manager_id_card_back_pic" class="avatar" />
                    <i v-if="!formLabelAlign.manager_id_card_back_pic" class="el-icon-plus avatar-uploader-icon"></i>
                    <div v-if="!formLabelAlign.manager_id_card_back_pic" class="el-upload__text">上传国徽面</div>
                  </el-upload>
                  <el-upload
                    class="avatar-uploader"
                    action="fakeaction"
                    :show-file-list="false"
                    multiple
                    accept=".jpg, .png, .jpeg, .bmp"
                    :http-request="(e) => upLoadImg(e, 'manager_id_card_front_pic')"
                  >
                    <img v-if="formLabelAlign.manager_id_card_front_pic" :src="formLabelAlign.manager_id_card_front_pic" class="avatar" />
                    <i v-if="!formLabelAlign.manager_id_card_front_pic" class="el-icon-plus avatar-uploader-icon"></i>
                    <div v-if="!formLabelAlign.manager_id_card_front_pic" class="el-upload__text">上传人像面</div>
                  </el-upload>
                  <el-button type="text" @click="isIdcard = true">查看示例</el-button>
                </div>
                <div class="el-upload__tip">
                  <div>1、身份证人像面照片需清晰完整</div>
                  <div>2、证件信息清晰可见，无反光遮挡</div>
                  <div style="height: 32px"></div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="公司对公户开户证明" prop="business_license_pic1">
                <div class="flex avatar-flex">
                  <el-upload
                    class="avatar-uploader"
                    action="fakeaction"
                    :show-file-list="false"
                    multiple
                    accept=".jpg, .png, .jpeg, .bmp"
                    :http-request="(e) => upLoadImg(e, 'business_license_pic1')"
                  >
                    <img v-if="formLabelAlign.business_license_pic1" :src="formLabelAlign.business_license_pic1" class="avatar" />
                    <i v-if="!formLabelAlign.business_license_pic1" class="el-icon-plus avatar-uploader-icon"></i>
                    <div v-if="!formLabelAlign.business_license_pic1" class="el-upload__text">上传</div>
                  </el-upload>
                  <el-button type="text" @click="isIdcard = true">查看示例</el-button>
                </div>
                <div class="el-upload__tip">
                  <div>1、请上传对公账户的开户许可证（需有银行章）、 印鉴卡、银行开户回单或者对公户网银交易回单（含银行电子章）</div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="法人个人银行卡" prop="business_license_pic2">
                <div class="flex avatar-flex">
                  <el-upload
                    class="avatar-uploader"
                    action="fakeaction"
                    :show-file-list="false"
                    multiple
                    accept=".jpg, .png, .jpeg, .bmp"
                    :http-request="(e) => upLoadImg(e, 'business_license_pic2')"
                  >
                    <img v-if="formLabelAlign.business_license_pic2" :src="formLabelAlign.business_license_pic2" class="avatar" />
                    <i v-if="!formLabelAlign.business_license_pic2" class="el-icon-plus avatar-uploader-icon"></i>
                    <div v-if="!formLabelAlign.business_license_pic2" class="el-upload__text">上传</div>
                  </el-upload>
                  <el-button type="text" @click="isIdcard = true">查看示例</el-button>
                </div>
                <div class="el-upload__tip">
                  <div>1、银行卡号需清晰可见</div>
                  <div>2、确保卡片完整、无遮挡</div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="公司门头照" prop="business_license_pic3">
                <div class="flex avatar-flex">
                  <el-upload
                    class="avatar-uploader"
                    action="fakeaction"
                    :show-file-list="false"
                    multiple
                    accept=".jpg, .png, .jpeg, .bmp"
                    :http-request="(e) => upLoadImg(e, 'business_license_pic3')"
                  >
                    <img v-if="formLabelAlign.business_license_pic3" :src="formLabelAlign.business_license_pic3" class="avatar" />
                    <i v-if="!formLabelAlign.business_license_pic3" class="el-icon-plus avatar-uploader-icon"></i>
                    <div v-if="!formLabelAlign.business_license_pic3" class="el-upload__text">上传</div>
                  </el-upload>
                  <el-button type="text" @click="isIdcard = true">查看示例</el-button>
                </div>
                <div class="el-upload__tip">
                  <div>1、需拍摄完整的门店外观</div>
                  <div>2、确保店铺招牌清晰可见</div>
                  <div>3、照片光线适中，无严重曝光或过暗</div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="公司内景照" prop="business_license_pic4">
                <div class="flex avatar-flex">
                  <el-upload
                    class="avatar-uploader"
                    action="fakeaction"
                    :show-file-list="false"
                    multiple
                    accept=".jpg, .png, .jpeg, .bmp"
                    :http-request="(e) => upLoadImg(e, 'business_license_pic4')"
                  >
                    <img v-if="formLabelAlign.business_license_pic4" :src="formLabelAlign.business_license_pic4" class="avatar" />
                    <i v-if="!formLabelAlign.business_license_pic4" class="el-icon-plus avatar-uploader-icon"></i>
                    <div v-if="!formLabelAlign.business_license_pic4" class="el-upload__text">上传</div>
                  </el-upload>
                  <el-button type="text" @click="isIdcard = true">查看示例</el-button>
                </div>
                <div class="el-upload__tip">
                  <div>1、需拍摄完整的门店外观</div>
                  <div>2、确保店铺招牌清晰可见</div>
                  <div>3、照片光线适中，无严重曝光或过暗</div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="结算方式指令细则确认函" prop="business_license_pic5">
                <div class="flex avatar-flex">
                  <el-upload
                    class="avatar-uploader"
                    action="fakeaction"
                    :show-file-list="false"
                    multiple
                    accept=".jpg, .png, .jpeg, .bmp"
                    :http-request="(e) => upLoadImg(e, 'business_license_pic5')"
                  >
                    <img v-if="formLabelAlign.business_license_pic5" :src="formLabelAlign.business_license_pic5" class="avatar" />
                    <i v-if="!formLabelAlign.business_license_pic5" class="el-icon-plus avatar-uploader-icon"></i>
                    <div v-if="!formLabelAlign.business_license_pic5" class="el-upload__text">上传</div>
                  </el-upload>
                  <el-button type="text" @click="isIdcard = true">查看示例</el-button>
                </div>
                <div class="el-upload__tip">
                  <div>1、请上传法定代表人亲笔签署或签章的签约承诺函扫描件。</div>
                  <div>2、亲笔签名内容清晰可见，不得有涂污，破损，字迹不清晰现象；</div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="签约承诺函" prop="business_license_pic6">
                <div class="flex avatar-flex">
                  <el-upload
                    class="avatar-uploader"
                    action="fakeaction"
                    :show-file-list="false"
                    multiple
                    accept=".jpg, .png, .jpeg, .bmp"
                    :http-request="(e) => upLoadImg(e, 'business_license_pic6')"
                  >
                    <img v-if="formLabelAlign.business_license_pic6" :src="formLabelAlign.business_license_pic6" class="avatar" />
                    <i v-if="!formLabelAlign.business_license_pic6" class="el-icon-plus avatar-uploader-icon"></i>
                    <div v-if="!formLabelAlign.business_license_pic6" class="el-upload__text">上传</div>
                  </el-upload>
                  <el-button type="text" @click="isIdcard = true">查看示例</el-button>
                </div>
                <div class="el-upload__tip">
                  <div>1、请上传法定代表人亲笔签署或签章的签约承诺函扫描件。</div>
                  <div>2、亲笔签名内容清晰可见，不得有涂污，破损，字迹不清晰现象；</div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>-->
        </div>

        <div v-if="isOrder == 1" class="shenheSecond">
          <img src alt />
          <h2>审核中</h2>
          <h3>请耐心等待，正在审核中</h3>
        </div>
        <el-form-item v-if="isOrder == 0">
          <div class="ui_spane">
            <el-button @click="reback">返回选择</el-button>
            <el-button type="primary" @click="saveDraft">保存草稿</el-button>
            <el-button type="primary" @click="submitForm('formName')">提交审核</el-button>
          </div>
        </el-form-item>
      </el-card>
    </el-form>
  </div>
</template>

<script>
import FromUpload from '@/components/FreeFromComponent/fromUpload.vue'
import fromSelect from '@/components/FreeFromComponent/fromSelect.vue'
import { create, ocrProcess, saveDraft } from '@/api/miniMaageApi'
export default {
  components: {
    FromUpload,
    fromSelect,
  },
  props: {
    detailInfo: {
      type: Object, // 定义数据类型，可以是String, Number, Object等
    },
  },
  data() {
    return {
      isOrder: 0,
      // 表单数据
      formData: {
        company_name: '',
        program_name_type: '',
        business_license_pic: '',
        manager_id_card_back_pic: '',
        manager_id_card_front_pic: '',

        business_license_pic1: '',
        business_license_pic2: '',
        business_license_pic3: '',
        business_license_pic4: '',
        business_license_pic5: '',
        business_license_pic6: '',
      },
      // el-form-item 配置项
      formItemConfig: [
        {
          label: '商户名称',
          prop: 'company_name',
          prop1: '',
          component: 'el-input', // el-input可以省略，默认使用el-input
          placeholder: '请输入商户名称', // placeholder可以省略，默认显示“请输入+label”
          span: 12, // 使用栅格布局
          rules: [{ required: true, message: '请输入商户名称', trigger: 'blur' }],
        },
        {
          label: '主体类型',
          prop: 'program_name_type',
          span: 12, // 使用栅格布局
          component: fromSelect, // el-input可以省略，默认使用el-input
          placeholder: '请选择主体类型', // placeholder可以省略，默认显示“请输入+label”
          clearable: true,
          width: '100%', // 设置宽度
          options: [
            { label: '企业', value: '1' },
            { label: '个体商户', value: '2' },
          ],
          onChange: (value) => {
            console.log(value)
          },
        },
        {
          label: '营业执照',
          prop: 'business_license_pic',
          prop1: '',
          type: 'upload',
          component: FromUpload, // el-input可以省略，默认使用el-input
          placeholder: '请上传营业执照', // placeholder可以省略，默认显示“请输入+label”
          span: 12, // 使用栅格布局
          rules: [{ required: true, message: '请输入营业执照', trigger: 'blur' }],
          tip: `<div>1、照片应正面拍摄、清晰、四角完整、无反光或遮挡</div>
                  <div>2、不得翻拍、截图、镜像、PS</div>
                  <div>3、营业执照信息应清晰可见，包括企业名称、统一社会信用代码等</div>
                `,
        },
        {
          label: '管理员身份证',
          prop: 'manager_id_card_front_pic',
          prop1: 'manager_id_card_back_pic',
          type: 'upload',
          component: FromUpload, // el-input可以省略，默认使用el-input
          placeholder: '请上传管理员身份证', // placeholder可以省略，默认显示“请输入+label”
          span: 12, // 使用栅格布局
          rules: [{ required: true, message: '请输入营业执照', trigger: 'blur' }],
          tip: ` <div>1、身份证人像面照片需清晰完整</div>
                  <div>2、证件信息清晰可见，无反光遮挡</div>
                  <div style="height: 32px"></div>
                `,
        },
        {
          label: '公司对公户开户证明',
          prop: 'business_license_pic1',
          prop1: '',
          type: 'upload',
          component: FromUpload, // el-input可以省略，默认使用el-input
          placeholder: '请上传公司对公户开户证明', // placeholder可以省略，默认显示“请输入+label”
          span: 12, // 使用栅格布局
          rules: [{ required: true, message: '请上传公司对公户开户证明', trigger: 'blur' }],
          tip: `  <div>1、请上传对公账户的开户许可证（需有银行章）、 印鉴卡、银行开户回单或者对公户网银交易回单（含银行电子章）</div>
                `,
        },
        {
          label: '法人个人银行卡',
          prop: 'business_license_pic2',
          prop1: '',
          type: 'upload',
          component: FromUpload, // el-input可以省略，默认使用el-input
          placeholder: '请上传法人个人银行卡', // placeholder可以省略，默认显示“请输入+label”
          span: 12, // 使用栅格布局
          rules: [{ required: true, message: '请上传法人个人银行卡', trigger: 'blur' }],
          tip: ` <div>1、银行卡号需清晰可见</div>
                  <div>2、确保卡片完整、无遮挡</div>
                `,
        },
        {
          label: '公司门头照',
          prop: 'business_license_pic3',
          prop1: '',
          type: 'upload',
          component: FromUpload, // el-input可以省略，默认使用el-input
          placeholder: '请上传公司门头照', // placeholder可以省略，默认显示“请输入+label”
          span: 12, // 使用栅格布局
          rules: [{ required: true, message: '请上传公司门头照', trigger: 'blur' }],
          tip: `  <div>1、需拍摄完整的门店外观</div>
                  <div>2、确保店铺招牌清晰可见</div>
                  <div>3、照片光线适中，无严重曝光或过暗</div>
                `,
        },
        {
          label: '公司内景照',
          prop: 'business_license_pic4',
          prop1: '',
          type: 'upload',
          component: FromUpload, // el-input可以省略，默认使用el-input
          placeholder: '请上传公司内景照', // placeholder可以省略，默认显示“请输入+label”
          span: 12, // 使用栅格布局
          rules: [{ required: true, message: '请上传公司内景照', trigger: 'blur' }],
          tip: `  <div>1、需拍摄完整的门店外观</div>
                  <div>2、确保店铺招牌清晰可见</div>
                  <div>3、照片光线适中，无严重曝光或过暗</div>
                `,
        },
        {
          label: '结算方式指令细则确认函',
          prop: 'business_license_pic5',
          prop1: '',
          type: 'upload',
          component: FromUpload, // el-input可以省略，默认使用el-input
          placeholder: '请上传结算方式指令细则确认函', // placeholder可以省略，默认显示“请输入+label”
          span: 12, // 使用栅格布局
          rules: [{ required: true, message: '请上传结算方式指令细则确认函', trigger: 'blur' }],
          downLoad: true,
          tip: `  <div>1、请上传法定代表人亲笔签署或签章的签约承诺函扫描件。</div>
                  <div>2、亲笔签名内容清晰可见，不得有涂污，破损，字迹不清晰现象；</div>
                `,
        },
        {
          label: '签约承诺函',
          prop: 'business_license_pic6',
          prop1: '',
          type: 'upload',
          component: FromUpload, // el-input可以省略，默认使用el-input
          placeholder: '请上传签约承诺函', // placeholder可以省略，默认显示“请输入+label”
          span: 12, // 使用栅格布局
          rules: [{ required: true, message: '请上传签约承诺函', trigger: 'blur' }],
          downLoad: true,
          tip: ` <div>1、请上传法定代表人亲笔签署或签章的签约承诺函扫描件。</div>
                  <div>2、亲笔签名内容清晰可见，不得有涂污，破损，字迹不清晰现象；</div>
                `,
        },
        // {
        //   label: '时间选择',
        //   prop: 'time',
        //   component: FromUpload,
        //   clearable: true,
        //   type: 'month',
        //   format: 'yyyy-MM',
        //   valueFormat: 'yyyy-MM',
        //   span: 12,
        //   width: '100%',
        // },
      ],

      formLabelAlign: {
        company_name: '',
        program_name_type: '',
        business_license_pic: '',
        manager_id_card_back_pic: '',
        manager_id_card_front_pic: '',

        business_license_pic1: '',
        business_license_pic2: '',
        business_license_pic3: '',
        business_license_pic4: '',
        business_license_pic5: '',
        business_license_pic6: '',
      },
      // rules: {
      //   company_name: [{ required: true, message: '请输入商户名称', trigger: 'blur' }],
      //   program_name_type: [{ required: true, message: '请选择主体类型', trigger: 'blur' }],
      //   business_license_pic: [{ required: true, message: '请上传营业执照', trigger: 'blur' }],
      //   manager_id_card_front_pic: [
      //     {
      //       required: true,
      //       validator: (rule, value, callback) => {
      //         if (this.formLabelAlign.manager_id_card_back_pic && this.formLabelAlign.manager_id_card_front_pic) {
      //           callback()
      //         } else {
      //           return callback(new Error('请上传管理员身份证身份证两面照片'))
      //         }
      //       },
      //       trigger: 'change',
      //     },
      //   ],
      //   business_license_pic1: [{ required: true, message: '请上传公司对公户开户证明', trigger: 'blur' }],
      //   business_license_pic2: [{ required: true, message: '请上传法人个人银行卡', trigger: 'blur' }],
      //   business_license_pic3: [{ required: true, message: '请上传公司门头照', trigger: 'blur' }],
      //   business_license_pic4: [{ required: true, message: '请上传公司内景照', trigger: 'blur' }],
      //   business_license_pic5: [{ required: true, message: '请上传结算方式指令细则确认函', trigger: 'blur' }],
      //   business_license_pic6: [{ required: true, message: '请上传签约承诺函', trigger: 'blur' }],
      // },
      // programList: [
      //   {
      //     name: '企业',
      //     id: 1,
      //   },
      //   {
      //     name: '个体商户',
      //     id: 2,
      //   },
      // ],
      fileList: [],
    }
  },
  created() {},
  mounted() {
    // if (this.detailInfo.id) {
    //   this.formLabelAlign = this.detailInfo
    //   this.updateFields = this.detailInfo.updateFields
    // }
  },
  methods: {
    deleteImg() {},
    /**
     * 上传图片并处理图片信息
     *
     * @param file 上传的文件对象
     * @param key 图片对应的字段名
     */
    upLoadImg(file, key) {
      // this.convertToBase64(file.file, key)
      // 上传
      // return
      this.$upLoadImg(file.file).then((res) => {
        // 上传完执行的操作
        this.formLabelAlign[key] = res.data.url
        switch (
          key // 根据上传的图片类型执行不同的操作
        ) {
          // 营业执照
          case 'business_license_pic':
            this.getOcrProcess('business', res.data.url, '0')
            break
          case 'legal_id_card_front_pic':
            this.getOcrProcess('idCardFront', res.data.url)
            break
          case 'legal_id_card_back_pic':
            this.getOcrProcess('idCardBack', res.data.url)
            break
        }
      })
    },
    /**
     * 获取OCR处理结果
     *
     * @param {string} type OCR处理类型
     * @param {File} img 待处理的图片文件
     * @returns {Promise<any>} 返回OCR处理结果
     */
    async getOcrProcess(type, img, i) {
      let res = await ocrProcess({ type: i, image: img })
      this.formLabelAlign[type] = res.data.image.url
      switch (type) {
        // 营业执照
        case 'business_license_pic':
          let params = res.data.ocr_business.content.result
          this.formLabelAlign.company_name = params.name
          this.formLabelAlign.unique_social_credit_code = params.registration_number
          break
        case 'legal_id_card_back_pic':
          let data = res.data.ocr_id_card
          this.formLabelAlign.legal_name = data.name
          this.formLabelAlign.legal_id_card_no = data.idNumber
          break
        case 'legal_id_card_front_pic':
          let data1 = res.data.ocr_id_card
          console.log(data1.validPeriod.split('-'))
          this.formLabelAlign.date = data1.validPeriod.split('-')
          break
      }
    },
    /**
     * 将文件转换为Base64编码的字符串
     *
     * @param file 文件对象，通常是通过文件输入获取的
     * @param key 用于标识或分类文件的唯一键
     */
    convertToBase64(file, key) {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => {
        const base64String = reader.result
        this.displayImage(base64String, key)
      }
    },
    displayImage(base64String, key) {
      // 显示图片的示例方法
      switch (
        key // 根据上传的图片类型执行不同的操作
      ) {
        // 营业执照
        case 'business_license_pic':
          this.getOcrProcess(key, base64String, '0')
          break
        case 'legal_id_card_front_pic':
          this.getOcrProcess(key, base64String, '1')
          break
        case 'legal_id_card_back_pic':
          this.getOcrProcess(key, base64String, '1')
          break
      }
    },
    /**
     * 处理文件上传超过限制的处理函数
     *
     * @param files 当前选择的文件列表
     * @param fileList 组件内部维护的文件列表
     */
    handleExceed(files, fileList) {
      console.log(fileList[0].raw)
      this.fileList = [files[0]] // 只保留最新的文件
      this.upLoadImg({ file: fileList[0].raw }, 'principal_authorization')
    },

    /**
     * 提交表单
     *
     * @param {string} formName 表单名称
     */
    submitForm(formName) {
      // 调用 FreeForm 组件的 validate() 方法，验证表单
      this.$refs.form.validate((valid, formData) => {
        console.log(valid, formData)
      })
      // this.isOrder++
      // setTimeout(() => {
      //   this.$emit('close')
      // }, 5000)
      // this.$refs[formName].validate((valid) => {
      //   if (valid) {
      //     let params = this.formLabelAlign
      //     create(params).then((res) => {
      //       console.log(res)
      //       this.$message({
      //         type: 'success',
      //         message: '提交成功!',
      //       })
      //     })
      //   } else {
      //     return false
      //   }
      // })
    },
    // 保存草稿
    async saveDraft() {
      let params = this.formLabelAlign
      let res = await saveDraft(params)
    },
    //返回选择
    reback() {
      this.$emit('close')
    },
  },
}
</script>

<style lang="scss" scoped>
.ui_flex {
  display: flex;
}
.n_check {
  transform: translate(380px, -62px);
  cursor: pointer;
  font-size: 12px;
  color: #0071fe;
  line-height: 12px;
}
.avatar-flex {
  align-items: flex-end;
}
.avatar-uploader {
  margin-right: 24px;
  width: 100px;
  height: 100px;
  border: 1px dashed #dcdfe6;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  text-align: center;
  &:hover {
    border-color: #409eff;
  }

  .el-upload__text {
    font-weight: 400;
    font-size: 12px;
    color: #606266;
    line-height: 17px;
  }
}
.avatar-uploader-icon {
  font-size: 15px;
  color: #8c939d;
  line-height: 20px;
  text-align: center;
  margin: 32px auto 0px;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
.avatar_img {
  display: block;
  width: 360px;
  height: 180px;
}
.shenheSecond {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.ui_spane {
  display: flex;
  justify-content: center;
}
</style>
