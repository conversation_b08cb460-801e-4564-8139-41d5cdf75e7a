<template>
  <div class>
    <div style="font-size: 20px; font-weight: 600">微信支付商户号申请</div>
    <el-steps :active="isOrder" finish-status="success" style="margin: 20px 0">
      <el-step title="资料填写" description></el-step>
      <el-step title="微信审核中" description></el-step>
      <el-step title="待账户验证及签约" description></el-step>
      <el-step title="完成开通" description></el-step>
    </el-steps>
    <div v-if="isOrder == 0" style="text-align: center; margin: 20px 0">
      <el-button @click="useSmInfoAuto" type="primary" icon="el-icon-search">使用小程序注册资料自动填充</el-button>
    </div>
    <div v-if="isOrder == 0">
      <el-form
        label-position="top"
        label-width="80px"
        ref="formName"
        :model="formLabelAlign"
        :rules="rules"
      >
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <div></div>
              <div>基本信息</div>
            </div>
          </template>
          <el-row :gutter="24">
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item prop="company_name">
                <!-- 标签中的提示符号 -->
                <template #label>
                  <span class="label-text">
                    商户名称
                    <el-tooltip effect="dark" content="请填写营业执照上的商户名称，需与营业执照保持一致" placement="top">
                      <i class="el-icon-warning-outline"></i>
                    </el-tooltip>
                  </span>
                </template>
                <el-input v-model="formLabelAlign.company_name" placeholder="请输入商户名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item prop="company_name1">
                <!-- 标签中的提示符号 -->
                <template #label>
                  <span class="label-text">
                    商户简称
                    <el-tooltip effect="dark" content="在支付完成页向买家展示，需与微信经营类目相关" placement="top">
                      <i class="el-icon-warning-outline"></i>
                    </el-tooltip>
                  </span>
                </template>
                <el-input v-model="formLabelAlign.company_name1" placeholder="请输入商户简称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item prop="company_name_phone">
                <!-- 标签中的提示符号 -->
                <template #label>
                  <span class="label-text">
                    客服电话
                    <el-tooltip
                      effect="dark"
                      content="将在交易记录中向买家展示，请确保电话畅通以便平台回拨确认"
                      placement="top"
                    >
                      <i class="el-icon-warning-outline"></i>
                    </el-tooltip>
                  </span>
                </template>
                <el-input
                  v-model="formLabelAlign.company_name_phone"
                  placeholder="将在交易记录中向买家展示，请确保电话畅通以便平台回拨确认"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="主体类型" prop="program_name_type">
                <el-select
                  v-model="formLabelAlign.program_name_type"
                  placeholder="请选择主体类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in programList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="登记证书照片" prop="business_license_pic">
                <template #label>
                  <span class="label-text">
                    登记证书照片
                    <el-tooltip effect="dark" content="请上传清晰的登记证书照片，需要能够看清证件上所有信息" placement="top">
                      <i class="el-icon-warning-outline"></i>
                    </el-tooltip>
                  </span>
                </template>
                <div class="flex avatar-flex">
                  <el-upload
                    class="avatar-uploader"
                    action="fakeaction"
                    :show-file-list="false"
                    multiple
                    accept=".jpg, .png, .jpeg, .bmp"
                    :http-request="(e) => upLoadImg(e, 'business_license_pic')"
                  >
                    <img
                      v-if="formLabelAlign.business_license_pic"
                      :src="formLabelAlign.business_license_pic"
                      class="avatar"
                    />
                    <i
                      v-if="!formLabelAlign.business_license_pic"
                      class="el-icon-plus avatar-uploader-icon"
                    ></i>
                    <div v-if="!formLabelAlign.business_license_pic" class="el-upload__text">上传</div>
                  </el-upload>
                  <el-button type="text" @click="isIdcard = true">查看示例</el-button>
                </div>
                <div class="el-upload__tip">
                  <div>1、照片应正面拍摄、清晰、四角完整、无反光或遮挡；不得翻拍、截图、镜像、PS；</div>
                  <div>2、上传彩色照片、彩色扫描件，复印件需加盖公章鲜章；</div>
                  <div>3、水印仅限于微信支付业务相关；</div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="单位证明函照片" prop="business_license_pic1">
                <template #label>
                  <span class="label-text">
                    单位证明函照片
                    <el-tooltip effect="dark" content="政府机关、事业单位需上传单位证明函照片" placement="top">
                      <i class="el-icon-warning-outline"></i>
                    </el-tooltip>
                  </span>
                </template>
                <div class="flex avatar-flex">
                  <el-upload
                    class="avatar-uploader"
                    action="fakeaction"
                    :show-file-list="false"
                    multiple
                    accept=".jpg, .png, .jpeg, .bmp"
                    :http-request="(e) => upLoadImg(e, 'business_license_pic1')"
                  >
                    <img
                      v-if="formLabelAlign.business_license_pic1"
                      :src="formLabelAlign.business_license_pic1"
                      class="avatar"
                    />
                    <i
                      v-if="!formLabelAlign.business_license_pic1"
                      class="el-icon-plus avatar-uploader-icon"
                    ></i>
                    <div v-if="!formLabelAlign.business_license_pic1" class="el-upload__text">上传</div>
                  </el-upload>
                  <el-button type="text" @click="isIdcard = true">查看示例</el-button>
                </div>
                <div class="el-upload__tip">
                  <div>1、政府机关、事业单位必须上传单位证明函；</div>
                  <div>请参照示例图打印单位证明函，全部信息需打印，不支持手写商户信息，并加盖公章；</div>
                  <div style="height: 32px"></div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item prop="company_name2">
                <!-- 标签中的提示符号 -->
                <template #label>
                  <span class="label-text">
                    证书号
                    <el-tooltip effect="dark" content="请填写营业执照/登记证书的注册地址" placement="top">
                      <i class="el-icon-warning-outline"></i>
                    </el-tooltip>
                  </span>
                </template>
                <el-input v-model="formLabelAlign.company_name2" placeholder="请填写营业执照/登记证书的注册地址"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="登记证书类型" prop="program_name_type1">
                <el-select
                  v-model="formLabelAlign.program_name_type1"
                  placeholder="请选择登记证书类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in programList1"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item prop="company_name_address">
                <!-- 标签中的提示符号 -->
                <template #label>
                  <span class="label-text">
                    注册地址
                    <el-tooltip effect="dark" content="请填写营业执照/登记证书的注册地址" placement="top">
                      <i class="el-icon-warning-outline"></i>
                    </el-tooltip>
                  </span>
                </template>
                <el-input
                  v-model="formLabelAlign.company_name_address"
                  placeholder="请填写营业执照/登记证书的注册地址"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="证件有效期" prop="date">
                <el-date-picker
                  v-model="formLabelAlign.date"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy.MM.dd"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
              <el-form-item label="是否金融机构" prop="program_name_type2">
                <el-select v-model="formLabelAlign.program_name_type2" placeholder="请选择是否金融机构">
                  <el-option
                    v-for="item in programList2"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <div></div>
              <div>法人信息</div>
            </div>
          </template>
          <el-row :gutter="24">
            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
              <el-form-item label="法人证件类型" prop="program_name_type3">
                <el-select
                  v-model="formLabelAlign.program_name_type3"
                  placeholder="请选择法人证件类型"
                  style="width: 50%"
                >
                  <el-option
                    v-for="item in programList3"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="法人证件正面照片" prop="legal_id_card_back_pic">
                <div class="flex avatar-flex">
                  <el-upload
                    class="avatar-uploader"
                    action="fakeaction"
                    :show-file-list="false"
                    multiple
                    accept=".jpg, .png, .jpeg, .bmp"
                    :http-request="(e) => upLoadImg(e, 'legal_id_card_back_pic')"
                  >
                    <img
                      v-if="formLabelAlign.legal_id_card_back_pic"
                      :src="formLabelAlign.legal_id_card_back_pic"
                      class="avatar"
                    />
                    <i
                      v-if="!formLabelAlign.legal_id_card_back_pic"
                      class="el-icon-plus avatar-uploader-icon"
                    ></i>
                    <div v-if="!formLabelAlign.legal_id_card_back_pic" class="el-upload__text">上传人像面</div>
                  </el-upload>
                  <el-button type="text" @click="isIdcard = true">查看示例</el-button>
                </div>
                <div class="el-upload__tip">
                  <div>1、当证件类型为身份证时，请上传个体户经营者/法人的身份证人像面照片；</div>
                  <div>2、正面拍摄、清晰、四角完整、无反光或遮挡；不得翻拍、截图、镜像、PS；</div>
                  <div>3、请上传彩色照片or彩色扫描件，复印件需加盖公章鲜章，可添加"微信支付"相关水印（如微信支付认证）；</div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="法人证件背面照片" prop="legal_id_card_front_pic">
                <div class="flex avatar-flex">
                  <el-upload
                    class="avatar-uploader"
                    action="fakeaction"
                    :show-file-list="false"
                    multiple
                    accept=".jpg, .png, .jpeg, .bmp"
                    :http-request="(e) => upLoadImg(e, 'legal_id_card_front_pic')"
                  >
                    <img
                      v-if="formLabelAlign.legal_id_card_front_pic"
                      :src="formLabelAlign.legal_id_card_front_pic"
                      class="avatar"
                    />
                    <i
                      v-if="!formLabelAlign.legal_id_card_front_pic"
                      class="el-icon-plus avatar-uploader-icon"
                    ></i>
                    <div
                      v-if="!formLabelAlign.legal_id_card_front_pic"
                      class="el-upload__text"
                    >上传国徽面</div>
                  </el-upload>
                  <el-button type="text" @click="isIdcard = true">查看示例</el-button>
                </div>
                <div class="el-upload__tip">
                  <div>1、当证件类型为身份证时，请上传个体户经营者/法人的身份证国徽面照片；</div>
                  <div>2、正面拍摄、清晰、四角完整、无反光或遮挡；不得翻拍、截图、镜像、PS；</div>
                  <div>3、请上传彩色照片or彩色扫描件，复印件需加盖公章鲜章，可添加"微信支付"相关水印（如微信支付认证）；</div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="法人姓名" prop="legal_name">
                <el-input v-model="formLabelAlign.legal_name" placeholder="请输入法人姓名"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="法人身份证号码" prop="legal_id_card_no">
                <el-input v-model="formLabelAlign.legal_id_card_no" placeholder="请输入法人身份证号码"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="证件有效期" prop="date1">
                <el-date-picker
                  v-model="formLabelAlign.date1"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy.MM.dd"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="居住地址" prop="legal_address">
                <el-input v-model="formLabelAlign.legal_address" placeholder="建议填写证件上的居住地址"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <div></div>
              <div>超级管理员信息</div>
            </div>
          </template>
          <el-row :gutter="24">
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="超级管理员类型" prop="program_name_type4">
                <el-select
                  v-model="formLabelAlign.program_name_type4"
                  placeholder="请选择超级管理员类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in programList4"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="超级管理员证件类型" prop="program_name_type5">
                <el-select
                  v-model="formLabelAlign.program_name_type5"
                  placeholder="请选择超级管理员证件类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in programList3"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="超级管理员证件正面照片" prop="legal_id_card_back_pic1">
                <div class="flex avatar-flex">
                  <el-upload
                    class="avatar-uploader"
                    action="fakeaction"
                    :show-file-list="false"
                    multiple
                    accept=".jpg, .png, .jpeg, .bmp"
                    :http-request="(e) => upLoadImg(e, 'legal_id_card_back_pic1')"
                  >
                    <img
                      v-if="formLabelAlign.legal_id_card_back_pic1"
                      :src="formLabelAlign.legal_id_card_back_pic1"
                      class="avatar"
                    />
                    <i
                      v-if="!formLabelAlign.legal_id_card_back_pic1"
                      class="el-icon-plus avatar-uploader-icon"
                    ></i>
                    <div
                      v-if="!formLabelAlign.legal_id_card_back_pic1"
                      class="el-upload__text"
                    >上传人像面</div>
                  </el-upload>
                  <el-button type="text" @click="isIdcard = true">查看示例</el-button>
                </div>
                <div class="el-upload__tip">
                  <div>1、若证件类型为身份证，请上传人像面照片；</div>
                  <div>2、正面拍摄、清晰、四角完整、无反光或遮挡；不得翻拍、截图、镜像、PS；</div>
                  <div>3、请上传彩色照片或彩色扫描件或复印件（需加盖公章鲜章）</div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="超级管理员证件反面照片" prop="legal_id_card_front_pic1">
                <div class="flex avatar-flex">
                  <el-upload
                    class="avatar-uploader"
                    action="fakeaction"
                    :show-file-list="false"
                    multiple
                    accept=".jpg, .png, .jpeg, .bmp"
                    :http-request="(e) => upLoadImg(e, 'legal_id_card_front_pic1')"
                  >
                    <img
                      v-if="formLabelAlign.legal_id_card_front_pic1"
                      :src="formLabelAlign.legal_id_card_front_pic1"
                      class="avatar"
                    />
                    <i
                      v-if="!formLabelAlign.legal_id_card_front_pic1"
                      class="el-icon-plus avatar-uploader-icon"
                    ></i>
                    <div
                      v-if="!formLabelAlign.legal_id_card_front_pic1"
                      class="el-upload__text"
                    >上传国徽面</div>
                  </el-upload>
                  <el-button type="text" @click="isIdcard = true">查看示例</el-button>
                </div>
                <div class="el-upload__tip">
                  <div>1、若证件类型为身份证，请上传国徽面照片；</div>
                  <div>2、正面拍摄、清晰、四角完整、无反光或遮挡；不得翻拍、截图、镜像、PS；</div>
                  <div>3、请上传彩色照片或彩色扫描件或复印件（需加盖公章鲜章）；</div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="超级管理员姓名" prop="legal_name1">
                <el-input v-model="formLabelAlign.legal_name1" placeholder="请输入超级管理员姓名"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="超级管理员证件号码" prop="legal_number">
                <el-input v-model="formLabelAlign.legal_number" placeholder="请输入超级管理员证件号码"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="证件有效期" prop="date2">
                <el-date-picker
                  v-model="formLabelAlign.date2"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy.MM.dd"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="居住地址" prop="legal_address1">
                <el-input v-model="formLabelAlign.legal_address1" placeholder="请输入居住地址"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="超级管理员手机号" prop="legal_phone">
                <el-input v-model="formLabelAlign.legal_phone" placeholder="请输入超级管理员手机号"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="超级管理员邮箱" prop="legal_email">
                <el-input v-model="formLabelAlign.legal_email" placeholder="超级管理员邮箱"></el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="超级管理员微信ID" prop="legal_wechat">
                <div class="ui_flex">
                  <el-input v-model="formLabelAlign.legal_wechat" placeholder="请输入超级管理员微信ID"></el-input>
                </div>
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
              <el-form-item label="业务办理授权函" prop="logo">
                <div class="flex avatar-flex">
                  <el-upload
                    class="avatar-uploader"
                    action="fakeaction"
                    :show-file-list="false"
                    multiple
                    accept=".jpg, .png, .jpeg, .bmp"
                    :http-request="(e) => upLoadImg(e, 'logo')"
                  >
                    <img v-if="formLabelAlign.logo" :src="formLabelAlign.logo" class="avatar" />
                    <i v-if="!formLabelAlign.logo" class="el-icon-plus avatar-uploader-icon"></i>
                    <div v-if="!formLabelAlign.logo" class="el-upload__text">上传</div>
                  </el-upload>
                </div>
                <div class="el-upload__tip">
                  <div>1、当超级管理员类型是经办人时，请上传业务办理授权函；</div>
                  <div>2、请参照示例图打印业务办理授权函，全部信息需打印，不支持手写商户信息，并加盖公章；</div>
                  <el-button type="text">
                    <i class="el-icon-download el-icon--right"></i>
                    下载业务办理授权函模板
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <div></div>
              <div>结算信息</div>
            </div>
          </template>
          <el-row :gutter="24">
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="所属行业" prop="program_name_type4">
                <el-select
                  v-model="formLabelAlign.program_name_type4"
                  placeholder="请选择所属行业"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in programList3"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="特殊资质图片" prop="manager_id_card_front_pic">
                <div class="flex avatar-flex">
                  <el-upload
                    class="avatar-uploader"
                    action="fakeaction"
                    :show-file-list="false"
                    multiple
                    accept=".jpg, .png, .jpeg, .bmp"
                    :http-request="(e) => upLoadImg(e, 'manager_id_card_front_pic')"
                  >
                    <img
                      v-if="formLabelAlign.manager_id_card_front_pic"
                      :src="formLabelAlign.manager_id_card_front_pic"
                      class="avatar"
                    />
                    <i
                      v-if="!formLabelAlign.manager_id_card_front_pic"
                      class="el-icon-plus avatar-uploader-icon"
                    ></i>
                    <div v-if="!formLabelAlign.manager_id_card_front_pic" class="el-upload__text">上传</div>
                  </el-upload>
                </div>
                <div class="el-upload__tip">
                  <div>该行业需要上传XXXXXXX；</div>
                  <el-button type="text">
                    <i class="el-icon-download el-icon--right"></i>
                    下载业务办理授权函模板
                  </el-button>
                </div>
              </el-form-item>
            </el-col>

            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="账户类型" prop="program_name_type6">
                <el-select
                  v-model="formLabelAlign.program_name_type6"
                  placeholder="请选择账户类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in programList3"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="开户名称" prop="manager_name">
                <el-input
                  v-model="formLabelAlign.manager_name"
                  placeholder="个人银行卡请填写证件姓名，对公账户请填写商户名称"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="开户银行" prop="program_name_type7">
                <el-select
                  v-model="formLabelAlign.program_name_type7"
                  placeholder="请选择开户银行"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in programList3"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="支行所在城市" prop="program_name_type8">
                <el-select
                  v-model="formLabelAlign.program_name_type8"
                  placeholder="请选择支行所在城市"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in programList3"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="开户银行支行名称" prop="program_name_type9">
                <el-select
                  v-model="formLabelAlign.program_name_type9"
                  placeholder="请选择开户银行支行名称"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in programList3"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
              <el-form-item label="银行账号" prop="manager_id_card_no">
                <el-input v-model="formLabelAlign.manager_id_card_no" placeholder="请输入银行账号"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </el-form>
    </div>
    <div v-if="isOrder == 1">
      <div class="n_tips">
        <img class="tip_icon" src alt />
        <div>
          <div class="tip_title">审核中</div>
          <div class="tip_name">您的申请已提交，预计1-3个工作日完成审核。</div>
        </div>
      </div>
      <div class="shenheSecond">
        <img src alt />
        <h2>正在审核中</h2>
        <h3>您可以先进行其他操作，审核完成后会通知您</h3>
      </div>
    </div>
    <div v-if="isOrder == 2">
      <div class="n_tips">
        <img class="tip_icon" src alt />
        <div>
          <div class="tip_name">请扫描以下二维码进行账户验证及签约</div>
        </div>
      </div>
      <div class="shenheSecond">
        <img src alt />
        <h3>请使用微信扫描二维码进行账户验证/签约</h3>
      </div>
    </div>
    <div class="ui_spane" v-if="isOrder == 0">
      <el-button @click="reback">返回选择</el-button>
      <el-button type="primary" @click="saveDraft">保存草稿</el-button>
      <el-button type="primary" @click="submitForm('formName')">提交审核</el-button>
    </div>
    <div class="ui_spane" v-if="isOrder == 1">
      <el-button @click="rePreback">上一步</el-button>
    </div>
  </div>
</template>

<script>
import { create, ocrProcess, saveDraft } from '@/api/miniMaageApi'
export default {
  props: {
    detailInfo: {
      type: Object, // 定义数据类型，可以是String, Number, Object等
    },
  },
  data() {
    return {
      isOrder: 0,
      formLabelAlign: {
        company_name: '',
        company_name1: '',
        company_name_phone: '',
        program_name_type: '',
        business_license_pic: '',
        business_license_pic1: '',
        company_name2: '',
        program_name_type1: '',
        company_name_address: '',
        date: '',
        program_name_type2: '',
        program_name_type3: '',
        legal_id_card_back_pic: '',
        legal_id_card_front_pic: '',
        legal_name: '',
        legal_id_card_no: '',
        date1: '',
        legal_address: '',
        program_name_type4: '',
        program_name_type5: '',
        legal_id_card_back_pic1: '',
        legal_id_card_front_pic1: '',
        legal_name1: '',
        legal_number: '',
        date2: '',
        legal_address1: '',
        legal_phone: '',
        legal_email: '',
        legal_wechat: '',
        logo: '',
        program_name_type4: '',
        manager_id_card_front_pic: '',
        program_name_type6: '',
        manager_name: '',
        program_name_type7: '',
        program_name_type8: '',
        program_name_type9: '',
        manager_id_card_no: '',
      },
      rules: {
        company_name: [{ required: true, message: '请输入商户名称', trigger: 'blur' }],
        company_name1: [{ required: true, message: '请输入商户简称', trigger: 'blur' }],
        company_name_phone: [{ required: true, message: '请输入客服电话', trigger: 'blur' }],
        program_name_type: [{ required: true, message: '请选择主体类型', trigger: 'blur' }],
        business_license_pic: [{ required: true, message: '请上传登记证书照片', trigger: 'blur' }],
        business_license_pic1: [
          { required: true, message: '请上传单位证明函照片', trigger: 'blur' },
        ],
        company_name2: [{ required: true, message: '请输入证书号', trigger: 'blur' }],
        program_name_type1: [{ required: true, message: '请选择登记证书类型', trigger: 'blur' }],
        company_name_address: [{ required: true, message: '请输入注册地址', trigger: 'blur' }],
        date: [{ required: true, message: '请选择有效期', trigger: 'blur' }],
        program_name_type2: [{ required: false, message: '请选择是否金融机构', trigger: 'blur' }],
        program_name_type3: [{ required: false, message: '请选择法人证件类型', trigger: 'blur' }],
        legal_id_card_back_pic: [
          { required: true, message: '请上传法人证件正面照片', trigger: 'blur' },
        ],
        legal_id_card_front_pic: [
          { required: true, message: '请上传法人证件背面照片', trigger: 'blur' },
        ],
        legal_name: [{ required: true, message: '请输入法人姓名', trigger: 'blur' }],
        legal_id_card_no: [{ required: true, message: '请输入法人证件号码', trigger: 'blur' }],
        date1: [{ required: true, message: '请选择证件有效期', trigger: 'blur' }],
        legal_address: [{ required: true, message: '请输入居住地址', trigger: 'blur' }],
        program_name_type4: [
          { required: false, message: '请选择法超级管理员类型', trigger: 'blur' },
        ],
        program_name_type5: [
          { required: false, message: '请选择超级管理员证件类型', trigger: 'blur' },
        ],
        legal_id_card_back_pic1: [
          { required: true, message: '请上传超级管理员证件正面照片', trigger: 'blur' },
        ],
        legal_id_card_front_pic1: [
          { required: true, message: '请上传超级管理员证件反面照片', trigger: 'blur' },
        ],
        legal_name1: [{ required: true, message: '请输入超级管理员姓名', trigger: 'blur' }],
        legal_number: [{ required: true, message: '请输入超级管理员证件号码', trigger: 'blur' }],
        date2: [{ required: true, message: '请选择证件有效期', trigger: 'blur' }],
        legal_address1: [{ required: true, message: '请输入居住地址', trigger: 'blur' }],
        legal_phone: [{ required: true, message: '请输入超级管理员手机号', trigger: 'blur' }],
        legal_email: [{ required: true, message: '请输入超级管理员邮箱', trigger: 'blur' }],
        legal_wechat: [{ required: true, message: '请输入超级管理员微信ID', trigger: 'blur' }],
        logo: [{ required: true, message: '请上传业务办理授权函', trigger: 'blur' }],
        program_name_type4: [{ required: true, message: '请选择所属行业', trigger: 'blur' }],
        manager_id_card_front_pic: [
          { required: true, message: '请上传特殊资质图片', trigger: 'blur' },
        ],
        program_name_type6: [{ required: true, message: '请选择账户类型', trigger: 'blur' }],
        manager_name: [{ required: true, message: '请输入开户名称', trigger: 'blur' }],
        program_name_type7: [{ required: true, message: '请选择开户银行', trigger: 'blur' }],
        program_name_type8: [{ required: true, message: '请选择支行所在城市', trigger: 'blur' }],
        program_name_type9: [
          { required: true, message: '请选择开户银行支行名称', trigger: 'blur' },
        ],
        manager_id_card_no: [{ required: true, message: '请输入银行账号', trigger: 'blur' }],
      },
      programList4: [
        {
          name: '法人/经营者',
          id: 1,
        },
        {
          name: '经办人',
          id: 2,
        },
      ],
      programList3: [
        {
          name: '中国大陆居民-身份证',
          id: 1,
        },
        {
          name: '其他国家或地区居民-护照',
          id: 2,
        },
        {
          name: '中国香港居民-来往内地通行证',
          id: 3,
        },
        {
          name: '中国澳门居民-来往内地通行证',
          id: 4,
        },
        {
          name: '中国台湾居民-来往内地通行证',
          id: 5,
        },
        {
          name: '外国人居留证',
          id: 6,
        },
        {
          name: '港澳居民证',
          id: 7,
        },
        {
          name: '台湾居民证',
          id: 8,
        },
      ],
      programList2: [
        {
          name: '是',
          id: 1,
        },
        {
          name: '否',
          id: 2,
        },
      ],
      programList1: [
        {
          name: '事业单位法人证书',
          id: 1,
        },
        {
          name: '政府设立的组织机构代码证',
          id: 2,
        },
        {
          name: '社会团体法人登记证书',
          id: 3,
        },
        {
          name: '基金会法人登记证书',
          id: 4,
        },
        {
          name: '其他证书',
          id: 5,
        },
      ],
      programList: [
        {
          name: '个体户',
          id: 1,
        },
        {
          name: '企业',
          id: 2,
        },
        {
          name: '事业单位',
          id: 3,
        },
        {
          name: '政府机关',
          id: 4,
        },
        {
          name: '社会组织',
          id: 5,
        },
      ],

      fileList: [],
      options: [],
      updateFields: {
        business_license_pic: true,
        category: true,
        cert_list: true,
        company_name: true,
        introduction: true,
        invoice_company_account: true,
        invoice_company_bank: true,
        invoice_company_register_address: true,
        invoice_company_telephone: true,
        invoice_remark: true,
        invoice_taxpayer_identification_number: true,
        invoice_type: true,
        is_auth_filing: true,
        is_auth_record: true,
        is_category_set: true,
        is_legal_face: true,
        is_legal_manager: true,
        is_name_checked: true,
        is_name_set: true,
        is_pay_auth: true,
        legal_email: true,
        legal_id_card_back_pic: true,
        legal_id_card_begin_date: true,
        legal_id_card_end_date: true,
        legal_id_card_front_pic: true,
        legal_id_card_no: true,
        legal_name: true,
        legal_optional_phone: true,
        legal_phone: true,
        legal_wechat: true,
        logo: true,
        manager_email: true,
        manager_id_card_back_pic: true,
        manager_id_card_begin_date: true,
        manager_id_card_end_date: true,
        manager_id_card_front_pic: true,
        manager_id_card_no: true,
        manager_name: true,
        manager_optional_phone: true,
        manager_phone: true,
        name: true,
        principal_authorization: true,
        program_name_type: true,
        trademark_licensing: true,
        unique_social_credit_code: true,
      },
    }
  },
  created() {},
  mounted() {
    // if (this.detailInfo.id) {
    //   this.formLabelAlign = this.detailInfo
    //   this.updateFields = this.detailInfo.updateFields
    // }
  },
  methods: {
    deleteImg() {},
    /**
     * 上传图片并处理图片信息
     *
     * @param file 上传的文件对象
     * @param key 图片对应的字段名
     */
    upLoadImg(file, key) {
      this.convertToBase64(file.file, key)
      // 上传
      return
      this.$upLoadImg(file.file).then((res) => {
        // 上传完执行的操作
        this.formLabelAlign[key] = res.data.url
        switch (
          key // 根据上传的图片类型执行不同的操作
        ) {
          // 营业执照
          case 'business_license_pic':
            this.getOcrProcess('business', res.data.url, '0')
            break
          case 'legal_id_card_front_pic':
            this.getOcrProcess('idCardFront', res.data.url)
            break
          case 'legal_id_card_back_pic':
            this.getOcrProcess('idCardBack', res.data.url)
            break
        }
      })
    },
    /**
     * 获取OCR处理结果
     *
     * @param {string} type OCR处理类型
     * @param {File} img 待处理的图片文件
     * @returns {Promise<any>} 返回OCR处理结果
     */
    async getOcrProcess(type, img, i) {
      let res = await ocrProcess({ type: i, image: img })
      this.formLabelAlign[type] = res.data.image.url
      switch (type) {
        // 营业执照
        case 'business_license_pic':
          let params = res.data.ocr_business.content.result
          this.formLabelAlign.company_name = params.name
          this.formLabelAlign.unique_social_credit_code = params.registration_number
          break
        case 'legal_id_card_back_pic':
          let data = res.data.ocr_id_card
          this.formLabelAlign.legal_name = data.name
          this.formLabelAlign.legal_id_card_no = data.idNumber
          break
        case 'legal_id_card_front_pic':
          let data1 = res.data.ocr_id_card
          console.log(data1.validPeriod.split('-'))
          this.formLabelAlign.date = data1.validPeriod.split('-')
          break
      }
    },
    /**
     * 将文件转换为Base64编码的字符串
     *
     * @param file 文件对象，通常是通过文件输入获取的
     * @param key 用于标识或分类文件的唯一键
     */
    convertToBase64(file, key) {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => {
        const base64String = reader.result
        this.displayImage(base64String, key)
      }
    },
    displayImage(base64String, key) {
      // 显示图片的示例方法
      switch (
        key // 根据上传的图片类型执行不同的操作
      ) {
        // 营业执照
        case 'business_license_pic':
          this.getOcrProcess(key, base64String, '0')
          break
        case 'legal_id_card_front_pic':
          this.getOcrProcess(key, base64String, '1')
          break
        case 'legal_id_card_back_pic':
          this.getOcrProcess(key, base64String, '1')
          break
      }
    },
    /**
     * 处理文件上传超过限制的处理函数
     *
     * @param files 当前选择的文件列表
     * @param fileList 组件内部维护的文件列表
     */
    handleExceed(files, fileList) {
      console.log(fileList[0].raw)
      this.fileList = [files[0]] // 只保留最新的文件
      this.upLoadImg({ file: fileList[0].raw }, 'principal_authorization')
    },

    /**
     * 提交表单
     *
     * @param {string} formName 表单名称
     */
    submitForm(formName) {
      this.isOrder++
      // this.$refs[formName].validate((valid) => {
      //   if (valid) {
      //     let params = this.formLabelAlign
      //     create(params).then((res) => {
      //       console.log(res)
      //       this.$message({
      //         type: 'success',
      //         message: '提交成功!',
      //       })
      //     })
      //   } else {
      //     return false
      //   }
      // })
    },
    // 保存草稿
    async saveDraft() {
      let params = this.formLabelAlign
      let res = await saveDraft(params)
    },
    //返回选择
    reback() {
      this.$emit('close')
    },
    rePreback() {
      this.isOrder--
    },
    //使用小程序自动填充
    useSmInfoAuto() {
      this.$message({
        type: 'success',
        message: '填充!',
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.ui_flex {
  display: flex;
}
.n_check {
  transform: translate(380px, -62px);
  cursor: pointer;
  font-size: 12px;
  color: #0071fe;
  line-height: 12px;
}
.avatar-flex {
  align-items: flex-end;
}
.avatar-uploader {
  margin-right: 24px;
  width: 100px;
  height: 100px;
  border: 1px dashed #dcdfe6;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  text-align: center;
  &:hover {
    border-color: #409eff;
  }

  .el-upload__text {
    font-weight: 400;
    font-size: 12px;
    color: #606266;
    line-height: 17px;
  }
}
.avatar-uploader-icon {
  font-size: 15px;
  color: #8c939d;
  line-height: 20px;
  text-align: center;
  margin: 32px auto 0px;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
.avatar_img {
  display: block;
  width: 360px;
  height: 180px;
}
.ui_spane {
  display: flex;
  justify-content: center;
}
.n_tips {
  display: flex;
  align-items: center;
  padding: 18px 20px;
  background: #eef6ff;
  border-radius: 8px;
  border: 1px solid #94c3ff;
  .tip_icon {
    display: block;
    width: 24px;
    height: 24px;
    margin-right: 10px;
  }
  .tip_title {
    font-size: 16px;
    color: #222222;
    line-height: 16px;
    font-weight: 600;
  }
  .tip_name {
    font-size: 14px;
    color: #666666;
    line-height: 20px;
    margin-top: 8px;
  }
}
.shenheSecond {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>
