<template>
  <div class>
    <div class="n_tips">
      <img class="tip_icon" :src="imgOssPath + '20250517_shuoming.png'" alt />
      <div>
        <div class="tip_title">商户号开通</div>
        <div class="tip_name">开通商户号后，您的小程序将可以接入支付功能，为用户提供更完整的服务体验</div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    data() {
      return {}
    },
    created() {},
    mounted() {},
    methods: {},
  }
</script>

<style lang="scss" scoped>
  .n_tips {
    display: flex;
    align-items: center;
    padding: 18px 20px;
    background: #eef6ff;
    border-radius: 8px;
    border: 1px solid #94c3ff;
    .tip_icon {
      display: block;
      width: 24px;
      height: 24px;
      margin-right: 10px;
    }
    .tip_title {
      font-size: 16px;
      color: #222222;
      line-height: 16px;
      font-weight: 600;
    }
    .tip_name {
      font-size: 14px;
      color: #666666;
      line-height: 20px;
      margin-top: 8px;
    }
  }
</style>
