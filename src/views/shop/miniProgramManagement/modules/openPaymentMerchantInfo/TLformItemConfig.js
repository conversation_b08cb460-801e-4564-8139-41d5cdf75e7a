import FromUpload from '@/components/FreeFromComponent/fromUpload.vue'
import fromSelect from '@/components/FreeFromComponent/fromSelect.vue'
// el-form-item 配置项
const TLformItemConfig = [
  {
    label: '商户名称',
    prop: 'company_name',
    prop1: '',
    component: 'el-input', // el-input可以省略，默认使用el-input
    placeholder: '请输入商户名称', // placeholder可以省略，默认显示“请输入+label”
    span: 12, // 使用栅格布局
    rules: [{ required: true, message: '请输入商户名称', trigger: 'blur' }],
  },
  {
    label: '主体类型',
    prop: 'company_type',
    span: 12, // 使用栅格布局
    component: fromSelect, // el-input可以省略，默认使用el-input
    placeholder: '请选择主体类型', // placeholder可以省略，默认显示“请输入+label”
    rules: [{ required: true, message: '请选择主体类型', trigger: 'blur' }],
    clearable: true,
    width: '100%', // 设置宽度
    options: [
      { label: '企业', value: '1' },
      { label: '个体工商户', value: '2' },
    ],
    onChange: (value) => {
      console.log(value)
    },
  },
  {
    label: '营业执照',
    prop: 'license',
    prop1: '',
    type: 'upload',
    component: FromUpload, // el-input可以省略，默认使用el-input
    placeholder: '请上传营业执照', // placeholder可以省略，默认显示“请输入+label”
    span: 12, // 使用栅格布局
    rules: [{ required: true, message: '请上传营业执照', trigger: 'blur' }],
    tip: `<div>1、照片应正面拍摄、清晰、四角完整、无反光或遮挡</div>
          <div>2、不得翻拍、截图、镜像、PS</div>
          <div>3、营业执照信息应清晰可见，包括企业名称、统一社会信用代码等</div>
        `,
  },
  {
    label: '管理员身份证',
    prop: 'legal_card_front',
    prop1: 'legal_card_back',
    type: 'upload',
    component: FromUpload, // el-input可以省略，默认使用el-input
    placeholder: '请上传管理员身份证', // placeholder可以省略，默认显示“请输入+label”
    span: 12, // 使用栅格布局
    rules: [{ required: true, message: '请上传身份证两面照片', trigger: 'blur' }],
    // rules: [
    //   {
    //     required: true,
    //     message: '请上传管理员身份证',
    //     trigger: 'blur',
    //     validator: (rule, value, callback) => {
    //       if (this.formData.manager_id_card_back_pic && this.formData.manager_id_card_front_pic) {
    //         callback()
    //       } else {
    //         return callback(new Error('请上传身份证两面照片'))
    //       }
    //     },
    //   },
    // ],
    tip: ` <div>1、身份证人像面照片需清晰完整</div>
          <div>2、证件信息清晰可见，无反光遮挡</div>
          <div style="height: 32px"></div>
        `,
  },
  {
    label: '公司对公户开户证明',
    prop: 'corporate_account_proof',
    prop1: '',
    type: 'upload',
    component: FromUpload, // el-input可以省略，默认使用el-input
    placeholder: '请上传公司对公户开户证明', // placeholder可以省略，默认显示“请输入+label”
    span: 12, // 使用栅格布局
    rules: [{ required: true, message: '请上传公司对公户开户证明', trigger: 'blur' }],
    tip: `  <div>1、请上传对公账户的开户许可证（需有银行章）、 印鉴卡、银行开户回单或者对公户网银交易回单（含银行电子章）</div>
        `,
  },
  {
    label: '法人个人银行卡',
    prop: 'legal_bank_card',
    prop1: '',
    type: 'upload',
    component: FromUpload, // el-input可以省略，默认使用el-input
    placeholder: '请上传法人个人银行卡', // placeholder可以省略，默认显示“请输入+label”
    span: 12, // 使用栅格布局
    rules: [{ required: true, message: '请上传法人个人银行卡', trigger: 'blur' }],
    tip: ` <div>1、银行卡号需清晰可见</div>
          <div>2、确保卡片完整、无遮挡</div>
        `,
  },
  {
    label: '公司门头照',
    prop: 'company_front_photo',
    prop1: '',
    type: 'upload',
    component: FromUpload, // el-input可以省略，默认使用el-input
    placeholder: '请上传公司门头照', // placeholder可以省略，默认显示“请输入+label”
    span: 12, // 使用栅格布局
    rules: [{ required: true, message: '请上传公司门头照', trigger: 'blur' }],
    tip: `  <div>1、需拍摄完整的门店外观</div>
          <div>2、确保店铺招牌清晰可见</div>
          <div>3、照片光线适中，无严重曝光或过暗</div>
        `,
  },
  {
    label: '公司内景照',
    prop: 'company_inside_photo',
    prop1: '',
    type: 'upload',
    component: FromUpload, // el-input可以省略，默认使用el-input
    placeholder: '请上传公司内景照', // placeholder可以省略，默认显示“请输入+label”
    span: 12, // 使用栅格布局
    rules: [{ required: true, message: '请上传公司内景照', trigger: 'blur' }],
    tip: `  <div>1、需拍摄完整的门店外观</div>
          <div>2、确保店铺招牌清晰可见</div>
          <div>3、照片光线适中，无严重曝光或过暗</div>
        `,
  },
  {
    label: '结算方式指令细则确认函',
    prop: 'settle_method_confirm',
    prop1: '',
    type: 'upload',
    component: FromUpload, // el-input可以省略，默认使用el-input
    placeholder: '请上传结算方式指令细则确认函', // placeholder可以省略，默认显示“请输入+label”
    span: 12, // 使用栅格布局
    rules: [{ required: true, message: '请上传结算方式指令细则确认函', trigger: 'blur' }],
    downLoad: true,
    tip: `  <div>1、请上传法定代表人亲笔签署或签章的签约承诺函扫描件。</div>
          <div>2、亲笔签名内容清晰可见，不得有涂污，破损，字迹不清晰现象；</div>
        `,
  },
  {
    label: '签约承诺函',
    prop: 'sign_commit',
    prop1: '',
    type: 'upload',
    component: FromUpload, // el-input可以省略，默认使用el-input
    placeholder: '请上传签约承诺函', // placeholder可以省略，默认显示“请输入+label”
    span: 12, // 使用栅格布局
    rules: [{ required: true, message: '请上传签约承诺函', trigger: 'blur' }],
    downLoad: true,
    tip: ` <div>1、请上传法定代表人亲笔签署或签章的签约承诺函扫描件。</div>
          <div>2、亲笔签名内容清晰可见，不得有涂污，破损，字迹不清晰现象；</div>
        `,
  },
  // {
  //   label: '时间选择',
  //   prop: 'time',
  //   component: FromUpload,
  //   clearable: true,
  //   type: 'month',
  //   format: 'yyyy-MM',
  //   valueFormat: 'yyyy-MM',
  //   span: 12,
  //   width: '100%',
  // },
]
export default TLformItemConfig
