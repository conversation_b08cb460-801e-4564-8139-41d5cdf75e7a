<!--
 * @Author: liqian liqian@123
 * @Date: 2025-04-25 09:21:24
 * @LastEditors: liqian <EMAIL>
 * @LastEditTime: 2025-07-18 10:28:59
 * @FilePath: \qst-merchant-admin-2.0\src\views\shop\miniProgramManagement\modules\openPaymentMerchantInfo\openPaymentMerchant.vue
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <el-card class="box-card">
      <div v-if="clickFlag.length == 0">
        <div v-if="mch_list.length > 0">
          <h2>已开通的商户号</h2>
          <el-table :data="mch_list" style="width: 100%">
            <el-table-column prop="channel_mch_no" label="商户号"></el-table-column>
            <el-table-column prop="company_name" label="商户名称"></el-table-column>
            <el-table-column prop="pay_channel_name" label="商户类型">
              <template slot-scope="scope">
                <div class="styleSpan">{{ scope.row.pay_channel_name }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="open_time" label="开通时间"></el-table-column>
            <el-table-column prop="status" label="状态">
              <template slot-scope="scope">
                <div v-if="scope.row.status == 'enable'" class="styleSpan">正常使用</div>
                <div v-if="scope.row.status == 'disabled'" class="styleSpan1" type="danger">
                  暂停使用
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-if="show_channel.length > 0">
          <h2>基本信息</h2>
          <div class="box_content">
            <div class="n_box n_rgt" v-for="(item, index) in show_channel" key="index">
              <img
                class="box_img"
                v-if="item.pay_channel == 'allinpay'"
                :src="imgOssPath + '20250517_tl.png'"
                alt
              />
              <img
                class="box_img"
                v-if="item.pay_channel == 'wechat'"
                :src="imgOssPath + '20250517_weixin.png'"
                alt
              />
              <div class="box_title">{{ item.pay_channel_name }}</div>
              <div class="box_name">{{ item.overview }}</div>
              <el-button
                class="n_btn"
                type="primary"
                size="mini"
                @click="applyClick(item.pay_channel)"
              >
                开始申请
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <tlPaymentMerchant @close="reback" v-if="clickFlag == 'allinpay'" />
      <wxPaymentMerchant @close="reback" v-if="clickFlag == 'wechat'" />
    </el-card>
  </div>
</template>
<script>
  import tlPaymentMerchant from './tlPaymentMerchant'
  import wxPaymentMerchant from './wxPaymentMerchant.vue'
  import {
    managePaymentChannels,
    checkOpenPaymentChannel,
    allinPayCompanyDetail,
  } from '@/api/miniMaageApi'
  export default {
    components: {
      tlPaymentMerchant,
      wxPaymentMerchant,
    },
    data() {
      return {
        clickFlag: '',
        show_channel: [], //需要展示的支付渠道
        mch_list: [], //已开通的支付渠道列表
      }
    },
    mounted() {
      // this.checkOpenPaymentChannelFn()
      this.allinPayCompanyDetailFn()
    },
    methods: {
      // 检查是否满足开通支付渠道的条件
      checkOpenPaymentChannelFn() {
        checkOpenPaymentChannel().then((res) => {
          console.log(res)
          if (res.code == 200) {
            if (res.data?.eligible) {
              this.managePaymentChannelsFn()
            } else {
              this.showAuthInfo(res.data.reasons[0])
            }
          }
        })
      },
      //不满足开通支付渠道的条件
      showAuthInfo(value) {
        this.$confirm(`${value}`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          showCancelButton: false,
          showClose: false,
          type: 'warning',
        }).then(() => {
          this.$emit('reclose')
        })
      },
      // 商户号管理列表
      managePaymentChannelsFn() {
        managePaymentChannels().then((res) => {
          this.show_channel = res.data?.show_channel || []
          //已开通的支付渠道列表
          this.mch_list = res.data?.mch_list || []
        })
      },
      //查看详情 "status": 0, //审核状态（0 草稿 1待审核，2审核成功，3 审核失败（审核失败支持重新提交审核））
      allinPayCompanyDetailFn() {
        allinPayCompanyDetail().then((res) => {
          if (res.data.length == 0) {
            this.checkOpenPaymentChannelFn()
          } else {
            if (res.data.status == 1 || res.data.status == 3) {
              this.applyClick('allinpay')
            }
            if (res.data.status == 2) {
              this.checkOpenPaymentChannelFn()
            }
          }
        })
      },
      applyClick(flag) {
        this.clickFlag = flag
      },
      reback() {
        this.clickFlag = ''
      },
    },
  }
</script>
<style lang="scss" scoped>
  .box-card {
    .box_content {
      display: flex;
      justify-content: center;
      .n_rgt {
        margin-right: 40px;
      }
      .n_box {
        width: 240px;
        height: 240px;
        background: #ffffff;
        border-radius: 10px;
        border: 1px solid #dcdfe6;
        box-sizing: border-box;
        padding: 0 17px 0 13px;
        .box_img {
          display: block;
          width: 32px;
          height: 32px;
          margin: 40px auto 10px;
        }
        .box_title {
          font-weight: 600;
          font-size: 16px;
          color: #222222;
          line-height: 16px;
          text-align: center;
        }
        .box_name {
          font-size: 14px;
          color: #666666;
          line-height: 20px;
          text-align: center;
          margin-top: 8px;
        }
        .n_btn {
          display: block;
          margin: 20px auto 0;
        }
      }
    }
    .styleSpan {
      width: 70px;
      height: 25px;
      // background: #eef6ff;
      border-radius: 4px;
      // border: 1px solid #94c3ff;
      color: #6e6e7a;
      text-align: center;
    }
    .styleSpan1 {
      width: 70px;
      height: 25px;
      // background: #f0f9eb;
      border-radius: 4px;
      // border: 1px solid #e1f3d8;
      color: red;
      text-align: center;
    }
  }
</style>
