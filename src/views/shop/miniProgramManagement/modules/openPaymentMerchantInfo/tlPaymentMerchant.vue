<template>
  <div class>
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div></div>
          <div style="font-size: 16px; font-weight: 600">通联支付商户号申请</div>
        </div>
      </template>
      <!-- <div v-if="showshenheInfo">
        <el-alert :title="fail_reason" type="error"></el-alert>
      </div> -->

      <el-steps :active="isOrder" finish-status="success" style="margin: 20px 0">
        <el-step title="资料填写" description="填写商户基本信息"></el-step>
        <el-step title="审核中" description="等待审核结果"></el-step>
        <el-step title="开通完成" description="商户号开通成功"></el-step>
      </el-steps>

      <div class="wapper"></div>

      <div v-if="isOrder == 0">
        <free-form
          ref="form"
          formRef="freeForm"
          :model="formData"
          :formItemConfig="formItemConfig"
          label-width="150px"
          label-position="top"
        />
      </div>
      <div v-if="isOrder == 1 && !showshenheInfo" class="shenheSecond">
        <img :src="imgOssPath + '20250517_shenhe.png'" alt="" />
        <h2>审核中</h2>
        <h3>请耐心等待，正在审核中</h3>
      </div>
      <div v-if="isOrder == 1 && showshenheInfo" class="shenheSecond">
        <img :src="imgOssPath + '20250517_shenhe.png'" alt="" />
        <h2 style="color: red">审核驳回</h2>
        <h3 style="color: red">{{ fail_reason }}</h3>
      </div>
      <div class="ui_spane" v-if="isOrder == 0">
        <el-button @click="reback">返回选择</el-button>
        <el-button type="primary" @click="saveDraft">保存草稿</el-button>
        <el-button type="primary" @click="submitForm('formName')">提交审核</el-button>
      </div>
      <div class="ui_spane">
        <el-button type="primary" v-if="showshenheInfo" @click="rebackPre">上一步</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
  import FromUpload from '@/components/FreeFromComponent/fromUpload.vue'
  import fromSelect from '@/components/FreeFromComponent/fromSelect.vue'
  import TLformItemConfig from './TLformItemConfig.js'
  import { getCompanyInfoApi } from '@/api/enterpriseManagement'
  import {
    createAllinPayCompany,
    allinPayCompanyTypeSelect,
    allinPayCompanyDetail,
    templateFile,
  } from '@/api/miniMaageApi'
  export default {
    components: {
      FromUpload,
      fromSelect,
    },
    props: {
      detailInfo: {
        type: Object, // 定义数据类型，可以是String, Number, Object等
      },
    },
    data() {
      return {
        isOrder: 0,
        showshenheInfo: false,
        fail_reason: '111',
        // 表单数据
        formData: {
          company_name: '',
          company_type: '',
          license: '',
          legal_card_front: '',
          legal_card_back: '',

          corporate_account_proof: '',
          legal_bank_card: '',
          company_front_photo: '',
          company_inside_photo: '',
          settle_method_confirm: '',
          sign_commit: '',
        },
        companyTypeSelect: [],
        // el-form-item 配置项
        formItemConfig: [
          {
            label: '商户名称',
            prop: 'company_name',
            prop1: '',
            component: 'el-input', // el-input可以省略，默认使用el-input
            placeholder: '请输入商户名称', // placeholder可以省略，默认显示“请输入+label”
            span: 12, // 使用栅格布局
            disabled: true,
            rules: [{ required: true, message: '请输入商户名称', trigger: 'blur' }],
          },
          {
            label: '主体类型',
            prop: 'company_type',
            span: 12, // 使用栅格布局
            component: fromSelect, // el-input可以省略，默认使用el-input
            placeholder: '请选择主体类型', // placeholder可以省略，默认显示“请输入+label”
            rules: [{ required: true, message: '请选择主体类型', trigger: 'blur' }],
            clearable: true,
            width: '100%', // 设置宽度
            disabled: false,
            options: [],
            onChange: (value) => {
              if (value == 1) {
                this.$set(this.formItemConfig[5], 'rules', [
                  {
                    required: false,
                    message: '请上传法人个人银行卡',
                    trigger: 'blur',
                  },
                ])
              }
              if (value == 2) {
                this.$set(this.formItemConfig[5], 'rules', [
                  {
                    required: true,
                    message: '请上传法人个人银行卡',
                    trigger: 'blur',
                  },
                ])
              }
              console.log(value)
            },
          },
          {
            label: '营业执照',
            prop: 'license',
            prop1: '',
            type: 'upload',
            type1: '',
            component: FromUpload, // el-input可以省略，默认使用el-input
            placeholder: '请上传营业执照', // placeholder可以省略，默认显示“请输入+label”
            span: 12, // 使用栅格布局
            disabled: false,
            lookTemplate: {}, //示例内容
            lookCard: false, //查看示例
            rules: [{ required: true, message: '请输入营业执照', trigger: 'blur' }],
            tip: `<div>1、照片应正面拍摄、清晰、四角完整、无反光或遮挡</div>
                  <div>2、不得翻拍、截图、镜像、PS</div>
                  <div>3、营业执照信息应清晰可见，包括企业名称、统一社会信用代码等</div>
                `,
          },
          {
            label: '管理员身份证',
            prop: 'legal_card_front',
            prop1: 'legal_card_back',
            type: 'upload',
            type1: '',
            component: FromUpload, // el-input可以省略，默认使用el-input
            placeholder: '请上传管理员身份证', // placeholder可以省略，默认显示“请输入+label”
            span: 12, // 使用栅格布局
            disabled: false,
            lookCardList: [
              require('../../../../../assets/enteroruse/20250306_id_card1.png'),
              require('../../../../../assets/enteroruse/20250306_id_card2.png'),
            ], //图片示例
            lookTemplate: {}, //示例内容
            lookCard: true, //查看示例
            rules: [
              {
                required: true,
                message: '请上传管理员身份证',
                trigger: 'blur',
                validator: (rule, value, callback) => {
                  if (this.formData.legal_card_front && this.formData.legal_card_back) {
                    callback()
                  } else {
                    return callback(new Error('请上传身份证两面照片'))
                  }
                },
              },
            ],
            tip: ` <div>1、身份证人像面照片需清晰完整</div>
                  <div>2、证件信息清晰可见，无反光遮挡</div>
                  <div style="height: 32px"></div>
                `,
          },
          {
            label: '公司对公户开户证明',
            prop: 'corporate_account_proof',
            prop1: '',
            type: 'upload',
            type1: '',
            component: FromUpload, // el-input可以省略，默认使用el-input
            placeholder: '请上传公司对公户开户证明', // placeholder可以省略，默认显示“请输入+label”
            span: 12, // 使用栅格布局
            disabled: false,
            lookCardList: [
              require('../../../../../assets/enteroruse/20250717_open_account1.png'),
              require('../../../../../assets/enteroruse/20250717_open_account2.png'),
              require('../../../../../assets/enteroruse/20250717_open_account3.png'),
            ], //图片示例
            lookTemplate: {}, //示例内容
            lookCard: true, //查看示例
            rules: [{ required: true, message: '请上传公司对公户开户证明', trigger: 'blur' }],
            tip: `  <div>1、请上传对公账户的开户许可证（需有银行章）、 印鉴卡、银行开户回单或者对公户网银交易回单（含银行电子章）</div>
                `,
          },
          {
            label: '法人个人银行卡',
            prop: 'legal_bank_card',
            prop1: '',
            type: 'upload',
            type1: '',
            component: FromUpload, // el-input可以省略，默认使用el-input
            placeholder: '请上传法人个人银行卡', // placeholder可以省略，默认显示“请输入+label”
            span: 12, // 使用栅格布局
            disabled: false,
            lookCardList: [require('../../../../../assets/enteroruse/20250717_bankcard.png')], //图片示例
            lookTemplate: {}, //示例内容
            lookCard: true, //查看示例
            rules: [
              {
                required: true,
                message: '请上传法人个人银行卡',
                trigger: 'blur',
              },
            ],
            tip: ` <div>1、银行卡号需清晰可见</div>
                  <div>2、确保卡片完整、无遮挡</div>
                `,
          },
          {
            label: '公司门头照',
            prop: 'company_front_photo',
            prop1: '',
            type: 'upload',
            type1: '',
            component: FromUpload, // el-input可以省略，默认使用el-input
            placeholder: '请上传公司门头照', // placeholder可以省略，默认显示“请输入+label”
            span: 12, // 使用栅格布局
            disabled: false,
            lookTemplate: {}, //示例内容
            lookCard: false, //查看示例
            rules: [{ required: true, message: '请上传公司门头照', trigger: 'blur' }],
            tip: `  <div>1、需拍摄完整的门店外观</div>
                  <div>2、确保店铺招牌清晰可见</div>
                  <div>3、照片光线适中，无严重曝光或过暗</div>
                `,
          },
          {
            label: '公司内景照',
            prop: 'company_inside_photo',
            prop1: '',
            type: 'upload',
            type1: '',
            component: FromUpload, // el-input可以省略，默认使用el-input
            placeholder: '请上传公司内景照', // placeholder可以省略，默认显示“请输入+label”
            span: 12, // 使用栅格布局
            disabled: false,
            lookTemplate: {}, //示例内容
            lookCard: false, //查看示例
            rules: [{ required: true, message: '请上传公司内景照', trigger: 'blur' }],
            tip: `  <div>1、需拍摄完整的门店外观</div>
                  <div>2、确保店铺招牌清晰可见</div>
                  <div>3、照片光线适中，无严重曝光或过暗</div>
                `,
          },
          {
            label: '结算方式指令细则确认函',
            prop: 'settle_method_confirm',
            prop1: '',
            type: 'upload',
            type1: 'file',
            component: FromUpload, // el-input可以省略，默认使用el-input
            placeholder: '请上传结算方式指令细则确认函', // placeholder可以省略，默认显示“请输入+label”
            span: 12, // 使用栅格布局
            disabled: false,
            lookTemplate: {}, //示例内容
            lookCard: false, //查看示例
            downLoad: true, //下载模版
            rules: [{ required: true, message: '请上传结算方式指令细则确认函', trigger: 'blur' }],
            tip: `  <div>1、请上传法定代表人亲笔签署或签章的签约承诺函扫描件。</div>
                  <div>2、亲笔签名内容清晰可见，不得有涂污，破损，字迹不清晰现象；</div>
                `,
          },
          {
            label: '签约承诺函',
            prop: 'sign_commit',
            prop1: '',
            type: 'upload',
            type1: 'file',
            component: FromUpload, // el-input可以省略，默认使用el-input
            placeholder: '请上传签约承诺函', // placeholder可以省略，默认显示“请输入+label”
            span: 12, // 使用栅格布局
            disabled: false,
            lookTemplate: {}, //示例内容
            lookCard: false, //查看示例
            downLoad: true, //下载模版
            rules: [{ required: true, message: '请上传签约承诺函', trigger: 'blur' }],
            tip: ` <div>1、请上传法定代表人亲笔签署或签章的签约承诺函扫描件。</div>
                  <div>2、亲笔签名内容清晰可见，不得有涂污，破损，字迹不清晰现象；</div>
                `,
          },
          // {
          //   label: '时间选择',
          //   prop: 'time',
          //   component: FromUpload,
          //   clearable: true,
          //   type: 'month',
          //   format: 'yyyy-MM',
          //   valueFormat: 'yyyy-MM',
          //   span: 12,
          //   width: '100%',
          // },
        ],
        // formItemConfig: TLformItemConfig,
        formDisabled: {
          company_name: true,
          company_type: true,
          license: true,
          legal_card_front: true,
          legal_card_back: true,
          corporate_account_proof: true,
          legal_bank_card: true,
          company_front_photo: true,
          company_inside_photo: true,
          settle_method_confirm: true,
          sign_commit: true,
        },
        companyData: {},
        templateFileList: [],
      }
    },
    created() {
      this.getCompanyInfo()
      this.allinPayCompanyTypeSelectFn()
      this.templateFileFn()
    },
    mounted() {
      // if (this.detailInfo.id) {
      //   this.formLabelAlign = this.detailInfo
      //   this.updateFields = this.detailInfo.updateFields
      // }
    },
    methods: {
      //  提交表单审核
      submitForm(formName) {
        // 调用 FreeForm 组件的 validate() 方法，验证表单
        this.$refs.form.validate((valid, formData) => {
          if (valid) {
            console.log(valid, formData)
            let params = {
              ...this.formData,
              status: 1,
            }
            createAllinPayCompany(params).then((res) => {
              if (res.code == 200) {
                this.$message({
                  type: 'success',
                  message: '提交成功!',
                })
                this.isOrder++
              }
            })
          }
        })
      },
      // 保存草稿
      async saveDraft() {
        let params = {
          ...this.formData,
          status: '0',
        }
        createAllinPayCompany(params).then((res) => {
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: '保存草稿成功!',
            })
          }
        })
      },
      //查看详情 "status": 0, //审核状态（0 草稿 1待审核，2审核成功，3 审核失败（审核失败支持重新提交审核））
      allinPayCompanyDetailFn() {
        allinPayCompanyDetail().then((res) => {
          if (res.data.length == 0) {
          } else {
            if (res.data.status == 0) {
              // this.formItemConfig.forEach((item, index) => {
              //   item.disabled = this.formDisabled[item.prop]
              // })
              this.isOrder = 0
            }
            if (res.data.status == 1) {
              this.isOrder = 1
            }
            if (res.data.status == 2) {
              this.isOrder = 2
            }
            if (res.data.status == 3) {
              this.showshenheInfo = true
              this.fail_reason = '驳回原因:' + res.data.fail_reason
              this.isOrder = 1
            }
          }
          this.formData = {
            ...this.formData,
            ...res.data,
            ...this.companyData,
          }
          if (this.formData.company_type == 1) {
            this.$set(this.formItemConfig[5], 'rules', [
              {
                required: false,
                message: '请上传法人个人银行卡',
                trigger: 'blur',
              },
            ])
          }
          if (this.formData.company_type == 2) {
            this.$set(this.formItemConfig[5], 'rules', [
              {
                required: true,
                message: '请上传法人个人银行卡',
                trigger: 'blur',
              },
            ])
          }
          console.log(res.data)
        })
      },
      //得到公司信息
      getCompanyInfo() {
        getCompanyInfoApi().then((res) => {
          if (res.code == 200) {
            this.companyData.company_name = this.formData.company_name
              ? this.formData.company_name
              : res.data.organization_name

            this.companyData.company_type = this.formData.company_type
              ? this.formData.company_type
              : res.data.organization_type == '个体工商户'
              ? 2
              : 1
            this.companyData.license = this.formData.license
              ? this.formData.license
              : res.data.license

            this.companyData.legal_card_front = this.formData.legal_card_front
              ? this.formData.legal_card_front
              : res.data.legal_card_front

            this.companyData.legal_card_back = this.formData.legal_card_back
              ? this.formData.legal_card_back
              : res.data.legal_card_back
            this.allinPayCompanyDetailFn()
          }
        })
      },
      //示例模版信息
      templateFileFn() {
        templateFile().then((res) => {
          if (res.code == 200) {
            this.templateFileList = res.data
            this.formItemConfig.forEach((item, index) => {
              item.lookTemplate = this.templateFileList[item.prop] || {}
            })
          }
        })
      },
      //得到下拉
      allinPayCompanyTypeSelectFn() {
        allinPayCompanyTypeSelect().then((res) => {
          if (res.data.length > 0) {
            this.$set(this.formItemConfig[1], 'options', res.data)
          }
        })
      },
      //返回选择
      reback() {
        this.$emit('close')
      },
      rebackPre() {
        this.showshenheInfo = false
        this.isOrder = 0
      },
    },
  }
</script>

<style lang="scss" scoped>
  .ui_flex {
    display: flex;
  }
  .n_check {
    transform: translate(380px, -62px);
    cursor: pointer;
    font-size: 12px;
    color: #0071fe;
    line-height: 12px;
  }
  .avatar-flex {
    align-items: flex-end;
  }
  .avatar-uploader {
    margin-right: 24px;
    width: 100px;
    height: 100px;
    border: 1px dashed #dcdfe6;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    text-align: center;
    &:hover {
      border-color: #409eff;
    }

    .el-upload__text {
      font-weight: 400;
      font-size: 12px;
      color: #606266;
      line-height: 17px;
    }
  }
  .avatar-uploader-icon {
    font-size: 15px;
    color: #8c939d;
    line-height: 20px;
    text-align: center;
    margin: 32px auto 0px;
  }

  .avatar {
    width: 100px;
    height: 100px;
    display: block;
  }
  .avatar_img {
    display: block;
    width: 360px;
    height: 180px;
  }
  .shenheSecond {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .ui_spane {
    display: flex;
    justify-content: center;
  }
</style>
