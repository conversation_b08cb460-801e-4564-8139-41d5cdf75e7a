<!--
 * @Author: shangfei <EMAIL>
 * @Date: 2025-05-17 11:13:14
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-19 16:36:01
 * @FilePath: /qst-merchant-admin-2.0/src/views/shop/miniProgramManagement/modules/faceRecognitionModules.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class>
    <el-card>
      <div>
        <p>1. 系统将通过微信向法人微信号发送验证通知。</p>
        <div class="flex">
          <span>2. 请法人在收到微信消息后24小时内完成验证。</span>
          <div v-if="detailInfo.show_again_fast_btn">
            <el-button @click="afershFn" class="time-out" type="primary" size="mini">重新触发认证</el-button>
          </div>
        </div>
        <p>3. 验证流程：</p>
        <div>
          <ul>
            <li>点击微信消息中的验证链接</li>
            <li>按提示上传身份证照片</li>
            <li>完成人脸识别</li>
          </ul>
        </div>
        <div v-if="detailInfo.fast_register_status_msg">
          <h3>验证结果</h3>
          <div class="error-info">{{detailInfo.fast_register_status_msg}}</div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { againFastRegisterWeapp } from '@/api/miniMaageApi.js'
export default {
  props: {
    detailInfo: {},
  },
  computed: {},
  data() {
    return {
      imgOssPath: this.imgOssPath,
    }
  },
  created() {},
  mounted() {},
  methods: {
    afershFn() {
      againFastRegisterWeapp().then((res) => {
        if (res.code == 200) {
          this.$emit('afershFn')
        }
      })
    },
  },
}
</script>

<style lang='scss' scoped>
.ui_spane {
  display: flex;
  justify-content: center;
}
.time-out {
  margin-left: 10px;
}
.error-info {
  min-height: 100px;
  padding: 10px;
  border: 1px dashed #ccc;
  border-radius: 8px;
}
</style>