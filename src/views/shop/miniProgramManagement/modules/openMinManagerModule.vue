<template>
  <div class="min-manager">
    <el-form
      label-position="top"
      label-width="80px"
      ref="formName"
      :model="formLabelAlign"
      :rules="rules"
    >
      <!-- 企业基本信息 -->
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <div></div>
            <div>企业基本信息</div>
          </div>
        </template>
        <el-row :gutter="24">
          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <el-form-item label="营业执照照片" prop="business_license_pic">
              <div class="up_line">
                <el-image
                  class="business_license_pic_img"
                  v-if="isView('business_license_pic')"
                  referrerpolicy="no-referrer"
                  :src="formLabelAlign.business_license_pic"
                  :preview-src-list="[formLabelAlign.business_license_pic]"
                ></el-image>

                <!-- avatar_img -->
                <div
                  class="business_license_pic_img"
                  v-else-if="
                    formLabelAlign.business_license_pic && formLabelAlign.business_license_pic != ''
                  "
                >
                  <PictureComponent
                    :imgParams="formLabelAlign.business_license_pic"
                    imgWidth="360px"
                    imgHeight="180px"
                    @deleteImg="deleteImg('business_license_pic')"
                  ></PictureComponent>
                </div>

                <div v-else>
                  <el-upload
                    class="upload-demo"
                    action="fakeaction"
                    :show-file-list="false"
                    multiple
                    drag
                    accept=".jpg, .png, .jpeg, .bmp"
                    :http-request="(e) => upLoadImg(e, 'business_license_pic')"
                  >
                    <div>
                      <i class="el-icon-upload"></i>
                      <div class="el-upload__text">
                        将文件拖到此处，或
                        <em>点击上传</em>
                      </div>
                    </div>
                    <div class="el-upload__tip" slot="tip">
                      请上传清晰且边角完整的营业执照原件照片，支持jpg、png格式，大小不超过5MB
                    </div>
                  </el-upload>
                  <div class="n_check" @click="isBusinessLcense = true">查看示例</div>
                </div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="备案地址" prop="province">
              <el-cascader
                :disabled="isView('province')"
                placeholder="请选择备案地址"
                style="width: 100%"
                v-model="formLabelAlign.addressList"
                :options="regionTreeAddress"
                @change="cascaderAddressChange"
                :props="{ label: 'name', value: 'code' }"
              ></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="备案详细地址" prop="address">
              <el-input
                :disabled="isView('address')"
                v-model="formLabelAlign.address"
                placeholder="请输入备案详细地址"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="企业名称" prop="company_name">
              <el-input
                disabled
                v-model="formLabelAlign.company_name"
                placeholder="请输入企业名称"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="统一社会信用代码" prop="unique_social_credit_code">
              <el-input
                disabled
                v-model="formLabelAlign.unique_social_credit_code"
                placeholder="请输入统一社会信用代码"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 法人信息 -->
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <div></div>
            <div>法人信息</div>
          </div>
        </template>
        <el-row :gutter="24">
          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <el-form-item label="法人身份证照片" prop="legal_id_card_back_pic">
              <div class="flex avatar-flex">
                <el-image
                  class="id_card_image"
                  v-if="isView('legal_id_card_front_pic')"
                  referrerpolicy="no-referrer"
                  :src="formLabelAlign.legal_id_card_front_pic"
                  :preview-src-list="[formLabelAlign.legal_id_card_front_pic]"
                ></el-image>

                <div
                  class="avatar-uploader"
                  v-else-if="
                    formLabelAlign.legal_id_card_front_pic &&
                    formLabelAlign.legal_id_card_front_pic != ''
                  "
                >
                  <PictureComponent
                    :imgParams="formLabelAlign.legal_id_card_front_pic"
                    imgWidth="100px"
                    imgHeight="100px"
                    @deleteImg="deleteImg('legal_id_card_front_pic')"
                  ></PictureComponent>
                </div>

                <el-upload
                  v-else
                  action="fakeaction"
                  :show-file-list="false"
                  multiple
                  accept=".jpg, .png, .jpeg, .bmp"
                  :http-request="(e) => upLoadImg(e, 'legal_id_card_front_pic')"
                >
                  <div class="avatar-uploader">
                    <img
                      v-if="formLabelAlign.legal_id_card_front_pic"
                      :src="formLabelAlign.legal_id_card_front_pic"
                      class="avatar"
                    />
                    <i
                      v-if="!formLabelAlign.legal_id_card_front_pic"
                      class="el-icon-plus avatar-uploader-icon"
                    ></i>
                    <div v-if="!formLabelAlign.legal_id_card_front_pic" class="el-upload__text">
                      上传人像面
                    </div>
                  </div>
                </el-upload>

                <!-- 身份证背面 -->
                <el-image
                  class="id_card_image"
                  style="margin: 0 10px"
                  v-if="isView('legal_id_card_back_pic')"
                  referrerpolicy="no-referrer"
                  :src="formLabelAlign.legal_id_card_back_pic"
                  :preview-src-list="[formLabelAlign.legal_id_card_back_pic]"
                ></el-image>

                <div
                  class="avatar-uploader"
                  v-else-if="
                    formLabelAlign.legal_id_card_back_pic &&
                    formLabelAlign.legal_id_card_back_pic != ''
                  "
                >
                  <PictureComponent
                    :imgParams="formLabelAlign.legal_id_card_back_pic"
                    imgWidth="100px"
                    imgHeight="100px"
                    @deleteImg="deleteImg('legal_id_card_back_pic')"
                  ></PictureComponent>
                </div>

                <el-upload
                  v-else
                  action="fakeaction"
                  :show-file-list="false"
                  multiple
                  accept=".jpg, .png, .jpeg, .bmp"
                  :http-request="(e) => upLoadImg(e, 'legal_id_card_back_pic')"
                >
                  <div class="avatar-uploader">
                    <img
                      v-if="formLabelAlign.legal_id_card_back_pic"
                      :src="formLabelAlign.legal_id_card_back_pic"
                      class="avatar"
                    />
                    <i
                      v-if="!formLabelAlign.legal_id_card_back_pic"
                      class="el-icon-plus avatar-uploader-icon"
                    ></i>
                    <div v-if="!formLabelAlign.legal_id_card_back_pic" class="el-upload__text">
                      上传国徽面
                    </div>
                  </div>
                </el-upload>
                <el-button type="text" @click="isIdcard = true">查看示例</el-button>
              </div>
              <div class="el-upload__tip">
                <div>请上传边角完整的法人身份证正反面照片，支持jpg、png格式，大小不超过5MB</div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="法人姓名" prop="legal_name">
              <el-input
                disabled
                v-model="formLabelAlign.legal_name"
                placeholder="请输入法人姓名"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="法人身份证号码" prop="legal_id_card_no">
              <el-input
                :disabled="isView('legal_id_card_no')"
                v-model="formLabelAlign.legal_id_card_no"
                placeholder="请输入法人身份证号码"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="证件有效期" prop="date" style="height: 30px">
              <el-date-picker
                :disabled="isView('legal_id_card_begin_date')"
                v-model="formLabelAlign.date"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                v-if="!legal_id_date"
              ></el-date-picker>
              <div v-if="legal_id_date" class="flex">
                <el-date-picker
                  @change="selectTime('legal_id_date1')"
                  :disabled="isView('legal_id_card_begin_date')"
                  style="width: 50%"
                  v-model="legal_id_date1"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择日期"
                ></el-date-picker>
                <span>至</span>
                <el-input disabled style="width: 50%" v-model="legal_id_date"></el-input>
              </div>
            </el-form-item>
          </el-col>
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="法人邮箱" prop="legal_email">
              <el-input
                :disabled="isView('legal_email')"
                v-model="formLabelAlign.legal_email"
                placeholder="请输入法人邮箱"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="法人手机号" prop="legal_phone">
              <el-input
                :disabled="isView('legal_phone')"
                v-model="formLabelAlign.legal_phone"
                placeholder="请输入法人手机号"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="法人应急手机号" prop="legal_optional_phone">
              <el-input
                :disabled="isView('legal_optional_phone')"
                v-model="formLabelAlign.legal_optional_phone"
                placeholder="请输入法人应急手机号"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="法人微信" prop="legal_wechat">
              <div class="ui_flex">
                <el-input
                  :disabled="isView('legal_wechat')"
                  v-model="formLabelAlign.legal_wechat"
                  placeholder="请输入法人微信"
                ></el-input>
                <el-button style="margin-left: 10px" type="text" @click="isWeChatImage = true">
                  查看示例
                </el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <div></div>
            <div>小程序基本信息</div>
          </div>
        </template>
        <el-row :gutter="24">
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="小程序名称" prop="name">
              <el-input
                :disabled="isView('name')"
                v-model="formLabelAlign.name"
                placeholder="请输入小程序名称"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="名称命名类型" prop="program_name_type">
              <el-select
                :disabled="isView('program_name_type')"
                v-model="formLabelAlign.program_name_type"
                placeholder="请选择名称命名类型"
                style="width: 100%"
              >
                <el-option
                  v-for="item in programList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            :xl="24"
            v-if="formLabelAlign.program_name_type == 2"
          >
            <el-form-item label="商标注册证书或商标授权书" prop="trademark_licensing">
              <div class="up_line">
                <el-image
                  class="business_license_pic_img"
                  v-if="isView('trademark_licensing')"
                  referrerpolicy="no-referrer"
                  :src="formLabelAlign.trademark_licensing"
                  :preview-src-list="[formLabelAlign.trademark_licensing]"
                ></el-image>

                <!-- avatar_img -->
                <div
                  class="business_license_pic_img"
                  v-else-if="
                    formLabelAlign.trademark_licensing && formLabelAlign.trademark_licensing != ''
                  "
                >
                  <PictureComponent
                    :imgParams="formLabelAlign.trademark_licensing"
                    imgWidth="360px"
                    imgHeight="180px"
                    @deleteImg="deleteImg('trademark_licensing')"
                  ></PictureComponent>
                </div>

                <div v-else>
                  <el-upload
                    class="upload-demo"
                    action="fakeaction"
                    :show-file-list="false"
                    drag
                    accept=".jpg, .png, .jpeg, .bmp"
                    :multiple="false"
                    :http-request="(e) => upLoadImage(e, 'trademark_licensing')"
                    :on-exceed="handleExceed"
                    :limit="1"
                    :on-preview="handlePreview"
                    :on-remove="handleRemove"
                  >
                    <div>
                      <i class="el-icon-upload"></i>
                      <div class="el-upload__text">
                        将文件拖到此处，或
                        <em>点击上传</em>
                      </div>
                    </div>
                    <div class="el-upload__tip" slot="tip">
                      请上传清晰的商标注册证书或商标授权书原件
                    </div>
                  </el-upload>
                  <!-- <div class="n_check" @click="isBusinessLcense = true">查看示例</div> -->
                </div>
              </div>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <el-form-item label="小程序图标" prop="logo">
              <div class="flex avatar-flex">
                <el-image
                  class="avatar border-image"
                  v-if="isView('logo')"
                  referrerpolicy="no-referrer"
                  :src="formLabelAlign.logo"
                  :preview-src-list="[formLabelAlign.logo]"
                ></el-image>

                <div
                  class="avatar-uploader"
                  v-else-if="formLabelAlign.logo && formLabelAlign.logo != ''"
                >
                  <PictureComponent
                    :imgParams="formLabelAlign.logo"
                    imgWidth="100px"
                    imgHeight="100px"
                    @deleteImg="deleteImg('logo')"
                  ></PictureComponent>
                </div>

                <el-upload
                  v-else
                  action="fakeaction"
                  :show-file-list="false"
                  multiple
                  accept=".jpg, .png, .jpeg, .bmp"
                  :http-request="(e) => upLoadImage(e, 'logo')"
                >
                  <div class="avatar-uploader">
                    <img v-if="formLabelAlign.logo" :src="formLabelAlign.logo" class="avatar" />
                    <i v-if="!formLabelAlign.logo" class="el-icon-plus avatar-uploader-icon"></i>
                    <div v-if="!formLabelAlign.logo" class="el-upload__text">上传</div>
                  </div>
                </el-upload>
              </div>
              <div class="el-upload__tip">
                <div>
                  请上传清晰的的小程序图标，支持jpg、png格式，大小不超过2MB，建议尺寸为144x144像素
                </div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :xs="20" :sm="20" :md="20" :lg="20" :xl="20">
            <el-form-item label="小程序简介" prop="introduction">
              <el-input
                type="textarea"
                :rows="3"
                placeholder="请输入小程序简介"
                :disabled="isView('introduction')"
                maxlength="120"
                show-word-limit
                v-model="formLabelAlign.introduction"
              ></el-input>
              <div class="el-upload__tip">
                <div>请简要描述小程序的主要功能和特点，不超过120字</div>
              </div>
            </el-form-item>
          </el-col>

          <!-- 主营类目 -->
          <el-col :xs="20" :sm="20" :md="20" :lg="20" :xl="20">
            <el-form-item label="主营类目" prop="category">
              <el-cascader
                :disabled="isView('category')"
                placeholder="请选择主营类目"
                style="width: 100%"
                v-model="formLabelAlign.category"
                :options="wechatCategory"
                @change="cascaderChange"
                :props="{ label: 'name', value: 'w_id' }"
              ></el-cascader>
            </el-form-item>
          </el-col>
          <!-- 实际经营内容 -->
          <el-col :xs="20" :sm="20" :md="20" :lg="20" :xl="20">
            <el-form-item label="实际经营内容" prop="icp_comment">
              <el-input
                placeholder="请输入实际经营内容"
                :disabled="isView('icp_comment')"
                maxlength="200"
                type="textarea"
                :rows="3"
                show-word-limit
                v-model="formLabelAlign.icp_comment"
              ></el-input>
              <div class="el-upload__tip">
                <div>小程序实际经营内容最少20个字</div>
              </div>
            </el-form-item>
          </el-col>
          <!-- 服务内容标识 -->
          <el-col :xs="20" :sm="20" :md="20" :lg="20" :xl="20">
            <el-form-item label="服务内容标识" prop="icp_service_category">
              <el-cascader
                ref="cascader"
                :disabled="isView('icp_service_category')"
                placeholder="请选择服务内容标识"
                style="width: 100%"
                v-model="formLabelAlign.icp_service_category"
                :options="WechatIcpService"
                @change="cascaderChange1"
                :props="{ label: 'name', value: 'type', multiple: true, emitPath: false }"
              ></el-cascader>
              <div class="el-upload__tip" v-for="(item, index) in icp_service_category_values">
                <span>{{ item.name }}：</span>
                <span>{{ item.remark ? item.remark : '' }}</span>
              </div>
            </el-form-item>
          </el-col>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            :xl="24"
            v-if="formLabelAlign.cert_list.length > 0"
          >
            <h3>经营资质</h3>
          </el-col>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            :xl="24"
            v-for="(item, index) in formLabelAlign.cert_list"
            :key="index"
          >
            <el-form-item :label="item.name" :prop="'cert_list' + index">
              <div class="up_line">
                <el-image
                  class="cert_list_image border-image"
                  referrerpolicy="no-referrer"
                  :src="item.value"
                  :preview-src-list="[item.value]"
                  v-if="isView('cert_list')"
                ></el-image>

                <div class="business_license_pic_img" v-else-if="item.value && item.value != ''">
                  <PictureComponent
                    :imgParams="item.value"
                    imgWidth="360px"
                    imgHeight="180px"
                    @deleteImg="deleteImg1(index)"
                  ></PictureComponent>
                </div>

                <el-upload
                  v-else
                  class="cert_list"
                  action="fakeaction"
                  :show-file-list="false"
                  multiple
                  drag
                  accept=".jpg, .png, .jpeg, .bmp"
                  :http-request="(e) => upLoadImage(e, 'cert_list', index)"
                >
                  <img :src="item.value" alt v-if="item.value" />
                  <div>
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">
                      将文件拖到此处，或
                      <em>点击上传</em>
                    </div>
                  </div>
                  <div class="el-upload__tip" slot="tip">
                    请上传清晰且边角完整的 {{ item.name }}原件照片，支持jpg、png格式，大小不超过5MB
                  </div>
                </el-upload>
                <div class="n_check" v-if="item.url" @click="viewCalc(item)">
                  {{ item | getShowFlag }}
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <!-- <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <div></div>
            <div>小程序管理员信息</div>
          </div>
        </template>
        <el-row :gutter="24">
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="法人作为管理员">
              <el-radio-group
                :disabled="isView('is_legal_manager')"
                v-model="formLabelAlign.is_legal_manager"
                @input="changeLegalManager"
              >
                <el-radio label="Y">是</el-radio>
                <el-radio label="N">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" v-if="formLabelAlign.is_legal_manager == 'N'">
          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <el-form-item label="管理员身份证" prop="manager_id_card_back_pic">
              <div class="flex avatar-flex">
                <el-image
                  class="avatar"
                  v-if="isView('manager_id_card_front_pic')"
                  referrerpolicy="no-referrer"
                  :src="formLabelAlign.manager_id_card_front_pic"
                  :preview-src-list="[formLabelAlign.manager_id_card_front_pic]"
                ></el-image>

                <div
                  class="avatar-uploader"
                  v-else-if="
                    formLabelAlign.manager_id_card_front_pic &&
                    formLabelAlign.manager_id_card_front_pic != ''
                  "
                >
                  <PictureComponent
                    :imgParams="formLabelAlign.manager_id_card_front_pic"
                    imgWidth="100px"
                    imgHeight="100px"
                    @deleteImg="deleteImg('manager_id_card_front_pic')"
                  ></PictureComponent>
                </div>

                <el-upload
                  v-else
                  action="fakeaction"
                  :show-file-list="false"
                  multiple
                  accept=".jpg, .png, .jpeg, .bmp"
                  :http-request="(e) => upLoadImg(e, 'manager_id_card_front_pic')"
                >
                  <div class="avatar-uploader">
                    <img
                      v-if="formLabelAlign.manager_id_card_front_pic"
                      :src="formLabelAlign.manager_id_card_front_pic"
                      class="avatar"
                    />

                    <i
                      v-if="!formLabelAlign.manager_id_card_front_pic"
                      class="el-icon-plus avatar-uploader-icon"
                    ></i>
                    <div v-if="!formLabelAlign.manager_id_card_front_pic" class="el-upload__text">
                      上传人像面
                    </div>
                  </div>
                </el-upload>

                <el-image
                  class="avatar"
                  v-if="isView('manager_id_card_back_pic')"
                  referrerpolicy="no-referrer"
                  style="margin-left: 20px; margin-right: 20px"
                  :src="formLabelAlign.manager_id_card_back_pic"
                  :preview-src-list="[formLabelAlign.manager_id_card_back_pic]"
                ></el-image>

                <div
                  class="avatar-uploader"
                  v-else-if="
                    formLabelAlign.manager_id_card_back_pic &&
                    formLabelAlign.manager_id_card_back_pic != ''
                  "
                >
                  <PictureComponent
                    :imgParams="formLabelAlign.manager_id_card_back_pic"
                    imgWidth="100px"
                    imgHeight="100px"
                    @deleteImg="deleteImg('manager_id_card_back_pic')"
                  ></PictureComponent>
                </div>

                <el-upload
                  v-else
                  action="fakeaction"
                  :show-file-list="false"
                  multiple
                  accept=".jpg, .png, .jpeg, .bmp"
                  :http-request="(e) => upLoadImg(e, 'manager_id_card_back_pic')"
                >
                  <div class="avatar-uploader">
                    <img
                      v-if="formLabelAlign.manager_id_card_back_pic"
                      :src="formLabelAlign.manager_id_card_back_pic"
                      class="avatar"
                    />

                    <i
                      v-if="!formLabelAlign.manager_id_card_back_pic"
                      class="el-icon-plus avatar-uploader-icon"
                    ></i>
                    <div v-if="!formLabelAlign.manager_id_card_back_pic" class="el-upload__text">
                      上传国徽面
                    </div>
                  </div>
                </el-upload>
                <el-button type="text" @click="isIdcard = true">查看示例</el-button>
              </div>
              <div class="el-upload__tip">
                <div>请上传管理员边角完整的身份证正反面照片，支持jpg、png格式，大小不超过5MB</div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="管理员姓名" prop="manager_name">
              <el-input
                :disabled="isView('manager_name')"
                v-model="formLabelAlign.manager_name"
                placeholder="请输入管理员姓名"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="管理员身份证号码" prop="manager_id_card_no">
              <el-input
                :disabled="isView('manager_id_card_no')"
                v-model="formLabelAlign.manager_id_card_no"
                placeholder="请输入管理员身份证号码"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="证件有效期" prop="manageDate" style="height: 30px">
              <el-date-picker
                :disabled="isView('manager_id_card_begin_date')"
                v-model="formLabelAlign.manageDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                v-if="!manager_id_date"
              ></el-date-picker>

              <div v-if="manager_id_date" class="flex">
                <el-date-picker
                  @change="selectTime('manager_id_date1')"
                  :disabled="isView('manager_id_card_begin_date')"
                  style="width: 50%"
                  v-model="manager_id_date1"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择日期"
                ></el-date-picker>
                <span>至</span>
                <el-input disabled style="width: 50%" v-model="manager_id_date"></el-input>
              </div>
            </el-form-item>
          </el-col>
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="管理员手机号" prop="manager_phone">
              <el-input
                :disabled="isView('manager_phone')"
                v-model="formLabelAlign.manager_phone"
                placeholder="请输入管理员手机号"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="管理员应急手机号" prop="manager_optional_phone">
              <el-input
                :disabled="isView('manager_optional_phone')"
                v-model="formLabelAlign.manager_optional_phone"
                placeholder="请输入管理员应急手机号"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="管理员邮箱" prop="manager_email">
              <el-input
                :disabled="isView('manager_email')"
                v-model="formLabelAlign.manager_email"
                placeholder="请输入管理员邮箱"
              ></el-input>
            </el-form-item>
          </el-col>
      </el-row>-->
      <!-- <el-form-item
          label="小程序负责人授权书"
          prop="principal_authorization"
          v-if="isView('principal_authorization')"
        >
          <el-image
            class="authorization border-image"
            referrerpolicy="no-referrer"
            :src="formLabelAlign.principal_authorization"
            :preview-src-list="[formLabelAlign.principal_authorization]"
          ></el-image>
        </el-form-item>
        <el-form-item label="小程序负责人授权书" prop="principal_authorization" v-else>
          <div class="up_line">
            <el-upload
              class="upload-demo"
              action="fakeaction"
              :multiple="false"
              drag
              accept=".jpg, .png, .jpeg, .bmp"
              :http-request="(e)=>upLoadImage(e,'principal_authorization')"
              :on-exceed="handleExceed"
              :limit="1"
              :file-list="fileList"
              :on-preview="handlePreview"
              :on-remove="handleRemove"
            >
              <div>
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">
                  将文件拖到此处，或
                  <em>点击上传</em>
                </div>
              </div>
              <div class="el-upload__tip" slot="tip">请上传小程序负责人授权书，支持pdf、jpg、png格式，大小不超过10MB</div>
            </el-upload>
          </div>
          <div>
            <i class="el-icon-download" style="color:#0071FE"></i>
            <el-button type="text">下载授权书模板</el-button>
          </div>
      </el-form-item>-->

      <!-- <el-form-item label="小程序负责人授权书" prop="principal_authorization">
          <div class="up_line">
            <el-image
              class="business_license_pic_img"
              v-if="isView('principal_authorization')"
              referrerpolicy="no-referrer"
              :src="formLabelAlign.principal_authorization"
              :preview-src-list="[formLabelAlign.principal_authorization]"
            ></el-image>

            <div
              class="business_license_pic_img"
              v-else-if="
                formLabelAlign.principal_authorization &&
                formLabelAlign.principal_authorization != ''
              "
            >
              <PictureComponent
                :imgParams="formLabelAlign.principal_authorization"
                imgWidth="360px"
                imgHeight="180px"
                @deleteImg="deleteImg('principal_authorization')"
              ></PictureComponent>
            </div>

            <div v-else>
              <el-upload
                class="upload-demo"
                action="fakeaction"
                :show-file-list="false"
                drag
                accept=".jpg, .png, .jpeg, .bmp"
                :multiple="false"
                :http-request="(e) => upLoadImage(e, 'principal_authorization')"
                :on-exceed="handleExceed"
                :limit="1"
                :on-preview="handlePreview"
                :on-remove="handleRemove"
              >
                <div>
                  <i class="el-icon-upload"></i>
                  <div class="el-upload__text">
                    将文件拖到此处，或
                    <em>点击上传</em>
                  </div>
                </div>
                <div class="el-upload__tip" slot="tip">
                  请上传小程序负责人授权书，支持pdf、jpg、png格式，大小不超过10MB
                </div>
              </el-upload>
            </div>
            <div>
              <i class="el-icon-download" style="color: #0071fe"></i>
              <el-button @click="uploadTemplate" type="text">下载授权书模板</el-button>
            </div>
          </div>
        </el-form-item>
      </el-card>-->
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <div></div>
            <div>发票信息</div>
          </div>
        </template>
        <el-row :gutter="24">
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12">
            <el-form-item label="发票类型" prop="invoice_type">
              <el-select
                v-model="formLabelAlign.invoice_type"
                placeholder="请选择发票类型"
                style="width: 100%"
                :disabled="isView('invoice_type')"
              >
                <el-option
                  v-for="item in invoiceList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col
            :xs="12"
            :sm="12"
            :md="12"
            :lg="12"
            :xl="12"
            v-if="formLabelAlign.invoice_type == 2 || formLabelAlign.invoice_type == 3"
          >
            <el-form-item label="纳税人识别号" prop="invoice_taxpayer_identification_number">
              <el-input
                v-model="formLabelAlign.invoice_taxpayer_identification_number"
                placeholder="请输入纳税人识别号"
                :disabled="isView('invoice_taxpayer_identification_number')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :xs="12"
            :sm="12"
            :md="12"
            :lg="12"
            :xl="12"
            v-if="formLabelAlign.invoice_type == 3"
          >
            <el-form-item label="企业电话" prop="invoice_company_telephone">
              <el-input
                :disabled="isView('invoice_company_telephone')"
                v-model="formLabelAlign.invoice_company_telephone"
                placeholder="请输入企业电话"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :xs="12"
            :sm="12"
            :md="12"
            :lg="12"
            :xl="12"
            v-if="formLabelAlign.invoice_type == 3"
          >
            <el-form-item label="企业注册地址" prop="invoice_company_register_address">
              <el-input
                :disabled="isView('invoice_company_register_address')"
                v-model="formLabelAlign.invoice_company_register_address"
                placeholder="请输入企业注册地址"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :xs="12"
            :sm="12"
            :md="12"
            :lg="12"
            :xl="12"
            v-if="formLabelAlign.invoice_type == 3"
          >
            <el-form-item label="企业开户银行">
              <el-input
                :disabled="isView('invoice_company_bank')"
                v-model="formLabelAlign.invoice_company_bank"
                placeholder="请输入企业开户银行"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :xs="12"
            :sm="12"
            :md="12"
            :lg="12"
            :xl="12"
            v-if="formLabelAlign.invoice_type == 3"
          >
            <el-form-item label="企业银行账号">
              <el-input
                :disabled="isView('invoice_company_account')"
                v-model="formLabelAlign.invoice_company_account"
                placeholder="请输入企业银行账号"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            :xl="24"
            v-if="formLabelAlign.invoice_type == 2 || formLabelAlign.invoice_type == 3"
          >
            <el-form-item label="发票备注">
              <el-input
                :disabled="isView('invoice_remark')"
                v-model="formLabelAlign.invoice_remark"
                type="textarea"
                placeholder="请输入发票备注"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item>
          <div class="ui_spane_height"></div>
        </el-form-item>
      </el-card>
    </el-form>

    <div class="ui_spane flex-c">
      <el-button v-if="isSaveDraft" @click="backSelect">返回选择</el-button>
      <el-button
        v-if="formLabelAlign.is_again_submit"
        type="primary"
        @click="submitForm('formName')"
      >
        提交
      </el-button>
      <el-button v-if="isSaveDraft" type="primary" @click="saveDraft">保存草稿</el-button>
    </div>

    <el-dialog
      :title="calc_item.name"
      :show-close="false"
      :visible.sync="isCalc"
      :close-on-click-modal="false"
    >
      <template slot="title">
        <div style="display: flex; justify-content: space-between">
          <span>{{ calc_item.name }}</span>
          <span
            @click="isCalc = false"
            style="color: rgb(144, 147, 153); font-size: 14px; cursor: pointer"
          >
            X
          </span>
        </div>
      </template>
      <el-image
        class="wechat-image"
        referrerpolicy="no-referrer"
        :src="calc_item.url"
        :preview-src-list="[calc_item.url]"
      ></el-image>
    </el-dialog>
    <el-dialog
      title="小程序负责人授权书"
      :visible.sync="isAuthorization"
      :close-on-click-modal="false"
    >
      <img class="wechat-image" :src="formLabelAlign.principal_authorization" alt />
    </el-dialog>
    <el-dialog title="微信示例" :visible.sync="isWeChatImage" :close-on-click-modal="false">
      <img class="wechat-image" :src="imgOssPath + '20250517_sf_wechat.png'" alt />
    </el-dialog>
    <el-dialog title="营业执照示例" :visible.sync="isBusinessLcense" :close-on-click-modal="false">
      <img class="business-lcense" src="@/assets/enteroruse/20250306_id_lcense.png" alt />
    </el-dialog>
    <el-dialog title="身份证示例" :visible.sync="isIdcard" :close-on-click-modal="false">
      <div class="id-card-model">
        <div>
          <img src="@/assets/enteroruse/20250306_id_card1.png" alt />
          <div>身份证人像面</div>
        </div>
        <div>
          <img src="@/assets/enteroruse/20250306_id_card2.png" alt />
          <div>身份证国徽面</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {
    create,
    ocrProcess,
    saveDraft,
    getWechatCategoryApi,
    editProgramApi,
    getWechatIcpServiceContentTypes,
    getRegionTree,
  } from '@/api/miniMaageApi'
  export default {
    props: {
      detailInfo: {
        type: Object, // 定义数据类型，可以是String, Number, Object等
      },
    },
    watch: {
      detailInfo: {
        handler(val) {
          console.log(val)
          this.formLabelAlign = Object.assign({}, this.formLabelAlign, val)
          // 授权书图片展示
          if (val.principal_authorization) {
            this.fileList = [
              {
                name: this.getImageName(val.principal_authorization),
                url: val.principal_authorization,
              },
            ]
          }
          if (val.legal_id_card_begin_date && val.legal_id_card_end_date) {
            this.formLabelAlign.date = [val.legal_id_card_begin_date, val.legal_id_card_end_date]
          }
          // 设置可编辑项
          if (val.updateFields) {
            this.updateFields = val.updateFields
          }
          this.legal_id_date =
            this.formLabelAlign.date &&
            (this.formLabelAlign.date[1].split('-').length == 3 ? '' : '长期')
          this.legal_id_date1 =
            this.formLabelAlign.date &&
            (this.formLabelAlign.date[1].split('-').length == 3 ? '' : this.formLabelAlign.date[0])
          // this.manager_id_date =
          //   this.formLabelAlign.manageDate &&
          //   (this.formLabelAlign.manageDate[1].split('-').length == 3 ? '' : '长期')
          // this.manager_id_date1 =
          //   this.formLabelAlign.manageDate &&
          //   (this.formLabelAlign.manageDate[1].split('-').length == 3
          //     ? ''
          //     : this.formLabelAlign.manageDate[0])
          if (this.formLabelAlign.province) {
            this.formLabelAlign.addressList = [
              this.formLabelAlign.province,
              this.formLabelAlign.city,
              this.formLabelAlign.district,
            ]
          }
        },
        deep: true,
        immediate: true,
      },
      'formLabelAlign.icp_service_category': {
        handler(newVal, oldval) {
          if (newVal.length > 5) {
            this.$message.error('最多选择5个服务内容标识')
            this.$nextTick(() => {
              this.formLabelAlign.icp_service_category = oldval // [...oldval].reverse()
              this.getCategoryValues(this.formLabelAlign.icp_service_category)
            })
            // this.$refs.cascader.panel.clearCheckedNodes()
          } else {
            this.getCategoryValues(this.formLabelAlign.icp_service_category)
          }
        },
        deep: true,
      },
    },
    data() {
      let validatePhone = (rule, value, callback, title) => {
        if ('' == value) {
          callback(new Error(title + '不能为空'))
        } else if (!/^1\d{10}$/.test(value)) {
          callback(new Error('请填写正确的' + title))
        } else {
          callback()
        }
      }
      let validateEmail = (rule, value, callback, title) => {
        if ('' == value) {
          callback(new Error(title + '不能为空'))
        } else if (!/^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/.test(value)) {
          callback(new Error('请填写正确的' + title))
        } else {
          callback()
        }
      }
      let invoice_number = (rule, value, callback, title) => {
        if ('' == value) {
          callback(new Error(title + '不能为空'))
        } else if (![15, 17, 18, 20].includes(value.length)) {
          callback(new Error('请填写正确的' + title))
        } else {
          callback()
        }
      }
      return {
        formLabelAlign: {
          // https://qst-static-resource.oss-cn-shenzhen.aliyuncs.com/storage/2025032710182867e4b5749e32a.png
          business_license_pic: '',
          business_license_address: '',
          legal_id_card_front_pic: '',
          legal_id_card_back_pic: '',
          company_name: '',
          unique_social_credit_code: '',
          legal_name: '',
          legal_id_card_no: '',
          date: '',
          legal_email: '',
          legal_phone: '',
          legal_optional_phone: '',
          legal_wechat: '',
          name: '',
          program_name_type: undefined,
          manager_name: '',
          manager_id_card_no: '',
          logo: '',
          introduction: '',
          category: [],
          icp_service_category: [],
          icp_comment: '',
          legal_id_card_begin_date: '',
          legal_id_card_end_date: '',
          cert_list: [],
          manager_id_card_begin_date: '',
          manager_id_card_end_date: '',
          trademark_licensing: '',
          is_legal_manager: 'Y',
          manager_id_card_back_pic: '',
          manager_id_card_front_pic: '',
          manager_phone: '',
          principal_authorization: '',
          invoice_taxpayer_identification_number: '',
          invoice_company_telephone: '',
        },
        rules: {
          business_license_pic: [{ required: true, message: '请上传营业执照', trigger: 'blur' }],
          company_name: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
          address: [{ required: true, message: '请输入备案所在详细地址', trigger: 'blur' }],
          province: [{ required: true, message: '请输入备案地址', trigger: 'change' }],
          unique_social_credit_code: [
            { required: true, message: '请输入统一社会信用代码', trigger: 'change' },
          ],
          legal_id_card_back_pic: [
            {
              required: true,
              validator: (rule, value, callback) => {
                if (
                  this.formLabelAlign.legal_id_card_front_pic &&
                  this.formLabelAlign.legal_id_card_back_pic
                ) {
                  callback()
                } else {
                  return callback(new Error('请上传身份证两面照片'))
                }
              },
              trigger: 'change',
            },
          ],
          legal_name: [{ required: true, message: '请输入法人姓名', trigger: 'blur' }],
          legal_id_card_no: [{ required: true, message: '请输入法人身份证号码', trigger: 'blur' }],
          date: [{ required: true, message: '请选择证件有效期', trigger: 'blur' }],
          legal_email: [
            {
              required: true,
              validator: (rule, value, callback) =>
                validateEmail(rule, value, callback, '法人邮箱'),
              trigger: 'blur',
            },
          ],
          legal_phone: [
            {
              required: true,
              validator: (rule, value, callback) =>
                validatePhone(rule, value, callback, '法人手机号'),
              trigger: 'blur',
            },
          ],
          legal_optional_phone: [
            {
              required: true,
              validator: (rule, value, callback) =>
                validatePhone(rule, value, callback, '法人应急手机号'),
              trigger: 'blur',
            },
          ],
          legal_wechat: [{ required: true, message: '请输入法人微信', trigger: 'blur' }],
          name: [{ required: true, message: '请输入小程序名称', trigger: 'blur' }],
          program_name_type: [{ required: true, message: '请选择名称命名类型', trigger: 'change' }],
          logo: [
            {
              required: true,
              validator: (rule, value, callback) => {
                if (this.formLabelAlign.logo) {
                  callback()
                } else {
                  return callback(new Error('请上传小程序图标'))
                }
              },
              trigger: 'change',
            },
          ],
          introduction: [
            { required: true, message: '请输入小程序简介', trigger: 'blur' },
            { min: 4, max: 120, message: '小程序简介最少4个字', trigger: 'blur' }, // 设置最小长度为20
          ],
          category: [{ required: true, message: '请选择主营类目', trigger: 'change' }],
          icp_service_category: [
            { required: true, message: '请选择服务内容标识', trigger: 'change' },
          ],
          icp_comment: [
            { required: true, message: '请输入实际经营内容', trigger: 'blur' },
            { min: 20, max: 200, message: '小程序实际经营内容最少20个字', trigger: 'blur' }, // 设置最小长度为20
          ],
          manager_id_card_back_pic: [
            {
              required: true,
              validator: (rule, value, callback) => {
                if (
                  this.formLabelAlign.manager_id_card_back_pic &&
                  this.formLabelAlign.manager_id_card_front_pic
                ) {
                  callback()
                } else {
                  return callback(new Error('请上传管理员身份证身份证两面照片'))
                }
              },
              trigger: 'change',
            },
          ],
          manager_name: [{ required: true, message: '请输入管理员姓名', trigger: 'blur' }],
          manager_id_card_no: [
            { required: true, message: '请输入管理员身份证号码', trigger: 'blur' },
          ],
          manageDate: [{ required: true, message: '请选择管理员证件有效期', trigger: 'blur' }],
          manager_phone: [
            {
              required: true,
              trigger: 'blur',
              validator: (rule, value, callback) =>
                validatePhone(rule, value, callback, '管理员手机号'),
            },
          ],
          manager_optional_phone: [
            {
              required: true,
              trigger: 'blur',
              validator: (rule, value, callback) =>
                validatePhone(rule, value, callback, '管理员应急手机号'),
            },
          ],
          manager_email: [
            {
              required: true,
              validator: (rule, value, callback) =>
                validateEmail(rule, value, callback, '管理员邮箱'),
              trigger: 'blur',
            },
          ],
          principal_authorization: [
            {
              required: true,
              validator: (rule, value, callback) => {
                if (this.formLabelAlign.principal_authorization) {
                  callback()
                } else {
                  return callback(new Error('请上传小程序负责人授权书'))
                }
              },
              trigger: 'change',
            },
          ],
          trademark_licensing: [
            {
              required: true,
              validator: (rule, value, callback) => {
                if (this.formLabelAlign.trademark_licensing) {
                  callback()
                } else {
                  return callback(new Error('请上传商标注册证书或商标授权书'))
                }
              },
              trigger: 'change',
            },
          ],
          invoice_type: [{ required: true, message: '请选择发票类型', trigger: 'change' }],

          invoice_taxpayer_identification_number: [
            {
              required: true,
              validator: (rule, value, callback) =>
                invoice_number(rule, value, callback, '纳税人识别号'),
              trigger: 'blur',
            },
          ],
          invoice_company_telephone: [
            { required: true, message: '请输入企业电话', trigger: 'blur' },
          ],
          invoice_company_register_address: [
            { required: true, message: '请输入企业注册地址', trigger: 'blur' },
          ],
        },
        invoiceList: [
          {
            name: '不开发票',
            id: 1,
          },
          {
            name: '电子发票',
            id: 2,
          },
          {
            name: '增值税专票(数电类型)',
            id: 3,
          },
        ],
        categoryList: [
          {
            name: '餐饮服务',
            id: 1,
          },
          {
            name: '零售门店',
            id: 2,
          },
        ],
        programList: [
          {
            name: '基于自选词汇命名',
            id: 1,
          },
          {
            name: '基于商标命名',
            id: 2,
          },
        ],

        fileList: [],
        options: [],
        updateFields: {},

        // 微信示例
        isWeChatImage: false,
        // 身份证示例
        isIdcard: false,
        // 营业执照示例
        isBusinessLcense: false,
        // 授权书预览
        isAuthorization: false,
        // 预览项
        isCalc: false,
        calc_item: {},

        // 主营类目
        wechatCategory: [],
        // 地址列表
        regionTreeAddress: [],
        //小程序服务标识
        WechatIcpService: [],
        // 选择后主营类目后的上传数据
        wechatCategoryData: [],
        //长期身份证
        legal_id_date: '',
        legal_id_date1: '',
        manager_id_date1: '',
        manager_id_date: '',
        icp_service_category_values: [], //服务内容标识
      }
    },
    created() {},
    mounted() {
      // if (this.detailInfo.id) {
      //   this.formLabelAlign = this.detailInfo
      //   this.updateFields = this.detailInfo.updateFields
      // }
      this.getWechatCategoryFn()
      this.getRegionTreeFn()
      this.getWechatIcpServiceContentTypesFn()
    },
    computed: {
      // 是否只查看
      isView() {
        return function (key) {
          return this.formLabelAlign.id ? !this.updateFields[key] : false
        }
      },

      // 是否提交过
      isSaveDraft() {
        return !this.formLabelAlign.id
      },
    },
    filters: {
      getShowFlag(item) {
        let urlLength = item.file_type && item.file_type.split('/').length
        let falgTip = item.file_type && item.file_type.split('/')[urlLength - 1]
        if (['jpg', 'png', 'jpeg', 'bmp'].includes(falgTip)) {
          return '查看示例'
        } else {
          return '下载模板'
        }
      },
    },
    methods: {
      // 获取微信类目
      getWechatCategoryFn() {
        getWechatCategoryApi().then((res) => {
          if (res.code == 200) {
            this.wechatCategory = res.data
          }
        })
      },
      // 获取微信地址
      getRegionTreeFn() {
        getRegionTree().then((res) => {
          if (res.code == 200) {
            this.regionTreeAddress = res.data
          }
        })
      },
      // 获取小程序服务内容展示
      getWechatIcpServiceContentTypesFn() {
        getWechatIcpServiceContentTypes().then((res) => {
          if (res.code == 200) {
            this.WechatIcpService = res.data
            this.getCategoryValues(this.formLabelAlign.icp_service_category || [])
          }
        })
      },
      getImageName(imagePath) {
        // 使用正则表达式匹配文件名和扩展名
        var match = imagePath.match(/([^/?#]+\.[^.]+)$/)
        if (match) {
          // 返回匹配结果，但不包括扩展名
          return match[0]
        }
        return '' // 如果没有找到匹配项，返回空字符串或适当的错误处理
      },

      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePreview(file) {
        this.isAuthorization = true
      },
      backSelect() {
        this.$emit('backSelect')
      },
      deleteImg(key) {
        this.formLabelAlign[key] = ''
      },
      deleteImg1(index) {
        this.formLabelAlign.cert_list[index].value = ''
      },
      // 预览图片
      viewCalc(item) {
        if (['image/png', 'image/jpeg', 'image/jpg'].includes(item.file_type)) {
          this.isCalc = true
          this.calc_item = item
        } else {
          window.open(item.url)
        }
      },

      // 上传图片
      upLoadImage(file, key, i) {
        // 上传
        this.$upLoadImg(file.file).then((res) => {
          // 上传完执行的操作
          if (key == 'cert_list') {
            this.$set(this.formLabelAlign.cert_list[i], 'value', res.data.url)
            this.$set(this.formLabelAlign.cert_list[i], 'file_type', res.data.extension)
            this.$refs.formName.validateField('cert_list' + i)
            return
          }
          this.formLabelAlign[key] = res.data.url
          this.$refs.formName.validateField(key)
        })
      },
      /**
       * 上传图片并处理图片信息
       *
       * @param file 上传的文件对象
       * @param key 图片对应的字段名
       */
      upLoadImg(file, key) {
        const reader = new FileReader()
        reader.readAsDataURL(file.file)
        reader.onload = () => {
          //  将文件转换为Base64编码的字符串
          const base64String = reader.result

          // 显示图片的示例方法
          switch (
            key // 根据上传的图片类型执行不同的操作
          ) {
            // 营业执照
            case 'business_license_pic':
              this.getOcrProcess(key, base64String, '0')
              break
            // 法人身份证正面
            case 'legal_id_card_front_pic':
              this.getOcrProcess(key, base64String, '1')
              break
            // 法人身份证反面
            case 'legal_id_card_back_pic':
              this.getOcrProcess(key, base64String, '1')
              break

            // 管理员身份证正面
            case 'manager_id_card_front_pic':
              this.getOcrProcess(key, base64String, '1')
              break

            // 管理员身份证正面
            case 'manager_id_card_back_pic':
              this.getOcrProcess(key, base64String, '1')
              break
          }
        }
      },

      /**
       * 获取OCR处理结果
       *
       * @param {string} type OCR处理类型
       * @param {File} img 待处理的图片文件
       * @returns {Promise<any>} 返回OCR处理结果
       */
      async getOcrProcess(type, img, i) {
        let res = await ocrProcess({ type: i, image: img })
        if (res.code != 200) {
          return
        }
        this.formLabelAlign[type] = res.data.image.url
        switch (type) {
          // 营业执照
          case 'business_license_pic':
            let params = res.data.ocr_business.content.result
            this.formLabelAlign.company_name = params.name
            this.formLabelAlign.unique_social_credit_code = params.registration_number
            this.formLabelAlign.business_license_address = params.address //ocr识别出来的address地址赋值给business_license_address

            this.$refs.formName.validateField(['company_name', 'unique_social_credit_code'])
            break

          // 法人身份证反面和正面
          case 'legal_id_card_front_pic':
            let data = res.data.ocr_id_card
            this.formLabelAlign.legal_name = data.name
            this.formLabelAlign.legal_id_card_no = data.idNumber
            if (!data.name || !data.idNumber) {
              this.formLabelAlign.legal_id_card_front_pic = ''
              this.$message.error('请上传身份证人像面')
              return
            }
            this.$refs.formName.validateField(['legal_name', 'legal_id_card_no'])
            break
          case 'legal_id_card_back_pic':
            let data1 = res.data.ocr_id_card
            if (data1.validPeriod) {
              this.formLabelAlign.date = data1.validPeriod.split('-')
              this.formLabelAlign.date = this.formLabelAlign.date.map((res) => {
                return res.replaceAll('.', '-')
              })
              this.formLabelAlign.date[1].split('-').length == 3
                ? (this.legal_id_date = '')
                : (this.legal_id_date1 = this.formLabelAlign.date[0])
              this.formLabelAlign.date[1].split('-').length == 3
                ? (this.legal_id_date = '')
                : (this.legal_id_date = '长期')
              console.log(this.formLabelAlign.date)
              this.$refs.formName.validateField('date')
            } else {
              this.formLabelAlign.legal_id_card_back_pic = ''
              this.formLabelAlign.date = ''
              this.legal_id_date = ''
              this.$message.error('请上传身份证国徽面')
              return
            }
            break

          // 管理员身份证正面
          case 'manager_id_card_front_pic':
            let data2 = res.data.ocr_id_card
            this.formLabelAlign.manager_name = data2.name
            this.formLabelAlign.manager_id_card_no = data2.idNumber
            if (!data2.name || !data2.idNumber) {
              this.formLabelAlign.manager_id_card_front_pic = ''
              this.$message.error('请上传身份证人像面')
              return
            }
            this.$refs.formName.validateField(['manager_id_card_no', 'manager_name'])
            break
          // 管理员身份证国徽
          case 'manager_id_card_back_pic':
            console.log(res)
            let data3 = res.data.ocr_id_card
            if (data3.validPeriod) {
              this.formLabelAlign.manageDate = data3.validPeriod.split('-')
              this.formLabelAlign.manageDate = this.formLabelAlign.manageDate.map((res) => {
                return res.replaceAll('.', '-')
              })

              this.formLabelAlign.manageDate[1].split('-').length == 3
                ? (this.manager_id_date = '')
                : (this.manager_id_date1 = this.formLabelAlign.date[0])
              this.formLabelAlign.manageDate[1].split('-').length == 3
                ? (this.manager_id_date = '')
                : (this.manager_id_date = '长期')
              this.$refs.formName.validateField('manageDate')
            } else {
              this.formLabelAlign.manager_id_card_back_pic = ''
              this.formLabelAlign.manageDate = ''
              this.manager_id_date = ''
              this.$message.error('请上传身份证国徽面')
              return
            }
            break
        }
        this.$refs.formName.validateField(type)
        console.log('-=-=-=-=-=-=-=-')
        console.log(type)
      },

      /**
       * 处理文件上传超过限制的处理函数
       *
       * @param files 当前选择的文件列表
       * @param fileList 组件内部维护的文件列表
       */
      handleExceed(files, fileList) {
        console.log(fileList[0].raw)
        this.fileList = [files[0]] // 只保留最新的文件
        this.upLoadImage({ file: fileList[0].raw }, 'principal_authorization')
      },
      //选择服务内容标识
      cascaderChange1(value) {
        // if (value.length > 5) {
        //   this.$message.error('最多选择5个服务内容标识')
        //   this.formLabelAlign.icp_service_category = value.splice(0, 5)
        //   this.getCategoryValues(this.formLabelAlign.icp_service_category)
        //   return
        // } else {
        //   this.getCategoryValues(value)
        // }
      },
      // 树形数据扁平化
      treeToFlat(data) {
        let ary = []
        data.forEach((item) => {
          if (item.children) {
            ary.push(item)
            if (item.children.length > 0) {
              ary.push(...this.treeToFlat(item.children))
            }
          } else {
            ary.push(item)
          }
          delete item.children
        })
        return ary
      },
      //得到服务标识备注
      getCategoryValues(value) {
        let newValue = typeof value == 'string' ? value.split(',') : value
        this.formLabelAlign.icp_service_category =
          typeof value == 'string' ? value.split(',') : value

        let values = []
        newValue.forEach((item, index) => {
          this.WechatIcpService.forEach((iitem) => {
            if (iitem.children) {
              let parentvalues = iitem.children.filter((it) => it.type == item)
              if (parentvalues.length) {
                values.push(parentvalues[0])
              }
            }
          })
        })
        console.log(values)

        this.icp_service_category_values = values
      },
      // 选择微信地址
      cascaderAddressChange(value) {
        if (value.length == 3) {
          this.formLabelAlign.province = value[0]
          this.formLabelAlign.city = value[1]
          this.formLabelAlign.district = value[2]
        }
        console.log('微信地址', this.formLabelAlign)
      },
      // 选择主营类目
      cascaderChange(value) {
        let wechatCategoryData = []
        let values = {}
        value.map((item, index) => {
          if (index == 0) {
            values = this.wechatCategory.filter((it) => it.w_id == item)[0]
          } else {
            values = values.children.filter((it) => it.w_id == item)[0]
          }
          if (index == value.length - 1) {
            wechatCategoryData = values.exter_list || []
          }
        })

        let cert_list_rule = {}
        wechatCategoryData.map((it, i) => {
          console.log(it, i)
          cert_list_rule[`cert_list${i}`] = [
            {
              required: values.is_sensitive == 'Y',
              validator: (rule, value, callback) => {
                console.log(it, i)
                if (it.value && it.value != '') {
                  callback()
                } else {
                  return callback(new Error('请上传' + it.name))
                }
              },
              trigger: 'change',
            },
          ]
        })

        this.formLabelAlign.cert_list = wechatCategoryData

        this.rules = {
          ...this.rules,
          ...cert_list_rule,
        }

        this.$nextTick((err) => {
          let kyes = Object.keys(cert_list_rule)
          console.log(kyes)
          for (let i = 0; i < kyes.length; i++) {
            this.$refs.formName.clearValidate(kyes[i])
          }
        })
      },

      /**
       * 提交表单
       *
       * @param {string} formName 表单名称
       */
      submitForm(formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            // 法人就是管理员信息一致，否则分开填写
            if (this.formLabelAlign.is_legal_manager == 'Y') {
              // this.formLabelAlign.manager_name = this.formLabelAlign.legal_name
              // this.formLabelAlign.manager_id_card_no = this.formLabelAlign.legal_id_card_no
              // this.formLabelAlign.manageDate = this.formLabelAlign.date
              // this.formLabelAlign.manager_phone = this.formLabelAlign.legal_phone
              // this.formLabelAlign.manager_optional_phone = this.formLabelAlign.legal_optional_phone
              // this.formLabelAlign.manager_email = this.formLabelAlign.legal_email
            }
            // 法人有效期
            if (this.formLabelAlign.date && this.formLabelAlign.date.length == 2) {
              this.formLabelAlign.legal_id_card_begin_date = this.formLabelAlign.date[0]
              this.formLabelAlign.legal_id_card_end_date = this.formLabelAlign.date[1]
            }
            // 管理员有效期
            // if (this.formLabelAlign.manageDate.length == 2) {
            //   this.formLabelAlign.manager_id_card_begin_date = this.formLabelAlign.manageDate[0]
            //   this.formLabelAlign.manager_id_card_end_date = this.formLabelAlign.manageDate[1]
            // }
            let params = this.formLabelAlign
            let api = this.formLabelAlign.id ? editProgramApi : create
            api(params).then((res) => {
              if (res.code == 200) {
                console.log(res)
                this.$emit('refreshInit', 1)
                this.$message({
                  type: 'success',
                  message: '提交成功!',
                })
              }
            })
          } else {
            return false
          }
        })
      },
      // 保存草稿
      async saveDraft() {
        let params = this.formLabelAlign
        let res = await saveDraft(params)
        if (res.code == 200) {
          this.$message({
            type: 'success',
            message: '保存成功!',
          })
        }
      },
      //法人作为管理员
      changeLegalManager(val) {
        console.log(val)
        if (val == 'N') {
          this.formLabelAlign.manager_id_card_front_pic = ''
          this.formLabelAlign.manager_id_card_back_pic = ''
          this.formLabelAlign.manager_name = ''
          this.formLabelAlign.manager_id_card_no = ''
          // this.formLabelAlign.manageDate = ''
          this.formLabelAlign.manager_phone = ''
          this.formLabelAlign.manager_optional_phone = ''
          this.formLabelAlign.manager_email = ''
          this.formLabelAlign.cert_list = []
        }
      },
      //下载授权书
      uploadTemplate() {
        // window.open()
      },
      // 选择身份证日期
      selectTime(code) {
        if (code == 'legal_id_date1') {
          this.formLabelAlign.date = [this[code], '长期']
        }
        // if (code == 'manager_id_date1') {
        //   this.formLabelAlign.manageDate = [this[code], '长期']
        // }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .min-manager img {
    object-fit: contain;
  }

  // 预览使用 start
  .min-manager ::v-deep .el-form-item__content {
    line-height: 10px !important;
  }

  // 提示整体样式
  .min-manager ::v-deep .el-upload__tip {
    font-size: 12px;
    line-height: 20px;
    color: #606266;
    margin-top: 0px;
  }

  // 营业执照图片样式
  .business_license_pic_img {
    width: 360px;
    height: 180px;
    border: 1px dashed #dcdfe6;
  }
  .business_license_pic_img ::v-deep img {
    object-fit: contain;
  }

  .border-image {
    border: 1px dashed #dcdfe6;
  }

  // 身份证图片样式
  .id_card_image {
    width: 100px;
    height: 100px;
    border: 1px dashed #dcdfe6;
  }

  // 授权书 图
  .authorization ::v-deep img {
    object-fit: contain !important;
    width: 360px;
    height: 180px;
  }
  // 上传类目图片样式
  .cert_list img {
    object-fit: contain !important;
    width: 360px;
    height: 180px;
  }
  .cert_list_image {
    width: 360px;
    height: 180px;
  }
  .cert_list_image ::v-deep img {
    object-fit: contain !important;
  }

  // 查看事例
  .n_check {
    transform: translate(380px, -46px);
    cursor: pointer;
    font-size: 12px;
    color: #0071fe;
    line-height: 12px;
  }

  // 微信照片
  .wechat-image {
    width: 400px;
    object-fit: cover;
    display: block;
    margin: 0 auto;
  }
  // 营业执照示例图片样式
  .business-lcense {
    width: 400px;
    height: 272px;
    display: block;
    margin: 0 auto;
  }
  // 身份证示例图片样式
  .id-card-model {
    display: flex;
    justify-content: space-between;
    text-align: center;
    img {
      width: 200px;
      height: 112px;
      margin-right: 24px;
    }
    div {
      font-weight: 400;
      font-size: 14px;
      color: #8c8c8c;
      line-height: 14px;
      margin-top: 15px;
    }
  }

  // 预览使用 end

  .ui_flex {
    display: flex;
  }

  .avatar-flex {
    align-items: flex-end;
  }
  .avatar-uploader {
    margin-right: 24px;
    width: 100px;
    height: 100px;
    border: 1px dashed #dcdfe6;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    text-align: center;
    &:hover {
      border-color: #409eff;
    }
    .el-upload {
      display: block;
    }

    .el-upload__text {
      font-weight: 400;
      font-size: 12px;
      color: #606266;
      line-height: 17px;
    }
  }
  .logo-uploader {
    z-index: 10;
  }
  .avatar-uploader-icon {
    font-size: 15px;
    color: #8c939d;
    line-height: 20px;
    text-align: center;
    margin: 32px auto 0px;
  }

  .avatar {
    width: 100px;
    height: 100px;
    display: block;
  }
  .avatar ::v-deep img {
    object-fit: contain !important;
  }
  .avatar_img {
    display: block;
    width: 360px;
    height: 180px;
  }
  .ui_spane {
    position: fixed;
    height: 80px;
    bottom: 0;
    width: calc(#{$base-right-content-width} - 40px);
    background: #fff;
    right: 20px;
  }
  .ui_spane_height {
    height: 80px;
  }
</style>
