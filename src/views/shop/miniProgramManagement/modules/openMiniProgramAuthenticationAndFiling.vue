<template>
  <div class>
    <el-card v-loading="loading">
      <div class="m10" v-for="(item,index) in detailInfo.icp_array" :key="index">
        {{index + 1}}. {{item.title}}
        <span
          :class="item.status == '1' ? 'n_success' : item.status == '2' ? 'n_error' : ''"
        >{{item.status_text}}</span>

        <el-button
          v-if="index == 0 && detailInfo.is_show_face_btn"
          type="primary"
          @click="reashAuth"
          size="mini"
          style="margin-left: 10px;"
        >重新认证</el-button>

        <div v-if="index == 0">人脸核身视频会用于生成核验照片，背景需白色或接近浅色，核验人不能裸露上半身，不能戴帽子、耳机、口罩等遮挡面部的物品。</div>

        <div v-if="item.status == '2'">
          <div class="error_div flex" v-if="item.reason != ''">
            <i class="el-icon-warning" style="color: #FF4D4D;"></i>
            <div>{{item.reason}}</div>
          </div>
          <div v-else-if="item.reason_array.length > 0">
            <h3>验证结果</h3>
            <div class="error-info">
              <p v-for="items,indexs in item.reason_array" :key="indexs">{{items}}</p>
              <!-- <p>请跳转到「补全认证资料」步骤进行修改。</p> -->
            </div>
          </div>
        </div>

        <qrcode :ids="index" v-if="item.url && item.status != '1'" :url="item.url"></qrcode>
      </div>

      <!-- <div class="m10" v-if="detailInfo.face_status == '1'">
        2. 发起小程序认证及备案
        <span class="n_success" v-if="detailInfo.auth_reason.length == 0">成功</span>
        <span class="n_error" v-if="detailInfo.auth_reason.length != 0">失败</span>
        <div v-if="detailInfo.auth_reason.length != 0">
          <h3>验证结果</h3>
          <div class="error-info">
            <p v-for="item,index in detailInfo.auth_reason" :key="index">{{item}}</p>
            <p>请跳转到「补全认证资料」步骤进行修改。</p>
          </div>
        </div>
      </div>

      <div class="m10" v-if="detailInfo.auth_reason.length == 0 && detailInfo.face_status == '1'">
        3. 支付认证费用缴纳
        <span class="n_success" v-if="detailInfo.is_pay_auth == 'Y'">成功</span>
        <qrcode
          :ids="2"
          v-if="detailInfo.is_pay_auth == 'N' && detailInfo.pay_url"
          :url="detailInfo.pay_url"
        ></qrcode>
      </div>

      <div class="m10" v-if="detailInfo.is_auth_filing == 'Y'">4.{{detailInfo.icp_auth_status_text}}</div>-->
    </el-card>
  </div>
</template>
<script>
import qrcode from '@/components/Qrcode/qrcode.vue'
import { createIcpVerifyTaskApi } from '@/api/miniMaageApi.js'
export default {
  props: {
    detailInfo: {
      type: Object, // 定义数据类型，可以是String, Number, Object等
    },
  },
  components: {
    qrcode,
  },
  computed: {
    name_checked() {
      return this.detailInfo.set_name_status == '1'
    },
    category_status() {
      return this.detailInfo.set_category_status == '1'
    },
  },
  data() {
    return {
      icp_auth_status: {
        0: '未开始认证备案',
        16: '完成审核费用支付',
        17: '审核派单成功',
        18: '小程序认证失败打回重填',
        19: '认证审核通过认证成功',
        20: '认证最终失败事件',
        21: '创建备案审核单失败事件',
        22: '备案审核派单成功',
        23: '备案审核平台驳回',
        24: '备案备案管局审核',
        25: '备案备案管局驳回',
        26: '认证及备案完成',
      },
      timeOut: null,
      loading: false,
    }
  },
  created() {},
  mounted() {},
  methods: {
    reashAuth() {
      this.loading = true
      createIcpVerifyTaskApi().then((res) => {
        if (res.code == 200) {
          this.$emit('afershFn')
        }
        this.loading = false
      })
    },
  },
}
</script>
<style lang='scss' scoped>
.n_success {
  color: #0071fe;
}
.n_error {
  color: red;
}
.n_review {
  color: #fe9c41;
}
.ui_spane {
  display: flex;
  justify-content: center;
}
.m10 + .m10 {
  margin-top: 10px;
}

.error_div {
  border-radius: 10px;
  border: 1px solid #ffefef;
  margin-top: 10px;
  align-items: flex-start;
  width: 346px;
  padding: 10px 20px 10px 10px;
  background: #ffefef;
  font-weight: 400;
  font-size: 14px;
  color: #ff4d4d;
  line-height: 20px;
  .el-icon-warning {
    margin-right: 2px;
    margin-top: 2px;
  }
}

.error-info {
  min-height: 100px;
  padding: 10px;
  border: 1px dashed #ccc;
  border-radius: 8px;
}
</style>