<template>
  <div class v-visibility-change="visibility">
    <el-card class="box-card" v-loading="isLoading">
      <template #header>
        <div class="ui_tab">
          <div :class="['tab_content', isActive == 1 ? 'tab_active' : '']" @click="tabChange(1)">
            <p>小程序接入</p>
            <p>授权或注册小程序</p>
          </div>
          <img class="tab_img" src="../../../assets/mini/rgt_20250320.png" alt />
          <div :class="['tab_content', isActive == 2 ? 'tab_active' : '']" @click="tabChange(2)">
            <p>代码管理</p>
            <p>管理小程序代码</p>
          </div>
          <img class="tab_img" src="../../../assets/mini/rgt_20250320.png" alt />
          <div :class="['tab_content', isActive == 3 ? 'tab_active' : '']" @click="tabChange(3)">
            <p>商户号申请</p>
            <p>开通支付商户号</p>
          </div>
        </div>
      </template>

      <!-- 注册成功 -->
      <div v-if="isMiniSuccess && isActive == 1">
        <el-descriptions title="已授权小程序" :column="2" border>
          <el-descriptions-item
            label="小程序名称"
            label-class-name="my-label"
            content-class-name="my-content"
          >{{miniInfo.name}}</el-descriptions-item>
          <el-descriptions-item label="小程序ID">{{miniInfo.app_id}}</el-descriptions-item>
          <el-descriptions-item label="主体名称">{{miniInfo.main_body}}</el-descriptions-item>
          <el-descriptions-item label="授权时间">
            <el-tag size="small">{{miniInfo.created_at}}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="授权状态">
            <el-tag size="small" v-if="miniInfo.status == 1">有效</el-tag>
            <el-tag type="danger" v-if="miniInfo.status == 2">无效</el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div v-if="isInitPage">
        <div class="n_tips">
          <img class="tip_icon" :src="imgOssPath + '20250517_sf_tip.png'" alt />
          <div>
            <div class="tip_title">小程序接入说明</div>
            <div class="tip_name">
              根据微信要求，需要授权小程序才可以发布哦，请点击【授权小程序】
              扫码授权。如提示未授权请重新点击发起授权。
            </div>
          </div>
        </div>

        <div class="box_content">
          <div class="n_box n_rgt">
            <img class="box_img" :src="imgOssPath + '20250517_sf_code.png'" alt />
            <div class="box_title">授权已有小程序</div>
            <div class="box_name">如果您已经拥有小程序，请选择此 选项进行授权接入</div>
            <el-button class="n_btn" type="primary" size="mini" @click="startAuth">开始授权</el-button>
          </div>
          <div class="n_box">
            <img class="box_img" :src="imgOssPath + '20250517_sf_link.png'" alt />
            <div class="box_title">快速注册小程序</div>
            <div class="box_name">如果您还没有小程序，可以选择 选择快速注册</div>
            <el-button
              class="n_btn"
              type="primary"
              size="mini"
              @click="selectOnClick('register')"
            >开始注册</el-button>
          </div>
        </div>
      </div>

      <!-- 代码管理模块 -->
      <codeManagementModules v-if="isActive == 2"></codeManagementModules>
      <!-- 商户号开通模块 -->
      <openPaymentModules v-if="isActive == 3" />

      <el-steps
        :active="step"
        finish-status="success"
        style="margin-top: 20px"
        v-if="isActive == 1 && isType == 'register'"
      >
        <el-step>
          <template #title>
            <span class="select-step" @click="stepViewClick(0)">补全认证资料</span>
          </template>
        </el-step>
        <el-step>
          <template #title>
            <span :class="{ 'select-step': step >= 1 }" @click="stepViewClick(1)">法人人脸识别</span>
          </template>
        </el-step>
        <el-step>
          <template #title>
            <span :class="{ 'select-step': step >= 2 }" @click="stepViewClick(2)">微信侧主体验证</span>
          </template>
        </el-step>
        <el-step>
          <template #title>
            <span :class="{ 'select-step': step >= 3 }" @click="stepViewClick(3)">小程序认证及备案</span>
          </template>
        </el-step>
        <el-step title>
          <template #title>
            <span :class="{ 'select-step': step == 4 }" @click="stepViewClick(4)">注册完成</span>
          </template>
        </el-step>
      </el-steps>
    </el-card>

    <!-- 未小程序注册 -->
    <div v-if="isType == 'register' && isActive == 1">
      <!-- 法人信息录入模块 start -->
      <openMinManagerModule
        :detailInfo="detailInfo"
        v-if="stepView >= 0 ? stepView == 0 : step == 0"
        @refreshInit="getProgramDetail"
        @backSelect="backSelect"
      ></openMinManagerModule>
      <!-- 法人信息录入模块 end-->

      <!-- 法人人脸识别模块 start-->
      <openFaceRecognitionModules
        :detailInfo="detailInfo"
        @afershFn="getProgramDetail"
        class="vue"
        v-if="stepView >= 0 ? stepView == 1 : step == 1"
      ></openFaceRecognitionModules>
      <!-- 法人人脸识别模块 end-->

      <!-- 微信侧主体验证模块 start-->
      <openMiniVerifyModules
        class="vue"
        :detailInfo="detailInfo"
        v-if="stepView >= 0 ? stepView == 2 : step == 2"
      ></openMiniVerifyModules>
      <!-- 微信侧主体验证模块 end-->

      <!-- 小程序认证及备案模块 start-->
      <openMiniProgramAuthenticationAndFiling
        class="vue"
        :detailInfo="detailInfo"
        @afershFn="getProgramDetail"
        v-if="stepView >= 0 ? stepView == 3 : step == 3"
      ></openMiniProgramAuthenticationAndFiling>

      <!-- 小程序认证及备案模块 end-->
      <!-- 注册成功模块 start-->
      <openMiniSuccessModules
        @miniSuccess="miniSuccess"
        v-if="stepView >= 0 ? stepView == 4 : step == 4"
      ></openMiniSuccessModules>
      <!-- 注册成功模块 end-->
    </div>

    <!-- 代码管理列表模块 start-->
    <codeManageListModules v-if="isActive == 2"></codeManageListModules>
    <!-- 代码管理列表模块 end-->

    <!-- 商户号开通模块 start-->
    <openPaymentMerchant @reclose="recloseBtn" v-if="isActive == 3" />
    <!-- 商户号开通模块 end-->
  </div>
</template>

<script>
import {
  getProgramDetail,
  getPreAuthorizationUrlApi,
  componentPreAuthorizationApi,
  getMerchantWxDetailApi,
} from '@/api/miniMaageApi.js'
import { getCompanyInfoApi } from '@/api/enterpriseManagement'
import openMinManagerModule from './modules/openMinManagerModule.vue'
import openMiniVerifyModules from './modules/openMiniVerifyModules.vue'
import openFaceRecognitionModules from './modules/openFaceRecognitionModules.vue'
import openMiniProgramAuthenticationAndFiling from './modules/openMiniProgramAuthenticationAndFiling.vue'
import openMiniSuccessModules from './modules/openMiniSuccessModules.vue'

import codeManagementModules from './modules/codeManagementModules.vue'
import codeManageListModules from './modules/codeManageListModules.vue'

import openPaymentMerchant from './modules/openPaymentMerchantInfo/openPaymentMerchant.vue'
import openPaymentModules from './modules/openPaymentMerchantInfo/openPaymentModules.vue'
import visibility from 'vue-visibility-change'

export default {
  name: 'minManagement',
  components: {
    openMinManagerModule,
    openMiniVerifyModules,
    openFaceRecognitionModules,
    openMiniProgramAuthenticationAndFiling,
    openMiniSuccessModules,
    codeManagementModules,
    codeManageListModules,
    openPaymentMerchant,
    openPaymentModules,
  },
  data() {
    return {
      // 选择项
      isActive: 1,
      // 步骤 第一步补全认证资料 第二步 微信侧主体验证 第三步 法人人脸识别 第四部 小程序认证及备案 第五步 注册完成
      step: 0,
      // 查看已完成的步骤
      stepView: -1,
      // 小程序注册完成
      isMiniSuccess: false,
      // 小程序开通流程
      isType: '',
      // 开通基本信息
      detailInfo: {},
      // 加载状态
      isLoading: true,
      // 小程序信息
      miniInfo: {},
    }
  },
  created() {
    this.getProgramDetail()
    this.timeOutFn()
    this.getAuth()
  },
  computed: {
    isInitPage() {
      return (
        // 栏目一 步骤1 无状态 加载完成 未授权成功
        this.isActive == 1 &&
        this.step == 0 &&
        this.isType == '' &&
        !this.isLoading &&
        !this.isMiniSuccess
      )
    },
  },
  watch: {
    isActive() {
      if (this.isActive == 1) {
        this.getProgramDetail()
      }
    },
    isMiniSuccess() {
      if (this.isMiniSuccess) {
        this.timeOut && clearInterval(this.timeOut)
      }
    },
  },
  beforeDestroy() {
    this.timeOut && clearInterval(this.timeOut)
  },
  methods: {
    visibility(et, hidden) {
      if (hidden === false) {
        this.getProgramDetail()
      }
    },
    // 第四步时需要10秒获取状态 -- 计时器更新状态
    timeOutFn() {
      let _this = this
      this.timeOut = setInterval(function () {
        getProgramDetail().then((res) => {
          if (res.code == 200) {
            if (res.data.step == 5) {
              _this.timeOutSuccess()
              clearInterval(_this.timeOut)
            } else if (res.data.step > 0) {
              // 开通流程 开通中
              this.isType = res.data.step > 1 ? 'register' : ''
              this.step = Number(res.data.step) - 1
            }
          }
        })
      }, 5000)
    },

    // 在线申请开通
    selectOnClick() {
      this.isType = 'register'
    },
    // 跳转已有小程序
    startAuth() {
      getPreAuthorizationUrlApi().then((res) => {
        if (res.code == 200) {
          window.open(res.data.url)
        }
      })
    },

    // 获取授权信息 -- 回调
    getAuth() {
      let query = this.$route.query
      if (query.auth_code) {
        componentPreAuthorizationApi({
          auth_code: query.auth_code,
        }).thne((res) => {
          if (res.code == 200) {
            this.getProgramDetail()
          }
        })
      }
    },

    // 查看步骤切换
    stepViewClick(e) {
      if (e <= this.step) {
        this.stepView = e
      }
    },

    /**
     * 获取注册详情
     *
     * @returns Promise<void> 无返回值
     */
    async getProgramDetail(init) {
      let res = await getProgramDetail()
      if (res.code == 200) {
        this.isLoading = false
        // 开通流程 开通中
        if (res.data.step != 5 && res.data.step) {
          this.isActive = 1
          this.isType = res.data.step > 1 ? 'register' : ''
          this.step = Number(res.data.step) - 1
        }
        if (init == 1) {
          this.stepView = -1
        }
        // 开通完成
        this.isMiniSuccess = res.data.step == 5

        this.detailInfo = res.data
        this.getCompanyInfo()
        this.getMerchantWxDetail()
      }
    },

    // 获取授权信息的接口
    getMerchantWxDetail() {
      getMerchantWxDetailApi().then((res) => {
        if (res.code == 200) {
          this.miniInfo = res.data
          this.isMiniSuccess = !!res.data.app_id
        }
      })
    },

    // 获取公司信息接口
    getCompanyInfo() {
      console.log(11)
      getCompanyInfoApi().then((res) => {
        if (res.code == 200) {
          let str = [
            { code: 'business_license_pic', code1: 'license' },
            { code: 'company_name', code1: 'organization_name' },
            { code: 'unique_social_credit_code', code1: 'organization_code' },
            { code: 'legal_id_card_front_pic', code1: 'legal_card_front' },
            { code: 'legal_id_card_back_pic', code1: 'legal_card_back' },
            { code: 'legal_name', code1: 'legal_name' },
            { code: 'legal_id_card_no', code1: 'legal_code' },
            { code: 'business_license_address', code1: 'address' },
          ]
          str.forEach((item) => {
            this.detailInfo[item.code] = !this.detailInfo[item.code]
              ? res.data[item.code1]
              : this.detailInfo[item.code]
          })
          // this.detailInfo.business_license_pic = res.data.license
          // this.detailInfo.company_name = res.data.organization_name
          // this.detailInfo.unique_social_credit_code = res.data.organization_code
          // this.detailInfo.legal_id_card_front_pic = res.data.legal_card_front
          // this.detailInfo.legal_id_card_back_pic = res.data.legal_card_back
          // this.detailInfo.legal_name = res.data.legal_name
          // this.detailInfo.legal_id_card_no = res.data.legal_code
        }
      })
    },

    backSelect() {
      this.isType = ''
      this.step = 0
      this.getProgramDetail()
    },
    tabChange(value) {
      this.isActive = value
    },

    recloseBtn() {
      this.isActive = 1
    },

    // 第四步 完成
    timeOutSuccess() {
      this.step = 4
      this.stepView = 4
    },
    // 完成注册后进入代码管理页面
    miniSuccess() {
      this.isMiniSuccess = true
      this.isActive = 2
    },
  },
}
</script>

<style lang="scss" scoped>
// 头部
.box-card ::v-deep {
  .el-card__header {
    padding: 14px 0 0 30px;
  }
}

// 选择项
.select-step {
  cursor: pointer;
}

.ui_tab {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e8e8e8;
  cursor: pointer;
  .tab_content {
    height: 50px;
    width: 120px;
    color: #a8abb2;
    font-size: 14px;
    line-height: 22px;

    &.tab_active {
      position: relative;
      color: #0071fe !important;
      &::after {
        position: absolute;
        bottom: 0px;
        content: '';
        width: 120px;
        height: 1px;
        background: #0071fe;
      }
    }
  }
  .tab_img {
    display: block;
    width: 16px;
    height: 16px;
    margin: 0 30px 0 9px;
  }
}
p {
  margin: 0;
  padding: 0;
}

.n_tips {
  display: flex;
  align-items: center;
  padding: 18px 20px;
  background: #eef6ff;
  border-radius: 8px;
  border: 1px solid #94c3ff;
  .tip_icon {
    display: block;
    width: 24px;
    height: 24px;
    margin-right: 10px;
  }
  .tip_title {
    font-size: 16px;
    color: #222222;
    line-height: 16px;
    font-weight: 600;
  }
  .tip_name {
    font-size: 14px;
    color: #666666;
    line-height: 20px;
    margin-top: 8px;
  }
}
.box_content {
  display: flex;
  justify-content: center;
  margin-top: 60px;
  .n_rgt {
    margin-right: 40px;
  }
  .n_box {
    width: 240px;
    height: 240px;
    background: #ffffff;
    border-radius: 10px;
    border: 1px solid #dcdfe6;
    box-sizing: border-box;
    padding: 0 17px 0 13px;
    .box_img {
      display: block;
      width: 32px;
      height: 32px;
      margin: 40px auto 10px;
    }
    .box_title {
      font-weight: 600;
      font-size: 16px;
      color: #222222;
      line-height: 16px;
      text-align: center;
    }
    .box_name {
      font-size: 14px;
      color: #666666;
      line-height: 20px;
      text-align: center;
      margin-top: 8px;
    }
    .n_btn {
      display: block;
      margin: 20px auto 0;
    }
  }
}
.ui_title {
  text-align: center;
}
.dia_img {
  display: block;
  margin: 0 auto;
  width: 120px;
  height: 120px;
}
.dia_name {
  text-align: center;
  font-size: 14px;
  color: #32363a;
  line-height: 14px;
}
</style>
