<template>
  <div>
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div></div>
          <div>基础设置</div>
        </div>
      </template>
      <el-form ref="form" :model="form" label-position="top" :rules="formRule">
        <el-form-item prop="is_open_comment">
          <template slot="label">
            开启评价
            <el-tooltip
              class="item"
              effect="dark"
              content="开启后用户完成订单后可对商品进行评价，商家审核通过后可展示在商品评价信息中默认开启"
              placement="top"
            >
              <img
                :src="imgOssPath + '20250619_sf_wenhao.png'"
                alt
                class="icon_wenhao"
                style="transform: translateY(2px)"
              />
            </el-tooltip>
          </template>
          <div class="flex">
            <el-switch
              @change="changeStatus(form)"
              active-value="Y"
              inactive-value="N"
              v-model="form.is_open_comment"
            ></el-switch>
            <span>{{ form.is_open_comment == 'Y' ? '开启' : '关闭' }}</span>
          </div>
        </el-form-item>
        <el-form-item prop="comment_limit">
          <template slot="label">
            订单完成
            <el-input-number
              :disabled="form.is_open_comment == 'N'"
              v-model="form.comment_limit"
              :controls="false"
              :min="1"
              placeholder="请输入天数"
              style="margin: 0 5px"
            />
            天以内允许评价
            <el-tooltip
              class="item"
              effect="dark"
              content="开启后用户完成订单后可对商品进行评价，商家审核通过后可展示在商品评价信息中默认开启"
              placement="top"
            >
              <img
                :src="imgOssPath + '20250619_sf_wenhao.png'"
                alt
                class="icon_wenhao"
                style="transform: translateY(2px)"
              />
            </el-tooltip>
          </template>
        </el-form-item>
        <el-form-item>
          <template slot="label" prop="is_auto_comment">
            自动评价
            <el-tooltip
              class="item"
              effect="dark"
              content="开启后，系统将自动为订单生成评价内容"
              placement="top"
            >
              <img
                :src="imgOssPath + '20250619_sf_wenhao.png'"
                alt
                class="icon_wenhao"
                style="transform: translateY(2px)"
              />
            </el-tooltip>
          </template>
          <div class="flex">
            <el-switch
              @change="changeStatus(form)"
              active-value="Y"
              inactive-value="N"
              v-model="form.is_auto_comment"
            ></el-switch>
            <span>{{ form.is_auto_comment == 'Y' ? '开启' : '关闭' }}</span>
          </div>
        </el-form-item>

        <el-form-item prop="auto_comment_content" v-if="form.is_auto_comment == 'Y'">
          <template slot="label">
            自定义评价内容
            <el-tooltip
              class="item"
              effect="dark"
              content="请输入自动评价内容，如：商品质量很好，物流速度快，服务态度佳，推荐购买！"
              placement="top"
            >
              <img
                :src="imgOssPath + '20250619_sf_wenhao.png'"
                alt
                class="icon_wenhao"
                style="transform: translateY(2px)"
              />
            </el-tooltip>
          </template>
          <el-input
            type="textarea"
            :rows="2"
            placeholder="请输入自动评价内容，如：商品质量很好，物流速度快，服务态度佳，推荐购买！"
            v-model="form.auto_comment_content"
            limit="200"
          ></el-input>
        </el-form-item>

        <el-form-item>
          <template slot="label" prop="is_auto_approved_pass">
            自动审核通过
            <el-tooltip
              class="item"
              effect="dark"
              content="开启后，符合条件的评价将自动通过审核，无需人工处理"
              placement="top"
            >
              <img
                :src="imgOssPath + '20250619_sf_wenhao.png'"
                alt
                class="icon_wenhao"
                style="transform: translateY(2px)"
              />
            </el-tooltip>
          </template>
          <div class="flex">
            <el-switch
              @change="changeStatus(form)"
              active-value="Y"
              inactive-value="N"
              v-model="form.is_auto_approved_pass"
            ></el-switch>
            <span>{{ form.is_auto_approved_pass == 'Y' ? '开启' : '关闭' }}</span>
          </div>
        </el-form-item>
        <el-form-item prop="radio">
          <template slot="label">
            自动审核规则
            <el-tooltip
              class="item"
              effect="dark"
              content="仅对修改后新生成的订单有效，仅针对于修改时未完成付款及新订单生效"
              placement="top"
            >
              <img
                :src="imgOssPath + '20250619_sf_wenhao.png'"
                alt
                class="icon_wenhao"
                style="transform: translateY(2px)"
              />
            </el-tooltip>
          </template>
          <template>
            <div class="lastchange flex">
              <div class="lastli">
                <el-radio
                  :disabled="form.is_auto_approved_pass == 'N'"
                  v-model="form.auto_approved_role"
                  label="1"
                >
                  全部自动通过审核
                </el-radio>
                <span class="tip">所有评价都将自动通过审核</span>
              </div>
              <div class="lastli">
                <el-radio
                  :disabled="form.is_auto_approved_pass == 'N'"
                  v-model="form.auto_approved_role"
                  label="2"
                >
                  评分超过指定分数自动通过审核
                </el-radio>
                <span class="tip">只有评分达到设定标准的评价才会自动通过</span>
              </div>
            </div>
          </template>
        </el-form-item>
        <el-form-item prop="auto_pass_threshold" v-if="form.auto_approved_role == 2">
          <template slot="label">
            自动通过评分阈值
            <el-tooltip
              class="item"
              effect="dark"
              content="仅对修改后新生成的订单有效，仅针对于修改时未完成付款及新订单生效"
              placement="top"
            >
              <img
                :src="imgOssPath + '20250619_sf_wenhao.png'"
                alt
                class="icon_wenhao"
                style="transform: translateY(2px)"
              />
            </el-tooltip>
          </template>
          <el-select v-model="form.auto_pass_threshold" placeholder="请选择" class="w100">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <div v-if="form.auto_approved_role == 1">
          <BaseContentTip
            tip="温馨提示：开启全部自动通过后，所有用户评价都将立即显示在商品页面，请谨慎使用。建议定期检查自动通过的评价内容，确保符合法律法规要求，避免不实、违规或侵权信息出现。"
          />
        </div>
        <div v-if="form.auto_approved_role == 2">
          <BaseContentTip
            :tip="`温馨提示：开启评分自动通过后，评分达到${form.auto_pass_threshold}分及以上的评价将自动通过审核，低于此分数的评价仍需人工审核。建议定期检查自动通过的评价内容，确保符合法律法规要求，避免不实、违规或侵权信息出现。`"
          />
        </div>
      </el-form>
    </el-card>
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div></div>
          <div>店铺通用配置</div>
        </div>
      </template>
      <el-form ref="shopForm" :model="shopForm" label-position="top" :rules="shopRule">
        <el-form-item prop="delivery_rules">
          <template slot="label">快递多计费方式计算规则</template>
          <template>
            <div class="flex">
              <el-radio v-model="shopForm.delivery_rules" label="1">组合运费</el-radio>
              <el-radio v-model="shopForm.delivery_rules" label="2">叠加运费</el-radio>
              <el-button @click="showImagesDialog" type="text">图示说明</el-button>
            </div>
          </template>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div></div>
          <div>物流设置</div>
        </div>
      </template>

      <el-form ref="basicConfig" :model="basicConfig" label-position="top" :rules="basicInfoRule">
        <el-form-item label="配送方式" prop="delivery_methods">
          <el-checkbox-group v-model="basicConfig.delivery_methods">
            <el-checkbox label="express">快递发货</el-checkbox>
            <el-checkbox label="pickup">自提</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item
          label="运费设置"
          prop="shipping_fee_type"
          v-if="basicConfig.delivery_methods && basicConfig.delivery_methods.includes('express')"
        >
          <el-radio-group v-model="basicConfig.shipping_fee_type">
            <el-radio label="free">包邮</el-radio>
            <el-radio label="fixed">固定运费</el-radio>
            <el-radio label="template">使用运费模板</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          label="固定运费"
          prop="fixed_fee"
          v-if="basicConfig.shipping_fee_type == 'fixed'"
        >
          <el-input
            type="text"
            @blur="handleNNDInput($event, 'fixed_fee')"
            v-model="basicConfig.fixed_fee"
            placeholder="请输入固定运费"
          >
            <template slot="prepend">￥</template>
          </el-input>
        </el-form-item>

        <el-form-item
          label="运费模板"
          prop="delivery_id"
          v-if="basicConfig.shipping_fee_type == 'template'"
        >
          <div class="flex">
            <el-select
              v-model="basicConfig.delivery_id"
              style="width: 100%"
              placeholder="请选择运费模板"
            >
              <el-option
                v-for="item in datailTemplateList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
            <el-tooltip
              class="item"
              effect="dark"
              :disabled="basicInfo.is_open_comment != 'Y'"
              content="该门店已被禁用暂不能添加运费模版"
              placement="top-start"
            >
              <el-button
                type="text"
                :disabled="basicInfo.is_open_comment == 'Y'"
                style="margin-left: 15px"
                @click="addTemplate"
                icon="el-icon-plus"
              >
                新建模版
              </el-button>
            </el-tooltip>
          </div>
        </el-form-item>

        <el-form-item
          label="自提点"
          prop="local_id"
          v-if="basicConfig.delivery_methods.includes('pickup')"
        >
          <el-select
            v-model="basicConfig.local_id"
            style="width: 100%"
            placeholder="请选择自提点"
            @change="selectLocal"
          >
            <el-option
              v-for="item in addressList"
              :key="item.id"
              :label="item.address"
              :value="item.id"
            >
              <template>
                <div class="selectdiv">
                  <span class="top">
                    {{ item.address }}
                    <!-- <span class="spanstyle" v-if="item.is_default == 'Y'">{{ item.is_default == 'Y' ? '默认' : '' }}</span> -->
                  </span>
                  <span class="bottom">联系人:{{ item.manage_name }}({{ item.phone }})</span>
                </div>
              </template>
            </el-option>
          </el-select>
        </el-form-item>
        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-form-item>
            <el-button @click="clearSet">重置</el-button>
            <el-button type="primary" @click="saveSetDetail">保存配置</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-card>
    <BaseImagesDialog ref="imagesDialog" />
  </div>
</template>

<script>
  import { getBaseSettingApi, saveBaseSettingApi } from '@/api/shop/basicSettings.js'
  import { getdeliveryList } from '@/api/shop/goodsSet/freightTemplate'
  import { getaddressList } from '@/api/enterpriseManagement/storeAddress'
  import { getmaparrList } from '@/utils/index'

  export default {
    name: 'basicSettings',
    data() {
      return {
        id: '',
        addressList: [],
        form: {
          // is_open_comment: 'N',
          // is_auto_comment: 'N',
          // is_auto_approved_pass: 'N',
          // auto_pass_threshold: '1',
          // auto_approved_role: '',
        },
        shopForm: {
          // delivery_rules: '',
        },
        basicInfo: {},
        basicConfig: {
          delivery_methods: [],
          delivery_methods_name: [], //
          shipping_fee_type: '',
          fixed_fee: '', // 固定运费
          delivery_id: '', // 运费模板
          local_id: '', // 自提点
          takes: [], //自提点选项
        },
        shopRule: {
          delivery_rules: [
            { required: true, message: '请选择快递多计费方式计算规则', trigger: 'change' },
          ],
        },
        basicInfoRule: {
          delivery_methods: [{ required: true, message: '请选择配送方式', trigger: 'change' }],
          shipping_fee_type: [{ required: true, message: '请选择运费设置', trigger: 'change' }],
          fixed_fee: [
            { required: true, message: '请输入固定运费', trigger: 'blur' },
            {
              required: true,
              validator: (rule, value, callback) => {
                if (value > 0) {
                  callback()
                } else {
                  return callback(new Error('输入必须大于0'))
                }
              },
              trigger: 'blur',
            },
          ],
          delivery_id: [{ required: true, message: '请选择运费模板', trigger: 'change' }],
          local_id: [{ required: true, message: '请选择自提点', trigger: 'change' }],
        },

        formRule: {
          is_open_comment: [{ required: true, message: '请选择开启评价', trigger: 'change' }],
          comment_limit: [{ required: true, message: '请输入订单天数', trigger: 'blur' }],
          auto_comment_content: [
            { required: true, message: '请输入自定义评价内容', trigger: 'blur' },
          ],
          auto_approved_role: [
            { required: true, message: '请选择自动审核规则', trigger: 'change' },
          ],
          auto_pass_threshold: [
            { required: true, message: '请选择自动通过评分阈值', trigger: 'change' },
          ],
        },
        // 评分
        options: [
          {
            label: '1',
            value: '1',
          },
          {
            label: '1.5',
            value: '1.5',
          },
          {
            label: '2',
            value: '2',
          },
          {
            label: '2.5',
            value: '2.5',
          },
          {
            label: '3',
            value: '3',
          },
          {
            label: '3.5',
            value: '3.5',
          },
          {
            label: '4',
            value: '4',
          },
          {
            label: '4.5',
            value: '4.5',
          },
          {
            label: '5',
            value: '5',
          },
        ],
        datailTemplateList: [], //门店运费模版
        shop_id: '',
      }
    },
    methods: {
      changeStatus() {
        if (this.form.is_open_comment == 'N') {
          this.form.is_auto_comment = 'N'
          this.form.is_auto_approved_pass = 'N'
        }
      },
      getDeliveryTemplete() {
        getdeliveryList({ shop_id: this.shop_id, limit: 100, page: 1 }).then((res) => {
          if (res.code == 200) {
            this.datailTemplateList = res.data.list
          }
        })
      },
      //自提地址
      getaddressListApi() {
        getaddressList({ type: 1, limit: 100, page: 1 }).then((res) => {
          if (res.code == 200) {
            this.addressList = res.data.list
          }
        })
      },
      selectLocal(val) {
        let currentList = getmaparrList(this.addressList, 'id', val)
        let selectJson = {
          id: currentList[0].id || '',
          address: currentList[0].address,
          manage_name: currentList[0].manage_name,
          contact_phone: currentList[0].phone,
        }
        this.basicConfig.takes = [selectJson]
        console.log(this.basicConfig.takes)
      },
      addTemplate() {
        this.$router.push({
          path: '/goodsSet/freightTemplate',
          query: {},
        })
      },
      handleNNDInput(e, code) {
        let val = e.target.value
        if (isNaN(val) || val < 0) {
          this.basicConfig[code] = ''
        } else {
          this.basicConfig[code] = Number(val).toFixed(2)
        }
      },
      // 获取详情数据
      getSetDetail() {
        getBaseSettingApi().then((res) => {
          if (res.code == 200) {
            this.id = res.data.id
            this.form = {
              is_open_comment: res.data.is_open_comment,
              is_auto_comment: res.data.is_auto_comment,
              is_auto_approved_pass: res.data.is_auto_approved_pass,
              auto_pass_threshold: res.data.auto_pass_threshold,
              auto_approved_role: res.data.auto_approved_role.toString(),
              comment_limit: res.data.comment_limit,
              unpaid_timeout: res.data.unpaid_timeout,
              auto_comment_content: res.data.auto_comment_content,
              allow_edit_price: res.data.allow_edit_price,
              price_edit_scope: res.data.price_edit_scope,
            }
            this.shopForm = {
              delivery_rules: res.data.delivery_rules.toString(),
            }
            this.basicConfig = {
              delivery_methods: res.data.delivery_methods,
              delivery_methods_name: res.data.delivery_methods_name, //
              shipping_fee_type: res.data.shipping_fee_type,
              fixed_fee: res.data.fixed_fee, // 固定运费
              delivery_id: res.data.delivery_id, // 运费模板
              local_id: res.data.local_id, // 自提点
              takes: res.data.takes, //自提点选项
            }
            this.$nextTick(() => {
              this.$refs.form.clearValidate()
            })
          }
        })
      },
      clearSet() {
        this.$refs.form.resetFields()
        this.$refs.shopForm.resetFields()
        this.$refs.basicConfig.resetFields()
      },
      // 保存设置
      saveSetDetail() {
        let isOk = true

        this.$refs.form.validate((valid) => {
          if (!valid) {
            isOk = false
          }
        })
        this.$refs.shopForm.validate((valid) => {
          if (!valid) {
            isOk = false
          }
        })
        this.$refs.basicConfig.validate((valid) => {
          if (!valid) {
            isOk = false
          }
        })

        if (!isOk) return

        let params = {
          id: this.id,
          ...this.form,
          ...this.shopForm,
          ...this.basicConfig,
        }
        saveBaseSettingApi(params).then((res) => {
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: '保存成功!',
            })
          }
        })
      },
      showImagesDialog() {
        this.$refs.imagesDialog.open()
      },
    },
    created() {
      this.getSetDetail()
      this.getDeliveryTemplete()
      this.getaddressListApi()
    },
    mounted() {},
  }
</script>

<style lang="scss" scoped>
  .w100 {
    width: 100%;
  }
  .w100 ::v-deep input {
    text-align: left !important;
  }
  .no-bottom {
    margin-bottom: 0 !important;
    font-weight: 600;
    font-size: 18px;
  }
  .lastchange {
    .lastli {
      display: flex;
      flex-direction: column;
      margin-right: 50px;
      .tip {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #8c8c8c;
        margin-left: 30px;
      }
    }
  }
</style>
