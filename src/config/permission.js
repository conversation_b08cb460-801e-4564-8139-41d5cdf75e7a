/*
 * @Author: shangfei <EMAIL>
 * @Date: 2025-03-11 18:19:33
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-03-25 16:08:33
 * @FilePath: /qst-merchant-admin-2.0/src/config/permission.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * 
 * @description 路由守卫，目前两种模式：all模式与intelligence模式
 */
import router from '@/router'
import store from '@/store'
import VabProgress from 'nprogress'
import 'nprogress/nprogress.css'
import getPageTitle from '@/utils/pageTitle'
import { authentication, loginInterception, progressBar, recordRoute, routesWhiteList } from '@/config'

VabProgress.configure({
  easing: 'ease',
  speed: 500,
  trickleSpeed: 200,
  showSpinner: false,
})
router.beforeResolve(async (to, from, next) => {
  if (progressBar) VabProgress.start()
  let hasToken = store.getters['user/accessToken']

  if (!loginInterception) hasToken = true

  if (hasToken) {
    if (to.path === '/login') {
      next({ path: '/' })
      if (progressBar) VabProgress.done()
    } else {
      const hasPermissions = store.getters['user/permissions'] && store.getters['user/permissions'].length > 0
      if (hasPermissions) {
        next()
      } else {
        try {
          // if (!loginInterception) {
          //   //settings.js loginInterception为false时，创建虚拟权限
          //   await store.dispatch('user/setPermissions', ['admin'])
          //   permissions = ['admin']
          // } else {
          //   permissions = await store.dispatch('user/getUserInfo')
          // }
          await store.dispatch('user/getUserInfo')
          await store.dispatch('user/setPermissions', ['admin'])
          let accessRoutes = []
          // if (authentication === 'intelligence') {
          //   accessRoutes = await store.dispatch('routes/setRoutes', permissions)
          // } else if (authentication === 'all') {
          accessRoutes = await store.dispatch('routes/setAllRoutes')
          let accessRoutesList = []
          accessRoutes.map(item => {
            if (item.component || item.path == '*') {
              return accessRoutesList.push(item)
            } else if (item.children && item.children.length > 0) {
              return accessRoutesList = [...accessRoutesList, ...item.children]
            }
          }).filter(item => item !== undefined)
          // }
          accessRoutesList.forEach((item) => {
            router.addRoute(item)
          })
          console.log(router)
          next({ ...to, replace: true })
        } catch {
          await store.dispatch('user/resetAccessToken')
          if (progressBar) VabProgress.done()
        }
      }
    }
  } else {
    if (routesWhiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      // if (recordRoute) {
      //   next(`/login?redirect=${to.path}`)
      // } else {
      next('/login')
      // }

      if (progressBar) VabProgress.done()
    }
  }
  document.title = getPageTitle(to.meta.title)
})
router.afterEach((to) => {
  if (to.meta.permissionsBtn) {
    store.dispatch('user/setPermissionsBtn', to.meta.permissionsBtn)
  }
  if (progressBar) VabProgress.done()
})
