/*
 * @Author: shangfei <EMAIL>
 * @Date: 2025-03-24 09:05:32
 * @LastEditors: shangfei <EMAIL>
 * @LastEditTime: 2025-05-24 09:45:53
 * @FilePath: /qst-merchant-admin-2.0/config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * 
 */
// develop	开发版
// trial	体验版
// release 正式版

// 测试环境
let test = {
  // path: 'http://**************:1024/',
  path: 'http://test-merchant.qidingdong.com/',
  // 接口环境
  baseURL: 'http://test-api.qidingdong.com',
  // baseURL: 'http://**************:8787',

  // 配置二维码环境
  envVersion: 'develop',
  // 是否校验二维码存在path
  check_path: false,
  type: 'test'
}

let master = {
  path: 'http://merchant.qidingdong.com/',
  // 接口环境
  baseURL: 'http://api.qidingdong.com',
  // 配置二维码环境
  envVersion: 'release',
  // 是否校验二维码存在path
  check_path: false,
  type: 'master'
}
export default test