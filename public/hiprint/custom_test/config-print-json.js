var configPrintJson = {
    "panels": [{
        "index": 0,
        "height": 297,
        "width": 210,
        "paperHeader": 0,
        "paperFooter": 805,
        "printElements": [{
            "tid": "configModule.name",
            "options": {
                "left": 20,
                "top": 25,
                "height": 42,
                "width": 107,
                "fontSize": 19,
                "fontWeight": "700",
                "textAlign": "center",
                "lineHeight": 39,
                "hideTitle": "1"
            }
        },
        {
            "tid": "configModule.email",
            "options": {
                "left": 390,
                "top": 35,
                "height": 13,
                "width": 165
            }
        }, {
            "tid": "configModule.address",
            "options": {
                "left": 390,
                "top": 55,
                "height": 13,
                "width": 165
            }
        }, {
            "tid": "configModule.phone",
            "options": {
                "left": 390,
                "top": 75,
                "height": 13,
                "width": 165
            }
        }, {
            "tid": "configModule.hline",
            "options": {
                "left": 10,
                "top": 110,
                "height": 10,
                "width": 573
            }
        }, {
            "tid": "configModule.customText",
            "options": {
                "left": 20,
                "top": 135,
                "height": 13,
                "width": 27,
                "title": "目标",
                "textAlign": "center"
            }
        }, {
            "tid": "configModule.target",
            "options": {
                "left": 20,
                "top": 160,
                "height": 13,
                "width": 562,
                "fontSize": 13,
                "hideTitle": "1"
            }
        }, {
            "tid": "configModule.customText",
            "options": {
                "left": 20,
                "top": 195,
                "height": 13,
                "width": 29,
                "title": "教育",
                "textAlign": "center"
            }
        }, {
            "tid": "configModule.professional",
            "options": {
                "left": 20,
                "top": 230,
                "height": 17,
                "width": 561,
                "fontSize": 12,
                "fontWeight": "600",
                "lineHeight": 17,
                "hideTitle": "1"
            }
        }, {
            "tid": "configModule.university",
            "options": {
                "left": 20,
                "top": 255,
                "height": 15,
                "width": 561,
                "fontWeight": "600",
                "hideTitle": "1"
            }
        }, {
            "tid": "configModule.universityAddress",
            "options": {
                "left": 20,
                "top": 280,
                "height": 13,
                "width": 120,
                "hideTitle": "1"
            }
        }, {
            "tid": "configModule.universityDate",
            "options": {
                "left": 20,
                "top": 305,
                "height": 13,
                "width": 120
            }
        }, {
            "tid": "configModule.tech",
            "options": {
                "left": 20,
                "top": 345,
                "height": 40,
                "width": 561
            }
        }, {
            "tid": "configModule.workExperience",
            "options": {
                "left": 20,
                "top": 420,
                "height": 44,
                "width": 559
            }
        }
        ],
        "paperNumberLeft": 565,
        "paperNumberTop": 819
    }]
}