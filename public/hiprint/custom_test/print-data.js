var printData = {
    company_name: '测试公司',
    receiver: '测试收货人', // 收货人
    order_time: '2018-05-05 12:00', // 下单时间
    order_number: '1234567890', // 订单号
    receiver_phone: '1234567890', // 联系电话
    receiver_address: '测试地址', // 详细地址
    buyer_message: '测试留言', // 买家留言
    seller_memo: '测试备注', // 卖家备注
    note: '测试备注', // 备注信息
    printer: '打印人', // 打印
    total_quantity: 2,
    order_total_price: 2,
    order_actual_payment: 2,
    table: [
        { id: '1', product_name: '测试商品1', product_spec: '蓝色', product_code: '120', product_quantity: '1', product_price: '10', product_discounted_unit_price: '1', product_total_price: '1', product_discounted_price: '1' },
        { id: '2', product_name: '测试商品2', product_spec: '蓝色', product_code: '120', product_quantity: '1', product_price: '10', product_discounted_unit_price: '1', product_total_price: '1', product_discounted_price: '1' },
    ]
};
