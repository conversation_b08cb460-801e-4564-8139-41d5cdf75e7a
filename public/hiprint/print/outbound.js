// 出库单
class JSON_DATA {
  width = 580; // 宽度
  key = 'id'; // 数据中的 唯一标识
  min_left = 20 // 最小左边距
  min_right = 20; // 最小右边距
  min_width = 180; // 普通宽度
  max_width = 270; // 半宽宽度
  min_height = 20; // 普通高度
  min_title_height = 30; // 普通高度
  table_height = 40; // 表格宽度
  init(options = {}, tableKey = {}) {
    let list = []
    Object.keys(options).forEach(key => {

      if (this[key] && options[key]) {
        let item = this[key]()
        list.push(item)
      }
    })
    console.log(list)
    list.push(this.table(tableKey, options))
    list.push(this.total(options.total_quantity, options.order_total_price, options.order_actual_payment))
    return list
  }
  // 备注
  total(isQuantity, isPrice, isPayment) {
    console.log('=======', isQuantity, isPrice, isPayment)
    return {
      options: {
        left: this.min_left,
        top: this.min_title_height + this.min_height * 2 + this.table_height,
        height: this.min_height,
        width: this.width - this.min_left - this.min_right,
        textAlign: 'right',
        textContentVerticalAlign: "middle",
      },
      printElementType: {
        tid: 'total',
        type: 'text',
        field: 'total',
        formatter: (title, value, options, templateData) => {
          let text = '总计：'
          let fu_hao = '，'
          let quantity = `数量：${templateData.total_quantity}`
          let price = `销售总金额：${templateData.order_total_price}`
          let payment = `实付金额：${templateData.order_actual_payment}`
          text += (isQuantity ? quantity : '') + (isQuantity && isPrice ? fu_hao : '') + (isPrice ? price : '') + ((isPrice || isQuantity) && isPayment ? fu_hao : '') + (isPayment ? payment : '')
          if (!isQuantity && !isPrice && isPayment) {
            return ''
          }
          return text
        }
      }
    }
  }


  // 表格数据
  table(tableKey, options) {
    let columns = {

      product_code: {
        title: '商品编码', field: 'product_code', colspan: 1, rowspan: 1, width: 50
      },
      product_name: {
        title: '商品名称', field: 'product_name', colspan: 1, rowspan: 1
      },
      // product_barcode: {
      //   title: '商品条码', field: 'product_barcode', colspan: 1, rowspan: 1
      // },
      product_quantity: {
        title: '数量', field: 'product_quantity', colspan: 1, rowspan: 1, width: 50
      },
      product_spec: {
        title: '规格', field: 'product_spec', colspan: 1, rowspan: 1
      },
      product_price: {
        title: '销售单价', field: 'product_price', colspan: 1, rowspan: 1, width: 50
      },

      product_discounted_unit_price: {
        title: '优惠后单价', field: 'product_discounted_unit_price', colspan: 1, rowspan: 1, width: 50
      },


      product_total_price: {
        title: '销售总金额', field: 'product_total_price', colspan: 1, rowspan: 1, width: 50
      },
      product_discounted_price: {
        title: '优惠后商品总金额', field: 'product_discounted_price', colspan: 1, rowspan: 1, width: 50
      },
    }
    let tableKeysList = Object.keys(tableKey).map(item => {
      return tableKey[item] && columns[item] ? columns[item] : null
    }).filter(item => !!item)
    return {
      options: {
        left: this.min_left,
        top: this.min_title_height + this.min_height * 2,
        height: this.table_height,
        width: this.width - this.min_left - this.min_right,
        textAlign: 'center',
      },
      printElementType: {
        title: '表格',
        type: 'table',
        field: 'table',
        rowStyler: (a, b, s) => {
          console.log(a, b, s)
          return {
            'height': '50px',
          }
        },
        columns: [
          [
            {
              title: '序号', field: 'id', colspan: 1, rowspan: 1, width: 50,
            },
            ...tableKeysList
          ]

        ],
      },
    }
  }

  // 公司名
  company_name() {
    return {
      options: {
        left: 0,
        top: 0,
        height: this.min_title_height,
        width: 580,
        textAlign: 'center',
        textContentVerticalAlign: "middle",
        data: '{company_name}出库单',
        fixed: "true",
        fontSize: '16'
      },
      printElementType: {
        type: 'text',
        field: 'company_name',
        formatter: (title, value, options, templateData) => {
          let text = options.data
          if (templateData && templateData[this.key]) {
            text = text.replace('{company_name}', value || '')
          }
          return text
        },
      },
    }
  }
  // 收件人
  receiver() {
    return {
      options: {
        left: this.min_left,
        top: this.min_title_height,
        height: this.min_height,
        width: this.min_width,
        data: '收件人:{receiver}',
        textContentVerticalAlign: "middle",
      },
      printElementType: {
        type: 'text',
        field: 'receiver',
        testData: '',
        formatter: (title, value, options, templateData) => {
          let text = options.data
          if (templateData && templateData[this.key]) {
            text = text.replace('{receiver}', value || '')
          }
          return text
        }
      }
    }
  }

  // 下单时间
  order_time() {
    return {
      options: {
        left: this.min_left + this.min_width,
        top: this.min_title_height,
        height: this.min_height,
        width: this.min_width,
        data: '下单时间:{order_time}',
        textContentVerticalAlign: "middle",
      },
      printElementType: {
        tid: 'order_time',
        type: 'text',
        field: 'order_time',
        formatter: (title, value, options, templateData) => {
          let text = options.data
          if (templateData && templateData[this.key]) {
            text = text.replace('{order_time}', value || '')
          }
          return text
        }
      }
    }
  }

  // 订单号
  order_number() {
    return {
      options: {
        left: this.min_left + this.min_width * 2,
        top: this.min_title_height,
        height: this.min_height,
        width: this.min_width,
        data: '订单号:{order_number}',
        textAlign: 'right',
        textContentVerticalAlign: "middle",
      },
      printElementType: {
        tid: 'order_number',
        type: 'text',
        field: 'order_number',
        formatter: (title, value, options, templateData) => {
          let text = options.data
          if (templateData && templateData[this.key]) {
            text = text.replace('{order_number}', value || '')
          }
          return text
        }
      }
    }
  }



  // 联系电话
  receiver_phone() {
    return {
      options: {
        left: this.min_left,
        top: this.min_title_height + this.min_height,
        height: this.min_height,
        width: this.min_width,
        data: '联系电话:{receiver_phone}',
        textContentVerticalAlign: "middle",
      },
      printElementType: {
        tid: 'receiver_phone',
        type: 'text',
        field: 'receiver_phone',
        formatter: (title, value, options, templateData) => {
          let text = options.data
          if (templateData && templateData[this.key]) {
            text = text.replace('{receiver_phone}', value || '')
          }
          return text
        }
      }
    }
  }

  // 收件地址
  receiver_address() {
    return {
      options: {
        left: this.min_left + this.min_width,
        top: this.min_title_height + this.min_height,
        height: this.min_height,
        width: this.min_width * 2,
        data: '收件地址: {receiver_address}',
        textAlign: 'left',
        textContentVerticalAlign: "middle",
      },
      printElementType: {
        tid: 'receiver_address',
        type: 'text',
        field: 'receiver_address',
        formatter: (title, value, options, templateData) => {
          let text = options.data
          if (templateData && templateData[this.key]) {
            text = text.replace('{receiver_address}', value || '')
          }
          return text
        }
      }
    }
  }

  // 买家留言
  buyer_message() {
    return {
      options: {
        left: this.min_left,
        top: this.min_title_height + this.min_height * 3 + this.table_height,
        height: this.min_height + 30,
        width: this.max_width,
        data: '买家留言:{buyer_message}',
        textContentVerticalAlign: "middle",
      },
      printElementType: {
        tid: 'buyer_message',
        type: 'text',
        field: 'buyer_message',
        formatter: (title, value, options, templateData) => {
          let text = options.data
          if (templateData && templateData[this.key]) {
            text = text.replace('{buyer_message}', value || '')
          }
          return text
        }
      }
    }
  }

  // 卖家备注
  seller_memo() {
    return {
      options: {
        left: this.min_left + this.max_width,
        top: this.min_title_height + this.min_height * 3 + this.table_height,
        height: this.min_height + 30,
        width: this.max_width,
        data: '卖家备注:{seller_memo}',
        textContentVerticalAlign: "middle",
        textAlign: 'right',
      },
      printElementType: {
        tid: 'seller_memo',
        type: 'text',
        field: 'seller_memo',
        formatter: (title, value, options, templateData) => {
          let text = options.data
          if (templateData && templateData[this.key]) {
            text = text.replace('{seller_memo}', value || '')
          }
          return text
        }
      }
    }
  }


  // 备注
  note() {
    return {
      options: {
        left: this.min_left,
        top: this.min_title_height + this.min_height * 4 + this.table_height + 30,
        height: this.min_height,
        width: this.min_width,
        data: '备注:{note}',
        textAlign: 'left',
        textContentVerticalAlign: "middle",
      },
      printElementType: {
        tid: 'note',
        type: 'text',
        field: 'note',
        formatter: (title, value, options, templateData) => {
          let text = options.data
          if (templateData && templateData[this.key]) {
            text = text.replace('{note}', value || '')
          }
          return text
        }
      }
    }
  }



  // 打印人
  printer() {
    return {
      options: {
        left: this.min_left + this.max_width,
        top: this.min_title_height + this.min_height * 4 + this.table_height + 30,
        height: this.min_height,
        width: this.max_width,
        textAlign: 'right',
        data: '打印人:{printer}',
        textContentVerticalAlign: "middle",
      },
      printElementType: {
        tid: 'printer',
        type: 'text',
        field: 'printer',
        formatter: (title, value, options, templateData) => {
          let text = options.data
          if (templateData && templateData[this.key]) {
            text = text.replace('{printer}', value || '')
          }
          return text
        }
      }
    }
  }

  // { "panels": [{ "index": 0, "paperType": "A4", "height": 297, "width": 210, "paperHeader": 0, "paperFooter": 841.8897637795277, "printElements": [{ "options": { "left": 177, "top": 27, "height": 27, "width": 264, "title": "{company_name}出库单", "textAlign": "center", "textContentVerticalAlign": "middle", "lineHeight": 16.5 }, "printElementType": { "type": "text" } }, { "options": { "left": 27, "top": 54, "height": 28.5, "width": 120, "title": "收件人:{receiver}", "field": "receiver", "textContentVerticalAlign": "middle" }, "printElementType": { "type": "text" } }, { "options": { "left": 253.5, "top": 52.5, "height": 30, "width": 120, "title": "下单时间:{order_time}", "textContentVerticalAlign": "middle" }, "printElementType": { "type": "text" } }, { "options": { "left": 385.5, "top": 54, "height": 28.5, "width": 181.5, "title": "订单号:{order_number}", "textAlign": "right", "textContentVerticalAlign": "middle" }, "printElementType": { "type": "text" } }, { "options": { "left": 385.5, "top": 82.5, "height": 28.5, "width": 181.5, "title": "收件地址: {receiver_address}", "textAlign": "right", "textContentVerticalAlign": "middle" }, "printElementType": { "type": "text" } }, { "options": { "left": 27, "top": 82.5, "height": 28.5, "width": 120, "title": "联系电话:{receiver_phone}", "textContentVerticalAlign": "middle" }, "printElementType": { "type": "text" } }, { "options": { "left": 28.5, "top": 196.5, "height": 27, "width": 120, "title": "买家留言:{buyer_message}", "textContentVerticalAlign": "middle" }, "printElementType": { "type": "text" } }, { "options": { "left": 445.5, "top": 196.5, "height": 25.5, "width": 120, "title": "备注:{buyer_note}", "textAlign": "right", "textContentVerticalAlign": "middle" }, "printElementType": { "type": "text" } }, { "options": { "left": 402, "top": 220.5, "height": 31.5, "width": 163.5, "title": "第{current_page}页,共{total_page}页", "textAlign": "right", "textContentVerticalAlign": "middle" }, "printElementType": { "type": "text" } }, { "options": { "left": 28.5, "top": 223.5, "height": 30, "width": 120, "title": "打印人:{printer}", "textContentVerticalAlign": "middle" }, "printElementType": { "type": "text" } }, { "options": { "left": 27, "top": 127.5, "height": 39, "width": 550, "columns": [[{ "width": 275, "colspan": 1, "rowspan": 1, "checked": true }, { "width": 275, "colspan": 1, "rowspan": 1, "checked": true }]] }, "printElementType": { "title": "表格", "type": "tableCustom" } }] }] }

}
export const json_data = new JSON_DATA();