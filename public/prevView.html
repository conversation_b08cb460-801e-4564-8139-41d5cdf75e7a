<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <title></title>
  <link href="hiprint/css/hiprint.css" rel="stylesheet" />
  <link href="hiprint/css/print-lock.css" rel="stylesheet" />
  <link href="hiprint/content/bootstrap.min.css" rel="stylesheet">
  <script src="hiprint/content/jquery.min.js"></script>
  <script src="hiprint/content/bootstrap.min.js"></script>
</head>

<body>
  <div id="zzc"></div>
  <!-- 模版预览 -->
  <div id="hiprintHtml"></div>
  <!-- 模版设计 -->
  <div id="printDesign"></div>
  <!-- <div id="print">打印</div> -->
  <script src="hiprint/custom_test/print-data.js"></script>
  <!--单独使用无需引入  -->
  <script src="hiprint/hiprint/polyfill.min.js"></script>
  <script src="hiprint/plugins/jquery.minicolors.min.js"></script>
  <script src="hiprint/plugins/JsBarcode.all.min.js"></script>
  <script src="hiprint/plugins/qrcode.js"></script>
  <script src="hiprint/hiprint.bundle.js"></script>
  <script src="hiprint/plugins/jquery.hiwprint.js"></script>

  <script>
    $(document).ready(function () {
      window.addEventListener('message', function (e) {
        console.log(e, '打印数据')
        // 打印数据接收
        // 打印数据
        // 打印类型 1 电子面单 2 小票 3 出库单
        // isOneListener 是否只监听一次数据
        if (e.data) {
          let {
            data,
            printType,
            isOneListener
          } = e.data
          // if (isOneListener) {
          //   window.removeEventListener('message')
          // }
          getPrevView(data, printType)
        }
      })
      function getPrevView(prevView, printType) {
        if (!prevView) {
          return
        }
        prevView = JSON.parse(prevView)
        var hiprintTemplate = new hiprint.PrintTemplate({
          template: prevView,
        });
        if (printType == 3) {
          $('#hiprintHtml').html(hiprintTemplate.getHtml(printData))
        } else {
          $('#printDesign').width(prevView.panels[0].width + 'mm');
          $('#printDesign').height(prevView.panels[0].height + 'mm');
          hiprintTemplate.design('#printDesign');
        }
      }
    });
  </script>
  <style>
    #printDesign {
      overflow: hidden;
    }

    #zzc {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 9999;
      cursor: pointer;
    }
  </style>
</body>


</html>