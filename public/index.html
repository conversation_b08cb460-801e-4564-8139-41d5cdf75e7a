<!DOCTYPE html>
<html lang="zh-cmn-Hans">

<head>
  <meta charset="utf-8" />
  <meta content="IE=edge" http-equiv="X-UA-Compatible" />
  <meta content="width=device-width,initial-scale=1.0" name="viewport" />
  <link href="<%= BASE_URL %>m96zg-jcng2-001.ico" rel="icon" />
  <title>
    <%= VUE_APP_TITLE %>
  </title>
  <meta content="<%= VUE_APP_TITLE %>" name="keywords" />
  <meta content="" name="description" />
  <meta content="<%= VUE_APP_AUTHOR %>" name="author" />
  <link href="<%= BASE_URL %>static/css/loading.css" rel="stylesheet" />
</head>

<body>
  <div id="vue-admin-better">
    <div class="first-loading-wrp">
      <div class="loading-wrp">
        <span class="dot dot-spin">
          <i></i>
          <i></i>
          <i></i>
          <i></i>
        </span>
      </div>
      <h1>
        <%= VUE_APP_TITLE %>
      </h1>
    </div>
  </div>
  <script>
    ; /^http(s*):\/\//.test(location.href)
  </script>
  <script>
    if (window.location.hostname !== 'localhost') {
      var _hmt = _hmt || []
        ; (function () {
          var hm = document.createElement('script')
          hm.src = 'https://hm.baidu.com/hm.js?7174bade1219f9cc272e7978f9523fc8'
          var s = document.getElementsByTagName('script')[0]
          s.parentNode.insertBefore(hm, s)
        })()
    }
  </script>
  <script charset="utf-8" src="./print.js"></script>

  <!-- 引入腾讯地图api -->
  <script charset="utf-8" src="http://map.qq.com/api/js?v=2.exp&key=PEXBZ-WSCKT-E2BXK-VPOVZ-KOP46-Q7F26"></script>
  <script charset="utf-8"
    src="https://map.qq.com/api/js?v=2.exp&key=PEXBZ-WSCKT-E2BXK-VPOVZ-KOP46-Q7F26&libraries=place"></script>
  <!--定位-->
  <script src="https://3gimg.qq.com/lightmap/components/geolocation/geolocation.min.js"></script>
  <script src="https://mapapi.qq.com/web/mapComponents/geoLocation/v/geolocation.min.js"></script>
</body>

</html>