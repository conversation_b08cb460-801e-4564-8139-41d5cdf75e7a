<!-- 打印页面 -->
<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <title></title>
  <link href="hiprint/css/hiprint.css" rel="stylesheet" />
  <link href="hiprint/css/print-lock.css" rel="stylesheet" />
  <link href="hiprint/content/bootstrap.min.css" rel="stylesheet">
  <script src="hiprint/content/jquery.min.js"></script>
  <script src="hiprint/content/bootstrap.min.js"></script>
  <script src="hiprint/content/axios.min.js"></script>
  <script src="hiprint/content/qs.js"></script>
  <script src="hiprint/content/socket.io.min.js"></script>

</head>

<body>
  <!-- <div id="print">打印</div> -->
  <script src="hiprint/custom_test/print-data.js"></script>
  <!--单独使用无需引入  -->
  <script src="hiprint/hiprint/polyfill.min.js"></script>
  <script src="hiprint/plugins/jquery.minicolors.min.js"></script>
  <script src="hiprint/plugins/JsBarcode.all.min.js"></script>
  <script src="hiprint/plugins/qrcode.js"></script>
  <script src="hiprint/hiprint.bundle.js"></script>
  <script src="hiprint/plugins/jquery.hiwprint.js"></script>

  <script>

    $(document).ready(function () {
      let base_url
      let base_token = ''
      let base_params = ''
      let print_type = ''
      window.addEventListener('message', function (e) {
        console.log(e)
        // 接收参数
        let {
          baseURL = '',
          token = '',
          params = {},
          printType = ''
        } = e.data;
        console.log(baseURL, token, params, printType)
        if (token && params && printType) {
          base_url = baseURL
          base_token = token
          print_type = printType
          base_params = params
          getPrint()
        } else {
          postMessage({
            code: 400,
            msg: '缺少参数'
          });
        }
      })

      // 请求数据
      function httpGet() {
        return new Promise((resolve, reject) => {
          const instance = axios.request({
            baseURL: base_url,
            url: base_params.url,
            data: base_params,
            method: 'post',
            timeout: 10000,
            headers: {
              'Authorization': 'Bearer ' + base_token,
              'Content-Type': 'application/json;charset=UTF-8'
            }
          }).then(res => {
            resolve(res.data)
          }).catch(err => {
            postMessage(err.data);
          })
        })
      }

      // 传递参数
      function postMessage({ code, msg }) {
        window.parent.postMessage({
          code,
          msg
        }, '*');
      }

      // 获取数据
      function getPrint() {
        console.log('-=-=-=')
        httpGet().then((res) => {
          console.log(res)
          if (res.code == 200) {
            executePrinting(res.data)


          }
        })
      }

      // 打印
      function executePrinting(data) {
        console.log(print_type)
        let hiprintTemplate
        let printTem

        // 1 电子面单
        if (print_type == 1) {
          hiprint.init({});
          let templates = data.map(item => {
            let hiprintTemplate = new hiprint.PrintTemplate({
              template: JSON.parse(item.template),
            })
            return {
              template: hiprintTemplate,
              data: item.print_data
            }
          })
          console.log(templates)
          hiprint.print2({
            templates: templates, options: { printer: '', landscape: true, paperNumberToggleInEven: false }
          }, function (data) { console.log('success') });
          return;

        } else {
          printTem = JSON.parse(data.printTem)
          // var hiprintTemplate = new hiprint.PrintTemplate({
          //   template: printTem,
          // });
        }


        // 打印成功
        hiprintTemplate.on('printSuccess', function (data) {
          postMessage(200, '打印成功');
        })
        // 打印失败
        hiprintTemplate.on('printError', function (data) {
          console.log(data);
          postMessage(500, data);
        })

        if (print_type == 1) {


        } else {
          hiprintTemplate.print2(
            data,
            {
              copies: 1, // 打印份数 默认 1
              paperNumberToggleInEven: false,
              // styleHandler: () => {
              //   return extendCss;
              // },
            }
          );
        }



      }
    });
  </script>
</body>


</html>